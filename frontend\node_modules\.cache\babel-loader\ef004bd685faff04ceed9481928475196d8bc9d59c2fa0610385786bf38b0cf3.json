{"ast": null, "code": "import { mark } from '../../adaptor';\nimport { flow, transformOptions, get, set } from '../../utils';\n/**\n * @param chart\n * @param options\n */\nexport function adaptor(params) {\n  var colorField = function (params) {\n    var options = params.options;\n    var _a = options.children,\n      children = _a === void 0 ? [] : _a,\n      legend = options.legend;\n    if (!legend) return params;\n    children.forEach(function (option) {\n      if (!get(option, 'colorField')) {\n        var yField_1 = get(option, 'yField');\n        set(option, 'colorField', function () {\n          return yField_1;\n        });\n      }\n    });\n    return params;\n  };\n  /**\n   * @description Top level annotations needs to share scale, when top level annotations is not empty, scale needs to be dynamically set.\n   */\n  var annotations = function (params) {\n    var options = params.options;\n    var _a = options.annotations,\n      annotations = _a === void 0 ? [] : _a,\n      _b = options.children,\n      children = _b === void 0 ? [] : _b,\n      scale = options.scale;\n    var sharedScale = false;\n    if (get(scale, 'y.key')) {\n      return params;\n    }\n    children.forEach(function (child, index) {\n      if (!get(child, 'scale.y.key')) {\n        var scaleKey_1 = \"child\".concat(index, \"Scale\");\n        set(child, 'scale.y.key', scaleKey_1);\n        var _a = child.annotations,\n          childAnnotations = _a === void 0 ? [] : _a;\n        /**\n         * @description If the child has annotations, the scale of the child needs to be assigned scaleKey to connect the annotation.\n         */\n        if (childAnnotations.length > 0) {\n          set(child, 'scale.y.independent', false);\n          childAnnotations.forEach(function (annotation) {\n            set(annotation, 'scale.y.key', scaleKey_1);\n          });\n        }\n        if (!sharedScale && annotations.length > 0 && get(child, 'scale.y.independent') === undefined) {\n          sharedScale = true;\n          set(child, 'scale.y.independent', false);\n          annotations.forEach(function (annotation) {\n            set(annotation, 'scale.y.key', scaleKey_1);\n          });\n        }\n      }\n    });\n    return params;\n  };\n  return flow(colorField, annotations, mark, transformOptions)(params);\n}", "map": {"version": 3, "names": ["mark", "flow", "transformOptions", "get", "set", "adaptor", "params", "colorField", "options", "_a", "children", "legend", "for<PERSON>ach", "option", "yField_1", "annotations", "_b", "scale", "sharedScale", "child", "index", "scaleKey_1", "concat", "childAnnotations", "length", "annotation", "undefined"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/@ant-design/plots/es/core/plots/dual-axes/adaptor.js"], "sourcesContent": ["import { mark } from '../../adaptor';\nimport { flow, transformOptions, get, set } from '../../utils';\n/**\n * @param chart\n * @param options\n */\nexport function adaptor(params) {\n    var colorField = function (params) {\n        var options = params.options;\n        var _a = options.children, children = _a === void 0 ? [] : _a, legend = options.legend;\n        if (!legend)\n            return params;\n        children.forEach(function (option) {\n            if (!get(option, 'colorField')) {\n                var yField_1 = get(option, 'yField');\n                set(option, 'colorField', function () { return yField_1; });\n            }\n        });\n        return params;\n    };\n    /**\n     * @description Top level annotations needs to share scale, when top level annotations is not empty, scale needs to be dynamically set.\n     */\n    var annotations = function (params) {\n        var options = params.options;\n        var _a = options.annotations, annotations = _a === void 0 ? [] : _a, _b = options.children, children = _b === void 0 ? [] : _b, scale = options.scale;\n        var sharedScale = false;\n        if (get(scale, 'y.key')) {\n            return params;\n        }\n        children.forEach(function (child, index) {\n            if (!get(child, 'scale.y.key')) {\n                var scaleKey_1 = \"child\".concat(index, \"Scale\");\n                set(child, 'scale.y.key', scaleKey_1);\n                var _a = child.annotations, childAnnotations = _a === void 0 ? [] : _a;\n                /**\n                 * @description If the child has annotations, the scale of the child needs to be assigned scaleKey to connect the annotation.\n                 */\n                if (childAnnotations.length > 0) {\n                    set(child, 'scale.y.independent', false);\n                    childAnnotations.forEach(function (annotation) {\n                        set(annotation, 'scale.y.key', scaleKey_1);\n                    });\n                }\n                if (!sharedScale && annotations.length > 0 && get(child, 'scale.y.independent') === undefined) {\n                    sharedScale = true;\n                    set(child, 'scale.y.independent', false);\n                    annotations.forEach(function (annotation) {\n                        set(annotation, 'scale.y.key', scaleKey_1);\n                    });\n                }\n            }\n        });\n        return params;\n    };\n    return flow(colorField, annotations, mark, transformOptions)(params);\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,eAAe;AACpC,SAASC,IAAI,EAAEC,gBAAgB,EAAEC,GAAG,EAAEC,GAAG,QAAQ,aAAa;AAC9D;AACA;AACA;AACA;AACA,OAAO,SAASC,OAAOA,CAACC,MAAM,EAAE;EAC5B,IAAIC,UAAU,GAAG,SAAAA,CAAUD,MAAM,EAAE;IAC/B,IAAIE,OAAO,GAAGF,MAAM,CAACE,OAAO;IAC5B,IAAIC,EAAE,GAAGD,OAAO,CAACE,QAAQ;MAAEA,QAAQ,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;MAAEE,MAAM,GAAGH,OAAO,CAACG,MAAM;IACtF,IAAI,CAACA,MAAM,EACP,OAAOL,MAAM;IACjBI,QAAQ,CAACE,OAAO,CAAC,UAAUC,MAAM,EAAE;MAC/B,IAAI,CAACV,GAAG,CAACU,MAAM,EAAE,YAAY,CAAC,EAAE;QAC5B,IAAIC,QAAQ,GAAGX,GAAG,CAACU,MAAM,EAAE,QAAQ,CAAC;QACpCT,GAAG,CAACS,MAAM,EAAE,YAAY,EAAE,YAAY;UAAE,OAAOC,QAAQ;QAAE,CAAC,CAAC;MAC/D;IACJ,CAAC,CAAC;IACF,OAAOR,MAAM;EACjB,CAAC;EACD;AACJ;AACA;EACI,IAAIS,WAAW,GAAG,SAAAA,CAAUT,MAAM,EAAE;IAChC,IAAIE,OAAO,GAAGF,MAAM,CAACE,OAAO;IAC5B,IAAIC,EAAE,GAAGD,OAAO,CAACO,WAAW;MAAEA,WAAW,GAAGN,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;MAAEO,EAAE,GAAGR,OAAO,CAACE,QAAQ;MAAEA,QAAQ,GAAGM,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;MAAEC,KAAK,GAAGT,OAAO,CAACS,KAAK;IACrJ,IAAIC,WAAW,GAAG,KAAK;IACvB,IAAIf,GAAG,CAACc,KAAK,EAAE,OAAO,CAAC,EAAE;MACrB,OAAOX,MAAM;IACjB;IACAI,QAAQ,CAACE,OAAO,CAAC,UAAUO,KAAK,EAAEC,KAAK,EAAE;MACrC,IAAI,CAACjB,GAAG,CAACgB,KAAK,EAAE,aAAa,CAAC,EAAE;QAC5B,IAAIE,UAAU,GAAG,OAAO,CAACC,MAAM,CAACF,KAAK,EAAE,OAAO,CAAC;QAC/ChB,GAAG,CAACe,KAAK,EAAE,aAAa,EAAEE,UAAU,CAAC;QACrC,IAAIZ,EAAE,GAAGU,KAAK,CAACJ,WAAW;UAAEQ,gBAAgB,GAAGd,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;QACtE;AAChB;AACA;QACgB,IAAIc,gBAAgB,CAACC,MAAM,GAAG,CAAC,EAAE;UAC7BpB,GAAG,CAACe,KAAK,EAAE,qBAAqB,EAAE,KAAK,CAAC;UACxCI,gBAAgB,CAACX,OAAO,CAAC,UAAUa,UAAU,EAAE;YAC3CrB,GAAG,CAACqB,UAAU,EAAE,aAAa,EAAEJ,UAAU,CAAC;UAC9C,CAAC,CAAC;QACN;QACA,IAAI,CAACH,WAAW,IAAIH,WAAW,CAACS,MAAM,GAAG,CAAC,IAAIrB,GAAG,CAACgB,KAAK,EAAE,qBAAqB,CAAC,KAAKO,SAAS,EAAE;UAC3FR,WAAW,GAAG,IAAI;UAClBd,GAAG,CAACe,KAAK,EAAE,qBAAqB,EAAE,KAAK,CAAC;UACxCJ,WAAW,CAACH,OAAO,CAAC,UAAUa,UAAU,EAAE;YACtCrB,GAAG,CAACqB,UAAU,EAAE,aAAa,EAAEJ,UAAU,CAAC;UAC9C,CAAC,CAAC;QACN;MACJ;IACJ,CAAC,CAAC;IACF,OAAOf,MAAM;EACjB,CAAC;EACD,OAAOL,IAAI,CAACM,UAAU,EAAEQ,WAAW,EAAEf,IAAI,EAAEE,gBAAgB,CAAC,CAACI,MAAM,CAAC;AACxE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}