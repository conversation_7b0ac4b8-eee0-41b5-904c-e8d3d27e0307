{"ast": null, "code": "export { isPointInPolygon } from './is-point-in-polygon';\nexport { isPolygonsIntersect } from './is-polygons-intersect';", "map": {"version": 3, "names": ["isPointInPolygon", "isPolygonsIntersect"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\util\\src\\math\\index.ts"], "sourcesContent": ["export { isPointInPolygon } from './is-point-in-polygon';\nexport { isPolygonsIntersect } from './is-polygons-intersect';\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,mBAAmB,QAAQ,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}