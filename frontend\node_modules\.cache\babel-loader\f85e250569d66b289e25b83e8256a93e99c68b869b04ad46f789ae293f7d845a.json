{"ast": null, "code": "import { createTheme } from '@ant-design/cssinjs';\nimport defaultDerivative from './index';\nconst defaultTheme = createTheme(defaultDerivative);\nexport default defaultTheme;", "map": {"version": 3, "names": ["createTheme", "defaultDerivative", "defaultTheme"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/antd/es/theme/themes/default/theme.js"], "sourcesContent": ["import { createTheme } from '@ant-design/cssinjs';\nimport defaultDerivative from './index';\nconst defaultTheme = createTheme(defaultDerivative);\nexport default defaultTheme;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,qBAAqB;AACjD,OAAOC,iBAAiB,MAAM,SAAS;AACvC,MAAMC,YAAY,GAAGF,WAAW,CAACC,iBAAiB,CAAC;AACnD,eAAeC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}