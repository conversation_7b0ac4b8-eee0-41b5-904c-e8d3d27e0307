{"ast": null, "code": "\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport React from 'react';\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport { DisabledContextProvider } from '../config-provider/DisabledContext';\nimport { useLocale } from '../locale';\nimport NormalCancelBtn from './components/NormalCancelBtn';\nimport NormalOkBtn from './components/NormalOkBtn';\nimport { ModalContextProvider } from './context';\nimport { getConfirmLocale } from './locale';\nexport function renderCloseIcon(prefixCls, closeIcon) {\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-close-x`\n  }, closeIcon || /*#__PURE__*/React.createElement(CloseOutlined, {\n    className: `${prefixCls}-close-icon`\n  }));\n}\nexport const Footer = props => {\n  const {\n    okText,\n    okType = 'primary',\n    cancelText,\n    confirmLoading,\n    onOk,\n    onCancel,\n    okButtonProps,\n    cancelButtonProps,\n    footer\n  } = props;\n  const [locale] = useLocale('Modal', getConfirmLocale());\n  // ================== Locale Text ==================\n  const okTextLocale = okText || (locale === null || locale === void 0 ? void 0 : locale.okText);\n  const cancelTextLocale = cancelText || (locale === null || locale === void 0 ? void 0 : locale.cancelText);\n  // ================= Context Value =================\n  const btnCtxValue = {\n    confirmLoading,\n    okButtonProps,\n    cancelButtonProps,\n    okTextLocale,\n    cancelTextLocale,\n    okType,\n    onOk,\n    onCancel\n  };\n  const btnCtxValueMemo = React.useMemo(() => btnCtxValue, _toConsumableArray(Object.values(btnCtxValue)));\n  let footerNode;\n  if (typeof footer === 'function' || typeof footer === 'undefined') {\n    footerNode = /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(NormalCancelBtn, null), /*#__PURE__*/React.createElement(NormalOkBtn, null));\n    if (typeof footer === 'function') {\n      footerNode = footer(footerNode, {\n        OkBtn: NormalOkBtn,\n        CancelBtn: NormalCancelBtn\n      });\n    }\n    footerNode = /*#__PURE__*/React.createElement(ModalContextProvider, {\n      value: btnCtxValueMemo\n    }, footerNode);\n  } else {\n    footerNode = footer;\n  }\n  return /*#__PURE__*/React.createElement(DisabledContextProvider, {\n    disabled: false\n  }, footerNode);\n};", "map": {"version": 3, "names": ["_toConsumableArray", "React", "CloseOutlined", "DisabledContextProvider", "useLocale", "NormalCancelBtn", "NormalOkBtn", "ModalContextProvider", "getConfirmLocale", "renderCloseIcon", "prefixCls", "closeIcon", "createElement", "className", "Footer", "props", "okText", "okType", "cancelText", "confirmLoading", "onOk", "onCancel", "okButtonProps", "cancelButtonProps", "footer", "locale", "okTextLocale", "cancelTextLocale", "btnCtxValue", "btnCtxValueMemo", "useMemo", "Object", "values", "footerNode", "Fragment", "OkBtn", "CancelBtn", "value", "disabled"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/antd/es/modal/shared.js"], "sourcesContent": ["\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport React from 'react';\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport { DisabledContextProvider } from '../config-provider/DisabledContext';\nimport { useLocale } from '../locale';\nimport NormalCancelBtn from './components/NormalCancelBtn';\nimport NormalOkBtn from './components/NormalOkBtn';\nimport { ModalContextProvider } from './context';\nimport { getConfirmLocale } from './locale';\nexport function renderCloseIcon(prefixCls, closeIcon) {\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-close-x`\n  }, closeIcon || /*#__PURE__*/React.createElement(CloseOutlined, {\n    className: `${prefixCls}-close-icon`\n  }));\n}\nexport const Footer = props => {\n  const {\n    okText,\n    okType = 'primary',\n    cancelText,\n    confirmLoading,\n    onOk,\n    onCancel,\n    okButtonProps,\n    cancelButtonProps,\n    footer\n  } = props;\n  const [locale] = useLocale('Modal', getConfirmLocale());\n  // ================== Locale Text ==================\n  const okTextLocale = okText || (locale === null || locale === void 0 ? void 0 : locale.okText);\n  const cancelTextLocale = cancelText || (locale === null || locale === void 0 ? void 0 : locale.cancelText);\n  // ================= Context Value =================\n  const btnCtxValue = {\n    confirmLoading,\n    okButtonProps,\n    cancelButtonProps,\n    okTextLocale,\n    cancelTextLocale,\n    okType,\n    onOk,\n    onCancel\n  };\n  const btnCtxValueMemo = React.useMemo(() => btnCtxValue, _toConsumableArray(Object.values(btnCtxValue)));\n  let footerNode;\n  if (typeof footer === 'function' || typeof footer === 'undefined') {\n    footerNode = /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(NormalCancelBtn, null), /*#__PURE__*/React.createElement(NormalOkBtn, null));\n    if (typeof footer === 'function') {\n      footerNode = footer(footerNode, {\n        OkBtn: NormalOkBtn,\n        CancelBtn: NormalCancelBtn\n      });\n    }\n    footerNode = /*#__PURE__*/React.createElement(ModalContextProvider, {\n      value: btnCtxValueMemo\n    }, footerNode);\n  } else {\n    footerNode = footer;\n  }\n  return /*#__PURE__*/React.createElement(DisabledContextProvider, {\n    disabled: false\n  }, footerNode);\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,aAAa,MAAM,0CAA0C;AACpE,SAASC,uBAAuB,QAAQ,oCAAoC;AAC5E,SAASC,SAAS,QAAQ,WAAW;AACrC,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,WAAW,MAAM,0BAA0B;AAClD,SAASC,oBAAoB,QAAQ,WAAW;AAChD,SAASC,gBAAgB,QAAQ,UAAU;AAC3C,OAAO,SAASC,eAAeA,CAACC,SAAS,EAAEC,SAAS,EAAE;EACpD,OAAO,aAAaV,KAAK,CAACW,aAAa,CAAC,MAAM,EAAE;IAC9CC,SAAS,EAAE,GAAGH,SAAS;EACzB,CAAC,EAAEC,SAAS,IAAI,aAAaV,KAAK,CAACW,aAAa,CAACV,aAAa,EAAE;IAC9DW,SAAS,EAAE,GAAGH,SAAS;EACzB,CAAC,CAAC,CAAC;AACL;AACA,OAAO,MAAMI,MAAM,GAAGC,KAAK,IAAI;EAC7B,MAAM;IACJC,MAAM;IACNC,MAAM,GAAG,SAAS;IAClBC,UAAU;IACVC,cAAc;IACdC,IAAI;IACJC,QAAQ;IACRC,aAAa;IACbC,iBAAiB;IACjBC;EACF,CAAC,GAAGT,KAAK;EACT,MAAM,CAACU,MAAM,CAAC,GAAGrB,SAAS,CAAC,OAAO,EAAEI,gBAAgB,CAAC,CAAC,CAAC;EACvD;EACA,MAAMkB,YAAY,GAAGV,MAAM,KAAKS,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACT,MAAM,CAAC;EAC9F,MAAMW,gBAAgB,GAAGT,UAAU,KAAKO,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACP,UAAU,CAAC;EAC1G;EACA,MAAMU,WAAW,GAAG;IAClBT,cAAc;IACdG,aAAa;IACbC,iBAAiB;IACjBG,YAAY;IACZC,gBAAgB;IAChBV,MAAM;IACNG,IAAI;IACJC;EACF,CAAC;EACD,MAAMQ,eAAe,GAAG5B,KAAK,CAAC6B,OAAO,CAAC,MAAMF,WAAW,EAAE5B,kBAAkB,CAAC+B,MAAM,CAACC,MAAM,CAACJ,WAAW,CAAC,CAAC,CAAC;EACxG,IAAIK,UAAU;EACd,IAAI,OAAOT,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,KAAK,WAAW,EAAE;IACjES,UAAU,GAAG,aAAahC,KAAK,CAACW,aAAa,CAACX,KAAK,CAACiC,QAAQ,EAAE,IAAI,EAAE,aAAajC,KAAK,CAACW,aAAa,CAACP,eAAe,EAAE,IAAI,CAAC,EAAE,aAAaJ,KAAK,CAACW,aAAa,CAACN,WAAW,EAAE,IAAI,CAAC,CAAC;IACjL,IAAI,OAAOkB,MAAM,KAAK,UAAU,EAAE;MAChCS,UAAU,GAAGT,MAAM,CAACS,UAAU,EAAE;QAC9BE,KAAK,EAAE7B,WAAW;QAClB8B,SAAS,EAAE/B;MACb,CAAC,CAAC;IACJ;IACA4B,UAAU,GAAG,aAAahC,KAAK,CAACW,aAAa,CAACL,oBAAoB,EAAE;MAClE8B,KAAK,EAAER;IACT,CAAC,EAAEI,UAAU,CAAC;EAChB,CAAC,MAAM;IACLA,UAAU,GAAGT,MAAM;EACrB;EACA,OAAO,aAAavB,KAAK,CAACW,aAAa,CAACT,uBAAuB,EAAE;IAC/DmC,QAAQ,EAAE;EACZ,CAAC,EAAEL,UAAU,CAAC;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}