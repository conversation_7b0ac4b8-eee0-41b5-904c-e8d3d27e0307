package com.yinma.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yinma.dto.BomDTO;
import com.yinma.entity.BomEntity;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * BOM管理Service接口
 * 提供BOM相关的业务逻辑处理
 * 
 * <AUTHOR> System
 * @since 2024-01-15
 */
public interface BomService extends IService<BomEntity> {

    /**
     * 分页查询BOM列表
     * 
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    IPage<BomDTO> getBomPage(BomDTO.BomQueryDTO queryDTO);

    /**
     * 根据ID查询BOM详情
     * 
     * @param bomId BOM ID
     * @param includeDetails 是否包含明细
     * @param includeChildren 是否包含子级
     * @return BOM详情
     */
    BomDTO getBomDetail(Long bomId, Boolean includeDetails, Boolean includeChildren);

    /**
     * 根据BOM编码查询BOM信息
     * 
     * @param bomCode BOM编码
     * @param version 版本号
     * @return BOM信息
     */
    BomDTO getBomByCode(String bomCode, String version);

    /**
     * 创建BOM
     * 
     * @param bomDTO BOM数据
     * @return 创建结果
     */
    BomDTO createBom(BomDTO bomDTO);

    /**
     * 更新BOM
     * 
     * @param bomDTO BOM数据
     * @return 更新结果
     */
    BomDTO updateBom(BomDTO bomDTO);

    /**
     * 删除BOM
     * 
     * @param bomId BOM ID
     * @param deleteBy 删除人
     * @return 删除结果
     */
    Boolean deleteBom(Long bomId, String deleteBy);

    /**
     * 批量删除BOM
     * 
     * @param bomIds BOM ID列表
     * @param deleteBy 删除人
     * @return 删除结果
     */
    Boolean batchDeleteBom(List<Long> bomIds, String deleteBy);

    /**
     * 复制BOM
     * 
     * @param sourceBomId 源BOM ID
     * @param targetBomCode 目标BOM编码
     * @param targetBomName 目标BOM名称
     * @param copyBy 复制人
     * @return 复制结果
     */
    BomDTO copyBom(Long sourceBomId, String targetBomCode, String targetBomName, String copyBy);

    /**
     * BOM版本管理
     * 
     * @param bomId BOM ID
     * @param versionAction 版本操作（CREATE_VERSION/ACTIVATE/ARCHIVE）
     * @param operateBy 操作人
     * @return 操作结果
     */
    BomDTO manageBomVersion(Long bomId, String versionAction, String operateBy);

    /**
     * 查询BOM版本历史
     * 
     * @param bomCode BOM编码
     * @return 版本历史列表
     */
    List<BomDTO> getBomVersionHistory(String bomCode);

    /**
     * 查询主BOM列表
     * 
     * @param bomType BOM类型
     * @param productCode 产品编码
     * @return 主BOM列表
     */
    List<BomDTO> getMainBomList(String bomType, String productCode);

    /**
     * 设置主BOM
     * 
     * @param bomId BOM ID
     * @param setBy 设置人
     * @return 设置结果
     */
    Boolean setMainBom(Long bomId, String setBy);

    /**
     * BOM结构树查询
     * 
     * @param bomId BOM ID
     * @param maxLevel 最大层级
     * @param expandAll 是否展开所有
     * @return BOM结构树
     */
    BomDTO getBomStructureTree(Long bomId, Integer maxLevel, Boolean expandAll);

    /**
     * 物料需求计算
     * 
     * @param bomId BOM ID
     * @param requiredQty 需求数量
     * @param expandSubBom 是否展开子BOM
     * @return 物料需求列表
     */
    List<BomDTO.MaterialRequirementDTO> calculateMaterialRequirements(Long bomId, Double requiredQty, Boolean expandSubBom);

    /**
     * BOM成本分析
     * 
     * @param bomId BOM ID
     * @param analysisType 分析类型（STANDARD/LATEST/AVERAGE）
     * @return 成本分析结果
     */
    BomDTO.CostAnalysisDTO analyzeBomCost(Long bomId, String analysisType);

    /**
     * BOM比较分析
     * 
     * @param sourceBomId 源BOM ID
     * @param targetBomId 目标BOM ID
     * @param compareLevel 比较层级
     * @return 比较结果
     */
    Map<String, Object> compareBom(Long sourceBomId, Long targetBomId, Integer compareLevel);

    /**
     * EBOM转换MBOM
     * 
     * @param ebomId EBOM ID
     * @param conversionRules 转换规则
     * @param convertBy 转换人
     * @return 转换结果
     */
    BomDTO convertEbomToMbom(Long ebomId, Map<String, Object> conversionRules, String convertBy);

    /**
     * PBOM转换MBOM
     * 
     * @param pbomId PBOM ID
     * @param conversionRules 转换规则
     * @param convertBy 转换人
     * @return 转换结果
     */
    BomDTO convertPbomToMbom(Long pbomId, Map<String, Object> conversionRules, String convertBy);

    /**
     * BOM有效性检查
     * 
     * @param bomId BOM ID
     * @return 检查结果
     */
    Map<String, Object> validateBom(Long bomId);

    /**
     * BOM完整性检查
     * 
     * @param bomId BOM ID
     * @return 检查结果
     */
    Map<String, Object> checkBomCompleteness(Long bomId);

    /**
     * 查询BOM使用情况
     * 
     * @param bomId BOM ID
     * @return 使用情况
     */
    Map<String, Object> getBomUsageInfo(Long bomId);

    /**
     * 查询物料在BOM中的使用情况
     * 
     * @param materialCode 物料编码
     * @return 使用情况列表
     */
    List<Map<String, Object>> getMaterialUsageInBom(String materialCode);

    /**
     * BOM变更影响分析
     * 
     * @param bomId BOM ID
     * @param changeType 变更类型
     * @param changeContent 变更内容
     * @return 影响分析结果
     */
    Map<String, Object> analyzeChangeImpact(Long bomId, String changeType, Map<String, Object> changeContent);

    /**
     * 批量导入BOM
     * 
     * @param bomDataList BOM数据列表
     * @param importBy 导入人
     * @return 导入结果
     */
    Map<String, Object> batchImportBom(List<Map<String, Object>> bomDataList, String importBy);

    /**
     * 导出BOM数据
     * 
     * @param bomIds BOM ID列表
     * @param exportFormat 导出格式（EXCEL/CSV/XML）
     * @param includeDetails 是否包含明细
     * @return 导出文件路径
     */
    String exportBomData(List<Long> bomIds, String exportFormat, Boolean includeDetails);

    /**
     * BOM审批
     * 
     * @param bomId BOM ID
     * @param approvalAction 审批动作（SUBMIT/APPROVE/REJECT/WITHDRAW）
     * @param approvalComment 审批意见
     * @param approveBy 审批人
     * @return 审批结果
     */
    Boolean approveBom(Long bomId, String approvalAction, String approvalComment, String approveBy);

    /**
     * 查询BOM审批历史
     * 
     * @param bomId BOM ID
     * @return 审批历史
     */
    List<Map<String, Object>> getBomApprovalHistory(Long bomId);

    /**
     * BOM冻结/解冻
     * 
     * @param bomId BOM ID
     * @param freezeAction 冻结动作（FREEZE/UNFREEZE）
     * @param freezeReason 冻结原因
     * @param operateBy 操作人
     * @return 操作结果
     */
    Boolean freezeBom(Long bomId, String freezeAction, String freezeReason, String operateBy);

    /**
     * 查询BOM统计信息
     * 
     * @param statisticsType 统计类型（TYPE/STATUS/COST/USAGE）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果
     */
    Map<String, Object> getBomStatistics(String statisticsType, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * BOM优化建议
     * 
     * @param bomId BOM ID
     * @param optimizationType 优化类型（COST/STRUCTURE/MATERIAL）
     * @return 优化建议
     */
    List<Map<String, Object>> getBomOptimizationSuggestions(Long bomId, String optimizationType);

    /**
     * 查询关键物料BOM
     * 
     * @param materialCode 物料编码
     * @param criticalLevel 关键等级
     * @return 关键物料BOM列表
     */
    List<BomDTO> getCriticalMaterialBoms(String materialCode, String criticalLevel);

    /**
     * BOM替代方案分析
     * 
     * @param bomId BOM ID
     * @param materialCode 物料编码
     * @return 替代方案
     */
    List<Map<String, Object>> analyzeBomSubstitutes(Long bomId, String materialCode);

    /**
     * 生成BOM编码
     * 
     * @param bomType BOM类型
     * @param productCode 产品编码
     * @return 生成的BOM编码
     */
    String generateBomCode(String bomType, String productCode);

    /**
     * 检查BOM编码是否存在
     * 
     * @param bomCode BOM编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    Boolean checkBomCodeExists(String bomCode, Long excludeId);

    /**
     * 同步BOM数据
     * 
     * @param bomId BOM ID
     * @param syncTarget 同步目标（ERP/PLM/MES）
     * @param syncBy 同步人
     * @return 同步结果
     */
    Map<String, Object> syncBomData(Long bomId, String syncTarget, String syncBy);

    /**
     * BOM数据清理
     * 
     * @param cleanupType 清理类型（OBSOLETE/DUPLICATE/INVALID）
     * @param cleanupBy 清理人
     * @return 清理结果
     */
    Map<String, Object> cleanupBomData(String cleanupType, String cleanupBy);
}