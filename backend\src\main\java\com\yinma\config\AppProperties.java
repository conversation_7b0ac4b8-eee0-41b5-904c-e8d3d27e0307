package com.yinma.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 应用配置属性
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
@Data
@Component
@ConfigurationProperties(prefix = "app")
public class AppProperties {

    /**
     * JWT配置
     */
    private Jwt jwt = new Jwt();

    /**
     * 文件上传配置
     */
    private Upload upload = new Upload();

    /**
     * 系统配置
     */
    private System system = new System();

    @Data
    public static class Jwt {
        /**
         * JWT密钥
         */
        private String secret = "YinMaSystemJwtSecretKey2024";

        /**
         * 访问令牌过期时间（秒）
         */
        private Long accessTokenExpiration = 7200L; // 2小时

        /**
         * 刷新令牌过期时间（秒）
         */
        private Long refreshTokenExpiration = 604800L; // 7天

        /**
         * 令牌前缀
         */
        private String tokenPrefix = "Bearer ";

        /**
         * 请求头名称
         */
        private String headerName = "Authorization";
    }

    @Data
    public static class Upload {
        /**
         * 上传路径
         */
        private String path = "./uploads";

        /**
         * 最大文件大小（MB）
         */
        private Integer maxFileSize = 10;

        /**
         * 允许的文件类型
         */
        private String[] allowedTypes = {"jpg", "jpeg", "png", "gif", "pdf", "doc", "docx", "xls", "xlsx"};

        /**
         * 访问URL前缀
         */
        private String urlPrefix = "/uploads";
    }

    @Data
    public static class System {
        /**
         * 系统名称
         */
        private String name = "YinMa系统";

        /**
         * 系统版本
         */
        private String version = "1.0.0";

        /**
         * 系统描述
         */
        private String description = "YinMa企业管理系统";

        /**
         * 是否开启验证码
         */
        private Boolean captchaEnabled = false;

        /**
         * 密码错误最大次数
         */
        private Integer maxPasswordRetry = 5;

        /**
         * 账户锁定时间（分钟）
         */
        private Integer lockTime = 30;

        /**
         * 是否开启注册功能
         */
        private Boolean registerEnabled = false;
    }
}