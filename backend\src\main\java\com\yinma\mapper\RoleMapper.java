package com.yinma.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinma.dto.RoleDTO;
import com.yinma.entity.RoleEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 角色信息Mapper接口
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
@Repository
@Mapper
public interface RoleMapper extends BaseMapper<RoleEntity> {

    /**
     * 分页查询角色列表
     * 
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 角色分页列表
     */
    @Select("<script>" +
            "SELECT r.*, " +
            "(SELECT COUNT(1) FROM sys_user_role ur WHERE ur.role_id = r.role_id) as userCount " +
            "FROM sys_role r " +
            "WHERE r.is_deleted = 0 " +
            "<if test='queryDTO.roleCode != null and queryDTO.roleCode != \"\"'>" +
            "AND r.role_code LIKE CONCAT('%', #{queryDTO.roleCode}, '%') " +
            "</if>" +
            "<if test='queryDTO.roleName != null and queryDTO.roleName != \"\"'>" +
            "AND r.role_name LIKE CONCAT('%', #{queryDTO.roleName}, '%') " +
            "</if>" +
            "<if test='queryDTO.roleType != null'>" +
            "AND r.role_type = #{queryDTO.roleType} " +
            "</if>" +
            "<if test='queryDTO.dataScope != null'>" +
            "AND r.data_scope = #{queryDTO.dataScope} " +
            "</if>" +
            "<if test='queryDTO.status != null'>" +
            "AND r.status = #{queryDTO.status} " +
            "</if>" +
            "<if test='queryDTO.isSystem != null'>" +
            "AND r.is_system = #{queryDTO.isSystem} " +
            "</if>" +
            "<if test='queryDTO.createTimeStart != null'>" +
            "AND r.create_time >= #{queryDTO.createTimeStart} " +
            "</if>" +
            "<if test='queryDTO.createTimeEnd != null'>" +
            "AND r.create_time <= #{queryDTO.createTimeEnd} " +
            "</if>" +
            "ORDER BY r.sort_order ASC, r.create_time DESC" +
            "</script>")
    IPage<RoleEntity> selectRolePage(Page<RoleEntity> page, @Param("queryDTO") RoleDTO.RoleQueryDTO queryDTO);

    /**
     * 根据角色编码查询角色
     * 
     * @param roleCode 角色编码
     * @return 角色信息
     */
    @Select("SELECT * FROM sys_role WHERE role_code = #{roleCode} AND is_deleted = 0")
    RoleEntity selectByRoleCode(@Param("roleCode") String roleCode);

    /**
     * 根据用户ID查询角色列表
     */
    @Select("SELECT r.* FROM sys_role r " +
            "INNER JOIN sys_user_role ur ON r.role_id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND r.status = 1 AND r.is_deleted = 0")
    List<RoleEntity> selectRolesByUserId(@Param("userId") Long userId);

    /**
     * 检查角色编码是否存在
     * 
     * @param roleCode 角色编码
     * @param excludeRoleId 排除的角色ID
     * @return 是否存在
     */
    @Select("<script>" +
            "SELECT COUNT(1) FROM sys_role " +
            "WHERE role_code = #{roleCode} AND is_deleted = 0 " +
            "<if test='excludeRoleId != null'>" +
            "AND role_id != #{excludeRoleId} " +
            "</if>" +
            "</script>")
    int checkRoleCodeExists(@Param("roleCode") String roleCode, @Param("excludeRoleId") Long excludeRoleId);

    /**
     * 检查角色名称是否存在
     * 
     * @param roleName 角色名称
     * @param excludeRoleId 排除的角色ID
     * @return 是否存在
     */
    @Select("<script>" +
            "SELECT COUNT(1) FROM sys_role " +
            "WHERE role_name = #{roleName} AND is_deleted = 0 " +
            "<if test='excludeRoleId != null'>" +
            "AND role_id != #{excludeRoleId} " +
            "</if>" +
            "</script>")
    int checkRoleNameExists(@Param("roleName") String roleName, @Param("excludeRoleId") Long excludeRoleId);

    /**
     * 查询用户的角色列表
     * 
     * @param userId 用户ID
     * @return 角色列表
     */
    @Select("SELECT r.* FROM sys_role r " +
            "INNER JOIN sys_user_role ur ON r.role_id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND r.status = 1 AND r.is_deleted = 0 " +
            "ORDER BY r.sort_order ASC")
    List<RoleEntity> selectByUserId(@Param("userId") Long userId);

    /**
     * 查询角色的权限列表
     * 
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    @Select("SELECT rp.permission_id FROM sys_role_permission rp " +
            "INNER JOIN sys_permission p ON rp.permission_id = p.permission_id " +
            "WHERE rp.role_id = #{roleId} AND p.status = 1 AND p.is_deleted = 0")
    List<Long> selectRolePermissions(@Param("roleId") Long roleId);

    /**
     * 查询角色的用户列表
     * 
     * @param roleId 角色ID
     * @return 用户列表
     */
    @Select("SELECT u.user_id, u.username, u.real_name, u.email, u.phone, " +
            "d.dept_name, p.position_name, u.status, ur.create_time as assignTime " +
            "FROM sys_user u " +
            "INNER JOIN sys_user_role ur ON u.user_id = ur.user_id " +
            "LEFT JOIN sys_dept d ON u.dept_id = d.dept_id " +
            "LEFT JOIN sys_position p ON u.position_id = p.position_id " +
            "WHERE ur.role_id = #{roleId} AND u.is_deleted = 0 " +
            "ORDER BY ur.create_time DESC")
    List<RoleDTO.RoleUserDTO> selectRoleUsers(@Param("roleId") Long roleId);

    /**
     * 更新角色状态
     * 
     * @param roleId 角色ID
     * @param status 状态
     * @return 更新行数
     */
    @Update("UPDATE sys_role SET status = #{status} WHERE role_id = #{roleId}")
    int updateStatus(@Param("roleId") Long roleId, @Param("status") Integer status);

    /**
     * 批量更新角色状态
     * 
     * @param roleIds 角色ID列表
     * @param status 状态
     * @return 更新行数
     */
    @Update("<script>" +
            "UPDATE sys_role SET status = #{status} " +
            "WHERE role_id IN " +
            "<foreach collection='roleIds' item='roleId' open='(' separator=',' close=')'>" +
            "#{roleId}" +
            "</foreach>" +
            "</script>")
    int batchUpdateStatus(@Param("roleIds") List<Long> roleIds, @Param("status") Integer status);

    /**
     * 删除角色权限关联
     * 
     * @param roleId 角色ID
     * @return 删除行数
     */
    @Delete("DELETE FROM sys_role_permission WHERE role_id = #{roleId}")
    int deleteRolePermissions(@Param("roleId") Long roleId);

    /**
     * 批量插入角色权限关联
     * 
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @return 插入行数
     */
    @Insert("<script>" +
            "INSERT INTO sys_role_permission (role_id, permission_id, create_time, create_user_id) VALUES " +
            "<foreach collection='permissionIds' item='permissionId' separator=','>" +
            "(#{roleId}, #{permissionId}, NOW(), #{createUserId})" +
            "</foreach>" +
            "</script>")
    int batchInsertRolePermissions(@Param("roleId") Long roleId, 
                                  @Param("permissionIds") List<Long> permissionIds,
                                  @Param("createUserId") Long createUserId);

    /**
     * 查询所有启用的角色
     * 
     * @return 角色列表
     */
    @Select("SELECT * FROM sys_role WHERE status = 1 AND is_deleted = 0 ORDER BY sort_order ASC, create_time DESC")
    List<RoleEntity> selectAllEnabled();

    /**
     * 查询系统角色列表
     * 
     * @return 系统角色列表
     */
    @Select("SELECT * FROM sys_role WHERE is_system = 1 AND is_deleted = 0 ORDER BY sort_order ASC")
    List<RoleEntity> selectSystemRoles();

    /**
     * 查询业务角色列表
     * 
     * @return 业务角色列表
     */
    @Select("SELECT * FROM sys_role WHERE is_system = 0 AND is_deleted = 0 ORDER BY sort_order ASC")
    List<RoleEntity> selectBusinessRoles();

    /**
     * 查询角色统计信息
     * 
     * @return 统计信息
     */
    @Select("SELECT " +
            "COUNT(1) as totalRoles, " +
            "SUM(CASE WHEN is_system = 1 THEN 1 ELSE 0 END) as systemRoles, " +
            "SUM(CASE WHEN is_system = 0 THEN 1 ELSE 0 END) as businessRoles, " +
            "SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as activeRoles, " +
            "SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as inactiveRoles, " +
            "SUM(CASE WHEN DATE(create_time) = CURDATE() THEN 1 ELSE 0 END) as todayNewRoles, " +
            "SUM(CASE WHEN YEAR(create_time) = YEAR(NOW()) AND MONTH(create_time) = MONTH(NOW()) THEN 1 ELSE 0 END) as monthNewRoles " +
            "FROM sys_role WHERE is_deleted = 0")
    RoleDTO.RoleStatisticsDTO selectRoleStatistics();

    /**
     * 查询角色层级关系
     * 
     * @return 角色层级列表
     */
    @Select("SELECT role_id, role_code, role_name, role_level, sort_order " +
            "FROM sys_role WHERE status = 1 AND is_deleted = 0 " +
            "ORDER BY role_level ASC, sort_order ASC")
    List<RoleEntity> selectRoleHierarchy();

    /**
     * 查询指定级别以下的角色
     * 
     * @param roleLevel 角色级别
     * @return 角色列表
     */
    @Select("SELECT * FROM sys_role " +
            "WHERE role_level > #{roleLevel} AND status = 1 AND is_deleted = 0 " +
            "ORDER BY role_level ASC, sort_order ASC")
    List<RoleEntity> selectRolesByLevelBelow(@Param("roleLevel") Integer roleLevel);

    /**
     * 查询用户可分配的角色列表
     * 
     * @param currentUserRoleLevel 当前用户角色级别
     * @return 角色列表
     */
    @Select("SELECT * FROM sys_role " +
            "WHERE role_level >= #{currentUserRoleLevel} AND status = 1 AND is_deleted = 0 " +
            "ORDER BY role_level ASC, sort_order ASC")
    List<RoleEntity> selectAssignableRoles(@Param("currentUserRoleLevel") Integer currentUserRoleLevel);

    /**
     * 查询角色数据权限范围统计
     * 
     * @return 数据权限统计列表
     */
    @Select("SELECT " +
            "CASE data_scope " +
            "WHEN 1 THEN '全部数据' " +
            "WHEN 2 THEN '部门数据' " +
            "WHEN 3 THEN '个人数据' " +
            "ELSE '未知' END as dataScopeName, " +
            "COUNT(1) as roleCount " +
            "FROM sys_role " +
            "WHERE is_deleted = 0 " +
            "GROUP BY data_scope " +
            "ORDER BY data_scope ASC")
    List<RoleDTO.DataScopeStatisticsDTO> selectDataScopeStatistics();

    /**
     * 查询最大排序号
     * 
     * @return 最大排序号
     */
    @Select("SELECT COALESCE(MAX(sort_order), 0) FROM sys_role WHERE is_deleted = 0")
    Integer selectMaxSortOrder();

    /**
     * 更新排序号
     * 
     * @param roleId 角色ID
     * @param sortOrder 排序号
     * @return 更新行数
     */
    @Update("UPDATE sys_role SET sort_order = #{sortOrder} WHERE role_id = #{roleId}")
    int updateSortOrder(@Param("roleId") Long roleId, @Param("sortOrder") Integer sortOrder);

    /**
     * 查询角色权限树
     * 
     * @param roleId 角色ID
     * @return 权限树列表
     */
    @Select("SELECT p.permission_id, p.parent_id, p.permission_code, p.permission_name, " +
            "p.permission_type, " +
            "CASE WHEN rp.permission_id IS NOT NULL THEN 1 ELSE 0 END as checked " +
            "FROM sys_permission p " +
            "LEFT JOIN sys_role_permission rp ON p.permission_id = rp.permission_id AND rp.role_id = #{roleId} " +
            "WHERE p.status = 1 AND p.is_deleted = 0 " +
            "ORDER BY p.sort_order ASC")
    List<RoleDTO.RolePermissionTreeDTO> selectRolePermissionTree(@Param("roleId") Long roleId);
}