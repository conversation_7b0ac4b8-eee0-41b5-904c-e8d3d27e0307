package com.yinma.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinma.entity.BomChangeLogEntity;
import com.yinma.dto.BomChangeLogDTO;
import java.util.List;
import java.util.Map;

/**
 * BOM变更日志管理Service接口
 * 提供BOM变更记录、审批流程、影响分析等业务逻辑处理
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
public interface BomChangeLogService extends IService<BomChangeLogEntity> {

    /**
     * 分页查询BOM变更日志
     * 
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Page<BomChangeLogDTO> queryPage(BomChangeLogDTO.BomChangeLogQueryDTO queryDTO);

    /**
     * 根据ID查询变更日志详情
     * 
     * @param changeLogId 变更日志ID
     * @return 变更日志详情
     */
    BomChangeLogDTO getChangeLogDetail(Long changeLogId);

    /**
     * 根据BOM ID查询变更历史
     * 
     * @param bomId BOM主表ID
     * @return 变更历史列表
     */
    List<BomChangeLogDTO> getChangeHistoryByBomId(Long bomId);

    /**
     * 创建变更请求
     * 
     * @param changeRequestDTO 变更请求信息
     * @return 变更日志ID
     */
    Long createChangeRequest(BomChangeLogDTO.ChangeRequestDTO changeRequestDTO);

    /**
     * 提交变更申请
     * 
     * @param changeLogId 变更日志ID
     * @param submitUserId 提交用户ID
     * @return 是否成功
     */
    Boolean submitChangeRequest(Long changeLogId, Long submitUserId);

    /**
     * 审批变更申请
     * 
     * @param changeLogId 变更日志ID
     * @param approvalUserId 审批用户ID
     * @param approved 是否通过
     * @param approvalComment 审批意见
     * @return 是否成功
     */
    Boolean approveChangeRequest(Long changeLogId, Long approvalUserId, Boolean approved, String approvalComment);

    /**
     * 执行变更
     * 
     * @param changeLogId 变更日志ID
     * @param executeUserId 执行用户ID
     * @return 是否成功
     */
    Boolean executeChange(Long changeLogId, Long executeUserId);

    /**
     * 撤销变更
     * 
     * @param changeLogId 变更日志ID
     * @param cancelUserId 撤销用户ID
     * @param cancelReason 撤销原因
     * @return 是否成功
     */
    Boolean cancelChange(Long changeLogId, Long cancelUserId, String cancelReason);

    /**
     * 变更影响分析
     * 
     * @param bomId BOM主表ID
     * @param changeContent 变更内容
     * @return 影响分析结果
     */
    BomChangeLogDTO.ChangeImpactAnalysisDTO analyzeChangeImpact(Long bomId, BomChangeLogDTO.ChangeContentDTO changeContent);

    /**
     * 查询影响的BOM列表
     * 
     * @param changeLogId 变更日志ID
     * @return 影响的BOM列表
     */
    List<BomChangeLogDTO.AffectedBomDTO> getAffectedBomList(Long changeLogId);

    /**
     * 批量创建变更日志
     * 
     * @param changeLogList 变更日志列表
     * @return 是否成功
     */
    Boolean batchCreateChangeLog(List<BomChangeLogDTO> changeLogList);

    /**
     * 批量更新变更状态
     * 
     * @param changeLogIds 变更日志ID列表
     * @param status 目标状态
     * @param updateUserId 更新用户ID
     * @return 是否成功
     */
    Boolean batchUpdateStatus(List<Long> changeLogIds, String status, Long updateUserId);

    /**
     * 删除变更日志
     * 
     * @param changeLogId 变更日志ID
     * @param deleteUserId 删除用户ID
     * @return 是否成功
     */
    Boolean deleteChangeLog(Long changeLogId, Long deleteUserId);

    /**
     * 批量删除变更日志
     * 
     * @param changeLogIds 变更日志ID列表
     * @param deleteUserId 删除用户ID
     * @return 是否成功
     */
    Boolean batchDeleteChangeLog(List<Long> changeLogIds, Long deleteUserId);

    /**
     * 获取变更统计数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据
     */
    Map<String, Object> getChangeStatistics(String startDate, String endDate);

    /**
     * 获取变更趋势数据
     * 
     * @param period 统计周期（day/week/month）
     * @param count 统计数量
     * @return 趋势数据
     */
    List<Map<String, Object>> getChangeTrend(String period, Integer count);

    /**
     * 获取变更类型分布
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 类型分布数据
     */
    List<Map<String, Object>> getChangeTypeDistribution(String startDate, String endDate);

    /**
     * 获取审批效率统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 审批效率数据
     */
    Map<String, Object> getApprovalEfficiencyStats(String startDate, String endDate);

    /**
     * 导出变更日志
     * 
     * @param queryDTO 查询条件
     * @return 导出文件路径
     */
    String exportChangeLog(BomChangeLogDTO.BomChangeLogQueryDTO queryDTO);

    /**
     * 导入变更日志
     * 
     * @param filePath 文件路径
     * @param importUserId 导入用户ID
     * @return 导入结果
     */
    Map<String, Object> importChangeLog(String filePath, Long importUserId);

    /**
     * 获取变更模板
     * 
     * @param changeType 变更类型
     * @return 变更模板
     */
    BomChangeLogDTO.ChangeContentDTO getChangeTemplate(String changeType);

    /**
     * 保存变更模板
     * 
     * @param templateName 模板名称
     * @param changeContent 变更内容
     * @param createUserId 创建用户ID
     * @return 是否成功
     */
    Boolean saveChangeTemplate(String templateName, BomChangeLogDTO.ChangeContentDTO changeContent, Long createUserId);

    /**
     * 获取我的变更申请
     * 
     * @param userId 用户ID
     * @param status 状态
     * @return 变更申请列表
     */
    List<BomChangeLogDTO> getMyChangeRequests(Long userId, String status);

    /**
     * 获取待我审批的变更
     * 
     * @param userId 用户ID
     * @return 待审批变更列表
     */
    List<BomChangeLogDTO> getPendingApprovals(Long userId);

    /**
     * 获取变更通知
     * 
     * @param userId 用户ID
     * @return 通知列表
     */
    List<Map<String, Object>> getChangeNotifications(Long userId);

    /**
     * 标记通知已读
     * 
     * @param notificationId 通知ID
     * @param userId 用户ID
     * @return 是否成功
     */
    Boolean markNotificationRead(Long notificationId, Long userId);

    /**
     * 获取变更日历
     * 
     * @param year 年份
     * @param month 月份
     * @return 日历数据
     */
    List<Map<String, Object>> getChangeCalendar(Integer year, Integer month);

    /**
     * 获取变更看板数据
     * 
     * @return 看板数据
     */
    Map<String, Object> getChangeDashboard();
}