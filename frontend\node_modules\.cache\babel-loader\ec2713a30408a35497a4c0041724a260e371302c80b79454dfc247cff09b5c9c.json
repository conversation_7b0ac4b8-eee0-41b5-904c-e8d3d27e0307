{"ast": null, "code": "import { quadtree } from \"d3-quadtree\";\nimport constant from \"./constant.js\";\nimport jiggle from \"./jiggle.js\";\nfunction x(d) {\n  return d.x + d.vx;\n}\nfunction y(d) {\n  return d.y + d.vy;\n}\nexport default function (radius) {\n  var nodes,\n    radii,\n    random,\n    strength = 1,\n    iterations = 1;\n  if (typeof radius !== \"function\") radius = constant(radius == null ? 1 : +radius);\n  function force() {\n    var i,\n      n = nodes.length,\n      tree,\n      node,\n      xi,\n      yi,\n      ri,\n      ri2;\n    for (var k = 0; k < iterations; ++k) {\n      tree = quadtree(nodes, x, y).visitAfter(prepare);\n      for (i = 0; i < n; ++i) {\n        node = nodes[i];\n        ri = radii[node.index], ri2 = ri * ri;\n        xi = node.x + node.vx;\n        yi = node.y + node.vy;\n        tree.visit(apply);\n      }\n    }\n    function apply(quad, x0, y0, x1, y1) {\n      var data = quad.data,\n        rj = quad.r,\n        r = ri + rj;\n      if (data) {\n        if (data.index > node.index) {\n          var x = xi - data.x - data.vx,\n            y = yi - data.y - data.vy,\n            l = x * x + y * y;\n          if (l < r * r) {\n            if (x === 0) x = jiggle(random), l += x * x;\n            if (y === 0) y = jiggle(random), l += y * y;\n            l = (r - (l = Math.sqrt(l))) / l * strength;\n            node.vx += (x *= l) * (r = (rj *= rj) / (ri2 + rj));\n            node.vy += (y *= l) * r;\n            data.vx -= x * (r = 1 - r);\n            data.vy -= y * r;\n          }\n        }\n        return;\n      }\n      return x0 > xi + r || x1 < xi - r || y0 > yi + r || y1 < yi - r;\n    }\n  }\n  function prepare(quad) {\n    if (quad.data) return quad.r = radii[quad.data.index];\n    for (var i = quad.r = 0; i < 4; ++i) {\n      if (quad[i] && quad[i].r > quad.r) {\n        quad.r = quad[i].r;\n      }\n    }\n  }\n  function initialize() {\n    if (!nodes) return;\n    var i,\n      n = nodes.length,\n      node;\n    radii = new Array(n);\n    for (i = 0; i < n; ++i) node = nodes[i], radii[node.index] = +radius(node, i, nodes);\n  }\n  force.initialize = function (_nodes, _random) {\n    nodes = _nodes;\n    random = _random;\n    initialize();\n  };\n  force.iterations = function (_) {\n    return arguments.length ? (iterations = +_, force) : iterations;\n  };\n  force.strength = function (_) {\n    return arguments.length ? (strength = +_, force) : strength;\n  };\n  force.radius = function (_) {\n    return arguments.length ? (radius = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : radius;\n  };\n  return force;\n}", "map": {"version": 3, "names": ["quadtree", "constant", "jiggle", "x", "d", "vx", "y", "vy", "radius", "nodes", "radii", "random", "strength", "iterations", "force", "i", "n", "length", "tree", "node", "xi", "yi", "ri", "ri2", "k", "visitAfter", "prepare", "index", "visit", "apply", "quad", "x0", "y0", "x1", "y1", "data", "rj", "r", "l", "Math", "sqrt", "initialize", "Array", "_nodes", "_random", "_", "arguments"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/d3-force/src/collide.js"], "sourcesContent": ["import {quadtree} from \"d3-quadtree\";\nimport constant from \"./constant.js\";\nimport jiggle from \"./jiggle.js\";\n\nfunction x(d) {\n  return d.x + d.vx;\n}\n\nfunction y(d) {\n  return d.y + d.vy;\n}\n\nexport default function(radius) {\n  var nodes,\n      radii,\n      random,\n      strength = 1,\n      iterations = 1;\n\n  if (typeof radius !== \"function\") radius = constant(radius == null ? 1 : +radius);\n\n  function force() {\n    var i, n = nodes.length,\n        tree,\n        node,\n        xi,\n        yi,\n        ri,\n        ri2;\n\n    for (var k = 0; k < iterations; ++k) {\n      tree = quadtree(nodes, x, y).visitAfter(prepare);\n      for (i = 0; i < n; ++i) {\n        node = nodes[i];\n        ri = radii[node.index], ri2 = ri * ri;\n        xi = node.x + node.vx;\n        yi = node.y + node.vy;\n        tree.visit(apply);\n      }\n    }\n\n    function apply(quad, x0, y0, x1, y1) {\n      var data = quad.data, rj = quad.r, r = ri + rj;\n      if (data) {\n        if (data.index > node.index) {\n          var x = xi - data.x - data.vx,\n              y = yi - data.y - data.vy,\n              l = x * x + y * y;\n          if (l < r * r) {\n            if (x === 0) x = jiggle(random), l += x * x;\n            if (y === 0) y = jiggle(random), l += y * y;\n            l = (r - (l = Math.sqrt(l))) / l * strength;\n            node.vx += (x *= l) * (r = (rj *= rj) / (ri2 + rj));\n            node.vy += (y *= l) * r;\n            data.vx -= x * (r = 1 - r);\n            data.vy -= y * r;\n          }\n        }\n        return;\n      }\n      return x0 > xi + r || x1 < xi - r || y0 > yi + r || y1 < yi - r;\n    }\n  }\n\n  function prepare(quad) {\n    if (quad.data) return quad.r = radii[quad.data.index];\n    for (var i = quad.r = 0; i < 4; ++i) {\n      if (quad[i] && quad[i].r > quad.r) {\n        quad.r = quad[i].r;\n      }\n    }\n  }\n\n  function initialize() {\n    if (!nodes) return;\n    var i, n = nodes.length, node;\n    radii = new Array(n);\n    for (i = 0; i < n; ++i) node = nodes[i], radii[node.index] = +radius(node, i, nodes);\n  }\n\n  force.initialize = function(_nodes, _random) {\n    nodes = _nodes;\n    random = _random;\n    initialize();\n  };\n\n  force.iterations = function(_) {\n    return arguments.length ? (iterations = +_, force) : iterations;\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = +_, force) : strength;\n  };\n\n  force.radius = function(_) {\n    return arguments.length ? (radius = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : radius;\n  };\n\n  return force;\n}\n"], "mappings": "AAAA,SAAQA,QAAQ,QAAO,aAAa;AACpC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,MAAM,MAAM,aAAa;AAEhC,SAASC,CAACA,CAACC,CAAC,EAAE;EACZ,OAAOA,CAAC,CAACD,CAAC,GAAGC,CAAC,CAACC,EAAE;AACnB;AAEA,SAASC,CAACA,CAACF,CAAC,EAAE;EACZ,OAAOA,CAAC,CAACE,CAAC,GAAGF,CAAC,CAACG,EAAE;AACnB;AAEA,eAAe,UAASC,MAAM,EAAE;EAC9B,IAAIC,KAAK;IACLC,KAAK;IACLC,MAAM;IACNC,QAAQ,GAAG,CAAC;IACZC,UAAU,GAAG,CAAC;EAElB,IAAI,OAAOL,MAAM,KAAK,UAAU,EAAEA,MAAM,GAAGP,QAAQ,CAACO,MAAM,IAAI,IAAI,GAAG,CAAC,GAAG,CAACA,MAAM,CAAC;EAEjF,SAASM,KAAKA,CAAA,EAAG;IACf,IAAIC,CAAC;MAAEC,CAAC,GAAGP,KAAK,CAACQ,MAAM;MACnBC,IAAI;MACJC,IAAI;MACJC,EAAE;MACFC,EAAE;MACFC,EAAE;MACFC,GAAG;IAEP,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,UAAU,EAAE,EAAEW,CAAC,EAAE;MACnCN,IAAI,GAAGlB,QAAQ,CAACS,KAAK,EAAEN,CAAC,EAAEG,CAAC,CAAC,CAACmB,UAAU,CAACC,OAAO,CAAC;MAChD,KAAKX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;QACtBI,IAAI,GAAGV,KAAK,CAACM,CAAC,CAAC;QACfO,EAAE,GAAGZ,KAAK,CAACS,IAAI,CAACQ,KAAK,CAAC,EAAEJ,GAAG,GAAGD,EAAE,GAAGA,EAAE;QACrCF,EAAE,GAAGD,IAAI,CAAChB,CAAC,GAAGgB,IAAI,CAACd,EAAE;QACrBgB,EAAE,GAAGF,IAAI,CAACb,CAAC,GAAGa,IAAI,CAACZ,EAAE;QACrBW,IAAI,CAACU,KAAK,CAACC,KAAK,CAAC;MACnB;IACF;IAEA,SAASA,KAAKA,CAACC,IAAI,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;MACnC,IAAIC,IAAI,GAAGL,IAAI,CAACK,IAAI;QAAEC,EAAE,GAAGN,IAAI,CAACO,CAAC;QAAEA,CAAC,GAAGf,EAAE,GAAGc,EAAE;MAC9C,IAAID,IAAI,EAAE;QACR,IAAIA,IAAI,CAACR,KAAK,GAAGR,IAAI,CAACQ,KAAK,EAAE;UAC3B,IAAIxB,CAAC,GAAGiB,EAAE,GAAGe,IAAI,CAAChC,CAAC,GAAGgC,IAAI,CAAC9B,EAAE;YACzBC,CAAC,GAAGe,EAAE,GAAGc,IAAI,CAAC7B,CAAC,GAAG6B,IAAI,CAAC5B,EAAE;YACzB+B,CAAC,GAAGnC,CAAC,GAAGA,CAAC,GAAGG,CAAC,GAAGA,CAAC;UACrB,IAAIgC,CAAC,GAAGD,CAAC,GAAGA,CAAC,EAAE;YACb,IAAIlC,CAAC,KAAK,CAAC,EAAEA,CAAC,GAAGD,MAAM,CAACS,MAAM,CAAC,EAAE2B,CAAC,IAAInC,CAAC,GAAGA,CAAC;YAC3C,IAAIG,CAAC,KAAK,CAAC,EAAEA,CAAC,GAAGJ,MAAM,CAACS,MAAM,CAAC,EAAE2B,CAAC,IAAIhC,CAAC,GAAGA,CAAC;YAC3CgC,CAAC,GAAG,CAACD,CAAC,IAAIC,CAAC,GAAGC,IAAI,CAACC,IAAI,CAACF,CAAC,CAAC,CAAC,IAAIA,CAAC,GAAG1B,QAAQ;YAC3CO,IAAI,CAACd,EAAE,IAAI,CAACF,CAAC,IAAImC,CAAC,KAAKD,CAAC,GAAG,CAACD,EAAE,IAAIA,EAAE,KAAKb,GAAG,GAAGa,EAAE,CAAC,CAAC;YACnDjB,IAAI,CAACZ,EAAE,IAAI,CAACD,CAAC,IAAIgC,CAAC,IAAID,CAAC;YACvBF,IAAI,CAAC9B,EAAE,IAAIF,CAAC,IAAIkC,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC;YAC1BF,IAAI,CAAC5B,EAAE,IAAID,CAAC,GAAG+B,CAAC;UAClB;QACF;QACA;MACF;MACA,OAAON,EAAE,GAAGX,EAAE,GAAGiB,CAAC,IAAIJ,EAAE,GAAGb,EAAE,GAAGiB,CAAC,IAAIL,EAAE,GAAGX,EAAE,GAAGgB,CAAC,IAAIH,EAAE,GAAGb,EAAE,GAAGgB,CAAC;IACjE;EACF;EAEA,SAASX,OAAOA,CAACI,IAAI,EAAE;IACrB,IAAIA,IAAI,CAACK,IAAI,EAAE,OAAOL,IAAI,CAACO,CAAC,GAAG3B,KAAK,CAACoB,IAAI,CAACK,IAAI,CAACR,KAAK,CAAC;IACrD,KAAK,IAAIZ,CAAC,GAAGe,IAAI,CAACO,CAAC,GAAG,CAAC,EAAEtB,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;MACnC,IAAIe,IAAI,CAACf,CAAC,CAAC,IAAIe,IAAI,CAACf,CAAC,CAAC,CAACsB,CAAC,GAAGP,IAAI,CAACO,CAAC,EAAE;QACjCP,IAAI,CAACO,CAAC,GAAGP,IAAI,CAACf,CAAC,CAAC,CAACsB,CAAC;MACpB;IACF;EACF;EAEA,SAASI,UAAUA,CAAA,EAAG;IACpB,IAAI,CAAChC,KAAK,EAAE;IACZ,IAAIM,CAAC;MAAEC,CAAC,GAAGP,KAAK,CAACQ,MAAM;MAAEE,IAAI;IAC7BT,KAAK,GAAG,IAAIgC,KAAK,CAAC1B,CAAC,CAAC;IACpB,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAEI,IAAI,GAAGV,KAAK,CAACM,CAAC,CAAC,EAAEL,KAAK,CAACS,IAAI,CAACQ,KAAK,CAAC,GAAG,CAACnB,MAAM,CAACW,IAAI,EAAEJ,CAAC,EAAEN,KAAK,CAAC;EACtF;EAEAK,KAAK,CAAC2B,UAAU,GAAG,UAASE,MAAM,EAAEC,OAAO,EAAE;IAC3CnC,KAAK,GAAGkC,MAAM;IACdhC,MAAM,GAAGiC,OAAO;IAChBH,UAAU,CAAC,CAAC;EACd,CAAC;EAED3B,KAAK,CAACD,UAAU,GAAG,UAASgC,CAAC,EAAE;IAC7B,OAAOC,SAAS,CAAC7B,MAAM,IAAIJ,UAAU,GAAG,CAACgC,CAAC,EAAE/B,KAAK,IAAID,UAAU;EACjE,CAAC;EAEDC,KAAK,CAACF,QAAQ,GAAG,UAASiC,CAAC,EAAE;IAC3B,OAAOC,SAAS,CAAC7B,MAAM,IAAIL,QAAQ,GAAG,CAACiC,CAAC,EAAE/B,KAAK,IAAIF,QAAQ;EAC7D,CAAC;EAEDE,KAAK,CAACN,MAAM,GAAG,UAASqC,CAAC,EAAE;IACzB,OAAOC,SAAS,CAAC7B,MAAM,IAAIT,MAAM,GAAG,OAAOqC,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG5C,QAAQ,CAAC,CAAC4C,CAAC,CAAC,EAAEJ,UAAU,CAAC,CAAC,EAAE3B,KAAK,IAAIN,MAAM;EAC/G,CAAC;EAED,OAAOM,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}