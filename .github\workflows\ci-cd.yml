name: 西安银马实业数字化管理系统 CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  release:
    types: [ published ]

env:
  NODE_VERSION: '18'
  DOCKER_REGISTRY: 'ghcr.io'
  IMAGE_NAME: 'yinma/digital-management'

jobs:
  # 代码质量检查
  code-quality:
    name: 代码质量检查
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: |
          backend/package-lock.json
          frontend/package-lock.json
    
    - name: 安装后端依赖
      working-directory: ./backend
      run: npm ci
      
    - name: 安装前端依赖
      working-directory: ./frontend
      run: npm ci
    
    - name: 后端代码检查
      working-directory: ./backend
      run: |
        npm run lint
        npm run format:check
    
    - name: 前端代码检查
      working-directory: ./frontend
      run: |
        npm run lint
        npm run format:check
    
    - name: 类型检查
      working-directory: ./frontend
      run: npm run type-check
    
    - name: 安全漏洞扫描
      run: |
        cd backend && npm audit --audit-level=high
        cd ../frontend && npm audit --audit-level=high

  # 单元测试
  unit-tests:
    name: 单元测试
    runs-on: ubuntu-latest
    needs: code-quality
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: yinma_test
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: |
          backend/package-lock.json
          frontend/package-lock.json
    
    - name: 安装后端依赖
      working-directory: ./backend
      run: npm ci
      
    - name: 安装前端依赖
      working-directory: ./frontend
      run: npm ci
    
    - name: 运行后端测试
      working-directory: ./backend
      env:
        NODE_ENV: test
        DB_HOST: localhost
        DB_PORT: 5432
        DB_NAME: yinma_test
        DB_USER: test_user
        DB_PASSWORD: test_password
        REDIS_HOST: localhost
        REDIS_PORT: 6379
        JWT_SECRET: test_jwt_secret
      run: |
        npm run test:unit
        npm run test:integration
    
    - name: 运行前端测试
      working-directory: ./frontend
      run: npm run test:coverage
    
    - name: 上传测试覆盖率报告
      uses: codecov/codecov-action@v3
      with:
        files: |
          ./backend/coverage/lcov.info
          ./frontend/coverage/lcov.info
        flags: unittests
        name: codecov-umbrella

  # 构建应用
  build:
    name: 构建应用
    runs-on: ubuntu-latest
    needs: unit-tests
    
    strategy:
      matrix:
        component: [backend, frontend]
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: ${{ matrix.component }}/package-lock.json
    
    - name: 安装依赖
      working-directory: ./${{ matrix.component }}
      run: npm ci
    
    - name: 构建应用
      working-directory: ./${{ matrix.component }}
      run: npm run build
    
    - name: 上传构建产物
      uses: actions/upload-artifact@v4
      with:
        name: ${{ matrix.component }}-build
        path: |
          ${{ matrix.component }}/dist
          ${{ matrix.component }}/build
        retention-days: 7

  # Docker镜像构建
  docker-build:
    name: Docker镜像构建
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'push' || github.event_name == 'release'
    
    strategy:
      matrix:
        component: [backend, frontend]
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 下载构建产物
      uses: actions/download-artifact@v4
      with:
        name: ${{ matrix.component }}-build
        path: ${{ matrix.component }}/
    
    - name: 设置 Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: 登录到容器注册表
      uses: docker/login-action@v3
      with:
        registry: ${{ env.DOCKER_REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: 提取元数据
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}-${{ matrix.component }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha,prefix={{branch}}-
    
    - name: 构建并推送Docker镜像
      uses: docker/build-push-action@v5
      with:
        context: ${{ matrix.component }}
        file: ${{ matrix.component }}/Dockerfile
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64

  # 安全扫描
  security-scan:
    name: 安全扫描
    runs-on: ubuntu-latest
    needs: docker-build
    if: github.event_name == 'push' || github.event_name == 'release'
    
    strategy:
      matrix:
        component: [backend, frontend]
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 运行 Trivy 漏洞扫描
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}-${{ matrix.component }}:${{ github.sha }}
        format: 'sarif'
        output: 'trivy-results-${{ matrix.component }}.sarif'
    
    - name: 上传 Trivy 扫描结果
      uses: github/codeql-action/upload-sarif@v3
      with:
        sarif_file: 'trivy-results-${{ matrix.component }}.sarif'

  # 端到端测试
  e2e-tests:
    name: 端到端测试
    runs-on: ubuntu-latest
    needs: docker-build
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 启动测试环境
      run: |
        # 使用构建的镜像启动服务
        export BACKEND_IMAGE=${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}-backend:${{ github.sha }}
        export FRONTEND_IMAGE=${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}-frontend:${{ github.sha }}
        docker-compose -f docker-compose.test.yml up -d
        
        # 等待服务启动
        sleep 60
        
        # 健康检查
        curl --retry 10 --retry-delay 5 --retry-connrefused http://localhost:8080/health
        curl --retry 10 --retry-delay 5 --retry-connrefused http://localhost:80
    
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
    
    - name: 安装 Playwright
      run: |
        npm install -g @playwright/test
        npx playwright install --with-deps
    
    - name: 运行端到端测试
      run: |
        cd e2e-tests
        npm ci
        npm run test
    
    - name: 上传测试报告
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: e2e-test-results
        path: e2e-tests/test-results/
        retention-days: 7
    
    - name: 清理测试环境
      if: always()
      run: docker-compose -f docker-compose.test.yml down -v

  # 性能测试
  performance-tests:
    name: 性能测试
    runs-on: ubuntu-latest
    needs: docker-build
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 启动测试环境
      run: |
        export BACKEND_IMAGE=${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}-backend:${{ github.sha }}
        export FRONTEND_IMAGE=${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}-frontend:${{ github.sha }}
        docker-compose -f docker-compose.test.yml up -d
        sleep 60
    
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
    
    - name: 安装 Artillery
      run: npm install -g artillery
    
    - name: 运行性能测试
      env:
        TEST_BASE_URL: http://localhost:8080
        TEST_DURATION: 180
        TEST_ARRIVAL_RATE: 5
        TEST_MAX_VUSERS: 20
      run: |
        cd scripts
        node performance-test.js
    
    - name: 上传性能测试报告
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: performance-test-results
        path: scripts/test-results/
        retention-days: 7
    
    - name: 清理测试环境
      if: always()
      run: docker-compose -f docker-compose.test.yml down -v

  # 部署到测试环境
  deploy-staging:
    name: 部署到测试环境
    runs-on: ubuntu-latest
    needs: [e2e-tests, security-scan]
    if: github.event_name == 'push' && github.ref == 'refs/heads/develop'
    environment:
      name: staging
      url: https://staging.yinma.com
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 部署到测试环境
      run: |
        echo "部署到测试环境..."
        # 这里添加实际的部署脚本
        # 例如：kubectl apply -f k8s/staging/
        # 或者：ansible-playbook deploy-staging.yml
    
    - name: 验证部署
      run: |
        curl --retry 10 --retry-delay 5 --retry-connrefused https://staging.yinma.com/health
    
    - name: 通知部署结果
      if: always()
      run: |
        echo "测试环境部署完成"
        # 发送通知到Slack/钉钉等

  # 部署到生产环境
  deploy-production:
    name: 部署到生产环境
    runs-on: ubuntu-latest
    needs: [e2e-tests, security-scan, performance-tests]
    if: github.event_name == 'release'
    environment:
      name: production
      url: https://yinma.com
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 等待手动批准
      uses: trstringer/manual-approval@v1
      with:
        secret: ${{ github.TOKEN }}
        approvers: admin,devops-team
        minimum-approvals: 2
        issue-title: "生产环境部署批准 - ${{ github.event.release.tag_name }}"
        issue-body: |
          请审核并批准生产环境部署：
          
          **版本**: ${{ github.event.release.tag_name }}
          **分支**: ${{ github.ref }}
          **提交**: ${{ github.sha }}
          
          **变更内容**:
          ${{ github.event.release.body }}
          
          **测试状态**:
          - ✅ 单元测试通过
          - ✅ 端到端测试通过
          - ✅ 安全扫描通过
          - ✅ 性能测试通过
    
    - name: 部署到生产环境
      run: |
        echo "部署到生产环境..."
        # 这里添加实际的部署脚本
        # 例如：kubectl apply -f k8s/production/
        # 或者：ansible-playbook deploy-production.yml
    
    - name: 验证部署
      run: |
        curl --retry 10 --retry-delay 5 --retry-connrefused https://yinma.com/health
    
    - name: 创建部署标签
      run: |
        git tag -a "deployed-${{ github.event.release.tag_name }}" -m "Deployed to production"
        git push origin "deployed-${{ github.event.release.tag_name }}"
    
    - name: 通知部署结果
      if: always()
      run: |
        echo "生产环境部署完成"
        # 发送通知到Slack/钉钉等

  # 清理旧镜像
  cleanup:
    name: 清理旧镜像
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    needs: [deploy-staging, deploy-production]
    
    steps:
    - name: 清理旧的Docker镜像
      run: |
        echo "清理超过30天的旧镜像..."
        # 这里添加清理脚本
        # 例如：删除超过30天的镜像标签

  # 发布通知
  notify:
    name: 发布通知
    runs-on: ubuntu-latest
    if: always()
    needs: [deploy-staging, deploy-production]
    
    steps:
    - name: 发送通知
      run: |
        echo "CI/CD流程完成"
        # 发送通知到团队沟通工具
        # 例如：Slack、钉钉、企业微信等