{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-YinMa\\\\frontend\\\\src\\\\pages\\\\FinancialControl\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Typography, Row, Col, Statistic, Table, Button, Space, Select, DatePicker, Tabs, Progress, Tag, Tooltip, Modal, Form, Input, InputNumber, message, Spin, Empty, Divider } from 'antd';\nimport { DollarOutlined, RiseOutlined, PieChartOutlined, BarChartOutlined, TrendingUpOutlined, TrendingDownOutlined, ExportOutlined, PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, CalculatorOutlined, BankOutlined, CreditCardOutlined, WalletOutlined } from '@ant-design/icons';\nimport { financialAPI } from '../../services/api';\nimport dayjs from 'dayjs';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Paragraph\n} = Typography;\nconst {\n  Option\n} = Select;\nconst {\n  RangePicker\n} = DatePicker;\nconst {\n  TabPane\n} = Tabs;\nconst {\n  TextArea\n} = Input;\nconst FinancialControl = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [financialData, setFinancialData] = useState({\n    overview: {\n      totalRevenue: ********,\n      totalCost: 8500000,\n      grossProfit: 4000000,\n      netProfit: 3200000,\n      profitMargin: 25.6,\n      budgetExecution: 85.2\n    },\n    trends: [],\n    costAnalysis: [],\n    budgets: [],\n    cashFlow: []\n  });\n  const [selectedPeriod, setSelectedPeriod] = useState('month');\n  const [dateRange, setDateRange] = useState([dayjs().subtract(6, 'month'), dayjs()]);\n  const [budgetModalVisible, setBudgetModalVisible] = useState(false);\n  const [selectedBudget, setSelectedBudget] = useState(null);\n  const [budgetForm] = Form.useForm();\n\n  // 模拟数据\n  const mockTrendData = [{\n    month: '2024-07',\n    revenue: 980000,\n    cost: 720000,\n    profit: 260000\n  }, {\n    month: '2024-08',\n    revenue: 1150000,\n    cost: 850000,\n    profit: 300000\n  }, {\n    month: '2024-09',\n    revenue: 1320000,\n    cost: 980000,\n    profit: 340000\n  }, {\n    month: '2024-10',\n    revenue: 1100000,\n    cost: 820000,\n    profit: 280000\n  }, {\n    month: '2024-11',\n    revenue: 1200000,\n    cost: 890000,\n    profit: 310000\n  }, {\n    month: '2024-12',\n    revenue: 915000,\n    cost: 680000,\n    profit: 235000\n  }];\n  const mockCostData = [{\n    name: '主机设备',\n    value: 3600000,\n    percentage: 42.4\n  }, {\n    name: '配料设备',\n    value: 1700000,\n    percentage: 20.0\n  }, {\n    name: '输送设备',\n    value: 800000,\n    percentage: 9.4\n  }, {\n    name: '控制系统',\n    value: 700000,\n    percentage: 8.2\n  }, {\n    name: '其他',\n    value: 1700000,\n    percentage: 20.0\n  }];\n  const mockBudgetData = [{\n    id: 1,\n    category: '生产成本',\n    budgetAmount: 6000000,\n    actualAmount: 5120000,\n    variance: -880000,\n    varianceRate: -14.7,\n    status: 'under_budget',\n    period: '2024年度',\n    responsible: '生产部'\n  }, {\n    id: 2,\n    category: '销售费用',\n    budgetAmount: 800000,\n    actualAmount: 920000,\n    variance: 120000,\n    varianceRate: 15.0,\n    status: 'over_budget',\n    period: '2024年度',\n    responsible: '销售部'\n  }];\n  useEffect(() => {\n    loadFinancialData();\n  }, [selectedPeriod, dateRange]);\n  const loadFinancialData = async () => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      setFinancialData({\n        overview: {\n          totalRevenue: ********,\n          totalCost: 8500000,\n          grossProfit: 4000000,\n          netProfit: 3200000,\n          profitMargin: 25.6,\n          budgetExecution: 85.2\n        },\n        trends: mockTrendData,\n        costAnalysis: mockCostData,\n        budgets: mockBudgetData,\n        cashFlow: []\n      });\n    } catch (error) {\n      message.error('加载财务数据失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const budgetColumns = [{\n    title: '预算类别',\n    dataIndex: 'category',\n    key: 'category',\n    width: 120\n  }, {\n    title: '预算金额',\n    dataIndex: 'budgetAmount',\n    key: 'budgetAmount',\n    width: 120,\n    render: value => `¥${value.toLocaleString()}`\n  }, {\n    title: '实际金额',\n    dataIndex: 'actualAmount',\n    key: 'actualAmount',\n    width: 120,\n    render: value => `¥${value.toLocaleString()}`\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: status === 'under_budget' ? 'green' : 'red',\n      children: status === 'under_budget' ? '节约' : '超支'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 120,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 50\n        }, this),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        danger: true,\n        icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 57\n        }, this),\n        children: \"\\u5220\\u9664\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '24px',\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          children: \"\\u8D22\\u52A1\\u7BA1\\u63A7\\u4E2D\\u5FC3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n          children: \"\\u8D22\\u52A1\\u7BA1\\u63A7\\u4E2D\\u5FC3\\u63D0\\u4F9B\\u5168\\u9762\\u7684\\u8D22\\u52A1\\u6570\\u636E\\u5206\\u6790\\u548C\\u7BA1\\u63A7\\u529F\\u80FD\\uFF0C\\u5305\\u62EC\\u6210\\u672C\\u63A7\\u5236\\u3001\\u6536\\u5165\\u5206\\u6790\\u3001\\u9884\\u7B97\\u7BA1\\u7406\\u7B49\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Select, {\n          value: selectedPeriod,\n          onChange: setSelectedPeriod,\n          style: {\n            width: 120\n          },\n          children: [/*#__PURE__*/_jsxDEV(Option, {\n            value: \"day\",\n            children: \"\\u65E5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Option, {\n            value: \"week\",\n            children: \"\\u5468\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Option, {\n            value: \"month\",\n            children: \"\\u6708\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Option, {\n            value: \"quarter\",\n            children: \"\\u5B63\\u5EA6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Option, {\n            value: \"year\",\n            children: \"\\u5E74\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(RangePicker, {\n          value: dateRange,\n          onChange: setDateRange,\n          style: {\n            width: 240\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 40\n          }, this),\n          children: \"\\u5BFC\\u51FA\\u62A5\\u8868\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Spin, {\n      spinning: loading,\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        style: {\n          marginBottom: '24px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: [/*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u603B\\u6536\\u5165\",\n              value: financialData.overview.totalRevenue,\n              precision: 0,\n              valueStyle: {\n                color: '#3f8600'\n              },\n              prefix: /*#__PURE__*/_jsxDEV(DollarOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 25\n              }, this),\n              suffix: \"\\u5143\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(TrendingUpOutlined, {\n                style: {\n                  color: '#3f8600',\n                  marginRight: '4px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: '#3f8600',\n                  fontSize: '12px'\n                },\n                children: \"\\u8F83\\u4E0A\\u6708 +12.5%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: [/*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u603B\\u6210\\u672C\",\n              value: financialData.overview.totalCost,\n              precision: 0,\n              valueStyle: {\n                color: '#cf1322'\n              },\n              prefix: /*#__PURE__*/_jsxDEV(RiseOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 25\n              }, this),\n              suffix: \"\\u5143\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(TrendingDownOutlined, {\n                style: {\n                  color: '#3f8600',\n                  marginRight: '4px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: '#3f8600',\n                  fontSize: '12px'\n                },\n                children: \"\\u8F83\\u4E0A\\u6708 -5.2%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: [/*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u6BDB\\u5229\\u6DA6\",\n              value: financialData.overview.grossProfit,\n              precision: 0,\n              valueStyle: {\n                color: '#1890ff'\n              },\n              prefix: /*#__PURE__*/_jsxDEV(CalculatorOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 25\n              }, this),\n              suffix: \"\\u5143\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '8px'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '12px',\n                  color: '#666'\n                },\n                children: [\"\\u6BDB\\u5229\\u7387: \", financialData.overview.profitMargin, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: [/*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u9884\\u7B97\\u6267\\u884C\\u7387\",\n              value: financialData.overview.budgetExecution,\n              precision: 1,\n              valueStyle: {\n                color: '#722ed1'\n              },\n              prefix: /*#__PURE__*/_jsxDEV(PieChartOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 25\n              }, this),\n              suffix: \"%\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '8px'\n              },\n              children: /*#__PURE__*/_jsxDEV(Progress, {\n                percent: financialData.overview.budgetExecution,\n                size: \"small\",\n                showInfo: false,\n                strokeColor: \"#722ed1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n        defaultActiveKey: \"budget\",\n        type: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u9884\\u7B97\\u7BA1\\u7406\",\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            title: \"\\u9884\\u7B97\\u6267\\u884C\\u60C5\\u51B5\",\n            extra: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 25\n              }, this),\n              onClick: () => setBudgetModalVisible(true),\n              children: \"\\u65B0\\u589E\\u9884\\u7B97\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 17\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              columns: budgetColumns,\n              dataSource: financialData.budgets,\n              rowKey: \"id\",\n              pagination: {\n                pageSize: 10,\n                showSizeChanger: true,\n                showQuickJumper: true,\n                showTotal: total => `共 ${total} 条记录`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this)\n        }, \"budget\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u6210\\u672C\\u5206\\u6790\",\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            title: \"\\u6210\\u672C\\u6784\\u6210\\u5206\\u6790\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '20px'\n              },\n              children: /*#__PURE__*/_jsxDEV(Empty, {\n                description: \"\\u56FE\\u8868\\u529F\\u80FD\\u5F00\\u53D1\\u4E2D...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this)\n        }, \"cost\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u8D8B\\u52BF\\u5206\\u6790\",\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            title: \"\\u6536\\u5165\\u6210\\u672C\\u8D8B\\u52BF\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '20px'\n              },\n              children: /*#__PURE__*/_jsxDEV(Empty, {\n                description: \"\\u56FE\\u8868\\u529F\\u80FD\\u5F00\\u53D1\\u4E2D...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this)\n        }, \"trends\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: selectedBudget ? '编辑预算' : '新增预算',\n      open: budgetModalVisible,\n      onCancel: () => {\n        setBudgetModalVisible(false);\n        setSelectedBudget(null);\n        budgetForm.resetFields();\n      },\n      footer: null,\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: budgetForm,\n        layout: \"vertical\",\n        onFinish: values => {\n          console.log('预算表单提交:', values);\n          message.success('操作成功');\n          setBudgetModalVisible(false);\n          budgetForm.resetFields();\n        },\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"category\",\n          label: \"\\u9884\\u7B97\\u7C7B\\u522B\",\n          rules: [{\n            required: true,\n            message: '请输入预算类别'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u9884\\u7B97\\u7C7B\\u522B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"budgetAmount\",\n          label: \"\\u9884\\u7B97\\u91D1\\u989D\",\n          rules: [{\n            required: true,\n            message: '请输入预算金额'\n          }],\n          children: /*#__PURE__*/_jsxDEV(InputNumber, {\n            style: {\n              width: '100%'\n            },\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u9884\\u7B97\\u91D1\\u989D\",\n            formatter: value => `¥ ${value}`.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ','),\n            parser: value => value.replace(/¥\\s?|(,*)/g, '')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"responsible\",\n          label: \"\\u8D1F\\u8D23\\u90E8\\u95E8\",\n          rules: [{\n            required: true,\n            message: '请输入负责部门'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8D1F\\u8D23\\u90E8\\u95E8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"period\",\n          label: \"\\u9884\\u7B97\\u5468\\u671F\",\n          rules: [{\n            required: true,\n            message: '请输入预算周期'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u9884\\u7B97\\u5468\\u671F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              children: selectedBudget ? '更新' : '创建'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => {\n                setBudgetModalVisible(false);\n                setSelectedBudget(null);\n                budgetForm.resetFields();\n              },\n              children: \"\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 197,\n    columnNumber: 5\n  }, this);\n};\n_s(FinancialControl, \"AAd+6th5xgv0fM4PEcwpFotfzPM=\", false, function () {\n  return [Form.useForm];\n});\n_c = FinancialControl;\nexport default FinancialControl;\nvar _c;\n$RefreshReg$(_c, \"FinancialControl\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Typography", "Row", "Col", "Statistic", "Table", "<PERSON><PERSON>", "Space", "Select", "DatePicker", "Tabs", "Progress", "Tag", "<PERSON><PERSON><PERSON>", "Modal", "Form", "Input", "InputNumber", "message", "Spin", "Empty", "Divider", "DollarOutlined", "RiseOutlined", "PieChartOutlined", "BarChartOutlined", "TrendingUpOutlined", "TrendingDownOutlined", "ExportOutlined", "PlusOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "CalculatorOutlined", "BankOutlined", "CreditCardOutlined", "WalletOutlined", "financialAPI", "dayjs", "jsxDEV", "_jsxDEV", "Title", "Paragraph", "Option", "RangePicker", "TabPane", "TextArea", "FinancialControl", "_s", "loading", "setLoading", "financialData", "setFinancialData", "overview", "totalRevenue", "totalCost", "grossProfit", "netProfit", "profitMargin", "budgetExecution", "trends", "costAnalysis", "budgets", "cashFlow", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedPeriod", "date<PERSON><PERSON><PERSON>", "setDateRange", "subtract", "budgetModalVisible", "setBudgetModalVisible", "<PERSON><PERSON><PERSON><PERSON>", "setSelected<PERSON><PERSON><PERSON>", "budgetForm", "useForm", "mockTrendData", "month", "revenue", "cost", "profit", "mockCostData", "name", "value", "percentage", "mockBudgetData", "id", "category", "budgetAmount", "actualAmount", "variance", "varianceRate", "status", "period", "responsible", "loadFinancialData", "Promise", "resolve", "setTimeout", "error", "budgetColumns", "title", "dataIndex", "key", "width", "render", "toLocaleString", "color", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_", "record", "size", "type", "icon", "danger", "style", "padding", "marginBottom", "display", "justifyContent", "alignItems", "level", "onChange", "spinning", "gutter", "span", "precision", "valueStyle", "prefix", "suffix", "marginTop", "marginRight", "fontSize", "percent", "showInfo", "strokeColor", "defaultActiveKey", "tab", "extra", "onClick", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "total", "description", "open", "onCancel", "resetFields", "footer", "form", "layout", "onFinish", "values", "console", "log", "success", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "formatter", "replace", "parser", "htmlType", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-YinMa/frontend/src/pages/FinancialControl/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Typography,\n  Row,\n  Col,\n  Statistic,\n  Table,\n  Button,\n  Space,\n  Select,\n  DatePicker,\n  Tabs,\n  Progress,\n  Tag,\n  Tooltip,\n  Modal,\n  Form,\n  Input,\n  InputNumber,\n  message,\n  Spin,\n  Empty,\n  Divider\n} from 'antd';\nimport {\n  DollarOutlined,\n  RiseOutlined,\n  PieChartOutlined,\n  Bar<PERSON><PERSON>Outlined,\n  TrendingUpOutlined,\n  TrendingDownOutlined,\n  ExportOutlined,\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  CalculatorOutlined,\n  BankOutlined,\n  CreditCardOutlined,\n  WalletOutlined\n} from '@ant-design/icons';\nimport { financialAPI } from '../../services/api';\nimport dayjs from 'dayjs';\n\nconst { Title, Paragraph } = Typography;\nconst { Option } = Select;\nconst { RangePicker } = DatePicker;\nconst { TabPane } = Tabs;\nconst { TextArea } = Input;\n\nconst FinancialControl = () => {\n  const [loading, setLoading] = useState(false);\n  const [financialData, setFinancialData] = useState({\n    overview: {\n      totalRevenue: ********,\n      totalCost: 8500000,\n      grossProfit: 4000000,\n      netProfit: 3200000,\n      profitMargin: 25.6,\n      budgetExecution: 85.2\n    },\n    trends: [],\n    costAnalysis: [],\n    budgets: [],\n    cashFlow: []\n  });\n  const [selectedPeriod, setSelectedPeriod] = useState('month');\n  const [dateRange, setDateRange] = useState([dayjs().subtract(6, 'month'), dayjs()]);\n  const [budgetModalVisible, setBudgetModalVisible] = useState(false);\n  const [selectedBudget, setSelectedBudget] = useState(null);\n  const [budgetForm] = Form.useForm();\n\n  // 模拟数据\n  const mockTrendData = [\n    { month: '2024-07', revenue: 980000, cost: 720000, profit: 260000 },\n    { month: '2024-08', revenue: 1150000, cost: 850000, profit: 300000 },\n    { month: '2024-09', revenue: 1320000, cost: 980000, profit: 340000 },\n    { month: '2024-10', revenue: 1100000, cost: 820000, profit: 280000 },\n    { month: '2024-11', revenue: 1200000, cost: 890000, profit: 310000 },\n    { month: '2024-12', revenue: 915000, cost: 680000, profit: 235000 }\n  ];\n\n  const mockCostData = [\n    { name: '主机设备', value: 3600000, percentage: 42.4 },\n    { name: '配料设备', value: 1700000, percentage: 20.0 },\n    { name: '输送设备', value: 800000, percentage: 9.4 },\n    { name: '控制系统', value: 700000, percentage: 8.2 },\n    { name: '其他', value: 1700000, percentage: 20.0 }\n  ];\n\n  const mockBudgetData = [\n    {\n      id: 1,\n      category: '生产成本',\n      budgetAmount: 6000000,\n      actualAmount: 5120000,\n      variance: -880000,\n      varianceRate: -14.7,\n      status: 'under_budget',\n      period: '2024年度',\n      responsible: '生产部'\n    },\n    {\n      id: 2,\n      category: '销售费用',\n      budgetAmount: 800000,\n      actualAmount: 920000,\n      variance: 120000,\n      varianceRate: 15.0,\n      status: 'over_budget',\n      period: '2024年度',\n      responsible: '销售部'\n    }\n  ];\n\n  useEffect(() => {\n    loadFinancialData();\n  }, [selectedPeriod, dateRange]);\n\n  const loadFinancialData = async () => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      setFinancialData({\n        overview: {\n          totalRevenue: ********,\n          totalCost: 8500000,\n          grossProfit: 4000000,\n          netProfit: 3200000,\n          profitMargin: 25.6,\n          budgetExecution: 85.2\n        },\n        trends: mockTrendData,\n        costAnalysis: mockCostData,\n        budgets: mockBudgetData,\n        cashFlow: []\n      });\n    } catch (error) {\n      message.error('加载财务数据失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const budgetColumns = [\n    {\n      title: '预算类别',\n      dataIndex: 'category',\n      key: 'category',\n      width: 120\n    },\n    {\n      title: '预算金额',\n      dataIndex: 'budgetAmount',\n      key: 'budgetAmount',\n      width: 120,\n      render: (value) => `¥${value.toLocaleString()}`\n    },\n    {\n      title: '实际金额',\n      dataIndex: 'actualAmount',\n      key: 'actualAmount',\n      width: 120,\n      render: (value) => `¥${value.toLocaleString()}`\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status) => (\n        <Tag color={status === 'under_budget' ? 'green' : 'red'}>\n          {status === 'under_budget' ? '节约' : '超支'}\n        </Tag>\n      )\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 120,\n      render: (_, record) => (\n        <Space size=\"small\">\n          <Button type=\"link\" size=\"small\" icon={<EditOutlined />}>\n            编辑\n          </Button>\n          <Button type=\"link\" size=\"small\" danger icon={<DeleteOutlined />}>\n            删除\n          </Button>\n        </Space>\n      )\n    }\n  ];\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n        <div>\n          <Title level={2}>财务管控中心</Title>\n          <Paragraph>\n            财务管控中心提供全面的财务数据分析和管控功能，包括成本控制、收入分析、预算管理等。\n          </Paragraph>\n        </div>\n        <Space>\n          <Select\n            value={selectedPeriod}\n            onChange={setSelectedPeriod}\n            style={{ width: 120 }}\n          >\n            <Option value=\"day\">日</Option>\n            <Option value=\"week\">周</Option>\n            <Option value=\"month\">月</Option>\n            <Option value=\"quarter\">季度</Option>\n            <Option value=\"year\">年</Option>\n          </Select>\n          <RangePicker\n            value={dateRange}\n            onChange={setDateRange}\n            style={{ width: 240 }}\n          />\n          <Button type=\"primary\" icon={<ExportOutlined />}>\n            导出报表\n          </Button>\n        </Space>\n      </div>\n\n      <Spin spinning={loading}>\n        {/* 财务概览 */}\n        <Row gutter={16} style={{ marginBottom: '24px' }}>\n          <Col span={6}>\n            <Card>\n              <Statistic\n                title=\"总收入\"\n                value={financialData.overview.totalRevenue}\n                precision={0}\n                valueStyle={{ color: '#3f8600' }}\n                prefix={<DollarOutlined />}\n                suffix=\"元\"\n              />\n              <div style={{ marginTop: '8px' }}>\n                <TrendingUpOutlined style={{ color: '#3f8600', marginRight: '4px' }} />\n                <span style={{ color: '#3f8600', fontSize: '12px' }}>较上月 +12.5%</span>\n              </div>\n            </Card>\n          </Col>\n          <Col span={6}>\n            <Card>\n              <Statistic\n                title=\"总成本\"\n                value={financialData.overview.totalCost}\n                precision={0}\n                valueStyle={{ color: '#cf1322' }}\n                prefix={<RiseOutlined />}\n                suffix=\"元\"\n              />\n              <div style={{ marginTop: '8px' }}>\n                <TrendingDownOutlined style={{ color: '#3f8600', marginRight: '4px' }} />\n                <span style={{ color: '#3f8600', fontSize: '12px' }}>较上月 -5.2%</span>\n              </div>\n            </Card>\n          </Col>\n          <Col span={6}>\n            <Card>\n              <Statistic\n                title=\"毛利润\"\n                value={financialData.overview.grossProfit}\n                precision={0}\n                valueStyle={{ color: '#1890ff' }}\n                prefix={<CalculatorOutlined />}\n                suffix=\"元\"\n              />\n              <div style={{ marginTop: '8px' }}>\n                <span style={{ fontSize: '12px', color: '#666' }}>毛利率: {financialData.overview.profitMargin}%</span>\n              </div>\n            </Card>\n          </Col>\n          <Col span={6}>\n            <Card>\n              <Statistic\n                title=\"预算执行率\"\n                value={financialData.overview.budgetExecution}\n                precision={1}\n                valueStyle={{ color: '#722ed1' }}\n                prefix={<PieChartOutlined />}\n                suffix=\"%\"\n              />\n              <div style={{ marginTop: '8px' }}>\n                <Progress\n                  percent={financialData.overview.budgetExecution}\n                  size=\"small\"\n                  showInfo={false}\n                  strokeColor=\"#722ed1\"\n                />\n              </div>\n            </Card>\n          </Col>\n        </Row>\n\n        {/* 详细分析 */}\n        <Tabs defaultActiveKey=\"budget\" type=\"card\">\n          <TabPane tab=\"预算管理\" key=\"budget\">\n            <Card\n              title=\"预算执行情况\"\n              extra={\n                <Button\n                  type=\"primary\"\n                  icon={<PlusOutlined />}\n                  onClick={() => setBudgetModalVisible(true)}\n                >\n                  新增预算\n                </Button>\n              }\n            >\n              <Table\n                columns={budgetColumns}\n                dataSource={financialData.budgets}\n                rowKey=\"id\"\n                pagination={{\n                  pageSize: 10,\n                  showSizeChanger: true,\n                  showQuickJumper: true,\n                  showTotal: (total) => `共 ${total} 条记录`\n                }}\n              />\n            </Card>\n          </TabPane>\n\n          <TabPane tab=\"成本分析\" key=\"cost\">\n            <Card title=\"成本构成分析\">\n              <div style={{ padding: '20px' }}>\n                <Empty description=\"图表功能开发中...\" />\n              </div>\n            </Card>\n          </TabPane>\n\n          <TabPane tab=\"趋势分析\" key=\"trends\">\n            <Card title=\"收入成本趋势\">\n              <div style={{ padding: '20px' }}>\n                <Empty description=\"图表功能开发中...\" />\n              </div>\n            </Card>\n          </TabPane>\n        </Tabs>\n      </Spin>\n\n      {/* 预算管理弹窗 */}\n      <Modal\n        title={selectedBudget ? '编辑预算' : '新增预算'}\n        open={budgetModalVisible}\n        onCancel={() => {\n          setBudgetModalVisible(false);\n          setSelectedBudget(null);\n          budgetForm.resetFields();\n        }}\n        footer={null}\n        width={600}\n      >\n        <Form\n          form={budgetForm}\n          layout=\"vertical\"\n          onFinish={(values) => {\n            console.log('预算表单提交:', values);\n            message.success('操作成功');\n            setBudgetModalVisible(false);\n            budgetForm.resetFields();\n          }}\n        >\n          <Form.Item\n            name=\"category\"\n            label=\"预算类别\"\n            rules={[{ required: true, message: '请输入预算类别' }]}\n          >\n            <Input placeholder=\"请输入预算类别\" />\n          </Form.Item>\n          <Form.Item\n            name=\"budgetAmount\"\n            label=\"预算金额\"\n            rules={[{ required: true, message: '请输入预算金额' }]}\n          >\n            <InputNumber\n              style={{ width: '100%' }}\n              placeholder=\"请输入预算金额\"\n              formatter={value => `¥ ${value}`.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')}\n              parser={value => value.replace(/¥\\s?|(,*)/g, '')}\n            />\n          </Form.Item>\n          <Form.Item\n            name=\"responsible\"\n            label=\"负责部门\"\n            rules={[{ required: true, message: '请输入负责部门' }]}\n          >\n            <Input placeholder=\"请输入负责部门\" />\n          </Form.Item>\n          <Form.Item\n            name=\"period\"\n            label=\"预算周期\"\n            rules={[{ required: true, message: '请输入预算周期' }]}\n          >\n            <Input placeholder=\"请输入预算周期\" />\n          </Form.Item>\n          <Form.Item>\n            <Space>\n              <Button type=\"primary\" htmlType=\"submit\">\n                {selectedBudget ? '更新' : '创建'}\n              </Button>\n              <Button onClick={() => {\n                setBudgetModalVisible(false);\n                setSelectedBudget(null);\n                budgetForm.resetFields();\n              }}>\n                取消\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default FinancialControl;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,GAAG,EACHC,OAAO,EACPC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,WAAW,EACXC,OAAO,EACPC,IAAI,EACJC,KAAK,EACLC,OAAO,QACF,MAAM;AACb,SACEC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,kBAAkB,EAClBC,oBAAoB,EACpBC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,kBAAkB,EAClBC,YAAY,EACZC,kBAAkB,EAClBC,cAAc,QACT,mBAAmB;AAC1B,SAASC,YAAY,QAAQ,oBAAoB;AACjD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAM;EAAEC,KAAK;EAAEC;AAAU,CAAC,GAAGzC,UAAU;AACvC,MAAM;EAAE0C;AAAO,CAAC,GAAGnC,MAAM;AACzB,MAAM;EAAEoC;AAAY,CAAC,GAAGnC,UAAU;AAClC,MAAM;EAAEoC;AAAQ,CAAC,GAAGnC,IAAI;AACxB,MAAM;EAAEoC;AAAS,CAAC,GAAG9B,KAAK;AAE1B,MAAM+B,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqD,aAAa,EAAEC,gBAAgB,CAAC,GAAGtD,QAAQ,CAAC;IACjDuD,QAAQ,EAAE;MACRC,YAAY,EAAE,QAAQ;MACtBC,SAAS,EAAE,OAAO;MAClBC,WAAW,EAAE,OAAO;MACpBC,SAAS,EAAE,OAAO;MAClBC,YAAY,EAAE,IAAI;MAClBC,eAAe,EAAE;IACnB,CAAC;IACDC,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGnE,QAAQ,CAAC,OAAO,CAAC;EAC7D,MAAM,CAACoE,SAAS,EAAEC,YAAY,CAAC,GAAGrE,QAAQ,CAAC,CAACwC,KAAK,CAAC,CAAC,CAAC8B,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE9B,KAAK,CAAC,CAAC,CAAC,CAAC;EACnF,MAAM,CAAC+B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACyE,cAAc,EAAEC,iBAAiB,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC2E,UAAU,CAAC,GAAG1D,IAAI,CAAC2D,OAAO,CAAC,CAAC;;EAEnC;EACA,MAAMC,aAAa,GAAG,CACpB;IAAEC,KAAK,EAAE,SAAS;IAAEC,OAAO,EAAE,MAAM;IAAEC,IAAI,EAAE,MAAM;IAAEC,MAAM,EAAE;EAAO,CAAC,EACnE;IAAEH,KAAK,EAAE,SAAS;IAAEC,OAAO,EAAE,OAAO;IAAEC,IAAI,EAAE,MAAM;IAAEC,MAAM,EAAE;EAAO,CAAC,EACpE;IAAEH,KAAK,EAAE,SAAS;IAAEC,OAAO,EAAE,OAAO;IAAEC,IAAI,EAAE,MAAM;IAAEC,MAAM,EAAE;EAAO,CAAC,EACpE;IAAEH,KAAK,EAAE,SAAS;IAAEC,OAAO,EAAE,OAAO;IAAEC,IAAI,EAAE,MAAM;IAAEC,MAAM,EAAE;EAAO,CAAC,EACpE;IAAEH,KAAK,EAAE,SAAS;IAAEC,OAAO,EAAE,OAAO;IAAEC,IAAI,EAAE,MAAM;IAAEC,MAAM,EAAE;EAAO,CAAC,EACpE;IAAEH,KAAK,EAAE,SAAS;IAAEC,OAAO,EAAE,MAAM;IAAEC,IAAI,EAAE,MAAM;IAAEC,MAAM,EAAE;EAAO,CAAC,CACpE;EAED,MAAMC,YAAY,GAAG,CACnB;IAAEC,IAAI,EAAE,MAAM;IAAEC,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE;EAAK,CAAC,EAClD;IAAEF,IAAI,EAAE,MAAM;IAAEC,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE;EAAK,CAAC,EAClD;IAAEF,IAAI,EAAE,MAAM;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAI,CAAC,EAChD;IAAEF,IAAI,EAAE,MAAM;IAAEC,KAAK,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAI,CAAC,EAChD;IAAEF,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE;EAAK,CAAC,CACjD;EAED,MAAMC,cAAc,GAAG,CACrB;IACEC,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,MAAM;IAChBC,YAAY,EAAE,OAAO;IACrBC,YAAY,EAAE,OAAO;IACrBC,QAAQ,EAAE,CAAC,MAAM;IACjBC,YAAY,EAAE,CAAC,IAAI;IACnBC,MAAM,EAAE,cAAc;IACtBC,MAAM,EAAE,QAAQ;IAChBC,WAAW,EAAE;EACf,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,MAAM;IAChBC,YAAY,EAAE,MAAM;IACpBC,YAAY,EAAE,MAAM;IACpBC,QAAQ,EAAE,MAAM;IAChBC,YAAY,EAAE,IAAI;IAClBC,MAAM,EAAE,aAAa;IACrBC,MAAM,EAAE,QAAQ;IAChBC,WAAW,EAAE;EACf,CAAC,CACF;EAED9F,SAAS,CAAC,MAAM;IACd+F,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAAC9B,cAAc,EAAEE,SAAS,CAAC,CAAC;EAE/B,MAAM4B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC5C,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA,MAAM,IAAI6C,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MACvD5C,gBAAgB,CAAC;QACfC,QAAQ,EAAE;UACRC,YAAY,EAAE,QAAQ;UACtBC,SAAS,EAAE,OAAO;UAClBC,WAAW,EAAE,OAAO;UACpBC,SAAS,EAAE,OAAO;UAClBC,YAAY,EAAE,IAAI;UAClBC,eAAe,EAAE;QACnB,CAAC;QACDC,MAAM,EAAEe,aAAa;QACrBd,YAAY,EAAEmB,YAAY;QAC1BlB,OAAO,EAAEsB,cAAc;QACvBrB,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOmC,KAAK,EAAE;MACdhF,OAAO,CAACgF,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRhD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiD,aAAa,GAAG,CACpB;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGtB,KAAK,IAAK,IAAIA,KAAK,CAACuB,cAAc,CAAC,CAAC;EAC/C,CAAC,EACD;IACEL,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGtB,KAAK,IAAK,IAAIA,KAAK,CAACuB,cAAc,CAAC,CAAC;EAC/C,CAAC,EACD;IACEL,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGb,MAAM,iBACbnD,OAAA,CAAC5B,GAAG;MAAC8F,KAAK,EAAEf,MAAM,KAAK,cAAc,GAAG,OAAO,GAAG,KAAM;MAAAgB,QAAA,EACrDhB,MAAM,KAAK,cAAc,GAAG,IAAI,GAAG;IAAI;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC;EAET,CAAC,EACD;IACEX,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACQ,CAAC,EAAEC,MAAM,kBAChBzE,OAAA,CAACjC,KAAK;MAAC2G,IAAI,EAAC,OAAO;MAAAP,QAAA,gBACjBnE,OAAA,CAAClC,MAAM;QAAC6G,IAAI,EAAC,MAAM;QAACD,IAAI,EAAC,OAAO;QAACE,IAAI,eAAE5E,OAAA,CAACV,YAAY;UAAA8E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAJ,QAAA,EAAC;MAEzD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTvE,OAAA,CAAClC,MAAM;QAAC6G,IAAI,EAAC,MAAM;QAACD,IAAI,EAAC,OAAO;QAACG,MAAM;QAACD,IAAI,eAAE5E,OAAA,CAACT,cAAc;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAJ,QAAA,EAAC;MAElE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAEX,CAAC,CACF;EAED,oBACEvE,OAAA;IAAK8E,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAZ,QAAA,gBAC9BnE,OAAA;MAAK8E,KAAK,EAAE;QAAEE,YAAY,EAAE,MAAM;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAhB,QAAA,gBAC3GnE,OAAA;QAAAmE,QAAA,gBACEnE,OAAA,CAACC,KAAK;UAACmF,KAAK,EAAE,CAAE;UAAAjB,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC/BvE,OAAA,CAACE,SAAS;UAAAiE,QAAA,EAAC;QAEX;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACNvE,OAAA,CAACjC,KAAK;QAAAoG,QAAA,gBACJnE,OAAA,CAAChC,MAAM;UACL0E,KAAK,EAAElB,cAAe;UACtB6D,QAAQ,EAAE5D,iBAAkB;UAC5BqD,KAAK,EAAE;YAAEf,KAAK,EAAE;UAAI,CAAE;UAAAI,QAAA,gBAEtBnE,OAAA,CAACG,MAAM;YAACuC,KAAK,EAAC,KAAK;YAAAyB,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC9BvE,OAAA,CAACG,MAAM;YAACuC,KAAK,EAAC,MAAM;YAAAyB,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC/BvE,OAAA,CAACG,MAAM;YAACuC,KAAK,EAAC,OAAO;YAAAyB,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChCvE,OAAA,CAACG,MAAM;YAACuC,KAAK,EAAC,SAAS;YAAAyB,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACnCvE,OAAA,CAACG,MAAM;YAACuC,KAAK,EAAC,MAAM;YAAAyB,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACTvE,OAAA,CAACI,WAAW;UACVsC,KAAK,EAAEhB,SAAU;UACjB2D,QAAQ,EAAE1D,YAAa;UACvBmD,KAAK,EAAE;YAAEf,KAAK,EAAE;UAAI;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACFvE,OAAA,CAAClC,MAAM;UAAC6G,IAAI,EAAC,SAAS;UAACC,IAAI,eAAE5E,OAAA,CAACZ,cAAc;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAAC;QAEjD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENvE,OAAA,CAACrB,IAAI;MAAC2G,QAAQ,EAAE7E,OAAQ;MAAA0D,QAAA,gBAEtBnE,OAAA,CAACtC,GAAG;QAAC6H,MAAM,EAAE,EAAG;QAACT,KAAK,EAAE;UAAEE,YAAY,EAAE;QAAO,CAAE;QAAAb,QAAA,gBAC/CnE,OAAA,CAACrC,GAAG;UAAC6H,IAAI,EAAE,CAAE;UAAArB,QAAA,eACXnE,OAAA,CAACxC,IAAI;YAAA2G,QAAA,gBACHnE,OAAA,CAACpC,SAAS;cACRgG,KAAK,EAAC,oBAAK;cACXlB,KAAK,EAAE/B,aAAa,CAACE,QAAQ,CAACC,YAAa;cAC3C2E,SAAS,EAAE,CAAE;cACbC,UAAU,EAAE;gBAAExB,KAAK,EAAE;cAAU,CAAE;cACjCyB,MAAM,eAAE3F,OAAA,CAAClB,cAAc;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BqB,MAAM,EAAC;YAAG;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACFvE,OAAA;cAAK8E,KAAK,EAAE;gBAAEe,SAAS,EAAE;cAAM,CAAE;cAAA1B,QAAA,gBAC/BnE,OAAA,CAACd,kBAAkB;gBAAC4F,KAAK,EAAE;kBAAEZ,KAAK,EAAE,SAAS;kBAAE4B,WAAW,EAAE;gBAAM;cAAE;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvEvE,OAAA;gBAAM8E,KAAK,EAAE;kBAAEZ,KAAK,EAAE,SAAS;kBAAE6B,QAAQ,EAAE;gBAAO,CAAE;gBAAA5B,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNvE,OAAA,CAACrC,GAAG;UAAC6H,IAAI,EAAE,CAAE;UAAArB,QAAA,eACXnE,OAAA,CAACxC,IAAI;YAAA2G,QAAA,gBACHnE,OAAA,CAACpC,SAAS;cACRgG,KAAK,EAAC,oBAAK;cACXlB,KAAK,EAAE/B,aAAa,CAACE,QAAQ,CAACE,SAAU;cACxC0E,SAAS,EAAE,CAAE;cACbC,UAAU,EAAE;gBAAExB,KAAK,EAAE;cAAU,CAAE;cACjCyB,MAAM,eAAE3F,OAAA,CAACjB,YAAY;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBqB,MAAM,EAAC;YAAG;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACFvE,OAAA;cAAK8E,KAAK,EAAE;gBAAEe,SAAS,EAAE;cAAM,CAAE;cAAA1B,QAAA,gBAC/BnE,OAAA,CAACb,oBAAoB;gBAAC2F,KAAK,EAAE;kBAAEZ,KAAK,EAAE,SAAS;kBAAE4B,WAAW,EAAE;gBAAM;cAAE;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzEvE,OAAA;gBAAM8E,KAAK,EAAE;kBAAEZ,KAAK,EAAE,SAAS;kBAAE6B,QAAQ,EAAE;gBAAO,CAAE;gBAAA5B,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNvE,OAAA,CAACrC,GAAG;UAAC6H,IAAI,EAAE,CAAE;UAAArB,QAAA,eACXnE,OAAA,CAACxC,IAAI;YAAA2G,QAAA,gBACHnE,OAAA,CAACpC,SAAS;cACRgG,KAAK,EAAC,oBAAK;cACXlB,KAAK,EAAE/B,aAAa,CAACE,QAAQ,CAACG,WAAY;cAC1CyE,SAAS,EAAE,CAAE;cACbC,UAAU,EAAE;gBAAExB,KAAK,EAAE;cAAU,CAAE;cACjCyB,MAAM,eAAE3F,OAAA,CAACP,kBAAkB;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC/BqB,MAAM,EAAC;YAAG;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACFvE,OAAA;cAAK8E,KAAK,EAAE;gBAAEe,SAAS,EAAE;cAAM,CAAE;cAAA1B,QAAA,eAC/BnE,OAAA;gBAAM8E,KAAK,EAAE;kBAAEiB,QAAQ,EAAE,MAAM;kBAAE7B,KAAK,EAAE;gBAAO,CAAE;gBAAAC,QAAA,GAAC,sBAAK,EAACxD,aAAa,CAACE,QAAQ,CAACK,YAAY,EAAC,GAAC;cAAA;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNvE,OAAA,CAACrC,GAAG;UAAC6H,IAAI,EAAE,CAAE;UAAArB,QAAA,eACXnE,OAAA,CAACxC,IAAI;YAAA2G,QAAA,gBACHnE,OAAA,CAACpC,SAAS;cACRgG,KAAK,EAAC,gCAAO;cACblB,KAAK,EAAE/B,aAAa,CAACE,QAAQ,CAACM,eAAgB;cAC9CsE,SAAS,EAAE,CAAE;cACbC,UAAU,EAAE;gBAAExB,KAAK,EAAE;cAAU,CAAE;cACjCyB,MAAM,eAAE3F,OAAA,CAAChB,gBAAgB;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC7BqB,MAAM,EAAC;YAAG;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACFvE,OAAA;cAAK8E,KAAK,EAAE;gBAAEe,SAAS,EAAE;cAAM,CAAE;cAAA1B,QAAA,eAC/BnE,OAAA,CAAC7B,QAAQ;gBACP6H,OAAO,EAAErF,aAAa,CAACE,QAAQ,CAACM,eAAgB;gBAChDuD,IAAI,EAAC,OAAO;gBACZuB,QAAQ,EAAE,KAAM;gBAChBC,WAAW,EAAC;cAAS;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvE,OAAA,CAAC9B,IAAI;QAACiI,gBAAgB,EAAC,QAAQ;QAACxB,IAAI,EAAC,MAAM;QAAAR,QAAA,gBACzCnE,OAAA,CAACK,OAAO;UAAC+F,GAAG,EAAC,0BAAM;UAAAjC,QAAA,eACjBnE,OAAA,CAACxC,IAAI;YACHoG,KAAK,EAAC,sCAAQ;YACdyC,KAAK,eACHrG,OAAA,CAAClC,MAAM;cACL6G,IAAI,EAAC,SAAS;cACdC,IAAI,eAAE5E,OAAA,CAACX,YAAY;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvB+B,OAAO,EAAEA,CAAA,KAAMxE,qBAAqB,CAAC,IAAI,CAAE;cAAAqC,QAAA,EAC5C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;YAAAJ,QAAA,eAEDnE,OAAA,CAACnC,KAAK;cACJ0I,OAAO,EAAE5C,aAAc;cACvB6C,UAAU,EAAE7F,aAAa,CAACW,OAAQ;cAClCmF,MAAM,EAAC,IAAI;cACXC,UAAU,EAAE;gBACVC,QAAQ,EAAE,EAAE;gBACZC,eAAe,EAAE,IAAI;gBACrBC,eAAe,EAAE,IAAI;gBACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;cAClC;YAAE;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC,GAxBe,QAAQ;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAyBvB,CAAC,eAEVvE,OAAA,CAACK,OAAO;UAAC+F,GAAG,EAAC,0BAAM;UAAAjC,QAAA,eACjBnE,OAAA,CAACxC,IAAI;YAACoG,KAAK,EAAC,sCAAQ;YAAAO,QAAA,eAClBnE,OAAA;cAAK8E,KAAK,EAAE;gBAAEC,OAAO,EAAE;cAAO,CAAE;cAAAZ,QAAA,eAC9BnE,OAAA,CAACpB,KAAK;gBAACoI,WAAW,EAAC;cAAY;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC,GALe,MAAM;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMrB,CAAC,eAEVvE,OAAA,CAACK,OAAO;UAAC+F,GAAG,EAAC,0BAAM;UAAAjC,QAAA,eACjBnE,OAAA,CAACxC,IAAI;YAACoG,KAAK,EAAC,sCAAQ;YAAAO,QAAA,eAClBnE,OAAA;cAAK8E,KAAK,EAAE;gBAAEC,OAAO,EAAE;cAAO,CAAE;cAAAZ,QAAA,eAC9BnE,OAAA,CAACpB,KAAK;gBAACoI,WAAW,EAAC;cAAY;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC,GALe,QAAQ;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPvE,OAAA,CAAC1B,KAAK;MACJsF,KAAK,EAAE7B,cAAc,GAAG,MAAM,GAAG,MAAO;MACxCkF,IAAI,EAAEpF,kBAAmB;MACzBqF,QAAQ,EAAEA,CAAA,KAAM;QACdpF,qBAAqB,CAAC,KAAK,CAAC;QAC5BE,iBAAiB,CAAC,IAAI,CAAC;QACvBC,UAAU,CAACkF,WAAW,CAAC,CAAC;MAC1B,CAAE;MACFC,MAAM,EAAE,IAAK;MACbrD,KAAK,EAAE,GAAI;MAAAI,QAAA,eAEXnE,OAAA,CAACzB,IAAI;QACH8I,IAAI,EAAEpF,UAAW;QACjBqF,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAGC,MAAM,IAAK;UACpBC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEF,MAAM,CAAC;UAC9B9I,OAAO,CAACiJ,OAAO,CAAC,MAAM,CAAC;UACvB7F,qBAAqB,CAAC,KAAK,CAAC;UAC5BG,UAAU,CAACkF,WAAW,CAAC,CAAC;QAC1B,CAAE;QAAAhD,QAAA,gBAEFnE,OAAA,CAACzB,IAAI,CAACqJ,IAAI;UACRnF,IAAI,EAAC,UAAU;UACfoF,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAErJ,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAyF,QAAA,eAEhDnE,OAAA,CAACxB,KAAK;YAACwJ,WAAW,EAAC;UAAS;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACZvE,OAAA,CAACzB,IAAI,CAACqJ,IAAI;UACRnF,IAAI,EAAC,cAAc;UACnBoF,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAErJ,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAyF,QAAA,eAEhDnE,OAAA,CAACvB,WAAW;YACVqG,KAAK,EAAE;cAAEf,KAAK,EAAE;YAAO,CAAE;YACzBiE,WAAW,EAAC,4CAAS;YACrBC,SAAS,EAAEvF,KAAK,IAAI,KAAKA,KAAK,EAAE,CAACwF,OAAO,CAAC,uBAAuB,EAAE,GAAG,CAAE;YACvEC,MAAM,EAAEzF,KAAK,IAAIA,KAAK,CAACwF,OAAO,CAAC,YAAY,EAAE,EAAE;UAAE;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eACZvE,OAAA,CAACzB,IAAI,CAACqJ,IAAI;UACRnF,IAAI,EAAC,aAAa;UAClBoF,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAErJ,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAyF,QAAA,eAEhDnE,OAAA,CAACxB,KAAK;YAACwJ,WAAW,EAAC;UAAS;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACZvE,OAAA,CAACzB,IAAI,CAACqJ,IAAI;UACRnF,IAAI,EAAC,QAAQ;UACboF,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAErJ,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAyF,QAAA,eAEhDnE,OAAA,CAACxB,KAAK;YAACwJ,WAAW,EAAC;UAAS;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACZvE,OAAA,CAACzB,IAAI,CAACqJ,IAAI;UAAAzD,QAAA,eACRnE,OAAA,CAACjC,KAAK;YAAAoG,QAAA,gBACJnE,OAAA,CAAClC,MAAM;cAAC6G,IAAI,EAAC,SAAS;cAACyD,QAAQ,EAAC,QAAQ;cAAAjE,QAAA,EACrCpC,cAAc,GAAG,IAAI,GAAG;YAAI;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACTvE,OAAA,CAAClC,MAAM;cAACwI,OAAO,EAAEA,CAAA,KAAM;gBACrBxE,qBAAqB,CAAC,KAAK,CAAC;gBAC5BE,iBAAiB,CAAC,IAAI,CAAC;gBACvBC,UAAU,CAACkF,WAAW,CAAC,CAAC;cAC1B,CAAE;cAAAhD,QAAA,EAAC;YAEH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC/D,EAAA,CAhXID,gBAAgB;EAAA,QAoBChC,IAAI,CAAC2D,OAAO;AAAA;AAAAmG,EAAA,GApB7B9H,gBAAgB;AAkXtB,eAAeA,gBAAgB;AAAC,IAAA8H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}