{"ast": null, "code": "// 为了解决 js 运算的精度问题\nexport function prettyNumber(n) {\n  return Math.abs(n) < 1e-14 ? n : parseFloat(n.toFixed(14));\n}", "map": {"version": 3, "names": ["prettyNumber", "n", "Math", "abs", "parseFloat", "toFixed"], "sources": ["utils/pretty-number.ts"], "sourcesContent": [null], "mappings": "AAAA;AACA,OAAM,SAAUA,YAAYA,CAACC,CAAS;EACpC,OAAOC,IAAI,CAACC,GAAG,CAACF,CAAC,CAAC,GAAG,KAAK,GAAGA,CAAC,GAAGG,UAAU,CAACH,CAAC,CAACI,OAAO,CAAC,EAAE,CAAC,CAAC;AAC5D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}