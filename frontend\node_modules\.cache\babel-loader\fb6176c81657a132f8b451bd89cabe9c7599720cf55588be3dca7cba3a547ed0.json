{"ast": null, "code": "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nfunction getCollapsible(collapsible) {\n  if (collapsible && typeof collapsible === 'object') {\n    return Object.assign(Object.assign({}, collapsible), {\n      showCollapsibleIcon: collapsible.showCollapsibleIcon === undefined ? 'auto' : collapsible.showCollapsibleIcon\n    });\n  }\n  const mergedCollapsible = !!collapsible;\n  return {\n    start: mergedCollapsible,\n    end: mergedCollapsible,\n    showCollapsibleIcon: 'auto'\n  };\n}\n/**\n * Convert `children` into `items`.\n */\nfunction useItems(children) {\n  const items = React.useMemo(() => toArray(children).filter(item => /*#__PURE__*/React.isValidElement(item)).map(node => {\n    const {\n      props\n    } = node;\n    const {\n        collapsible\n      } = props,\n      restProps = __rest(props, [\"collapsible\"]);\n    return Object.assign(Object.assign({}, restProps), {\n      collapsible: getCollapsible(collapsible)\n    });\n  }), [children]);\n  return items;\n}\nexport default useItems;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "toArray", "getCollapsible", "collapsible", "assign", "showCollapsibleIcon", "undefined", "mergedCollapsible", "start", "end", "useItems", "children", "items", "useMemo", "filter", "item", "isValidElement", "map", "node", "props", "restProps"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/antd/es/splitter/hooks/useItems.js"], "sourcesContent": ["var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nfunction getCollapsible(collapsible) {\n  if (collapsible && typeof collapsible === 'object') {\n    return Object.assign(Object.assign({}, collapsible), {\n      showCollapsibleIcon: collapsible.showCollapsibleIcon === undefined ? 'auto' : collapsible.showCollapsibleIcon\n    });\n  }\n  const mergedCollapsible = !!collapsible;\n  return {\n    start: mergedCollapsible,\n    end: mergedCollapsible,\n    showCollapsibleIcon: 'auto'\n  };\n}\n/**\n * Convert `children` into `items`.\n */\nfunction useItems(children) {\n  const items = React.useMemo(() => toArray(children).filter(item => /*#__PURE__*/React.isValidElement(item)).map(node => {\n    const {\n      props\n    } = node;\n    const {\n        collapsible\n      } = props,\n      restProps = __rest(props, [\"collapsible\"]);\n    return Object.assign(Object.assign({}, restProps), {\n      collapsible: getCollapsible(collapsible)\n    });\n  }), [children]);\n  return items;\n}\nexport default useItems;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,6BAA6B;AACjD,SAASC,cAAcA,CAACC,WAAW,EAAE;EACnC,IAAIA,WAAW,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;IAClD,OAAOZ,MAAM,CAACa,MAAM,CAACb,MAAM,CAACa,MAAM,CAAC,CAAC,CAAC,EAAED,WAAW,CAAC,EAAE;MACnDE,mBAAmB,EAAEF,WAAW,CAACE,mBAAmB,KAAKC,SAAS,GAAG,MAAM,GAAGH,WAAW,CAACE;IAC5F,CAAC,CAAC;EACJ;EACA,MAAME,iBAAiB,GAAG,CAAC,CAACJ,WAAW;EACvC,OAAO;IACLK,KAAK,EAAED,iBAAiB;IACxBE,GAAG,EAAEF,iBAAiB;IACtBF,mBAAmB,EAAE;EACvB,CAAC;AACH;AACA;AACA;AACA;AACA,SAASK,QAAQA,CAACC,QAAQ,EAAE;EAC1B,MAAMC,KAAK,GAAGZ,KAAK,CAACa,OAAO,CAAC,MAAMZ,OAAO,CAACU,QAAQ,CAAC,CAACG,MAAM,CAACC,IAAI,IAAI,aAAaf,KAAK,CAACgB,cAAc,CAACD,IAAI,CAAC,CAAC,CAACE,GAAG,CAACC,IAAI,IAAI;IACtH,MAAM;MACJC;IACF,CAAC,GAAGD,IAAI;IACR,MAAM;QACFf;MACF,CAAC,GAAGgB,KAAK;MACTC,SAAS,GAAGlC,MAAM,CAACiC,KAAK,EAAE,CAAC,aAAa,CAAC,CAAC;IAC5C,OAAO5B,MAAM,CAACa,MAAM,CAACb,MAAM,CAACa,MAAM,CAAC,CAAC,CAAC,EAAEgB,SAAS,CAAC,EAAE;MACjDjB,WAAW,EAAED,cAAc,CAACC,WAAW;IACzC,CAAC,CAAC;EACJ,CAAC,CAAC,EAAE,CAACQ,QAAQ,CAAC,CAAC;EACf,OAAOC,KAAK;AACd;AACA,eAAeF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}