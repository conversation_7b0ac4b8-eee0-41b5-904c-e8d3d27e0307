{"ast": null, "code": "import MutateObserver from \"./MutateObserver\";\nimport useMutateObserver from \"./useMutateObserver\";\nexport { useMutateObserver };\nexport default MutateObserver;", "map": {"version": 3, "names": ["MutateObserver", "useMutateObserver"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/@rc-component/mutate-observer/es/index.js"], "sourcesContent": ["import MutateObserver from \"./MutateObserver\";\nimport useMutateObserver from \"./useMutateObserver\";\nexport { useMutateObserver };\nexport default MutateObserver;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASA,iBAAiB;AAC1B,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}