{"ast": null, "code": "function forEachRight(arr, callback) {\n  for (let i = arr.length - 1; i >= 0; i--) {\n    const element = arr[i];\n    callback(element, i, arr);\n  }\n}\nexport { forEachRight };", "map": {"version": 3, "names": ["forEachRight", "arr", "callback", "i", "length", "element"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/es-toolkit/dist/array/forEachRight.mjs"], "sourcesContent": ["function forEachRight(arr, callback) {\n    for (let i = arr.length - 1; i >= 0; i--) {\n        const element = arr[i];\n        callback(element, i, arr);\n    }\n}\n\nexport { forEachRight };\n"], "mappings": "AAAA,SAASA,YAAYA,CAACC,GAAG,EAAEC,QAAQ,EAAE;EACjC,KAAK,IAAIC,CAAC,GAAGF,GAAG,CAACG,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACtC,MAAME,OAAO,GAAGJ,GAAG,CAACE,CAAC,CAAC;IACtBD,QAAQ,CAACG,OAAO,EAAEF,CAAC,EAAEF,GAAG,CAAC;EAC7B;AACJ;AAEA,SAASD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}