{"ast": null, "code": "\"use client\";\n\nimport React from 'react';\nimport HolderOutlined from \"@ant-design/icons/es/icons/HolderOutlined\";\nimport classNames from 'classnames';\nimport RcTree from 'rc-tree';\nimport initCollapseMotion from '../_util/motion';\nimport { ConfigContext } from '../config-provider';\nimport { useToken } from '../theme/internal';\nimport useStyle from './style';\nimport dropIndicatorRender from './utils/dropIndicator';\nimport SwitcherIconCom from './utils/iconUtil';\nconst Tree = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _a;\n  const {\n    getPrefixCls,\n    direction,\n    virtual,\n    tree\n  } = React.useContext(ConfigContext);\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    showIcon = false,\n    showLine,\n    switcherIcon,\n    switcherLoadingIcon,\n    blockNode = false,\n    children,\n    checkable = false,\n    selectable = true,\n    draggable,\n    motion: customMotion,\n    style\n  } = props;\n  const prefixCls = getPrefixCls('tree', customizePrefixCls);\n  const rootPrefixCls = getPrefixCls();\n  const motion = customMotion !== null && customMotion !== void 0 ? customMotion : Object.assign(Object.assign({}, initCollapseMotion(rootPrefixCls)), {\n    motionAppear: false\n  });\n  const newProps = Object.assign(Object.assign({}, props), {\n    checkable,\n    selectable,\n    showIcon,\n    motion,\n    blockNode,\n    showLine: Boolean(showLine),\n    dropIndicatorRender\n  });\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const [, token] = useToken();\n  const itemHeight = token.paddingXS / 2 + (((_a = token.Tree) === null || _a === void 0 ? void 0 : _a.titleHeight) || token.controlHeightSM);\n  const draggableConfig = React.useMemo(() => {\n    if (!draggable) {\n      return false;\n    }\n    let mergedDraggable = {};\n    switch (typeof draggable) {\n      case 'function':\n        mergedDraggable.nodeDraggable = draggable;\n        break;\n      case 'object':\n        mergedDraggable = Object.assign({}, draggable);\n        break;\n      default:\n        break;\n      // Do nothing\n    }\n    if (mergedDraggable.icon !== false) {\n      mergedDraggable.icon = mergedDraggable.icon || /*#__PURE__*/React.createElement(HolderOutlined, null);\n    }\n    return mergedDraggable;\n  }, [draggable]);\n  const renderSwitcherIcon = nodeProps => (/*#__PURE__*/React.createElement(SwitcherIconCom, {\n    prefixCls: prefixCls,\n    switcherIcon: switcherIcon,\n    switcherLoadingIcon: switcherLoadingIcon,\n    treeNodeProps: nodeProps,\n    showLine: showLine\n  }));\n  return wrapCSSVar(/*#__PURE__*/\n  // @ts-ignore\n  React.createElement(RcTree, Object.assign({\n    itemHeight: itemHeight,\n    ref: ref,\n    virtual: virtual\n  }, newProps, {\n    // newProps may contain style so declare style below it\n    style: Object.assign(Object.assign({}, tree === null || tree === void 0 ? void 0 : tree.style), style),\n    prefixCls: prefixCls,\n    className: classNames({\n      [`${prefixCls}-icon-hide`]: !showIcon,\n      [`${prefixCls}-block-node`]: blockNode,\n      [`${prefixCls}-unselectable`]: !selectable,\n      [`${prefixCls}-rtl`]: direction === 'rtl'\n    }, tree === null || tree === void 0 ? void 0 : tree.className, className, hashId, cssVarCls),\n    direction: direction,\n    checkable: checkable ? /*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-checkbox-inner`\n    }) : checkable,\n    selectable: selectable,\n    switcherIcon: renderSwitcherIcon,\n    draggable: draggableConfig\n  }), children));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Tree.displayName = 'Tree';\n}\nexport default Tree;", "map": {"version": 3, "names": ["React", "Holder<PERSON><PERSON><PERSON>", "classNames", "<PERSON><PERSON><PERSON><PERSON>", "initCollapseMotion", "ConfigContext", "useToken", "useStyle", "dropIndicatorRender", "SwitcherIconCom", "Tree", "forwardRef", "props", "ref", "_a", "getPrefixCls", "direction", "virtual", "tree", "useContext", "prefixCls", "customizePrefixCls", "className", "showIcon", "showLine", "switcherIcon", "switcherLoadingIcon", "blockNode", "children", "checkable", "selectable", "draggable", "motion", "customMotion", "style", "rootPrefixCls", "Object", "assign", "motionAppear", "newProps", "Boolean", "wrapCSSVar", "hashId", "cssVarCls", "token", "itemHeight", "paddingXS", "titleHeight", "controlHeightSM", "draggableConfig", "useMemo", "mergedDraggable", "nodeDraggable", "icon", "createElement", "renderSwitcherIcon", "nodeProps", "treeNodeProps", "process", "env", "NODE_ENV", "displayName"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/antd/es/tree/Tree.js"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport HolderOutlined from \"@ant-design/icons/es/icons/HolderOutlined\";\nimport classNames from 'classnames';\nimport RcTree from 'rc-tree';\nimport initCollapseMotion from '../_util/motion';\nimport { ConfigContext } from '../config-provider';\nimport { useToken } from '../theme/internal';\nimport useStyle from './style';\nimport dropIndicatorRender from './utils/dropIndicator';\nimport SwitcherIconCom from './utils/iconUtil';\nconst Tree = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _a;\n  const {\n    getPrefixCls,\n    direction,\n    virtual,\n    tree\n  } = React.useContext(ConfigContext);\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    showIcon = false,\n    showLine,\n    switcherIcon,\n    switcherLoadingIcon,\n    blockNode = false,\n    children,\n    checkable = false,\n    selectable = true,\n    draggable,\n    motion: customMotion,\n    style\n  } = props;\n  const prefixCls = getPrefixCls('tree', customizePrefixCls);\n  const rootPrefixCls = getPrefixCls();\n  const motion = customMotion !== null && customMotion !== void 0 ? customMotion : Object.assign(Object.assign({}, initCollapseMotion(rootPrefixCls)), {\n    motionAppear: false\n  });\n  const newProps = Object.assign(Object.assign({}, props), {\n    checkable,\n    selectable,\n    showIcon,\n    motion,\n    blockNode,\n    showLine: Boolean(showLine),\n    dropIndicatorRender\n  });\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const [, token] = useToken();\n  const itemHeight = token.paddingXS / 2 + (((_a = token.Tree) === null || _a === void 0 ? void 0 : _a.titleHeight) || token.controlHeightSM);\n  const draggableConfig = React.useMemo(() => {\n    if (!draggable) {\n      return false;\n    }\n    let mergedDraggable = {};\n    switch (typeof draggable) {\n      case 'function':\n        mergedDraggable.nodeDraggable = draggable;\n        break;\n      case 'object':\n        mergedDraggable = Object.assign({}, draggable);\n        break;\n      default:\n        break;\n      // Do nothing\n    }\n    if (mergedDraggable.icon !== false) {\n      mergedDraggable.icon = mergedDraggable.icon || /*#__PURE__*/React.createElement(HolderOutlined, null);\n    }\n    return mergedDraggable;\n  }, [draggable]);\n  const renderSwitcherIcon = nodeProps => (/*#__PURE__*/React.createElement(SwitcherIconCom, {\n    prefixCls: prefixCls,\n    switcherIcon: switcherIcon,\n    switcherLoadingIcon: switcherLoadingIcon,\n    treeNodeProps: nodeProps,\n    showLine: showLine\n  }));\n  return wrapCSSVar(\n  /*#__PURE__*/\n  // @ts-ignore\n  React.createElement(RcTree, Object.assign({\n    itemHeight: itemHeight,\n    ref: ref,\n    virtual: virtual\n  }, newProps, {\n    // newProps may contain style so declare style below it\n    style: Object.assign(Object.assign({}, tree === null || tree === void 0 ? void 0 : tree.style), style),\n    prefixCls: prefixCls,\n    className: classNames({\n      [`${prefixCls}-icon-hide`]: !showIcon,\n      [`${prefixCls}-block-node`]: blockNode,\n      [`${prefixCls}-unselectable`]: !selectable,\n      [`${prefixCls}-rtl`]: direction === 'rtl'\n    }, tree === null || tree === void 0 ? void 0 : tree.className, className, hashId, cssVarCls),\n    direction: direction,\n    checkable: checkable ? /*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-checkbox-inner`\n    }) : checkable,\n    selectable: selectable,\n    switcherIcon: renderSwitcherIcon,\n    draggable: draggableConfig\n  }), children));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Tree.displayName = 'Tree';\n}\nexport default Tree;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,cAAc,MAAM,2CAA2C;AACtE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,MAAM,MAAM,SAAS;AAC5B,OAAOC,kBAAkB,MAAM,iBAAiB;AAChD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,OAAOC,QAAQ,MAAM,SAAS;AAC9B,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,OAAOC,eAAe,MAAM,kBAAkB;AAC9C,MAAMC,IAAI,GAAG,aAAaV,KAAK,CAACW,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EACzD,IAAIC,EAAE;EACN,MAAM;IACJC,YAAY;IACZC,SAAS;IACTC,OAAO;IACPC;EACF,CAAC,GAAGlB,KAAK,CAACmB,UAAU,CAACd,aAAa,CAAC;EACnC,MAAM;IACJe,SAAS,EAAEC,kBAAkB;IAC7BC,SAAS;IACTC,QAAQ,GAAG,KAAK;IAChBC,QAAQ;IACRC,YAAY;IACZC,mBAAmB;IACnBC,SAAS,GAAG,KAAK;IACjBC,QAAQ;IACRC,SAAS,GAAG,KAAK;IACjBC,UAAU,GAAG,IAAI;IACjBC,SAAS;IACTC,MAAM,EAAEC,YAAY;IACpBC;EACF,CAAC,GAAGtB,KAAK;EACT,MAAMQ,SAAS,GAAGL,YAAY,CAAC,MAAM,EAAEM,kBAAkB,CAAC;EAC1D,MAAMc,aAAa,GAAGpB,YAAY,CAAC,CAAC;EACpC,MAAMiB,MAAM,GAAGC,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAGG,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEjC,kBAAkB,CAAC+B,aAAa,CAAC,CAAC,EAAE;IACnJG,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,MAAMC,QAAQ,GAAGH,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEzB,KAAK,CAAC,EAAE;IACvDiB,SAAS;IACTC,UAAU;IACVP,QAAQ;IACRS,MAAM;IACNL,SAAS;IACTH,QAAQ,EAAEgB,OAAO,CAAChB,QAAQ,CAAC;IAC3BhB;EACF,CAAC,CAAC;EACF,MAAM,CAACiC,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGpC,QAAQ,CAACa,SAAS,CAAC;EAC3D,MAAM,GAAGwB,KAAK,CAAC,GAAGtC,QAAQ,CAAC,CAAC;EAC5B,MAAMuC,UAAU,GAAGD,KAAK,CAACE,SAAS,GAAG,CAAC,IAAI,CAAC,CAAChC,EAAE,GAAG8B,KAAK,CAAClC,IAAI,MAAM,IAAI,IAAII,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiC,WAAW,KAAKH,KAAK,CAACI,eAAe,CAAC;EAC3I,MAAMC,eAAe,GAAGjD,KAAK,CAACkD,OAAO,CAAC,MAAM;IAC1C,IAAI,CAACnB,SAAS,EAAE;MACd,OAAO,KAAK;IACd;IACA,IAAIoB,eAAe,GAAG,CAAC,CAAC;IACxB,QAAQ,OAAOpB,SAAS;MACtB,KAAK,UAAU;QACboB,eAAe,CAACC,aAAa,GAAGrB,SAAS;QACzC;MACF,KAAK,QAAQ;QACXoB,eAAe,GAAGf,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEN,SAAS,CAAC;QAC9C;MACF;QACE;MACF;IACF;IACA,IAAIoB,eAAe,CAACE,IAAI,KAAK,KAAK,EAAE;MAClCF,eAAe,CAACE,IAAI,GAAGF,eAAe,CAACE,IAAI,IAAI,aAAarD,KAAK,CAACsD,aAAa,CAACrD,cAAc,EAAE,IAAI,CAAC;IACvG;IACA,OAAOkD,eAAe;EACxB,CAAC,EAAE,CAACpB,SAAS,CAAC,CAAC;EACf,MAAMwB,kBAAkB,GAAGC,SAAS,KAAK,aAAaxD,KAAK,CAACsD,aAAa,CAAC7C,eAAe,EAAE;IACzFW,SAAS,EAAEA,SAAS;IACpBK,YAAY,EAAEA,YAAY;IAC1BC,mBAAmB,EAAEA,mBAAmB;IACxC+B,aAAa,EAAED,SAAS;IACxBhC,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;EACH,OAAOiB,UAAU,CACjB;EACA;EACAzC,KAAK,CAACsD,aAAa,CAACnD,MAAM,EAAEiC,MAAM,CAACC,MAAM,CAAC;IACxCQ,UAAU,EAAEA,UAAU;IACtBhC,GAAG,EAAEA,GAAG;IACRI,OAAO,EAAEA;EACX,CAAC,EAAEsB,QAAQ,EAAE;IACX;IACAL,KAAK,EAAEE,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEnB,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACgB,KAAK,CAAC,EAAEA,KAAK,CAAC;IACtGd,SAAS,EAAEA,SAAS;IACpBE,SAAS,EAAEpB,UAAU,CAAC;MACpB,CAAC,GAAGkB,SAAS,YAAY,GAAG,CAACG,QAAQ;MACrC,CAAC,GAAGH,SAAS,aAAa,GAAGO,SAAS;MACtC,CAAC,GAAGP,SAAS,eAAe,GAAG,CAACU,UAAU;MAC1C,CAAC,GAAGV,SAAS,MAAM,GAAGJ,SAAS,KAAK;IACtC,CAAC,EAAEE,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACI,SAAS,EAAEA,SAAS,EAAEoB,MAAM,EAAEC,SAAS,CAAC;IAC5F3B,SAAS,EAAEA,SAAS;IACpBa,SAAS,EAAEA,SAAS,GAAG,aAAa7B,KAAK,CAACsD,aAAa,CAAC,MAAM,EAAE;MAC9DhC,SAAS,EAAE,GAAGF,SAAS;IACzB,CAAC,CAAC,GAAGS,SAAS;IACdC,UAAU,EAAEA,UAAU;IACtBL,YAAY,EAAE8B,kBAAkB;IAChCxB,SAAS,EAAEkB;EACb,CAAC,CAAC,EAAErB,QAAQ,CAAC,CAAC;AAChB,CAAC,CAAC;AACF,IAAI8B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzClD,IAAI,CAACmD,WAAW,GAAG,MAAM;AAC3B;AACA,eAAenD,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}