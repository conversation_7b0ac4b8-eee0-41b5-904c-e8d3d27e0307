package com.yinma.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yinma.entity.ProjectDelivery;
import com.yinma.dto.ProjectDeliveryDTO;
import com.yinma.vo.ProjectDeliveryVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 项目交付管理服务接口
 * 银马实业项目交付管理系统
 * 
 * <AUTHOR>
 * @since 2024-05-20
 */
public interface ProjectDeliveryService extends IService<ProjectDelivery> {

    /**
     * 分页查询项目交付
     * 
     * @param page 分页参数
     * @param projectCode 项目编号
     * @param projectName 项目名称
     * @param customerName 客户名称
     * @param deliveryStatus 交付状态
     * @param projectManager 项目经理
     * @param planDeliveryDateStart 计划交付日期开始
     * @param planDeliveryDateEnd 计划交付日期结束
     * @return 分页结果
     */
    IPage<ProjectDeliveryVO> getProjectDeliveryPage(Page<ProjectDelivery> page, String projectCode, 
                                                   String projectName, String customerName, 
                                                   String deliveryStatus, String projectManager,
                                                   String planDeliveryDateStart, String planDeliveryDateEnd);

    /**
     * 根据ID查询项目交付详情
     * 
     * @param id 项目交付ID
     * @return 项目交付详情
     */
    ProjectDeliveryVO getProjectDeliveryDetailById(Long id);

    /**
     * 创建项目交付
     * 
     * @param projectDeliveryDTO 项目交付信息
     * @return 创建的项目交付
     */
    ProjectDelivery createProjectDelivery(ProjectDeliveryDTO projectDeliveryDTO);

    /**
     * 更新项目交付
     * 
     * @param id 项目交付ID
     * @param projectDeliveryDTO 项目交付信息
     * @return 更新后的项目交付
     */
    ProjectDelivery updateProjectDelivery(Long id, ProjectDeliveryDTO projectDeliveryDTO);

    /**
     * 删除项目交付
     * 
     * @param id 项目交付ID
     * @return 删除结果
     */
    boolean deleteProjectDelivery(Long id);

    /**
     * 批量删除项目交付
     * 
     * @param ids 项目交付ID列表
     * @return 删除结果
     */
    boolean batchDeleteProjectDelivery(List<Long> ids);

    /**
     * 更新项目交付状态
     * 
     * @param id 项目交付ID
     * @param status 新状态
     * @param remark 备注
     * @return 更新后的项目交付
     */
    ProjectDelivery updateProjectDeliveryStatus(Long id, String status, String remark);

    /**
     * 获取项目交付物清单
     * 
     * @param id 项目交付ID
     * @return 交付物清单
     */
    List<Map<String, Object>> getProjectDeliverables(Long id);

    /**
     * 更新项目交付物清单
     * 
     * @param id 项目交付ID
     * @param deliverablesList 交付物清单
     */
    void updateProjectDeliverables(Long id, List<Map<String, Object>> deliverablesList);

    /**
     * 获取项目里程碑
     * 
     * @param id 项目交付ID
     * @return 里程碑列表
     */
    List<Map<String, Object>> getProjectMilestones(Long id);

    /**
     * 更新项目里程碑
     * 
     * @param id 项目交付ID
     * @param milestonesList 里程碑列表
     */
    void updateProjectMilestones(Long id, List<Map<String, Object>> milestonesList);

    /**
     * 获取项目进度
     * 
     * @param id 项目交付ID
     * @return 项目进度
     */
    Map<String, Object> getProjectProgress(Long id);

    /**
     * 更新项目进度
     * 
     * @param id 项目交付ID
     * @param progressInfo 进度信息
     */
    void updateProjectProgress(Long id, Map<String, Object> progressInfo);

    /**
     * 获取项目团队
     * 
     * @param id 项目交付ID
     * @return 项目团队
     */
    List<Map<String, Object>> getProjectTeam(Long id);

    /**
     * 更新项目团队
     * 
     * @param id 项目交付ID
     * @param teamList 团队成员列表
     */
    void updateProjectTeam(Long id, List<Map<String, Object>> teamList);

    /**
     * 获取项目风险
     * 
     * @param id 项目交付ID
     * @return 项目风险列表
     */
    List<Map<String, Object>> getProjectRisks(Long id);

    /**
     * 添加项目风险
     * 
     * @param id 项目交付ID
     * @param riskInfo 风险信息
     */
    void addProjectRisk(Long id, Map<String, Object> riskInfo);

    /**
     * 更新项目风险
     * 
     * @param id 项目交付ID
     * @param riskId 风险ID
     * @param riskInfo 风险信息
     */
    void updateProjectRisk(Long id, Long riskId, Map<String, Object> riskInfo);

    /**
     * 删除项目风险
     * 
     * @param id 项目交付ID
     * @param riskId 风险ID
     * @return 删除结果
     */
    boolean deleteProjectRisk(Long id, Long riskId);

    /**
     * 获取项目问题
     * 
     * @param id 项目交付ID
     * @return 项目问题列表
     */
    List<Map<String, Object>> getProjectIssues(Long id);

    /**
     * 添加项目问题
     * 
     * @param id 项目交付ID
     * @param issueInfo 问题信息
     */
    void addProjectIssue(Long id, Map<String, Object> issueInfo);

    /**
     * 更新项目问题
     * 
     * @param id 项目交付ID
     * @param issueId 问题ID
     * @param issueInfo 问题信息
     */
    void updateProjectIssue(Long id, Long issueId, Map<String, Object> issueInfo);

    /**
     * 删除项目问题
     * 
     * @param id 项目交付ID
     * @param issueId 问题ID
     * @return 删除结果
     */
    boolean deleteProjectIssue(Long id, Long issueId);

    /**
     * 获取项目预算
     * 
     * @param id 项目交付ID
     * @return 项目预算
     */
    Map<String, Object> getProjectBudget(Long id);

    /**
     * 更新项目预算
     * 
     * @param id 项目交付ID
     * @param budgetInfo 预算信息
     */
    void updateProjectBudget(Long id, Map<String, Object> budgetInfo);

    /**
     * 获取项目成本
     * 
     * @param id 项目交付ID
     * @return 项目成本列表
     */
    List<Map<String, Object>> getProjectCosts(Long id);

    /**
     * 记录项目成本
     * 
     * @param id 项目交付ID
     * @param costRecord 成本记录
     */
    void recordProjectCost(Long id, Map<String, Object> costRecord);

    /**
     * 获取项目质量检查
     * 
     * @param id 项目交付ID
     * @return 质量检查列表
     */
    List<Map<String, Object>> getProjectQuality(Long id);

    /**
     * 添加质量检查记录
     * 
     * @param id 项目交付ID
     * @param qualityRecord 质量检查记录
     */
    void addProjectQuality(Long id, Map<String, Object> qualityRecord);

    /**
     * 获取项目验收信息
     * 
     * @param id 项目交付ID
     * @return 项目验收信息
     */
    Map<String, Object> getProjectAcceptance(Long id);

    /**
     * 更新项目验收信息
     * 
     * @param id 项目交付ID
     * @param acceptanceInfo 验收信息
     */
    void updateProjectAcceptance(Long id, Map<String, Object> acceptanceInfo);

    /**
     * 提交项目验收
     * 
     * @param id 项目交付ID
     * @param submitInfo 验收提交信息
     */
    void submitProjectAcceptance(Long id, Map<String, Object> submitInfo);

    /**
     * 审批项目验收
     * 
     * @param id 项目交付ID
     * @param approveInfo 审批信息
     */
    void approveProjectAcceptance(Long id, Map<String, Object> approveInfo);

    /**
     * 拒绝项目验收
     * 
     * @param id 项目交付ID
     * @param reason 拒绝原因
     */
    void rejectProjectAcceptance(Long id, String reason);

    /**
     * 上传项目文档
     * 
     * @param id 项目交付ID
     * @param file 文档文件
     * @param docType 文档类型
     * @param description 文档描述
     * @return 文档URL
     */
    String uploadProjectDocument(Long id, MultipartFile file, String docType, String description);

    /**
     * 获取项目文档列表
     * 
     * @param id 项目交付ID
     * @param docType 文档类型
     * @return 文档列表
     */
    List<Map<String, Object>> getProjectDocuments(Long id, String docType);

    /**
     * 删除项目文档
     * 
     * @param id 项目交付ID
     * @param docId 文档ID
     * @return 删除结果
     */
    boolean deleteProjectDocument(Long id, Long docId);

    /**
     * 获取项目沟通记录
     * 
     * @param id 项目交付ID
     * @return 沟通记录列表
     */
    List<Map<String, Object>> getProjectCommunications(Long id);

    /**
     * 添加沟通记录
     * 
     * @param id 项目交付ID
     * @param communicationRecord 沟通记录
     */
    void addProjectCommunication(Long id, Map<String, Object> communicationRecord);

    /**
     * 获取项目时间线
     * 
     * @param id 项目交付ID
     * @return 时间线列表
     */
    List<Map<String, Object>> getProjectTimeline(Long id);

    /**
     * 添加时间线事件
     * 
     * @param id 项目交付ID
     * @param timelineEvent 时间线事件
     */
    void addProjectTimelineEvent(Long id, Map<String, Object> timelineEvent);

    /**
     * 获取项目报告
     * 
     * @param id 项目交付ID
     * @param reportType 报告类型
     * @return 报告列表
     */
    List<Map<String, Object>> getProjectReports(Long id, String reportType);

    /**
     * 生成项目报告
     * 
     * @param id 项目交付ID
     * @param reportType 报告类型
     * @param reportParams 报告参数
     * @return 报告URL
     */
    String generateProjectReport(Long id, String reportType, Map<String, Object> reportParams);

    /**
     * 导出项目交付数据
     * 
     * @param projectCode 项目编号
     * @param customerName 客户名称
     * @param deliveryStatus 交付状态
     * @param format 导出格式
     * @return 导出文件URL
     */
    String exportProjectDelivery(String projectCode, String customerName, String deliveryStatus, String format);

    /**
     * 导入项目交付数据
     * 
     * @param file 导入文件
     * @return 导入结果
     */
    Map<String, Object> importProjectDelivery(MultipartFile file);

    /**
     * 获取项目交付统计数据
     * 
     * @param timeRange 时间范围
     * @return 统计数据
     */
    Map<String, Object> getProjectDeliveryStatistics(String timeRange);

    /**
     * 获取项目交付看板数据
     * 
     * @param timeRange 时间范围
     * @return 看板数据
     */
    Map<String, Object> getProjectDeliveryDashboard(String timeRange);

    /**
     * 获取项目交付选项列表
     * 
     * @return 选项列表
     */
    Map<String, List<Map<String, Object>>> getProjectDeliveryOptions();

    /**
     * 验证项目编号唯一性
     * 
     * @param projectCode 项目编号
     * @param excludeId 排除的ID
     * @return 是否唯一
     */
    boolean validateProjectCode(String projectCode, Long excludeId);

    /**
     * 启动项目
     * 
     * @param id 项目交付ID
     * @param startInfo 启动信息
     */
    void startProject(Long id, Map<String, Object> startInfo);

    /**
     * 暂停项目
     * 
     * @param id 项目交付ID
     * @param reason 暂停原因
     */
    void pauseProject(Long id, String reason);

    /**
     * 恢复项目
     * 
     * @param id 项目交付ID
     * @param resumeInfo 恢复信息
     */
    void resumeProject(Long id, Map<String, Object> resumeInfo);

    /**
     * 完成项目
     * 
     * @param id 项目交付ID
     * @param completeInfo 完成信息
     */
    void completeProject(Long id, Map<String, Object> completeInfo);

    /**
     * 取消项目
     * 
     * @param id 项目交付ID
     * @param reason 取消原因
     */
    void cancelProject(Long id, String reason);

    /**
     * 归档项目
     * 
     * @param id 项目交付ID
     * @param archiveInfo 归档信息
     */
    void archiveProject(Long id, Map<String, Object> archiveInfo);

    /**
     * 获取项目绩效分析
     * 
     * @param id 项目交付ID
     * @return 绩效分析
     */
    Map<String, Object> getProjectPerformance(Long id);

    /**
     * 获取项目经验教训
     * 
     * @param id 项目交付ID
     * @return 经验教训列表
     */
    List<Map<String, Object>> getProjectLessons(Long id);

    /**
     * 添加项目经验教训
     * 
     * @param id 项目交付ID
     * @param lessonInfo 经验教训
     */
    void addProjectLesson(Long id, Map<String, Object> lessonInfo);

    /**
     * 获取客户满意度
     * 
     * @param id 项目交付ID
     * @return 客户满意度
     */
    Map<String, Object> getCustomerSatisfaction(Long id);

    /**
     * 记录客户满意度
     * 
     * @param id 项目交付ID
     * @param satisfactionInfo 满意度信息
     */
    void recordCustomerSatisfaction(Long id, Map<String, Object> satisfactionInfo);
}