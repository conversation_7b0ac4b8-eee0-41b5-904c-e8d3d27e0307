{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst isUnsafeProperty = require('../../_internal/isUnsafeProperty.js');\nconst isDeepKey = require('../_internal/isDeepKey.js');\nconst toKey = require('../_internal/toKey.js');\nconst toPath = require('../util/toPath.js');\nfunction get(object, path, defaultValue) {\n  if (object == null) {\n    return defaultValue;\n  }\n  switch (typeof path) {\n    case 'string':\n      {\n        if (isUnsafeProperty.isUnsafeProperty(path)) {\n          return defaultValue;\n        }\n        const result = object[path];\n        if (result === undefined) {\n          if (isDeepKey.isDeepKey(path)) {\n            return get(object, toPath.toPath(path), defaultValue);\n          } else {\n            return defaultValue;\n          }\n        }\n        return result;\n      }\n    case 'number':\n    case 'symbol':\n      {\n        if (typeof path === 'number') {\n          path = toKey.toKey(path);\n        }\n        const result = object[path];\n        if (result === undefined) {\n          return defaultValue;\n        }\n        return result;\n      }\n    default:\n      {\n        if (Array.isArray(path)) {\n          return getWithPath(object, path, defaultValue);\n        }\n        if (Object.is(path?.valueOf(), -0)) {\n          path = '-0';\n        } else {\n          path = String(path);\n        }\n        if (isUnsafeProperty.isUnsafeProperty(path)) {\n          return defaultValue;\n        }\n        const result = object[path];\n        if (result === undefined) {\n          return defaultValue;\n        }\n        return result;\n      }\n  }\n}\nfunction getWithPath(object, path, defaultValue) {\n  if (path.length === 0) {\n    return defaultValue;\n  }\n  let current = object;\n  for (let index = 0; index < path.length; index++) {\n    if (current == null) {\n      return defaultValue;\n    }\n    if (isUnsafeProperty.isUnsafeProperty(path[index])) {\n      return defaultValue;\n    }\n    current = current[path[index]];\n  }\n  if (current === undefined) {\n    return defaultValue;\n  }\n  return current;\n}\nexports.get = get;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "isUnsafeProperty", "require", "<PERSON><PERSON><PERSON><PERSON>ey", "to<PERSON><PERSON>", "to<PERSON><PERSON>", "get", "object", "path", "defaultValue", "result", "undefined", "Array", "isArray", "getWithPath", "is", "valueOf", "String", "length", "current", "index"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/es-toolkit/dist/compat/object/get.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst isUnsafeProperty = require('../../_internal/isUnsafeProperty.js');\nconst isDeepKey = require('../_internal/isDeepKey.js');\nconst toKey = require('../_internal/toKey.js');\nconst toPath = require('../util/toPath.js');\n\nfunction get(object, path, defaultValue) {\n    if (object == null) {\n        return defaultValue;\n    }\n    switch (typeof path) {\n        case 'string': {\n            if (isUnsafeProperty.isUnsafeProperty(path)) {\n                return defaultValue;\n            }\n            const result = object[path];\n            if (result === undefined) {\n                if (isDeepKey.isDeepKey(path)) {\n                    return get(object, toPath.toPath(path), defaultValue);\n                }\n                else {\n                    return defaultValue;\n                }\n            }\n            return result;\n        }\n        case 'number':\n        case 'symbol': {\n            if (typeof path === 'number') {\n                path = toKey.toKey(path);\n            }\n            const result = object[path];\n            if (result === undefined) {\n                return defaultValue;\n            }\n            return result;\n        }\n        default: {\n            if (Array.isArray(path)) {\n                return getWithPath(object, path, defaultValue);\n            }\n            if (Object.is(path?.valueOf(), -0)) {\n                path = '-0';\n            }\n            else {\n                path = String(path);\n            }\n            if (isUnsafeProperty.isUnsafeProperty(path)) {\n                return defaultValue;\n            }\n            const result = object[path];\n            if (result === undefined) {\n                return defaultValue;\n            }\n            return result;\n        }\n    }\n}\nfunction getWithPath(object, path, defaultValue) {\n    if (path.length === 0) {\n        return defaultValue;\n    }\n    let current = object;\n    for (let index = 0; index < path.length; index++) {\n        if (current == null) {\n            return defaultValue;\n        }\n        if (isUnsafeProperty.isUnsafeProperty(path[index])) {\n            return defaultValue;\n        }\n        current = current[path[index]];\n    }\n    if (current === undefined) {\n        return defaultValue;\n    }\n    return current;\n}\n\nexports.get = get;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,gBAAgB,GAAGC,OAAO,CAAC,qCAAqC,CAAC;AACvE,MAAMC,SAAS,GAAGD,OAAO,CAAC,2BAA2B,CAAC;AACtD,MAAME,KAAK,GAAGF,OAAO,CAAC,uBAAuB,CAAC;AAC9C,MAAMG,MAAM,GAAGH,OAAO,CAAC,mBAAmB,CAAC;AAE3C,SAASI,GAAGA,CAACC,MAAM,EAAEC,IAAI,EAAEC,YAAY,EAAE;EACrC,IAAIF,MAAM,IAAI,IAAI,EAAE;IAChB,OAAOE,YAAY;EACvB;EACA,QAAQ,OAAOD,IAAI;IACf,KAAK,QAAQ;MAAE;QACX,IAAIP,gBAAgB,CAACA,gBAAgB,CAACO,IAAI,CAAC,EAAE;UACzC,OAAOC,YAAY;QACvB;QACA,MAAMC,MAAM,GAAGH,MAAM,CAACC,IAAI,CAAC;QAC3B,IAAIE,MAAM,KAAKC,SAAS,EAAE;UACtB,IAAIR,SAAS,CAACA,SAAS,CAACK,IAAI,CAAC,EAAE;YAC3B,OAAOF,GAAG,CAACC,MAAM,EAAEF,MAAM,CAACA,MAAM,CAACG,IAAI,CAAC,EAAEC,YAAY,CAAC;UACzD,CAAC,MACI;YACD,OAAOA,YAAY;UACvB;QACJ;QACA,OAAOC,MAAM;MACjB;IACA,KAAK,QAAQ;IACb,KAAK,QAAQ;MAAE;QACX,IAAI,OAAOF,IAAI,KAAK,QAAQ,EAAE;UAC1BA,IAAI,GAAGJ,KAAK,CAACA,KAAK,CAACI,IAAI,CAAC;QAC5B;QACA,MAAME,MAAM,GAAGH,MAAM,CAACC,IAAI,CAAC;QAC3B,IAAIE,MAAM,KAAKC,SAAS,EAAE;UACtB,OAAOF,YAAY;QACvB;QACA,OAAOC,MAAM;MACjB;IACA;MAAS;QACL,IAAIE,KAAK,CAACC,OAAO,CAACL,IAAI,CAAC,EAAE;UACrB,OAAOM,WAAW,CAACP,MAAM,EAAEC,IAAI,EAAEC,YAAY,CAAC;QAClD;QACA,IAAId,MAAM,CAACoB,EAAE,CAACP,IAAI,EAAEQ,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;UAChCR,IAAI,GAAG,IAAI;QACf,CAAC,MACI;UACDA,IAAI,GAAGS,MAAM,CAACT,IAAI,CAAC;QACvB;QACA,IAAIP,gBAAgB,CAACA,gBAAgB,CAACO,IAAI,CAAC,EAAE;UACzC,OAAOC,YAAY;QACvB;QACA,MAAMC,MAAM,GAAGH,MAAM,CAACC,IAAI,CAAC;QAC3B,IAAIE,MAAM,KAAKC,SAAS,EAAE;UACtB,OAAOF,YAAY;QACvB;QACA,OAAOC,MAAM;MACjB;EACJ;AACJ;AACA,SAASI,WAAWA,CAACP,MAAM,EAAEC,IAAI,EAAEC,YAAY,EAAE;EAC7C,IAAID,IAAI,CAACU,MAAM,KAAK,CAAC,EAAE;IACnB,OAAOT,YAAY;EACvB;EACA,IAAIU,OAAO,GAAGZ,MAAM;EACpB,KAAK,IAAIa,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGZ,IAAI,CAACU,MAAM,EAAEE,KAAK,EAAE,EAAE;IAC9C,IAAID,OAAO,IAAI,IAAI,EAAE;MACjB,OAAOV,YAAY;IACvB;IACA,IAAIR,gBAAgB,CAACA,gBAAgB,CAACO,IAAI,CAACY,KAAK,CAAC,CAAC,EAAE;MAChD,OAAOX,YAAY;IACvB;IACAU,OAAO,GAAGA,OAAO,CAACX,IAAI,CAACY,KAAK,CAAC,CAAC;EAClC;EACA,IAAID,OAAO,KAAKR,SAAS,EAAE;IACvB,OAAOF,YAAY;EACvB;EACA,OAAOU,OAAO;AAClB;AAEAtB,OAAO,CAACS,GAAG,GAAGA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}