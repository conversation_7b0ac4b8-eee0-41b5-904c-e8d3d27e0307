{"ast": null, "code": "import isArrayLike from './is-array-like';\nvar filter = function (arr, func) {\n  if (!isArrayLike(arr)) {\n    return arr;\n  }\n  var result = [];\n  for (var index = 0; index < arr.length; index++) {\n    var value = arr[index];\n    if (func(value, index)) {\n      result.push(value);\n    }\n  }\n  return result;\n};\nexport default filter;", "map": {"version": 3, "names": ["isArrayLike", "filter", "arr", "func", "result", "index", "length", "value", "push"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\util\\src\\lodash\\filter.ts"], "sourcesContent": ["import isArrayLike from './is-array-like';\n\nconst filter = function <T>(arr: T[], func: (v: T, idx: number) => boolean): T[] {\n  if (!isArrayLike(arr)) {\n    return arr;\n  }\n  const result: T[] = [];\n  for (let index = 0; index < arr.length; index++) {\n    const value = arr[index];\n    if (func(value, index)) {\n      result.push(value);\n    }\n  }\n\n  return result;\n};\n\nexport default filter;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,iBAAiB;AAEzC,IAAMC,MAAM,GAAG,SAAAA,CAAaC,GAAQ,EAAEC,IAAoC;EACxE,IAAI,CAACH,WAAW,CAACE,GAAG,CAAC,EAAE;IACrB,OAAOA,GAAG;EACZ;EACA,IAAME,MAAM,GAAQ,EAAE;EACtB,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGH,GAAG,CAACI,MAAM,EAAED,KAAK,EAAE,EAAE;IAC/C,IAAME,KAAK,GAAGL,GAAG,CAACG,KAAK,CAAC;IACxB,IAAIF,IAAI,CAACI,KAAK,EAAEF,KAAK,CAAC,EAAE;MACtBD,MAAM,CAACI,IAAI,CAACD,KAAK,CAAC;IACpB;EACF;EAEA,OAAOH,MAAM;AACf,CAAC;AAED,eAAeH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}