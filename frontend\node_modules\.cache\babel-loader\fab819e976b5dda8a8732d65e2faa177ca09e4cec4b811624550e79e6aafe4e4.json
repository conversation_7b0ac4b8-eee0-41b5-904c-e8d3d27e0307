{"ast": null, "code": "import { omit } from '@antv/util';\n// Get point1 point2 radius.\nconst getR = (point1, point2) => {\n  return Math.sqrt(Math.pow(point1[0] - point2[0], 2) + Math.pow(point1[1] - point2[1], 2)) / 2;\n};\n// 计算两点之间的角度\nconst getAngle = (start, end, center) => {\n  const startAngle = Math.atan2(start[1] - center[1], start[0] - center[0]);\n  const endAngle = Math.atan2(end[1] - center[1], end[0] - center[0]);\n  let angle = endAngle - startAngle;\n  // 确保角度在 0-2π 之间\n  if (angle < 0) angle += Math.PI * 2;\n  return angle;\n};\n// Gauge round.\nexport const Round = (options, context) => {\n  if (!context) return;\n  const {\n    coordinate\n  } = context;\n  if (!(coordinate === null || coordinate === void 0 ? void 0 : coordinate.getCenter)) return;\n  // Get coordinate center point.\n  const center = coordinate.getCenter();\n  return (points, cfg, defaultCfg) => {\n    const {\n      document\n    } = context.canvas;\n    const {\n      color,\n      index\n    } = cfg;\n    const g = document.createElement('g', {});\n    const minR = getR(points[0], points[1]);\n    const maxR = getR(points[0], center) * 2;\n    // 计算每个弧段的角度\n    // 判断是否需要使用大弧度（角度大于180度）\n    const isHalf = getAngle(points[3], points[0], center) > Math.PI;\n    /**\n     * MinR small circle radius,  maxR big circle radius.\n     * Draw four arcs.\n     * Style lineWidth and stroke for the time being inset.\n     */\n    const roundPath = document.createElement('path', {\n      style: Object.assign(Object.assign(Object.assign({\n        d: [['M', ...points[0]], ['A', minR, minR, 0, 1, 0, ...points[1]], ['A', maxR + minR * 2, maxR + minR * 2, 0, isHalf ? 1 : 0, 0, ...points[2]], ['A', minR, minR, 0, 1, index === 0 ? 0 : 1, ...points[3]], ['A', maxR, maxR, 0, isHalf ? 1 : 0, 1, ...points[0]], ['Z']]\n      }, defaultCfg), omit(options, ['shape', 'last', 'first'])), {\n        fill: color || defaultCfg.color\n      })\n    });\n    g.appendChild(roundPath);\n    return g;\n  };\n};", "map": {"version": 3, "names": ["omit", "getR", "point1", "point2", "Math", "sqrt", "pow", "getAngle", "start", "end", "center", "startAngle", "atan2", "endAngle", "angle", "PI", "Round", "options", "context", "coordinate", "getCenter", "points", "cfg", "defaultCfg", "document", "canvas", "color", "index", "g", "createElement", "minR", "maxR", "is<PERSON>alf", "roundPath", "style", "Object", "assign", "d", "fill", "append<PERSON><PERSON><PERSON>"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\g2\\src\\shape\\gauge\\round.ts"], "sourcesContent": ["import { omit } from '@antv/util';\nimport type { Vector2, ShapeComponent as SC } from '../../runtime';\n\nexport type RoundOptions = Record<string, any>;\n\n// Get point1 point2 radius.\nconst getR = (point1, point2) => {\n  return (\n    Math.sqrt(\n      Math.pow(point1[0] - point2[0], 2) + Math.pow(point1[1] - point2[1], 2),\n    ) / 2\n  );\n};\n\n// 计算两点之间的角度\nconst getAngle = (start, end, center) => {\n  const startAngle = Math.atan2(start[1] - center[1], start[0] - center[0]);\n  const endAngle = Math.atan2(end[1] - center[1], end[0] - center[0]);\n  let angle = endAngle - startAngle;\n  // 确保角度在 0-2π 之间\n  if (angle < 0) angle += Math.PI * 2;\n  return angle;\n};\n\n// Gauge round.\nexport const Round: SC<RoundOptions> = (options, context) => {\n  if (!context) return;\n  const { coordinate } = context;\n  if (!coordinate?.getCenter) return;\n  // Get coordinate center point.\n  const center = coordinate.getCenter() as Vector2;\n\n  return (points, cfg, defaultCfg) => {\n    const { document } = context.canvas;\n    const { color, index } = cfg;\n\n    const g = document.createElement('g', {});\n\n    const minR = getR(points[0], points[1]);\n    const maxR = getR(points[0], center) * 2;\n\n    // 计算每个弧段的角度\n    // 判断是否需要使用大弧度（角度大于180度）\n    const isHalf = getAngle(points[3], points[0], center) > Math.PI;\n\n    /**\n     * MinR small circle radius,  maxR big circle radius.\n     * Draw four arcs.\n     * Style lineWidth and stroke for the time being inset.\n     */\n    const roundPath = document.createElement('path', {\n      style: {\n        d: [\n          ['M', ...points[0]],\n          ['A', minR, minR, 0, 1, 0, ...points[1]],\n          [\n            'A',\n            maxR + minR * 2,\n            maxR + minR * 2,\n            0,\n            isHalf ? 1 : 0,\n            0,\n            ...points[2],\n          ],\n          ['A', minR, minR, 0, 1, index === 0 ? 0 : 1, ...points[3]],\n          ['A', maxR, maxR, 0, isHalf ? 1 : 0, 1, ...points[0]],\n          ['Z'],\n        ],\n        ...defaultCfg,\n        ...omit(options, ['shape', 'last', 'first']),\n        fill: color || defaultCfg.color,\n      },\n    });\n\n    g.appendChild(roundPath);\n\n    return g;\n  };\n};\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,YAAY;AAKjC;AACA,MAAMC,IAAI,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAI;EAC9B,OACEC,IAAI,CAACC,IAAI,CACPD,IAAI,CAACE,GAAG,CAACJ,MAAM,CAAC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGC,IAAI,CAACE,GAAG,CAACJ,<PERSON>AM,<PERSON>AC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CACxE,GAAG,CAAC;AAET,CAAC;AAED;AACA,MAAMI,QAAQ,GAAGA,CAACC,KAAK,EAAEC,GAAG,EAAEC,MAAM,KAAI;EACtC,MAAMC,UAAU,GAAGP,IAAI,CAACQ,KAAK,CAACJ,KAAK,CAAC,CAAC,CAAC,GAAGE,MAAM,CAAC,CAAC,CAAC,EAAEF,KAAK,CAAC,CAAC,CAAC,GAAGE,MAAM,CAAC,CAAC,CAAC,CAAC;EACzE,MAAMG,QAAQ,GAAGT,IAAI,CAACQ,KAAK,CAACH,GAAG,CAAC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC,EAAED,GAAG,CAAC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC,CAAC;EACnE,IAAII,KAAK,GAAGD,QAAQ,GAAGF,UAAU;EACjC;EACA,IAAIG,KAAK,GAAG,CAAC,EAAEA,KAAK,IAAIV,IAAI,CAACW,EAAE,GAAG,CAAC;EACnC,OAAOD,KAAK;AACd,CAAC;AAED;AACA,OAAO,MAAME,KAAK,GAAqBA,CAACC,OAAO,EAAEC,OAAO,KAAI;EAC1D,IAAI,CAACA,OAAO,EAAE;EACd,MAAM;IAAEC;EAAU,CAAE,GAAGD,OAAO;EAC9B,IAAI,EAACC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEC,SAAS,GAAE;EAC5B;EACA,MAAMV,MAAM,GAAGS,UAAU,CAACC,SAAS,EAAa;EAEhD,OAAO,CAACC,MAAM,EAAEC,GAAG,EAAEC,UAAU,KAAI;IACjC,MAAM;MAAEC;IAAQ,CAAE,GAAGN,OAAO,CAACO,MAAM;IACnC,MAAM;MAAEC,KAAK;MAAEC;IAAK,CAAE,GAAGL,GAAG;IAE5B,MAAMM,CAAC,GAAGJ,QAAQ,CAACK,aAAa,CAAC,GAAG,EAAE,EAAE,CAAC;IAEzC,MAAMC,IAAI,GAAG7B,IAAI,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IACvC,MAAMU,IAAI,GAAG9B,IAAI,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAEX,MAAM,CAAC,GAAG,CAAC;IAExC;IACA;IACA,MAAMsB,MAAM,GAAGzB,QAAQ,CAACc,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEX,MAAM,CAAC,GAAGN,IAAI,CAACW,EAAE;IAE/D;;;;;IAKA,MAAMkB,SAAS,GAAGT,QAAQ,CAACK,aAAa,CAAC,MAAM,EAAE;MAC/CK,KAAK,EAAAC,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA;QACHC,CAAC,EAAE,CACD,CAAC,GAAG,EAAE,GAAGhB,MAAM,CAAC,CAAC,CAAC,CAAC,EACnB,CAAC,GAAG,EAAES,IAAI,EAAEA,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAGT,MAAM,CAAC,CAAC,CAAC,CAAC,EACxC,CACE,GAAG,EACHU,IAAI,GAAGD,IAAI,GAAG,CAAC,EACfC,IAAI,GAAGD,IAAI,GAAG,CAAC,EACf,CAAC,EACDE,MAAM,GAAG,CAAC,GAAG,CAAC,EACd,CAAC,EACD,GAAGX,MAAM,CAAC,CAAC,CAAC,CACb,EACD,CAAC,GAAG,EAAES,IAAI,EAAEA,IAAI,EAAE,CAAC,EAAE,CAAC,EAAEH,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAGN,MAAM,CAAC,CAAC,CAAC,CAAC,EAC1D,CAAC,GAAG,EAAEU,IAAI,EAAEA,IAAI,EAAE,CAAC,EAAEC,MAAM,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,GAAGX,MAAM,CAAC,CAAC,CAAC,CAAC,EACrD,CAAC,GAAG,CAAC;MACN,GACEE,UAAU,GACVvB,IAAI,CAACiB,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QAC5CqB,IAAI,EAAEZ,KAAK,IAAIH,UAAU,CAACG;MAAK;KAElC,CAAC;IAEFE,CAAC,CAACW,WAAW,CAACN,SAAS,CAAC;IAExB,OAAOL,CAAC;EACV,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}