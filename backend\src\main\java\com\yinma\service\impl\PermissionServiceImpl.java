package com.yinma.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinma.dto.PermissionDTO;
import com.yinma.entity.PermissionEntity;
import com.yinma.mapper.PermissionMapper;
import com.yinma.mapper.RolePermissionMapper;
import com.yinma.service.PermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 权限信息Service实现类
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PermissionServiceImpl extends ServiceImpl<PermissionMapper, PermissionEntity> implements PermissionService {

    private final PermissionMapper permissionMapper;
    private final RolePermissionMapper rolePermissionMapper;

    @Override
    public IPage<PermissionDTO> selectPermissionPage(PermissionDTO.PermissionQueryDTO queryDTO) {
        Page<PermissionEntity> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        IPage<PermissionEntity> entityPage = permissionMapper.selectPermissionPage(page, queryDTO);
        
        IPage<PermissionDTO> dtoPage = new Page<>();
        BeanUtils.copyProperties(entityPage, dtoPage);
        
        List<PermissionDTO> dtoList = entityPage.getRecords().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        dtoPage.setRecords(dtoList);
        
        return dtoPage;
    }

    @Override
    @Cacheable(value = "permission", key = "#permissionId")
    public PermissionDTO selectPermissionById(Long permissionId) {
        PermissionEntity entity = permissionMapper.selectById(permissionId);
        return entity != null ? convertToDTO(entity) : null;
    }

    @Override
    @Cacheable(value = "permission", key = "'code:' + #permissionCode")
    public PermissionDTO selectPermissionByCode(String permissionCode) {
        PermissionEntity entity = permissionMapper.selectPermissionByCode(permissionCode);
        return entity != null ? convertToDTO(entity) : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "permission", allEntries = true)
    public Long createPermission(PermissionDTO.PermissionCreateDTO createDTO) {
        // 检查权限编码是否存在
        if (checkPermissionCodeExists(createDTO.getPermissionCode(), null)) {
            throw new RuntimeException("权限编码已存在: " + createDTO.getPermissionCode());
        }
        
        // 检查权限名称是否存在
        if (checkPermissionNameExists(createDTO.getPermissionName(), null)) {
            throw new RuntimeException("权限名称已存在: " + createDTO.getPermissionName());
        }
        
        // 如果有父权限，检查父权限是否存在
        if (createDTO.getParentId() != null && createDTO.getParentId() > 0) {
            PermissionEntity parentEntity = permissionMapper.selectById(createDTO.getParentId());
            if (parentEntity == null) {
                throw new RuntimeException("父权限不存在");
            }
        }
        
        PermissionEntity entity = new PermissionEntity();
        BeanUtils.copyProperties(createDTO, entity);
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        
        // 设置排序号
        if (entity.getSortOrder() == null) {
            Integer maxSortOrder = permissionMapper.selectMaxSortOrderByParentId(entity.getParentId());
            entity.setSortOrder(maxSortOrder != null ? maxSortOrder + 1 : 1);
        }
        
        permissionMapper.insert(entity);
        
        log.info("创建权限成功, permissionId: {}, permissionCode: {}", entity.getPermissionId(), entity.getPermissionCode());
        return entity.getPermissionId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "permission", allEntries = true)
    public Boolean updatePermission(PermissionDTO.PermissionUpdateDTO updateDTO) {
        PermissionEntity existingEntity = permissionMapper.selectById(updateDTO.getPermissionId());
        if (existingEntity == null) {
            throw new RuntimeException("权限不存在");
        }
        
        // 检查权限编码是否存在（排除自己）
        if (checkPermissionCodeExists(updateDTO.getPermissionCode(), updateDTO.getPermissionId())) {
            throw new RuntimeException("权限编码已存在: " + updateDTO.getPermissionCode());
        }
        
        // 检查权限名称是否存在（排除自己）
        if (checkPermissionNameExists(updateDTO.getPermissionName(), updateDTO.getPermissionId())) {
            throw new RuntimeException("权限名称已存在: " + updateDTO.getPermissionName());
        }
        
        // 如果有父权限，检查父权限是否存在且不能是自己或自己的子权限
        if (updateDTO.getParentId() != null && updateDTO.getParentId() > 0) {
            if (updateDTO.getParentId().equals(updateDTO.getPermissionId())) {
                throw new RuntimeException("父权限不能是自己");
            }
            
            PermissionEntity parentEntity = permissionMapper.selectById(updateDTO.getParentId());
            if (parentEntity == null) {
                throw new RuntimeException("父权限不存在");
            }
            
            // 检查是否会形成循环引用
            if (isChildPermission(updateDTO.getPermissionId(), updateDTO.getParentId())) {
                throw new RuntimeException("不能将权限移动到其子权限下");
            }
        }
        
        PermissionEntity entity = new PermissionEntity();
        BeanUtils.copyProperties(updateDTO, entity);
        entity.setUpdateTime(LocalDateTime.now());
        
        int result = permissionMapper.updateById(entity);
        
        log.info("更新权限成功, permissionId: {}, permissionCode: {}", entity.getPermissionId(), entity.getPermissionCode());
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "permission", allEntries = true)
    public Boolean deletePermission(Long permissionId) {
        PermissionEntity entity = permissionMapper.selectById(permissionId);
        if (entity == null) {
            throw new RuntimeException("权限不存在");
        }
        
        // 检查是否有子权限
        List<PermissionEntity> childPermissions = permissionMapper.selectChildPermissions(permissionId);
        if (!CollectionUtils.isEmpty(childPermissions)) {
            throw new RuntimeException("权限下存在子权限，无法删除");
        }
        
        // 检查是否有角色关联此权限
        List<Long> roleIds = rolePermissionMapper.selectRoleIdsByPermissionId(permissionId);
        if (!CollectionUtils.isEmpty(roleIds)) {
            throw new RuntimeException("权限被角色使用，无法删除");
        }
        
        // 删除权限
        int result = permissionMapper.deleteById(permissionId);
        
        log.info("删除权限成功, permissionId: {}, permissionCode: {}", permissionId, entity.getPermissionCode());
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "permission", allEntries = true)
    public Boolean batchDeletePermissions(List<Long> permissionIds) {
        if (CollectionUtils.isEmpty(permissionIds)) {
            return false;
        }
        
        for (Long permissionId : permissionIds) {
            deletePermission(permissionId);
        }
        
        log.info("批量删除权限成功, permissionIds: {}", permissionIds);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "permission", allEntries = true)
    public Boolean enablePermission(Long permissionId) {
        return updatePermissionStatus(permissionId, 1);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "permission", allEntries = true)
    public Boolean disablePermission(Long permissionId) {
        return updatePermissionStatus(permissionId, 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "permission", allEntries = true)
    public Boolean batchUpdatePermissionStatus(List<Long> permissionIds, Integer status) {
        if (CollectionUtils.isEmpty(permissionIds)) {
            return false;
        }
        
        int result = permissionMapper.batchUpdatePermissionStatus(permissionIds, status);
        
        log.info("批量更新权限状态成功, permissionIds: {}, status: {}", permissionIds, status);
        return result > 0;
    }

    @Override
    public Boolean checkPermissionCodeExists(String permissionCode, Long excludePermissionId) {
        return permissionMapper.checkPermissionCodeExists(permissionCode, excludePermissionId) > 0;
    }

    @Override
    public Boolean checkPermissionNameExists(String permissionName, Long excludePermissionId) {
        return permissionMapper.checkPermissionNameExists(permissionName, excludePermissionId) > 0;
    }

    @Override
    @Cacheable(value = "permission", key = "'tree:' + #enabledOnly")
    public List<PermissionDTO.PermissionTreeDTO> selectPermissionTree(Boolean enabledOnly) {
        List<PermissionEntity> allPermissions = permissionMapper.selectPermissionTree(enabledOnly);
        return buildPermissionTree(allPermissions, null);
    }

    @Override
    public List<PermissionDTO> selectChildPermissions(Long parentId) {
        List<PermissionEntity> entities = permissionMapper.selectChildPermissions(parentId);
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<PermissionDTO> selectUserPermissions(Long userId) {
        List<PermissionEntity> entities = permissionMapper.selectUserPermissions(userId);
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "permission", allEntries = true)
    public Boolean movePermission(Long permissionId, Long newParentId, Integer newSortOrder) {
        PermissionEntity entity = permissionMapper.selectById(permissionId);
        if (entity == null) {
            throw new RuntimeException("权限不存在");
        }
        
        // 检查新父权限是否存在
        if (newParentId != null && newParentId > 0) {
            PermissionEntity parentEntity = permissionMapper.selectById(newParentId);
            if (parentEntity == null) {
                throw new RuntimeException("新父权限不存在");
            }
            
            // 检查是否会形成循环引用
            if (isChildPermission(permissionId, newParentId)) {
                throw new RuntimeException("不能将权限移动到其子权限下");
            }
        }
        
        entity.setParentId(newParentId);
        if (newSortOrder != null) {
            entity.setSortOrder(newSortOrder);
        }
        entity.setUpdateTime(LocalDateTime.now());
        
        int result = permissionMapper.updateById(entity);
        
        log.info("移动权限成功, permissionId: {}, newParentId: {}, newSortOrder: {}", 
                permissionId, newParentId, newSortOrder);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "permission", allEntries = true)
    public Boolean sortPermissions(List<PermissionDTO.PermissionSortDTO> sortList) {
        if (CollectionUtils.isEmpty(sortList)) {
            return false;
        }
        
        for (PermissionDTO.PermissionSortDTO sortDTO : sortList) {
            permissionMapper.updateSortOrder(sortDTO.getPermissionId(), sortDTO.getSortOrder());
        }
        
        log.info("权限排序成功, sortList: {}", sortList);
        return true;
    }

    @Override
    public PermissionDTO.PermissionStatisticsDTO selectPermissionStatistics() {
        return permissionMapper.selectPermissionStatistics();
    }

    @Override
    public List<PermissionDTO> exportPermissions(PermissionDTO.PermissionQueryDTO queryDTO) {
        List<PermissionEntity> entities = permissionMapper.selectPermissionList(queryDTO);
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "permission", allEntries = true)
    public PermissionDTO.ImportResultDTO importPermissions(List<PermissionDTO.PermissionImportDTO> permissions) {
        PermissionDTO.ImportResultDTO result = new PermissionDTO.ImportResultDTO();
        result.setTotalCount(permissions.size());
        
        int successCount = 0;
        int failureCount = 0;
        
        for (PermissionDTO.PermissionImportDTO importDTO : permissions) {
            try {
                // 检查权限编码是否存在
                if (checkPermissionCodeExists(importDTO.getPermissionCode(), null)) {
                    failureCount++;
                    continue;
                }
                
                PermissionEntity entity = new PermissionEntity();
                BeanUtils.copyProperties(importDTO, entity);
                entity.setCreateTime(LocalDateTime.now());
                entity.setUpdateTime(LocalDateTime.now());
                
                permissionMapper.insert(entity);
                successCount++;
                
            } catch (Exception e) {
                log.error("导入权限失败, permissionCode: {}", importDTO.getPermissionCode(), e);
                failureCount++;
            }
        }
        
        result.setSuccessCount(successCount);
        result.setFailureCount(failureCount);
        
        log.info("导入权限完成, 总数: {}, 成功: {}, 失败: {}", result.getTotalCount(), successCount, failureCount);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "permission", allEntries = true)
    public Long copyPermission(Long sourcePermissionId, String newPermissionName, String newPermissionCode, Long targetParentId) {
        PermissionEntity sourcePermission = permissionMapper.selectById(sourcePermissionId);
        if (sourcePermission == null) {
            throw new RuntimeException("源权限不存在");
        }
        
        // 检查新权限编码是否存在
        if (checkPermissionCodeExists(newPermissionCode, null)) {
            throw new RuntimeException("权限编码已存在: " + newPermissionCode);
        }
        
        // 检查新权限名称是否存在
        if (checkPermissionNameExists(newPermissionName, null)) {
            throw new RuntimeException("权限名称已存在: " + newPermissionName);
        }
        
        // 创建新权限
        PermissionEntity newPermission = new PermissionEntity();
        BeanUtils.copyProperties(sourcePermission, newPermission);
        newPermission.setPermissionId(null);
        newPermission.setPermissionCode(newPermissionCode);
        newPermission.setPermissionName(newPermissionName);
        newPermission.setParentId(targetParentId != null ? targetParentId : sourcePermission.getParentId());
        newPermission.setCreateTime(LocalDateTime.now());
        newPermission.setUpdateTime(LocalDateTime.now());
        
        // 设置排序号
        Integer maxSortOrder = permissionMapper.selectMaxSortOrderByParentId(newPermission.getParentId());
        newPermission.setSortOrder(maxSortOrder != null ? maxSortOrder + 1 : 1);
        
        permissionMapper.insert(newPermission);
        
        log.info("复制权限成功, sourcePermissionId: {}, newPermissionId: {}, newPermissionCode: {}", 
                sourcePermissionId, newPermission.getPermissionId(), newPermissionCode);
        return newPermission.getPermissionId();
    }

    @Override
    @CacheEvict(value = "permission", allEntries = true)
    public Boolean syncPermissionCache() {
        // TODO: 实现权限缓存同步逻辑
        log.info("同步权限缓存成功");
        return true;
    }

    @Override
    @CacheEvict(value = "permission", allEntries = true)
    public Boolean refreshMenuPermissions() {
        // TODO: 实现菜单权限刷新逻辑
        log.info("刷新菜单权限成功");
        return true;
    }

    /**
     * 更新权限状态
     */
    private Boolean updatePermissionStatus(Long permissionId, Integer status) {
        PermissionEntity entity = permissionMapper.selectById(permissionId);
        if (entity == null) {
            throw new RuntimeException("权限不存在");
        }
        
        entity.setStatus(status);
        entity.setUpdateTime(LocalDateTime.now());
        
        int result = permissionMapper.updateById(entity);
        
        log.info("更新权限状态成功, permissionId: {}, status: {}", permissionId, status);
        return result > 0;
    }

    /**
     * 检查是否是子权限
     */
    private Boolean isChildPermission(Long parentId, Long childId) {
        if (parentId == null || childId == null) {
            return false;
        }
        
        List<PermissionEntity> childPermissions = permissionMapper.selectChildPermissions(parentId);
        for (PermissionEntity child : childPermissions) {
            if (child.getPermissionId().equals(childId)) {
                return true;
            }
            // 递归检查
            if (isChildPermission(child.getPermissionId(), childId)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 构建权限树
     */
    private List<PermissionDTO.PermissionTreeDTO> buildPermissionTree(List<PermissionEntity> allPermissions, Long parentId) {
        List<PermissionDTO.PermissionTreeDTO> tree = new ArrayList<>();
        
        for (PermissionEntity permission : allPermissions) {
            if ((parentId == null && permission.getParentId() == null) || 
                (parentId != null && parentId.equals(permission.getParentId()))) {
                
                PermissionDTO.PermissionTreeDTO treeNode = new PermissionDTO.PermissionTreeDTO();
                BeanUtils.copyProperties(permission, treeNode);
                
                // 递归构建子节点
                List<PermissionDTO.PermissionTreeDTO> children = buildPermissionTree(allPermissions, permission.getPermissionId());
                treeNode.setChildren(children);
                
                tree.add(treeNode);
            }
        }
        
        // 按排序号排序
        tree.sort((a, b) -> {
            Integer sortA = a.getSortOrder() != null ? a.getSortOrder() : 0;
            Integer sortB = b.getSortOrder() != null ? b.getSortOrder() : 0;
            return sortA.compareTo(sortB);
        });
        
        return tree;
    }

    /**
     * 实体转DTO
     */
    private PermissionDTO convertToDTO(PermissionEntity entity) {
        if (entity == null) {
            return null;
        }
        
        PermissionDTO dto = new PermissionDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}