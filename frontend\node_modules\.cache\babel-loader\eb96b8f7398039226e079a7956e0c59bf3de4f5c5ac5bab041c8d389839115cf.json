{"ast": null, "code": "import { __assign, __extends, __rest } from \"tslib\";\nimport { deepMix } from '@antv/util';\nimport { DisplayObject, Group } from '../../shapes';\nimport { deepAssign, select } from '../../util';\nvar Columns = /** @class */function (_super) {\n  __extends(Columns, _super);\n  function Columns(_a) {\n    var _this = this;\n    var style = _a.style,\n      rest = __rest(_a, [\"style\"]);\n    _this = _super.call(this, deepMix({}, {\n      type: 'column'\n    }, __assign({\n      style: style\n    }, rest))) || this;\n    _this.columnsGroup = new Group({\n      name: 'columns'\n    });\n    _this.appendChild(_this.columnsGroup);\n    _this.render();\n    return _this;\n  }\n  Columns.prototype.render = function () {\n    var _a = this.attributes,\n      columns = _a.columns,\n      x = _a.x,\n      y = _a.y;\n    this.columnsGroup.style.transform = \"translate(\".concat(x, \", \").concat(y, \")\");\n    select(this.columnsGroup).selectAll('.column').data(columns.flat()).join(function (enter) {\n      return enter.append('rect').attr('className', 'column').each(function (style) {\n        this.attr(style);\n      });\n    }, function (update) {\n      return update.each(function (style) {\n        this.attr(style);\n      });\n    }, function (exit) {\n      return exit.remove();\n    });\n  };\n  Columns.prototype.update = function (attr) {\n    this.attr(deepAssign({}, this.attributes, attr));\n    this.render();\n  };\n  Columns.prototype.clear = function () {\n    this.removeChildren();\n  };\n  return Columns;\n}(DisplayObject);\nexport { Columns };", "map": {"version": 3, "names": ["deepMix", "DisplayObject", "Group", "deepAssign", "select", "Columns", "_super", "__extends", "_a", "_this", "style", "rest", "__rest", "call", "type", "__assign", "columnsGroup", "name", "append<PERSON><PERSON><PERSON>", "render", "prototype", "attributes", "columns", "x", "y", "transform", "concat", "selectAll", "data", "flat", "join", "enter", "append", "attr", "each", "update", "exit", "remove", "clear", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\component\\src\\ui\\sparkline\\columns.ts"], "sourcesContent": ["import { deepMix } from '@antv/util';\nimport { BaseStyleProps, DisplayObject, Group, GroupStyleProps } from '../../shapes';\nimport { deepAssign, select } from '../../util';\n\nexport type ColumnStyleProps = GroupStyleProps;\n\nexport interface ColumnsStyleProps extends BaseStyleProps {\n  x?: number;\n  y?: number;\n  columns: ColumnStyleProps[][];\n}\n\nexport class Columns extends DisplayObject<ColumnsStyleProps> {\n  columnsGroup: Group;\n\n  constructor({ style, ...rest }: Partial<DisplayObject<ColumnsStyleProps>>) {\n    super(deepMix({}, { type: 'column' }, { style, ...rest }));\n    this.columnsGroup = new Group({ name: 'columns' });\n    this.appendChild(this.columnsGroup);\n    this.render();\n  }\n\n  public render(): void {\n    const { columns, x, y } = this.attributes;\n    this.columnsGroup.style.transform = `translate(${x}, ${y})`;\n\n    select(this.columnsGroup)\n      .selectAll('.column')\n      .data(columns.flat())\n      .join(\n        (enter) =>\n          enter\n            .append('rect')\n            .attr('className', 'column')\n            .each(function (style) {\n              this.attr(style);\n            }),\n        (update) =>\n          update.each(function (style) {\n            this.attr(style);\n          }),\n        (exit) => exit.remove()\n      );\n  }\n\n  public update(attr: Partial<ColumnsStyleProps>): void {\n    this.attr(deepAssign({}, this.attributes, attr));\n    this.render();\n  }\n\n  public clear(): void {\n    this.removeChildren();\n  }\n}\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAAyBC,aAAa,EAAEC,KAAK,QAAyB,cAAc;AACpF,SAASC,UAAU,EAAEC,MAAM,QAAQ,YAAY;AAU/C,IAAAC,OAAA,0BAAAC,MAAA;EAA6BC,SAAA,CAAAF,OAAA,EAAAC,MAAA;EAG3B,SAAAD,QAAYG,EAA6D;IAAzE,IAAAC,KAAA;IAAc,IAAAC,KAAK,GAAAF,EAAA,CAAAE,KAAA;MAAKC,IAAI,GAAAC,MAAA,CAAAJ,EAAA,EAAhB,SAAkB,CAAF;IAC1BC,KAAA,GAAAH,MAAK,CAAAO,IAAA,OAACb,OAAO,CAAC,EAAE,EAAE;MAAEc,IAAI,EAAE;IAAQ,CAAE,EAAAC,QAAA;MAAIL,KAAK,EAAAA;IAAA,GAAKC,IAAI,EAAG,CAAC;IAC1DF,KAAI,CAACO,YAAY,GAAG,IAAId,KAAK,CAAC;MAAEe,IAAI,EAAE;IAAS,CAAE,CAAC;IAClDR,KAAI,CAACS,WAAW,CAACT,KAAI,CAACO,YAAY,CAAC;IACnCP,KAAI,CAACU,MAAM,EAAE;;EACf;EAEOd,OAAA,CAAAe,SAAA,CAAAD,MAAM,GAAb;IACQ,IAAAX,EAAA,GAAoB,IAAI,CAACa,UAAU;MAAjCC,OAAO,GAAAd,EAAA,CAAAc,OAAA;MAAEC,CAAC,GAAAf,EAAA,CAAAe,CAAA;MAAEC,CAAC,GAAAhB,EAAA,CAAAgB,CAAoB;IACzC,IAAI,CAACR,YAAY,CAACN,KAAK,CAACe,SAAS,GAAG,aAAAC,MAAA,CAAaH,CAAC,QAAAG,MAAA,CAAKF,CAAC,MAAG;IAE3DpB,MAAM,CAAC,IAAI,CAACY,YAAY,CAAC,CACtBW,SAAS,CAAC,SAAS,CAAC,CACpBC,IAAI,CAACN,OAAO,CAACO,IAAI,EAAE,CAAC,CACpBC,IAAI,CACH,UAACC,KAAK;MACJ,OAAAA,KAAK,CACFC,MAAM,CAAC,MAAM,CAAC,CACdC,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,CAC3BC,IAAI,CAAC,UAAUxB,KAAK;QACnB,IAAI,CAACuB,IAAI,CAACvB,KAAK,CAAC;MAClB,CAAC,CAAC;IALJ,CAKI,EACN,UAACyB,MAAM;MACL,OAAAA,MAAM,CAACD,IAAI,CAAC,UAAUxB,KAAK;QACzB,IAAI,CAACuB,IAAI,CAACvB,KAAK,CAAC;MAClB,CAAC,CAAC;IAFF,CAEE,EACJ,UAAC0B,IAAI;MAAK,OAAAA,IAAI,CAACC,MAAM,EAAE;IAAb,CAAa,CACxB;EACL,CAAC;EAEMhC,OAAA,CAAAe,SAAA,CAAAe,MAAM,GAAb,UAAcF,IAAgC;IAC5C,IAAI,CAACA,IAAI,CAAC9B,UAAU,CAAC,EAAE,EAAE,IAAI,CAACkB,UAAU,EAAEY,IAAI,CAAC,CAAC;IAChD,IAAI,CAACd,MAAM,EAAE;EACf,CAAC;EAEMd,OAAA,CAAAe,SAAA,CAAAkB,KAAK,GAAZ;IACE,IAAI,CAACC,cAAc,EAAE;EACvB,CAAC;EACH,OAAAlC,OAAC;AAAD,CAAC,CAzC4BJ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}