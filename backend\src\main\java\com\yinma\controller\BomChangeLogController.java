package com.yinma.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinma.common.Result;
import com.yinma.dto.BomChangeLogDTO;
import com.yinma.entity.BomChangeLogEntity;
import com.yinma.service.BomChangeLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * BOM变更日志管理Controller
 * 
 * <AUTHOR>
 * @since 2024-01-20
 */
@Api(tags = "BOM变更日志管理")
@RestController
@RequestMapping("/api/bom-change-log")
public class BomChangeLogController {

    @Autowired
    private BomChangeLogService bomChangeLogService;

    @ApiOperation("分页查询变更日志列表")
    @GetMapping("/page")
    public Result<IPage<BomChangeLogDTO>> queryPage(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("BOM ID") @RequestParam(required = false) Long bomId,
            @ApiParam("变更类型") @RequestParam(required = false) String changeType,
            @ApiParam("变更操作") @RequestParam(required = false) String changeOperation,
            @ApiParam("审批状态") @RequestParam(required = false) String approvalStatus,
            @ApiParam("执行状态") @RequestParam(required = false) String executionStatus,
            @ApiParam("创建用户ID") @RequestParam(required = false) Long createUserId,
            @ApiParam("开始时间") @RequestParam(required = false) String startTime,
            @ApiParam("结束时间") @RequestParam(required = false) String endTime) {
        
        Page<BomChangeLogEntity> page = new Page<>(current, size);
        BomChangeLogDTO.BomChangeLogQueryDTO queryDTO = new BomChangeLogDTO.BomChangeLogQueryDTO();
        queryDTO.setBomId(bomId);
        queryDTO.setChangeType(changeType);
        queryDTO.setChangeOperation(changeOperation);
        queryDTO.setApprovalStatus(approvalStatus);
        queryDTO.setExecutionStatus(executionStatus);
        queryDTO.setCreateUserId(createUserId);
        queryDTO.setStartTime(startTime);
        queryDTO.setEndTime(endTime);
        
        IPage<BomChangeLogDTO> result = bomChangeLogService.queryPage(page, queryDTO);
        return Result.success(result);
    }

    @ApiOperation("获取变更日志详情")
    @GetMapping("/{logId}")
    public Result<BomChangeLogDTO> getChangeLogDetail(
            @ApiParam("日志ID") @PathVariable Long logId) {
        
        BomChangeLogDTO changeLog = bomChangeLogService.getChangeLogDetail(logId);
        if (changeLog == null) {
            return Result.error("变更日志不存在");
        }
        return Result.success(changeLog);
    }

    @ApiOperation("获取BOM变更历史")
    @GetMapping("/history/{bomId}")
    public Result<List<BomChangeLogDTO>> getChangeHistory(
            @ApiParam("BOM ID") @PathVariable Long bomId) {
        
        List<BomChangeLogDTO> history = bomChangeLogService.getChangeHistory(bomId);
        return Result.success(history);
    }

    @ApiOperation("获取受影响的BOM列表")
    @GetMapping("/affected-boms/{logId}")
    public Result<List<BomChangeLogDTO.AffectedBomDTO>> getAffectedBomList(
            @ApiParam("日志ID") @PathVariable Long logId) {
        
        List<BomChangeLogDTO.AffectedBomDTO> affectedBoms = bomChangeLogService.getAffectedBomList(logId);
        return Result.success(affectedBoms);
    }

    @ApiOperation("创建变更请求")
    @PostMapping("/request")
    public Result<BomChangeLogDTO> createChangeRequest(
            @Valid @RequestBody BomChangeLogDTO.ChangeRequestDTO requestDTO) {
        
        BomChangeLogDTO result = bomChangeLogService.createChangeRequest(requestDTO);
        return Result.success(result);
    }

    @ApiOperation("提交变更申请")
    @PutMapping("/submit/{logId}")
    public Result<BomChangeLogDTO> submitChangeApplication(
            @ApiParam("日志ID") @PathVariable Long logId,
            @Valid @RequestBody BomChangeLogDTO changeLogDTO) {
        
        changeLogDTO.setLogId(logId);
        BomChangeLogDTO result = bomChangeLogService.submitChangeApplication(changeLogDTO);
        return Result.success(result);
    }

    @ApiOperation("审批变更申请")
    @PutMapping("/approve/{logId}")
    public Result<BomChangeLogDTO> approveChangeApplication(
            @ApiParam("日志ID") @PathVariable Long logId,
            @ApiParam("是否通过") @RequestParam Boolean approved,
            @ApiParam("审批意见") @RequestParam(required = false) String comment,
            @ApiParam("审批用户ID") @RequestParam Long userId) {
        
        BomChangeLogDTO result = bomChangeLogService.approveChangeApplication(logId, approved, comment, userId);
        return Result.success(result);
    }

    @ApiOperation("执行变更")
    @PutMapping("/execute/{logId}")
    public Result<BomChangeLogDTO> executeChange(
            @ApiParam("日志ID") @PathVariable Long logId,
            @ApiParam("执行用户ID") @RequestParam Long userId) {
        
        BomChangeLogDTO result = bomChangeLogService.executeChange(logId, userId);
        return Result.success(result);
    }

    @ApiOperation("撤销变更")
    @PutMapping("/revoke/{logId}")
    public Result<BomChangeLogDTO> revokeChange(
            @ApiParam("日志ID") @PathVariable Long logId,
            @ApiParam("撤销原因") @RequestParam String reason,
            @ApiParam("撤销用户ID") @RequestParam Long userId) {
        
        BomChangeLogDTO result = bomChangeLogService.revokeChange(logId, reason, userId);
        return Result.success(result);
    }

    @ApiOperation("分析变更影响")
    @PostMapping("/analyze-impact")
    public Result<BomChangeLogDTO.ChangeImpactAnalysisDTO> analyzeChangeImpact(
            @ApiParam("BOM ID") @RequestParam Long bomId,
            @ApiParam("变更类型") @RequestParam String changeType,
            @ApiParam("变更内容") @RequestParam String changeContent) {
        
        BomChangeLogDTO.ChangeImpactAnalysisDTO analysis = 
            bomChangeLogService.analyzeChangeImpact(bomId, changeType, changeContent);
        return Result.success(analysis);
    }

    @ApiOperation("获取变更统计信息")
    @GetMapping("/statistics")
    public Result<BomChangeLogDTO.ChangeStatisticsDTO> getChangeStatistics(
            @ApiParam("BOM ID") @RequestParam(required = false) Long bomId,
            @ApiParam("变更类型") @RequestParam(required = false) String changeType,
            @ApiParam("变更操作") @RequestParam(required = false) String changeOperation,
            @ApiParam("审批状态") @RequestParam(required = false) String approvalStatus,
            @ApiParam("执行状态") @RequestParam(required = false) String executionStatus,
            @ApiParam("创建用户ID") @RequestParam(required = false) Long createUserId,
            @ApiParam("开始时间") @RequestParam(required = false) String startTime,
            @ApiParam("结束时间") @RequestParam(required = false) String endTime) {
        
        BomChangeLogDTO.BomChangeLogQueryDTO queryDTO = new BomChangeLogDTO.BomChangeLogQueryDTO();
        queryDTO.setBomId(bomId);
        queryDTO.setChangeType(changeType);
        queryDTO.setChangeOperation(changeOperation);
        queryDTO.setApprovalStatus(approvalStatus);
        queryDTO.setExecutionStatus(executionStatus);
        queryDTO.setCreateUserId(createUserId);
        queryDTO.setStartTime(startTime);
        queryDTO.setEndTime(endTime);
        
        BomChangeLogDTO.ChangeStatisticsDTO statistics = bomChangeLogService.getChangeStatistics(queryDTO);
        return Result.success(statistics);
    }

    @ApiOperation("导出变更日志")
    @GetMapping("/export")
    public Result<String> exportChangeLog(
            @ApiParam("BOM ID") @RequestParam(required = false) Long bomId,
            @ApiParam("变更类型") @RequestParam(required = false) String changeType,
            @ApiParam("变更操作") @RequestParam(required = false) String changeOperation,
            @ApiParam("审批状态") @RequestParam(required = false) String approvalStatus,
            @ApiParam("执行状态") @RequestParam(required = false) String executionStatus,
            @ApiParam("创建用户ID") @RequestParam(required = false) Long createUserId,
            @ApiParam("开始时间") @RequestParam(required = false) String startTime,
            @ApiParam("结束时间") @RequestParam(required = false) String endTime,
            @ApiParam("导出格式") @RequestParam(defaultValue = "EXCEL") String format) {
        
        try {
            BomChangeLogDTO.BomChangeLogQueryDTO queryDTO = new BomChangeLogDTO.BomChangeLogQueryDTO();
            queryDTO.setBomId(bomId);
            queryDTO.setChangeType(changeType);
            queryDTO.setChangeOperation(changeOperation);
            queryDTO.setApprovalStatus(approvalStatus);
            queryDTO.setExecutionStatus(executionStatus);
            queryDTO.setCreateUserId(createUserId);
            queryDTO.setStartTime(startTime);
            queryDTO.setEndTime(endTime);
            
            byte[] data = bomChangeLogService.exportChangeLog(queryDTO);
            // TODO: 返回文件下载链接或直接返回文件流
            return Result.success("导出成功");
        } catch (Exception e) {
            return Result.error("导出失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取变更类型选项")
    @GetMapping("/change-types")
    public Result<List<String>> getChangeTypes() {
        List<String> changeTypes = List.of(
            "STRUCTURE",    // 结构变更
            "MATERIAL",     // 物料变更
            "QUANTITY",     // 数量变更
            "ATTRIBUTE",    // 属性变更
            "VERSION",      // 版本变更
            "STATUS"        // 状态变更
        );
        return Result.success(changeTypes);
    }

    @ApiOperation("获取变更操作选项")
    @GetMapping("/change-operations")
    public Result<List<String>> getChangeOperations() {
        List<String> changeOperations = List.of(
            "ADD",          // 新增
            "UPDATE",       // 更新
            "DELETE",       // 删除
            "REPLACE",      // 替换
            "MOVE",         // 移动
            "COPY"          // 复制
        );
        return Result.success(changeOperations);
    }

    @ApiOperation("获取审批状态选项")
    @GetMapping("/approval-statuses")
    public Result<List<String>> getApprovalStatuses() {
        List<String> approvalStatuses = List.of(
            "PENDING",      // 待审批
            "SUBMITTED",    // 已提交
            "APPROVED",     // 已通过
            "REJECTED"      // 已拒绝
        );
        return Result.success(approvalStatuses);
    }

    @ApiOperation("获取执行状态选项")
    @GetMapping("/execution-statuses")
    public Result<List<String>> getExecutionStatuses() {
        List<String> executionStatuses = List.of(
            "NOT_EXECUTED", // 未执行
            "EXECUTING",    // 执行中
            "EXECUTED",     // 已执行
            "FAILED",       // 执行失败
            "REVOKED"       // 已撤销
        );
        return Result.success(executionStatuses);
    }

    @ApiOperation("获取变更对象类型选项")
    @GetMapping("/change-object-types")
    public Result<List<String>> getChangeObjectTypes() {
        List<String> changeObjectTypes = List.of(
            "BOM",          // BOM本身
            "BOM_DETAIL",   // BOM明细
            "MATERIAL",     // 物料
            "RELATIONSHIP", // 关系
            "ATTRIBUTE"     // 属性
        );
        return Result.success(changeObjectTypes);
    }

    @ApiOperation("批量审批变更申请")
    @PutMapping("/batch-approve")
    public Result<List<BomChangeLogDTO>> batchApproveChangeApplications(
            @ApiParam("批量审批请求") @RequestBody BomChangeLogDTO.BatchApprovalDTO batchApprovalDTO) {
        
        List<BomChangeLogDTO> results = new ArrayList<>();
        for (Long logId : batchApprovalDTO.getLogIds()) {
            try {
                BomChangeLogDTO result = bomChangeLogService.approveChangeApplication(
                    logId, 
                    batchApprovalDTO.getApproved(), 
                    batchApprovalDTO.getComment(), 
                    batchApprovalDTO.getUserId()
                );
                results.add(result);
            } catch (Exception e) {
                // 记录失败的审批
                BomChangeLogDTO failedResult = new BomChangeLogDTO();
                failedResult.setLogId(logId);
                failedResult.setApprovalComment("批量审批失败: " + e.getMessage());
                results.add(failedResult);
            }
        }
        
        return Result.success(results);
    }

    @ApiOperation("批量执行变更")
    @PutMapping("/batch-execute")
    public Result<List<BomChangeLogDTO>> batchExecuteChanges(
            @ApiParam("日志ID列表") @RequestBody List<Long> logIds,
            @ApiParam("执行用户ID") @RequestParam Long userId) {
        
        List<BomChangeLogDTO> results = new ArrayList<>();
        for (Long logId : logIds) {
            try {
                BomChangeLogDTO result = bomChangeLogService.executeChange(logId, userId);
                results.add(result);
            } catch (Exception e) {
                // 记录失败的执行
                BomChangeLogDTO failedResult = new BomChangeLogDTO();
                failedResult.setLogId(logId);
                failedResult.setExecutionComment("批量执行失败: " + e.getMessage());
                results.add(failedResult);
            }
        }
        
        return Result.success(results);
    }

    @ApiOperation("获取我的变更申请")
    @GetMapping("/my-requests")
    public Result<IPage<BomChangeLogDTO>> getMyChangeRequests(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("用户ID") @RequestParam Long userId,
            @ApiParam("审批状态") @RequestParam(required = false) String approvalStatus,
            @ApiParam("执行状态") @RequestParam(required = false) String executionStatus) {
        
        Page<BomChangeLogEntity> page = new Page<>(current, size);
        BomChangeLogDTO.BomChangeLogQueryDTO queryDTO = new BomChangeLogDTO.BomChangeLogQueryDTO();
        queryDTO.setCreateUserId(userId);
        queryDTO.setApprovalStatus(approvalStatus);
        queryDTO.setExecutionStatus(executionStatus);
        
        IPage<BomChangeLogDTO> result = bomChangeLogService.queryPage(page, queryDTO);
        return Result.success(result);
    }

    @ApiOperation("获取待我审批的变更")
    @GetMapping("/pending-approval")
    public Result<IPage<BomChangeLogDTO>> getPendingApprovalChanges(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("用户ID") @RequestParam Long userId) {
        
        Page<BomChangeLogEntity> page = new Page<>(current, size);
        BomChangeLogDTO.BomChangeLogQueryDTO queryDTO = new BomChangeLogDTO.BomChangeLogQueryDTO();
        queryDTO.setApprovalStatus("SUBMITTED");
        // TODO: 根据用户权限过滤待审批的变更
        
        IPage<BomChangeLogDTO> result = bomChangeLogService.queryPage(page, queryDTO);
        return Result.success(result);
    }
}