server:
  port: 8080
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: yinma-management-system
  
  profiles:
    active: dev
  
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: org.postgresql.Driver
    url: *************************************************************************************************************
    username: yinma_user
    password: yinma_password
    
    # Druid连接池配置
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      # 监控配置
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: admin123
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
    open-in-view: false
  
  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms
  
  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 600000
  
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB
  
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:/mapper/**/*.xml

# 日志配置
logging:
  level:
    com.yinma: debug
    org.springframework.security: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/yinma-system.log
    max-size: 10MB
    max-history: 30

# Knife4j配置
knife4j:
  enable: true
  openapi:
    title: 西安银马实业数字化管理系统API
    description: 企业级数字化管理系统接口文档
    version: 1.0.0
    concat: 开发团队
    url: http://localhost:8080
    email: <EMAIL>
    license: Apache 2.0
    license-url: https://www.apache.org/licenses/LICENSE-2.0
  setting:
    language: zh_cn
    enable-swagger-models: true
    enable-document-manage: true
    swagger-model-name: 实体类列表

# 应用自定义配置
yinma:
  # JWT配置
  jwt:
    secret: yinma-management-system-jwt-secret-key-2024
    expiration: 86400000  # 24小时
    header: Authorization
    prefix: Bearer 
  
  # 文件存储配置
  file:
    upload-path: /data/uploads/
    max-size: 10485760  # 10MB
    allowed-types: jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx
  
  # 系统配置
  system:
    name: 西安银马实业数字化管理系统
    version: 1.0.0
    copyright: Copyright © 2024 西安银马实业发展有限公司
    
---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  
  datasource:
    url: **************************************************************************************************************
    username: postgres
    password: postgres
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true

logging:
  level:
    root: info
    com.yinma: debug

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false

logging:
  level:
    root: warn
    com.yinma: info
  file:
    name: /var/log/yinma/application.log