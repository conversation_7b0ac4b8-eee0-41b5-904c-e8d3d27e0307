package com.yinma.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinma.entity.MaterialEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 物料主数据Mapper接口
 * 提供物料主数据相关的数据库操作
 * 
 * <AUTHOR> System
 * @since 2024-01-15
 */
@Repository
@Mapper
public interface MaterialMapper extends BaseMapper<MaterialEntity> {

    /**
     * 分页查询物料主数据
     * 
     * @param page 分页参数
     * @param materialCode 物料编码
     * @param materialName 物料名称
     * @param materialType 物料类型
     * @param materialCategory 物料分类
     * @param materialStatus 物料状态
     * @param abcCategory ABC分类
     * @param supplierCode 供应商编码
     * @return 分页结果
     */
    @Select({
        "<script>",
        "SELECT m.*, ",
        "       s.supplier_name as main_supplier_name,",
        "       COALESCE(stock.current_stock, 0) as current_stock,",
        "       COALESCE(stock.available_stock, 0) as available_stock,",
        "       COALESCE(stock.reserved_stock, 0) as reserved_stock",
        "FROM material_master m",
        "LEFT JOIN supplier_master s ON m.main_supplier_code = s.supplier_code AND s.deleted = 0",
        "LEFT JOIN (",
        "  SELECT material_code, ",
        "         SUM(current_qty) as current_stock,",
        "         SUM(available_qty) as available_stock,",
        "         SUM(reserved_qty) as reserved_stock",
        "  FROM inventory_stock WHERE deleted = 0",
        "  GROUP BY material_code",
        ") stock ON m.material_code = stock.material_code",
        "WHERE m.deleted = 0",
        "<if test='materialCode != null and materialCode != ""'>",
        "  AND m.material_code LIKE CONCAT('%', #{materialCode}, '%')",
        "</if>",
        "<if test='materialName != null and materialName != ""'>",
        "  AND m.material_name LIKE CONCAT('%', #{materialName}, '%')",
        "</if>",
        "<if test='materialType != null and materialType != ""'>",
        "  AND m.material_type = #{materialType}",
        "</if>",
        "<if test='materialCategory != null and materialCategory != ""'>",
        "  AND m.material_category = #{materialCategory}",
        "</if>",
        "<if test='materialStatus != null and materialStatus != ""'>",
        "  AND m.material_status = #{materialStatus}",
        "</if>",
        "<if test='abcCategory != null and abcCategory != ""'>",
        "  AND m.abc_category = #{abcCategory}",
        "</if>",
        "<if test='supplierCode != null and supplierCode != ""'>",
        "  AND m.main_supplier_code = #{supplierCode}",
        "</if>",
        "ORDER BY m.material_code",
        "</script>"
    })
    IPage<MaterialEntity> selectMaterialPage(Page<MaterialEntity> page,
                                            @Param("materialCode") String materialCode,
                                            @Param("materialName") String materialName,
                                            @Param("materialType") String materialType,
                                            @Param("materialCategory") String materialCategory,
                                            @Param("materialStatus") String materialStatus,
                                            @Param("abcCategory") String abcCategory,
                                            @Param("supplierCode") String supplierCode);

    /**
     * 根据物料编码查询物料信息（含库存）
     * 
     * @param materialCode 物料编码
     * @return 物料信息
     */
    @Select({
        "SELECT m.*, ",
        "       s.supplier_name as main_supplier_name,",
        "       s.contact_person, s.contact_phone, s.contact_email,",
        "       COALESCE(stock.current_stock, 0) as current_stock,",
        "       COALESCE(stock.available_stock, 0) as available_stock,",
        "       COALESCE(stock.reserved_stock, 0) as reserved_stock,",
        "       COALESCE(stock.on_order_stock, 0) as on_order_stock",
        "FROM material_master m",
        "LEFT JOIN supplier_master s ON m.main_supplier_code = s.supplier_code AND s.deleted = 0",
        "LEFT JOIN (",
        "  SELECT material_code, ",
        "         SUM(current_qty) as current_stock,",
        "         SUM(available_qty) as available_stock,",
        "         SUM(reserved_qty) as reserved_stock,",
        "         SUM(on_order_qty) as on_order_stock",
        "  FROM inventory_stock WHERE deleted = 0",
        "  GROUP BY material_code",
        ") stock ON m.material_code = stock.material_code",
        "WHERE m.material_code = #{materialCode} AND m.deleted = 0"
    })
    MaterialEntity selectMaterialWithStock(@Param("materialCode") String materialCode);

    /**
     * 查询物料供应商列表
     * 
     * @param materialCode 物料编码
     * @return 供应商列表
     */
    @Select({
        "SELECT ms.*, s.supplier_name, s.contact_person, s.contact_phone",
        "FROM material_supplier ms",
        "INNER JOIN supplier_master s ON ms.supplier_code = s.supplier_code AND s.deleted = 0",
        "WHERE ms.material_code = #{materialCode} AND ms.deleted = 0",
        "ORDER BY ms.is_main_supplier DESC, ms.priority ASC"
    })
    List<Map<String, Object>> selectMaterialSuppliers(@Param("materialCode") String materialCode);

    /**
     * 查询物料价格历史
     * 
     * @param materialCode 物料编码
     * @param limit 限制数量
     * @return 价格历史
     */
    @Select({
        "SELECT mph.*, s.supplier_name",
        "FROM material_price_history mph",
        "LEFT JOIN supplier_master s ON mph.supplier_code = s.supplier_code AND s.deleted = 0",
        "WHERE mph.material_code = #{materialCode} AND mph.deleted = 0",
        "ORDER BY mph.effective_date DESC, mph.id DESC",
        "<if test='limit != null and limit > 0'>",
        "  LIMIT #{limit}",
        "</if>"
    })
    List<Map<String, Object>> selectMaterialPriceHistory(@Param("materialCode") String materialCode, @Param("limit") Integer limit);

    /**
     * 查询物料库存明细
     * 
     * @param materialCode 物料编码
     * @return 库存明细
     */
    @Select({
        "SELECT is.*, w.warehouse_name, l.location_name",
        "FROM inventory_stock is",
        "LEFT JOIN warehouse_master w ON is.warehouse_code = w.warehouse_code AND w.deleted = 0",
        "LEFT JOIN storage_location l ON is.location_code = l.location_code AND l.deleted = 0",
        "WHERE is.material_code = #{materialCode} AND is.deleted = 0",
        "  AND (is.current_qty > 0 OR is.reserved_qty > 0 OR is.on_order_qty > 0)",
        "ORDER BY is.warehouse_code, is.location_code"
    })
    List<Map<String, Object>> selectMaterialStockDetails(@Param("materialCode") String materialCode);

    /**
     * 查询物料替代关系
     * 
     * @param materialCode 物料编码
     * @return 替代关系列表
     */
    @Select({
        "SELECT mr.*, ",
        "       m1.material_name as main_material_name,",
        "       m2.material_name as substitute_material_name",
        "FROM material_substitute mr",
        "LEFT JOIN material_master m1 ON mr.main_material_code = m1.material_code AND m1.deleted = 0",
        "LEFT JOIN material_master m2 ON mr.substitute_material_code = m2.material_code AND m2.deleted = 0",
        "WHERE (mr.main_material_code = #{materialCode} OR mr.substitute_material_code = #{materialCode})",
        "  AND mr.deleted = 0",
        "ORDER BY mr.substitute_type, mr.priority"
    })
    List<Map<String, Object>> selectMaterialSubstitutes(@Param("materialCode") String materialCode);

    /**
     * 查询低库存物料
     * 
     * @param warehouseCode 仓库编码
     * @return 低库存物料列表
     */
    @Select({
        "<script>",
        "SELECT m.*, ",
        "       COALESCE(stock.current_stock, 0) as current_stock,",
        "       COALESCE(stock.available_stock, 0) as available_stock,",
        "       m.safety_stock,",
        "       m.min_stock,",
        "       (m.safety_stock - COALESCE(stock.current_stock, 0)) as shortage_qty",
        "FROM material_master m",
        "LEFT JOIN (",
        "  SELECT material_code, ",
        "         SUM(current_qty) as current_stock,",
        "         SUM(available_qty) as available_stock",
        "  FROM inventory_stock ",
        "  WHERE deleted = 0",
        "  <if test='warehouseCode != null and warehouseCode != ""'>",
        "    AND warehouse_code = #{warehouseCode}",
        "  </if>",
        "  GROUP BY material_code",
        ") stock ON m.material_code = stock.material_code",
        "WHERE m.deleted = 0 AND m.material_status = 'ACTIVE'",
        "  AND COALESCE(stock.current_stock, 0) <= m.safety_stock",
        "ORDER BY (m.safety_stock - COALESCE(stock.current_stock, 0)) DESC",
        "</script>"
    })
    List<MaterialEntity> selectLowStockMaterials(@Param("warehouseCode") String warehouseCode);

    /**
     * 查询超期物料
     * 
     * @param warehouseCode 仓库编码
     * @return 超期物料列表
     */
    @Select({
        "<script>",
        "SELECT m.*, ",
        "       ib.batch_no, ib.production_date, ib.expiry_date,",
        "       DATEDIFF(ib.expiry_date, CURDATE()) as remaining_days,",
        "       ib.current_qty as batch_qty",
        "FROM material_master m",
        "INNER JOIN inventory_batch ib ON m.material_code = ib.material_code AND ib.deleted = 0",
        "WHERE m.deleted = 0 AND m.material_status = 'ACTIVE'",
        "  AND ib.expiry_date IS NOT NULL",
        "  AND ib.expiry_date <= DATE_ADD(CURDATE(), INTERVAL m.shelf_life DAY)",
        "  AND ib.current_qty > 0",
        "<if test='warehouseCode != null and warehouseCode != ""'>",
        "  AND ib.warehouse_code = #{warehouseCode}",
        "</if>",
        "ORDER BY ib.expiry_date ASC",
        "</script>"
    })
    List<Map<String, Object>> selectExpiredMaterials(@Param("warehouseCode") String warehouseCode);

    /**
     * 查询物料分类统计
     * 
     * @return 分类统计
     */
    @Select({
        "SELECT ",
        "  material_type,",
        "  material_category,",
        "  COUNT(*) as material_count,",
        "  SUM(CASE WHEN material_status = 'ACTIVE' THEN 1 ELSE 0 END) as active_count,",
        "  AVG(standard_cost) as avg_cost,",
        "  SUM(standard_cost * COALESCE(stock.current_stock, 0)) as total_value",
        "FROM material_master m",
        "LEFT JOIN (",
        "  SELECT material_code, SUM(current_qty) as current_stock",
        "  FROM inventory_stock WHERE deleted = 0",
        "  GROUP BY material_code",
        ") stock ON m.material_code = stock.material_code",
        "WHERE m.deleted = 0",
        "GROUP BY material_type, material_category",
        "ORDER BY material_type, material_category"
    })
    List<Map<String, Object>> selectMaterialCategoryStatistics();

    /**
     * 查询ABC分析数据
     * 
     * @param analysisType 分析类型（VALUE/QUANTITY/FREQUENCY）
     * @return ABC分析结果
     */
    @Select({
        "<script>",
        "SELECT ",
        "  m.material_code, m.material_name, m.material_type,",
        "  <choose>",
        "    <when test='analysisType == "VALUE"'>",
        "      m.standard_cost * COALESCE(stock.current_stock, 0) as analysis_value,",
        "      'VALUE' as analysis_type",
        "    </when>",
        "    <when test='analysisType == "QUANTITY"'>",
        "      COALESCE(stock.current_stock, 0) as analysis_value,",
        "      'QUANTITY' as analysis_type",
        "    </when>",
        "    <otherwise>",
        "      COALESCE(usage.usage_frequency, 0) as analysis_value,",
        "      'FREQUENCY' as analysis_type",
        "    </otherwise>",
        "  </choose>",
        "FROM material_master m",
        "LEFT JOIN (",
        "  SELECT material_code, SUM(current_qty) as current_stock",
        "  FROM inventory_stock WHERE deleted = 0",
        "  GROUP BY material_code",
        ") stock ON m.material_code = stock.material_code",
        "<if test='analysisType == "FREQUENCY"'>",
        "LEFT JOIN (",
        "  SELECT material_code, COUNT(*) as usage_frequency",
        "  FROM material_transaction ",
        "  WHERE deleted = 0 AND transaction_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)",
        "  GROUP BY material_code",
        ") usage ON m.material_code = usage.material_code",
        "</if>",
        "WHERE m.deleted = 0 AND m.material_status = 'ACTIVE'",
        "ORDER BY analysis_value DESC",
        "</script>"
    })
    List<Map<String, Object>> selectAbcAnalysisData(@Param("analysisType") String analysisType);

    /**
     * 批量更新物料ABC分类
     * 
     * @param materials 物料列表
     * @return 更新数量
     */
    @Update({
        "<script>",
        "<foreach collection='materials' item='material' separator=';'>",
        "UPDATE material_master SET ",
        "  abc_category = #{material.abcCategory},",
        "  update_by = #{material.updateBy},",
        "  update_time = #{material.updateTime}",
        "WHERE material_code = #{material.materialCode} AND deleted = 0",
        "</foreach>",
        "</script>"
    })
    int batchUpdateAbcCategory(@Param("materials") List<MaterialEntity> materials);

    /**
     * 批量更新物料价格
     * 
     * @param materials 物料列表
     * @return 更新数量
     */
    @Update({
        "<script>",
        "<foreach collection='materials' item='material' separator=';'>",
        "UPDATE material_master SET ",
        "  standard_cost = #{material.standardCost},",
        "  latest_purchase_price = #{material.latestPurchasePrice},",
        "  price_update_time = #{material.priceUpdateTime},",
        "  update_by = #{material.updateBy},",
        "  update_time = #{material.updateTime}",
        "WHERE material_code = #{material.materialCode} AND deleted = 0",
        "</foreach>",
        "</script>"
    })
    int batchUpdateMaterialPrice(@Param("materials") List<MaterialEntity> materials);

    /**
     * 查询物料使用情况
     * 
     * @param materialCode 物料编码
     * @return 使用情况
     */
    @Select({
        "SELECT ",
        "  'BOM' as usage_type,",
        "  COUNT(DISTINCT bd.bom_id) as usage_count,",
        "  GROUP_CONCAT(DISTINCT b.bom_code ORDER BY b.bom_code) as usage_details",
        "FROM bom_detail bd",
        "INNER JOIN bom_master b ON bd.bom_id = b.id AND b.deleted = 0",
        "WHERE bd.material_code = #{materialCode} AND bd.deleted = 0",
        "UNION ALL",
        "SELECT ",
        "  'PRODUCTION' as usage_type,",
        "  COUNT(*) as usage_count,",
        "  CONCAT('最近', COUNT(*), '次生产使用') as usage_details",
        "FROM production_material_usage pmu",
        "WHERE pmu.material_code = #{materialCode} AND pmu.deleted = 0",
        "  AND pmu.usage_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)",
        "UNION ALL",
        "SELECT ",
        "  'PURCHASE' as usage_type,",
        "  COUNT(*) as usage_count,",
        "  CONCAT('最近', COUNT(*), '次采购') as usage_details",
        "FROM purchase_order_detail pod",
        "INNER JOIN purchase_order po ON pod.order_id = po.id AND po.deleted = 0",
        "WHERE pod.material_code = #{materialCode} AND pod.deleted = 0",
        "  AND po.order_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)"
    })
    List<Map<String, Object>> selectMaterialUsageInfo(@Param("materialCode") String materialCode);

    /**
     * 检查物料编码是否存在
     * 
     * @param materialCode 物料编码
     * @param excludeId 排除的ID
     * @return 存在数量
     */
    @Select({
        "<script>",
        "SELECT COUNT(*) FROM material_master ",
        "WHERE material_code = #{materialCode} AND deleted = 0",
        "<if test='excludeId != null'>",
        "  AND id != #{excludeId}",
        "</if>",
        "</script>"
    })
    int checkMaterialCodeExists(@Param("materialCode") String materialCode, @Param("excludeId") Long excludeId);

    /**
     * 查询物料编码建议
     * 
     * @param materialType 物料类型
     * @param materialCategory 物料分类
     * @return 建议编码
     */
    @Select({
        "SELECT ",
        "  CASE ",
        "    WHEN #{materialType} = 'RAW' THEN CONCAT('R', DATE_FORMAT(NOW(), '%y%m'), LPAD(IFNULL(MAX(CAST(SUBSTRING(material_code, 6) AS UNSIGNED)), 0) + 1, 4, '0'))",
        "    WHEN #{materialType} = 'PURCHASE' THEN CONCAT('P', DATE_FORMAT(NOW(), '%y%m'), LPAD(IFNULL(MAX(CAST(SUBSTRING(material_code, 6) AS UNSIGNED)), 0) + 1, 4, '0'))",
        "    WHEN #{materialType} = 'SEMI' THEN CONCAT('S', DATE_FORMAT(NOW(), '%y%m'), LPAD(IFNULL(MAX(CAST(SUBSTRING(material_code, 6) AS UNSIGNED)), 0) + 1, 4, '0'))",
        "    WHEN #{materialType} = 'PRODUCT' THEN CONCAT('F', DATE_FORMAT(NOW(), '%y%m'), LPAD(IFNULL(MAX(CAST(SUBSTRING(material_code, 6) AS UNSIGNED)), 0) + 1, 4, '0'))",
        "    ELSE CONCAT('M', DATE_FORMAT(NOW(), '%y%m'), LPAD(IFNULL(MAX(CAST(SUBSTRING(material_code, 6) AS UNSIGNED)), 0) + 1, 4, '0'))",
        "  END as suggested_code",
        "FROM material_master ",
        "WHERE material_type = #{materialType} ",
        "  AND material_code LIKE CONCAT(",
        "    CASE ",
        "      WHEN #{materialType} = 'RAW' THEN 'R'",
        "      WHEN #{materialType} = 'PURCHASE' THEN 'P'",
        "      WHEN #{materialType} = 'SEMI' THEN 'S'",
        "      WHEN #{materialType} = 'PRODUCT' THEN 'F'",
        "      ELSE 'M'",
        "    END,",
        "    DATE_FORMAT(NOW(), '%y%m'), '%'",
        "  )",
        "  AND deleted = 0"
    })
    String suggestMaterialCode(@Param("materialType") String materialType, @Param("materialCategory") String materialCategory);

    /**
     * 查询物料主数据完整信息
     * 
     * @param materialCode 物料编码
     * @return 完整信息
     */
    @Select({
        "SELECT m.*, ",
        "       s.supplier_name as main_supplier_name,",
        "       s.contact_person as supplier_contact,",
        "       s.contact_phone as supplier_phone,",
        "       COALESCE(stock.current_stock, 0) as current_stock,",
        "       COALESCE(stock.available_stock, 0) as available_stock,",
        "       COALESCE(stock.reserved_stock, 0) as reserved_stock,",
        "       COALESCE(stock.on_order_stock, 0) as on_order_stock,",
        "       COALESCE(usage.monthly_usage, 0) as monthly_usage,",
        "       COALESCE(usage.yearly_usage, 0) as yearly_usage",
        "FROM material_master m",
        "LEFT JOIN supplier_master s ON m.main_supplier_code = s.supplier_code AND s.deleted = 0",
        "LEFT JOIN (",
        "  SELECT material_code, ",
        "         SUM(current_qty) as current_stock,",
        "         SUM(available_qty) as available_stock,",
        "         SUM(reserved_qty) as reserved_stock,",
        "         SUM(on_order_qty) as on_order_stock",
        "  FROM inventory_stock WHERE deleted = 0",
        "  GROUP BY material_code",
        ") stock ON m.material_code = stock.material_code",
        "LEFT JOIN (",
        "  SELECT material_code,",
        "         SUM(CASE WHEN transaction_date >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH) THEN ABS(transaction_qty) ELSE 0 END) as monthly_usage,",
        "         SUM(CASE WHEN transaction_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH) THEN ABS(transaction_qty) ELSE 0 END) as yearly_usage",
        "  FROM material_transaction ",
        "  WHERE deleted = 0 AND transaction_type IN ('ISSUE', 'CONSUME')",
        "  GROUP BY material_code",
        ") usage ON m.material_code = usage.material_code",
        "WHERE m.material_code = #{materialCode} AND m.deleted = 0"
    })
    MaterialEntity selectMaterialFullInfo(@Param("materialCode") String materialCode);
}