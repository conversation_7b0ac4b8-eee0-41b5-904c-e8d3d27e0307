package com.yinma.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinma.entity.Manufacturing;
import com.yinma.dto.ManufacturingDTO;
import com.yinma.vo.ManufacturingVO;
import com.yinma.mapper.ManufacturingMapper;
import com.yinma.service.ManufacturingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 设备制造管理服务实现类
 * 银马实业设备制造管理系统
 * 
 * <AUTHOR>
 * @since 2024-05-20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ManufacturingServiceImpl implements ManufacturingService {

    private final ManufacturingMapper manufacturingMapper;

    @Override
    public IPage<ManufacturingVO> getManufacturingPage(Page<Manufacturing> page, String orderCode, 
                                                      String productName, String productSeries, String status, 
                                                      String customerName, String planStartDate, String planEndDate) {
        QueryWrapper<Manufacturing> queryWrapper = new QueryWrapper<>();
        
        if (StringUtils.hasText(orderCode)) {
            queryWrapper.like("order_code", orderCode);
        }
        if (StringUtils.hasText(productName)) {
            queryWrapper.like("product_name", productName);
        }
        if (StringUtils.hasText(productSeries)) {
            queryWrapper.eq("product_series", productSeries);
        }
        if (StringUtils.hasText(status)) {
            queryWrapper.eq("status", status);
        }
        if (StringUtils.hasText(customerName)) {
            queryWrapper.like("customer_name", customerName);
        }
        if (StringUtils.hasText(planStartDate)) {
            queryWrapper.ge("plan_start_date", planStartDate);
        }
        if (StringUtils.hasText(planEndDate)) {
            queryWrapper.le("plan_end_date", planEndDate);
        }
        
        queryWrapper.orderByDesc("create_time");
        
        IPage<Manufacturing> manufacturingPage = manufacturingMapper.selectPage(page, queryWrapper);
        
        // 转换为VO
        IPage<ManufacturingVO> result = new Page<>();
        BeanUtils.copyProperties(manufacturingPage, result);
        
        List<ManufacturingVO> voList = new ArrayList<>();
        for (Manufacturing manufacturing : manufacturingPage.getRecords()) {
            ManufacturingVO vo = new ManufacturingVO();
            BeanUtils.copyProperties(manufacturing, vo);
            voList.add(vo);
        }
        result.setRecords(voList);
        
        return result;
    }

    @Override
    public ManufacturingVO getManufacturingDetailById(Long id) {
        Manufacturing manufacturing = manufacturingMapper.selectById(id);
        if (manufacturing == null) {
            throw new RuntimeException("制造订单不存在");
        }
        
        ManufacturingVO vo = new ManufacturingVO();
        BeanUtils.copyProperties(manufacturing, vo);
        
        return vo;
    }

    @Override
    @Transactional
    public Manufacturing createManufacturing(ManufacturingDTO manufacturingDTO) {
        // 验证订单编号唯一性
        if (!validateOrderCode(manufacturingDTO.getOrderCode(), null)) {
            throw new RuntimeException("订单编号已存在");
        }
        
        Manufacturing manufacturing = new Manufacturing();
        BeanUtils.copyProperties(manufacturingDTO, manufacturing);
        
        manufacturing.setCreateTime(LocalDateTime.now());
        manufacturing.setUpdateTime(LocalDateTime.now());
        manufacturing.setStatus("待开始");
        
        manufacturingMapper.insert(manufacturing);
        
        log.info("创建制造订单成功，订单编号：{}", manufacturing.getOrderCode());
        return manufacturing;
    }

    @Override
    @Transactional
    public Manufacturing updateManufacturing(Long id, ManufacturingDTO manufacturingDTO) {
        Manufacturing existing = manufacturingMapper.selectById(id);
        if (existing == null) {
            throw new RuntimeException("制造订单不存在");
        }
        
        // 验证订单编号唯一性（排除当前记录）
        if (!validateOrderCode(manufacturingDTO.getOrderCode(), id)) {
            throw new RuntimeException("订单编号已存在");
        }
        
        BeanUtils.copyProperties(manufacturingDTO, existing, "id", "createTime");
        existing.setUpdateTime(LocalDateTime.now());
        
        manufacturingMapper.updateById(existing);
        
        log.info("更新制造订单成功，订单编号：{}", existing.getOrderCode());
        return existing;
    }

    @Override
    @Transactional
    public boolean deleteManufacturing(Long id) {
        Manufacturing manufacturing = manufacturingMapper.selectById(id);
        if (manufacturing == null) {
            throw new RuntimeException("制造订单不存在");
        }
        
        // 检查订单状态，只有待开始和已取消的订单可以删除
        if (!"待开始".equals(manufacturing.getStatus()) && !"已取消".equals(manufacturing.getStatus())) {
            throw new RuntimeException("只有待开始和已取消的订单可以删除");
        }
        
        int result = manufacturingMapper.deleteById(id);
        
        log.info("删除制造订单成功，订单编号：{}", manufacturing.getOrderCode());
        return result > 0;
    }

    @Override
    @Transactional
    public boolean batchDeleteManufacturing(List<Long> ids) {
        for (Long id : ids) {
            deleteManufacturing(id);
        }
        return true;
    }

    @Override
    @Transactional
    public Manufacturing updateManufacturingStatus(Long id, String status, String remark) {
        Manufacturing manufacturing = manufacturingMapper.selectById(id);
        if (manufacturing == null) {
            throw new RuntimeException("制造订单不存在");
        }
        
        manufacturing.setStatus(status);
        manufacturing.setRemark(remark);
        manufacturing.setUpdateTime(LocalDateTime.now());
        
        manufacturingMapper.updateById(manufacturing);
        
        log.info("更新制造订单状态成功，订单编号：{}，新状态：{}", manufacturing.getOrderCode(), status);
        return manufacturing;
    }

    @Override
    @Transactional
    public Manufacturing copyManufacturing(Long id, String newOrderCode) {
        Manufacturing original = manufacturingMapper.selectById(id);
        if (original == null) {
            throw new RuntimeException("原制造订单不存在");
        }
        
        // 验证新订单编号唯一性
        if (!validateOrderCode(newOrderCode, null)) {
            throw new RuntimeException("新订单编号已存在");
        }
        
        Manufacturing copy = new Manufacturing();
        BeanUtils.copyProperties(original, copy, "id", "orderCode", "createTime", "updateTime");
        
        copy.setOrderCode(newOrderCode);
        copy.setStatus("待开始");
        copy.setCreateTime(LocalDateTime.now());
        copy.setUpdateTime(LocalDateTime.now());
        
        manufacturingMapper.insert(copy);
        
        log.info("复制制造订单成功，原订单编号：{}，新订单编号：{}", original.getOrderCode(), newOrderCode);
        return copy;
    }

    @Override
    public List<Map<String, Object>> getManufacturingBOM(Long id) {
        // 模拟BOM数据
        List<Map<String, Object>> bomList = new ArrayList<>();
        
        Map<String, Object> bom1 = new HashMap<>();
        bom1.put("id", 1);
        bom1.put("componentCode", "YM-2025-001");
        bom1.put("componentName", "压振主机");
        bom1.put("specification", "2025型");
        bom1.put("quantity", 1);
        bom1.put("unit", "台");
        bom1.put("unitPrice", 150000.00);
        bom1.put("totalPrice", 150000.00);
        bomList.add(bom1);
        
        Map<String, Object> bom2 = new HashMap<>();
        bom2.put("id", 2);
        bom2.put("componentCode", "YM-2025-002");
        bom2.put("componentName", "振动系统");
        bom2.put("specification", "高频振动");
        bom2.put("quantity", 1);
        bom2.put("unit", "套");
        bom2.put("unitPrice", 80000.00);
        bom2.put("totalPrice", 80000.00);
        bomList.add(bom2);
        
        return bomList;
    }

    @Override
    @Transactional
    public void updateManufacturingBOM(Long id, List<Map<String, Object>> bomList) {
        // 实际实现中应该更新数据库中的BOM数据
        log.info("更新制造订单BOM清单，订单ID：{}", id);
    }

    @Override
    public List<Map<String, Object>> getManufacturingProcess(Long id) {
        // 模拟工艺流程数据
        List<Map<String, Object>> processList = new ArrayList<>();
        
        Map<String, Object> process1 = new HashMap<>();
        process1.put("id", 1);
        process1.put("processCode", "P001");
        process1.put("processName", "原料配比");
        process1.put("description", "按配方进行原料配比");
        process1.put("duration", 2);
        process1.put("unit", "小时");
        process1.put("status", "已完成");
        processList.add(process1);
        
        Map<String, Object> process2 = new HashMap<>();
        process2.put("id", 2);
        process2.put("processCode", "P002");
        process2.put("processName", "压制成型");
        process2.put("description", "使用压振一体工艺进行成型");
        process2.put("duration", 4);
        process2.put("unit", "小时");
        process2.put("status", "进行中");
        processList.add(process2);
        
        return processList;
    }

    @Override
    @Transactional
    public void updateManufacturingProcess(Long id, List<Map<String, Object>> processList) {
        log.info("更新制造工艺流程，订单ID：{}", id);
    }

    @Override
    public Map<String, Object> getManufacturingProgress(Long id) {
        Map<String, Object> progress = new HashMap<>();
        progress.put("totalProgress", 65);
        progress.put("currentStage", "压制成型");
        progress.put("completedStages", 3);
        progress.put("totalStages", 8);
        progress.put("estimatedCompletion", "2024-06-15");
        return progress;
    }

    @Override
    @Transactional
    public void updateManufacturingProgress(Long id, Map<String, Object> progressInfo) {
        log.info("更新制造进度，订单ID：{}", id);
    }

    @Override
    public List<Map<String, Object>> getManufacturingQuality(Long id) {
        List<Map<String, Object>> qualityList = new ArrayList<>();
        
        Map<String, Object> quality1 = new HashMap<>();
        quality1.put("id", 1);
        quality1.put("checkTime", "2024-05-20 10:30:00");
        quality1.put("checkItem", "尺寸检测");
        quality1.put("standard", "±0.5mm");
        quality1.put("actualValue", "0.2mm");
        quality1.put("result", "合格");
        quality1.put("checker", "张工");
        qualityList.add(quality1);
        
        return qualityList;
    }

    @Override
    @Transactional
    public void addManufacturingQuality(Long id, Map<String, Object> qualityRecord) {
        log.info("添加质量检测记录，订单ID：{}", id);
    }

    @Override
    public List<Map<String, Object>> getManufacturingMaterials(Long id) {
        List<Map<String, Object>> materialsList = new ArrayList<>();
        
        Map<String, Object> material1 = new HashMap<>();
        material1.put("id", 1);
        material1.put("materialCode", "M001");
        material1.put("materialName", "水泥");
        material1.put("consumedQuantity", 500);
        material1.put("unit", "kg");
        material1.put("consumeTime", "2024-05-20 09:00:00");
        material1.put("operator", "李师傅");
        materialsList.add(material1);
        
        return materialsList;
    }

    @Override
    @Transactional
    public void recordManufacturingMaterials(Long id, Map<String, Object> materialRecord) {
        log.info("记录物料消耗，订单ID：{}", id);
    }

    @Override
    public List<Map<String, Object>> getManufacturingWorkers(Long id) {
        List<Map<String, Object>> workersList = new ArrayList<>();
        
        Map<String, Object> worker1 = new HashMap<>();
        worker1.put("id", 1);
        worker1.put("workerName", "王师傅");
        worker1.put("workDate", "2024-05-20");
        worker1.put("workHours", 8);
        worker1.put("workType", "设备操作");
        worker1.put("hourlyRate", 50.0);
        worker1.put("totalCost", 400.0);
        workersList.add(worker1);
        
        return workersList;
    }

    @Override
    @Transactional
    public void recordManufacturingWorkers(Long id, Map<String, Object> workerRecord) {
        log.info("记录工人工时，订单ID：{}", id);
    }

    @Override
    public List<Map<String, Object>> getManufacturingEquipment(Long id) {
        List<Map<String, Object>> equipmentList = new ArrayList<>();
        
        Map<String, Object> equipment1 = new HashMap<>();
        equipment1.put("id", 1);
        equipment1.put("equipmentCode", "E001");
        equipment1.put("equipmentName", "银马2025压振全能机");
        equipment1.put("useDate", "2024-05-20");
        equipment1.put("useHours", 6);
        equipment1.put("operator", "张师傅");
        equipment1.put("status", "正常");
        equipmentList.add(equipment1);
        
        return equipmentList;
    }

    @Override
    @Transactional
    public void recordManufacturingEquipment(Long id, Map<String, Object> equipmentRecord) {
        log.info("记录设备使用，订单ID：{}", id);
    }

    @Override
    public Map<String, Object> getManufacturingCost(Long id) {
        Map<String, Object> cost = new HashMap<>();
        cost.put("materialCost", 50000.0);
        cost.put("laborCost", 15000.0);
        cost.put("equipmentCost", 8000.0);
        cost.put("overheadCost", 5000.0);
        cost.put("totalCost", 78000.0);
        cost.put("budgetCost", 80000.0);
        cost.put("variance", 2000.0);
        return cost;
    }

    @Override
    @Transactional
    public void updateManufacturingCost(Long id, Map<String, Object> costInfo) {
        log.info("更新制造成本，订单ID：{}", id);
    }

    @Override
    public String uploadManufacturingImage(Long id, MultipartFile file, String imageType) {
        // 实际实现中应该上传文件到文件服务器
        String imageUrl = "/uploads/manufacturing/" + id + "/" + file.getOriginalFilename();
        log.info("上传制造过程图片，订单ID：{}，图片类型：{}", id, imageType);
        return imageUrl;
    }

    @Override
    public List<Map<String, Object>> getManufacturingImages(Long id) {
        List<Map<String, Object>> imagesList = new ArrayList<>();
        
        Map<String, Object> image1 = new HashMap<>();
        image1.put("id", 1);
        image1.put("imageUrl", "/uploads/manufacturing/1/process1.jpg");
        image1.put("imageType", "生产过程");
        image1.put("uploadTime", "2024-05-20 14:30:00");
        image1.put("uploader", "张工");
        imagesList.add(image1);
        
        return imagesList;
    }

    @Override
    @Transactional
    public boolean deleteManufacturingImage(Long id, Long imageId) {
        log.info("删除制造过程图片，订单ID：{}，图片ID：{}", id, imageId);
        return true;
    }

    @Override
    public String uploadManufacturingDocument(Long id, MultipartFile file, String docType) {
        String docUrl = "/uploads/manufacturing/" + id + "/docs/" + file.getOriginalFilename();
        log.info("上传制造文档，订单ID：{}，文档类型：{}", id, docType);
        return docUrl;
    }

    @Override
    public List<Map<String, Object>> getManufacturingDocuments(Long id) {
        List<Map<String, Object>> docsList = new ArrayList<>();
        
        Map<String, Object> doc1 = new HashMap<>();
        doc1.put("id", 1);
        doc1.put("docUrl", "/uploads/manufacturing/1/docs/工艺文件.pdf");
        doc1.put("docType", "工艺文件");
        doc1.put("docName", "工艺文件.pdf");
        doc1.put("uploadTime", "2024-05-20 09:00:00");
        doc1.put("uploader", "技术部");
        docsList.add(doc1);
        
        return docsList;
    }

    @Override
    @Transactional
    public boolean deleteManufacturingDocument(Long id, Long docId) {
        log.info("删除制造文档，订单ID：{}，文档ID：{}", id, docId);
        return true;
    }

    @Override
    public String exportManufacturing(String orderCode, String productName, String status, String format) {
        String exportUrl = "/exports/manufacturing_" + System.currentTimeMillis() + "." + format;
        log.info("导出制造订单数据，格式：{}", format);
        return exportUrl;
    }

    @Override
    @Transactional
    public Map<String, Object> importManufacturing(MultipartFile file) {
        Map<String, Object> result = new HashMap<>();
        result.put("totalCount", 10);
        result.put("successCount", 8);
        result.put("failCount", 2);
        result.put("message", "导入完成");
        log.info("导入制造订单数据，文件：{}", file.getOriginalFilename());
        return result;
    }

    @Override
    public Map<String, Object> getManufacturingStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalOrders", 156);
        statistics.put("inProgressOrders", 23);
        statistics.put("completedOrders", 128);
        statistics.put("cancelledOrders", 5);
        statistics.put("totalValue", 15600000.0);
        statistics.put("avgCompletionTime", 15.5);
        return statistics;
    }

    @Override
    public List<Map<String, Object>> getManufacturingOptions(String productSeries, String status) {
        List<Map<String, Object>> options = new ArrayList<>();
        
        Map<String, Object> option1 = new HashMap<>();
        option1.put("value", "YM-2025");
        option1.put("label", "银马2025系列");
        options.add(option1);
        
        Map<String, Object> option2 = new HashMap<>();
        option2.put("value", "YM-ELSA");
        option2.put("label", "爱尔莎系列");
        options.add(option2);
        
        return options;
    }

    @Override
    public boolean validateOrderCode(String orderCode, Long excludeId) {
        QueryWrapper<Manufacturing> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_code", orderCode);
        if (excludeId != null) {
            queryWrapper.ne("id", excludeId);
        }
        
        Long count = manufacturingMapper.selectCount(queryWrapper);
        return count == 0;
    }

    @Override
    public List<Map<String, Object>> getManufacturingTimeline(Long id) {
        List<Map<String, Object>> timeline = new ArrayList<>();
        
        Map<String, Object> event1 = new HashMap<>();
        event1.put("id", 1);
        event1.put("eventTime", "2024-05-20 09:00:00");
        event1.put("eventType", "订单创建");
        event1.put("description", "制造订单创建成功");
        event1.put("operator", "系统");
        timeline.add(event1);
        
        Map<String, Object> event2 = new HashMap<>();
        event2.put("id", 2);
        event2.put("eventTime", "2024-05-20 10:00:00");
        event2.put("eventType", "生产开始");
        event2.put("description", "开始原料配比工序");
        event2.put("operator", "李师傅");
        timeline.add(event2);
        
        return timeline;
    }

    @Override
    @Transactional
    public void addManufacturingTimelineEvent(Long id, Map<String, Object> timelineEvent) {
        log.info("添加制造时间线事件，订单ID：{}", id);
    }

    @Override
    public List<Map<String, Object>> getManufacturingAlerts(Long id) {
        List<Map<String, Object>> alerts = new ArrayList<>();
        
        Map<String, Object> alert1 = new HashMap<>();
        alert1.put("id", 1);
        alert1.put("alertType", "延期预警");
        alert1.put("alertLevel", "中");
        alert1.put("alertMessage", "当前进度可能导致延期交付");
        alert1.put("alertTime", "2024-05-20 15:00:00");
        alert1.put("status", "未处理");
        alerts.add(alert1);
        
        return alerts;
    }

    @Override
    @Transactional
    public void createManufacturingAlert(Long id, Map<String, Object> alertInfo) {
        log.info("创建制造预警，订单ID：{}", id);
    }

    @Override
    @Transactional
    public void handleManufacturingAlert(Long id, Long alertId, Map<String, Object> handleInfo) {
        log.info("处理制造预警，订单ID：{}，预警ID：{}", id, alertId);
    }

    @Override
    public Map<String, Object> getManufacturingPerformance(Long id) {
        Map<String, Object> performance = new HashMap<>();
        performance.put("efficiency", 85.5);
        performance.put("qualityRate", 98.2);
        performance.put("onTimeRate", 92.0);
        performance.put("costVariance", -2.5);
        return performance;
    }

    @Override
    public Map<String, Object> getManufacturingEfficiency(Long id) {
        Map<String, Object> efficiency = new HashMap<>();
        efficiency.put("plannedHours", 120);
        efficiency.put("actualHours", 108);
        efficiency.put("efficiencyRate", 90.0);
        efficiency.put("bottleneckProcess", "质量检测");
        return efficiency;
    }

    @Override
    public Map<String, Object> getManufacturingSchedule(Long id) {
        Map<String, Object> schedule = new HashMap<>();
        schedule.put("planStartDate", "2024-05-20");
        schedule.put("planEndDate", "2024-06-15");
        schedule.put("actualStartDate", "2024-05-20");
        schedule.put("estimatedEndDate", "2024-06-18");
        schedule.put("delayDays", 3);
        return schedule;
    }

    @Override
    @Transactional
    public void updateManufacturingSchedule(Long id, Map<String, Object> scheduleInfo) {
        log.info("更新制造排程，订单ID：{}", id);
    }

    @Override
    public Map<String, Object> getManufacturingResources(Long id) {
        Map<String, Object> resources = new HashMap<>();
        resources.put("assignedWorkers", 8);
        resources.put("assignedEquipment", 3);
        resources.put("allocatedMaterials", "已分配");
        resources.put("resourceUtilization", 75.0);
        return resources;
    }

    @Override
    @Transactional
    public void updateManufacturingResources(Long id, Map<String, Object> resourcesInfo) {
        log.info("更新制造资源配置，订单ID：{}", id);
    }

    @Override
    @Transactional
    public void startManufacturing(Long id, Map<String, Object> startInfo) {
        updateManufacturingStatus(id, "进行中", "生产开始");
        log.info("启动制造订单，订单ID：{}", id);
    }

    @Override
    @Transactional
    public void pauseManufacturing(Long id, String reason) {
        updateManufacturingStatus(id, "暂停", reason);
        log.info("暂停制造订单，订单ID：{}，原因：{}", id, reason);
    }

    @Override
    @Transactional
    public void resumeManufacturing(Long id, Map<String, Object> resumeInfo) {
        updateManufacturingStatus(id, "进行中", "生产恢复");
        log.info("恢复制造订单，订单ID：{}", id);
    }

    @Override
    @Transactional
    public void completeManufacturing(Long id, Map<String, Object> completeInfo) {
        updateManufacturingStatus(id, "已完成", "生产完成");
        log.info("完成制造订单，订单ID：{}", id);
    }

    @Override
    @Transactional
    public void cancelManufacturing(Long id, String reason) {
        updateManufacturingStatus(id, "已取消", reason);
        log.info("取消制造订单，订单ID：{}，原因：{}", id, reason);
    }

    @Override
    public String generateManufacturingQRCode(Long id) {
        String qrCodeUrl = "/qrcodes/manufacturing_" + id + ".png";
        log.info("生成制造订单二维码，订单ID：{}", id);
        return qrCodeUrl;
    }

    @Override
    public String generateManufacturingReport(Long id, String reportType) {
        String reportUrl = "/reports/manufacturing_" + id + "_" + reportType + ".pdf";
        log.info("生成制造报告，订单ID：{}，报告类型：{}", id, reportType);
        return reportUrl;
    }

    @Override
    public Map<String, Object> getManufacturingDashboard(String timeRange) {
        Map<String, Object> dashboard = new HashMap<>();
        dashboard.put("totalOrders", 156);
        dashboard.put("inProgressOrders", 23);
        dashboard.put("completedToday", 5);
        dashboard.put("delayedOrders", 3);
        dashboard.put("productionEfficiency", 88.5);
        dashboard.put("qualityRate", 97.8);
        return dashboard;
    }

    @Override
    public Map<String, Object> getManufacturingCapacity(String startDate, String endDate) {
        Map<String, Object> capacity = new HashMap<>();
        capacity.put("totalCapacity", 1000);
        capacity.put("usedCapacity", 750);
        capacity.put("availableCapacity", 250);
        capacity.put("utilizationRate", 75.0);
        return capacity;
    }

    @Override
    public Map<String, Object> getManufacturingWorkload(String department, String timeRange) {
        Map<String, Object> workload = new HashMap<>();
        workload.put("totalWorkload", 800);
        workload.put("completedWorkload", 600);
        workload.put("remainingWorkload", 200);
        workload.put("workloadRate", 75.0);
        return workload;
    }

    @Override
    public Map<String, Object> getManufacturingTrends(String metricType, String timeRange) {
        Map<String, Object> trends = new HashMap<>();
        trends.put("trendData", Arrays.asList(85, 87, 89, 88, 90, 92, 88));
        trends.put("trendDirection", "上升");
        trends.put("avgValue", 88.4);
        trends.put("maxValue", 92);
        trends.put("minValue", 85);
        return trends;
    }
}