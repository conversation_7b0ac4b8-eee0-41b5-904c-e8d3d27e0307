package com.yinma.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinma.dto.BomChangeLogDTO;
import com.yinma.entity.BomChangeLogEntity;
import com.yinma.entity.BomEntity;
import com.yinma.mapper.BomChangeLogMapper;
import com.yinma.mapper.BomMapper;
import com.yinma.service.BomChangeLogService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * BOM变更日志管理Service实现类
 * 
 * <AUTHOR>
 * @since 2024-01-20
 */
@Service
public class BomChangeLogServiceImpl extends ServiceImpl<BomChangeLogMapper, BomChangeLogEntity> implements BomChangeLogService {

    @Autowired
    private BomChangeLogMapper bomChangeLogMapper;
    
    @Autowired
    private BomMapper bomMapper;

    @Override
    public IPage<BomChangeLogDTO> queryPage(Page<BomChangeLogEntity> page, BomChangeLogDTO.BomChangeLogQueryDTO queryDTO) {
        IPage<BomChangeLogEntity> entityPage = bomChangeLogMapper.selectPageWithBom(page, queryDTO);
        
        IPage<BomChangeLogDTO> dtoPage = new Page<>();
        BeanUtils.copyProperties(entityPage, dtoPage);
        
        List<BomChangeLogDTO> dtoList = convertToChangeLogDTOList(entityPage.getRecords());
        dtoPage.setRecords(dtoList);
        
        return dtoPage;
    }

    @Override
    public BomChangeLogDTO getChangeLogDetail(Long logId) {
        BomChangeLogEntity changeLog = bomChangeLogMapper.selectDetailWithBom(logId);
        if (changeLog == null) {
            return null;
        }
        return convertToChangeLogDTO(changeLog);
    }

    @Override
    public List<BomChangeLogDTO> getChangeHistory(Long bomId) {
        List<BomChangeLogEntity> changeHistory = bomChangeLogMapper.selectChangeHistory(bomId);
        return convertToChangeLogDTOList(changeHistory);
    }

    @Override
    public List<BomChangeLogDTO.AffectedBomDTO> getAffectedBomList(Long logId) {
        return bomChangeLogMapper.selectAffectedBomList(logId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomChangeLogDTO createChangeRequest(BomChangeLogDTO.ChangeRequestDTO requestDTO) {
        // 参数校验
        validateChangeRequest(requestDTO);
        
        // 检查BOM是否存在
        BomEntity bom = bomMapper.selectById(requestDTO.getBomId());
        if (bom == null) {
            throw new RuntimeException("BOM不存在");
        }
        
        BomChangeLogEntity changeLog = new BomChangeLogEntity();
        changeLog.setBomId(requestDTO.getBomId());
        changeLog.setChangeType(requestDTO.getChangeType());
        changeLog.setChangeOperation(requestDTO.getChangeOperation());
        changeLog.setChangeObjectType(requestDTO.getChangeObjectType());
        changeLog.setChangeReason(requestDTO.getChangeReason());
        changeLog.setChangeDescription(requestDTO.getChangeDescription());
        changeLog.setChangeContent(requestDTO.getChangeContent());
        changeLog.setApprovalStatus("PENDING"); // 待审批
        changeLog.setExecutionStatus("NOT_EXECUTED"); // 未执行
        changeLog.setCreateTime(LocalDateTime.now());
        changeLog.setUpdateTime(LocalDateTime.now());
        changeLog.setCreateUserId(requestDTO.getCreateUserId());
        changeLog.setUpdateUserId(requestDTO.getCreateUserId());
        
        bomChangeLogMapper.insert(changeLog);
        
        return getChangeLogDetail(changeLog.getLogId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomChangeLogDTO submitChangeApplication(BomChangeLogDTO changeLogDTO) {
        // 参数校验
        validateChangeApplication(changeLogDTO);
        
        BomChangeLogEntity changeLog = bomChangeLogMapper.selectById(changeLogDTO.getLogId());
        if (changeLog == null) {
            throw new RuntimeException("变更日志不存在");
        }
        
        if (!"PENDING".equals(changeLog.getApprovalStatus())) {
            throw new RuntimeException("只有待审批状态的变更才能提交申请");
        }
        
        // 更新变更信息
        BeanUtils.copyProperties(changeLogDTO, changeLog);
        changeLog.setApprovalStatus("SUBMITTED"); // 已提交
        changeLog.setSubmitTime(LocalDateTime.now());
        changeLog.setUpdateTime(LocalDateTime.now());
        
        bomChangeLogMapper.updateById(changeLog);
        
        return getChangeLogDetail(changeLog.getLogId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomChangeLogDTO approveChangeApplication(Long logId, Boolean approved, String comment, Long userId) {
        BomChangeLogEntity changeLog = bomChangeLogMapper.selectById(logId);
        if (changeLog == null) {
            throw new RuntimeException("变更日志不存在");
        }
        
        if (!"SUBMITTED".equals(changeLog.getApprovalStatus())) {
            throw new RuntimeException("只有已提交状态的变更才能审批");
        }
        
        changeLog.setApprovalStatus(approved ? "APPROVED" : "REJECTED");
        changeLog.setApprovalComment(comment);
        changeLog.setApprovalTime(LocalDateTime.now());
        changeLog.setApprovalUserId(userId);
        changeLog.setUpdateTime(LocalDateTime.now());
        changeLog.setUpdateUserId(userId);
        
        bomChangeLogMapper.updateById(changeLog);
        
        return getChangeLogDetail(changeLog.getLogId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomChangeLogDTO executeChange(Long logId, Long userId) {
        BomChangeLogEntity changeLog = bomChangeLogMapper.selectById(logId);
        if (changeLog == null) {
            throw new RuntimeException("变更日志不存在");
        }
        
        if (!"APPROVED".equals(changeLog.getApprovalStatus())) {
            throw new RuntimeException("只有已审批通过的变更才能执行");
        }
        
        if ("EXECUTED".equals(changeLog.getExecutionStatus())) {
            throw new RuntimeException("变更已执行，不能重复执行");
        }
        
        try {
            // 执行具体的变更操作
            executeChangeOperation(changeLog);
            
            // 更新执行状态
            changeLog.setExecutionStatus("EXECUTED");
            changeLog.setExecutionTime(LocalDateTime.now());
            changeLog.setExecutionUserId(userId);
            changeLog.setUpdateTime(LocalDateTime.now());
            changeLog.setUpdateUserId(userId);
            
            bomChangeLogMapper.updateById(changeLog);
            
        } catch (Exception e) {
            // 执行失败
            changeLog.setExecutionStatus("FAILED");
            changeLog.setExecutionComment(e.getMessage());
            changeLog.setUpdateTime(LocalDateTime.now());
            changeLog.setUpdateUserId(userId);
            
            bomChangeLogMapper.updateById(changeLog);
            
            throw new RuntimeException("变更执行失败: " + e.getMessage());
        }
        
        return getChangeLogDetail(changeLog.getLogId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomChangeLogDTO revokeChange(Long logId, String reason, Long userId) {
        BomChangeLogEntity changeLog = bomChangeLogMapper.selectById(logId);
        if (changeLog == null) {
            throw new RuntimeException("变更日志不存在");
        }
        
        if (!"EXECUTED".equals(changeLog.getExecutionStatus())) {
            throw new RuntimeException("只有已执行的变更才能撤销");
        }
        
        try {
            // 执行撤销操作
            revokeChangeOperation(changeLog);
            
            // 更新状态
            changeLog.setExecutionStatus("REVOKED");
            changeLog.setRevokeReason(reason);
            changeLog.setRevokeTime(LocalDateTime.now());
            changeLog.setRevokeUserId(userId);
            changeLog.setUpdateTime(LocalDateTime.now());
            changeLog.setUpdateUserId(userId);
            
            bomChangeLogMapper.updateById(changeLog);
            
        } catch (Exception e) {
            throw new RuntimeException("变更撤销失败: " + e.getMessage());
        }
        
        return getChangeLogDetail(changeLog.getLogId());
    }

    @Override
    public BomChangeLogDTO.ChangeImpactAnalysisDTO analyzeChangeImpact(Long bomId, String changeType, String changeContent) {
        // 分析变更影响
        BomChangeLogDTO.ChangeImpactAnalysisDTO analysis = new BomChangeLogDTO.ChangeImpactAnalysisDTO();
        
        // 查找受影响的BOM
        List<BomChangeLogDTO.AffectedBomDTO> affectedBoms = bomChangeLogMapper.selectAffectedBomsByChange(bomId, changeType);
        analysis.setAffectedBomList(affectedBoms);
        
        // 计算影响范围
        analysis.setImpactScope(calculateImpactScope(affectedBoms));
        
        // 评估风险等级
        analysis.setRiskLevel(assessRiskLevel(changeType, affectedBoms.size()));
        
        // 生成建议
        analysis.setRecommendations(generateRecommendations(changeType, affectedBoms));
        
        return analysis;
    }

    @Override
    public BomChangeLogDTO.ChangeStatisticsDTO getChangeStatistics(BomChangeLogDTO.BomChangeLogQueryDTO queryDTO) {
        return bomChangeLogMapper.selectChangeStatistics(queryDTO);
    }

    @Override
    public byte[] exportChangeLog(BomChangeLogDTO.BomChangeLogQueryDTO queryDTO) {
        // TODO: 实现导出功能
        throw new RuntimeException("导出功能待实现");
    }

    /**
     * 校验变更请求
     */
    private void validateChangeRequest(BomChangeLogDTO.ChangeRequestDTO requestDTO) {
        if (requestDTO.getBomId() == null) {
            throw new RuntimeException("BOM ID不能为空");
        }
        
        if (!StringUtils.hasText(requestDTO.getChangeType())) {
            throw new RuntimeException("变更类型不能为空");
        }
        
        if (!StringUtils.hasText(requestDTO.getChangeOperation())) {
            throw new RuntimeException("变更操作不能为空");
        }
        
        if (!StringUtils.hasText(requestDTO.getChangeReason())) {
            throw new RuntimeException("变更原因不能为空");
        }
        
        if (requestDTO.getCreateUserId() == null) {
            throw new RuntimeException("创建用户ID不能为空");
        }
    }

    /**
     * 校验变更申请
     */
    private void validateChangeApplication(BomChangeLogDTO changeLogDTO) {
        if (changeLogDTO.getLogId() == null) {
            throw new RuntimeException("变更日志ID不能为空");
        }
        
        if (!StringUtils.hasText(changeLogDTO.getChangeDescription())) {
            throw new RuntimeException("变更描述不能为空");
        }
    }

    /**
     * 执行变更操作
     */
    private void executeChangeOperation(BomChangeLogEntity changeLog) {
        String changeType = changeLog.getChangeType();
        String changeOperation = changeLog.getChangeOperation();
        
        switch (changeType) {
            case "STRUCTURE":
                executeStructureChange(changeLog);
                break;
            case "MATERIAL":
                executeMaterialChange(changeLog);
                break;
            case "QUANTITY":
                executeQuantityChange(changeLog);
                break;
            case "ATTRIBUTE":
                executeAttributeChange(changeLog);
                break;
            default:
                throw new RuntimeException("不支持的变更类型: " + changeType);
        }
    }

    /**
     * 执行结构变更
     */
    private void executeStructureChange(BomChangeLogEntity changeLog) {
        // TODO: 实现结构变更逻辑
        // 根据changeContent解析具体的变更内容并执行
    }

    /**
     * 执行物料变更
     */
    private void executeMaterialChange(BomChangeLogEntity changeLog) {
        // TODO: 实现物料变更逻辑
    }

    /**
     * 执行数量变更
     */
    private void executeQuantityChange(BomChangeLogEntity changeLog) {
        // TODO: 实现数量变更逻辑
    }

    /**
     * 执行属性变更
     */
    private void executeAttributeChange(BomChangeLogEntity changeLog) {
        // TODO: 实现属性变更逻辑
    }

    /**
     * 撤销变更操作
     */
    private void revokeChangeOperation(BomChangeLogEntity changeLog) {
        // TODO: 实现撤销变更逻辑
        // 根据变更类型执行相反的操作
    }

    /**
     * 计算影响范围
     */
    private String calculateImpactScope(List<BomChangeLogDTO.AffectedBomDTO> affectedBoms) {
        if (CollectionUtils.isEmpty(affectedBoms)) {
            return "无影响";
        }
        
        int count = affectedBoms.size();
        if (count <= 5) {
            return "小范围影响";
        } else if (count <= 20) {
            return "中等范围影响";
        } else {
            return "大范围影响";
        }
    }

    /**
     * 评估风险等级
     */
    private String assessRiskLevel(String changeType, int affectedCount) {
        // 根据变更类型和影响范围评估风险
        if ("STRUCTURE".equals(changeType)) {
            return affectedCount > 10 ? "高风险" : "中风险";
        } else if ("MATERIAL".equals(changeType)) {
            return affectedCount > 20 ? "高风险" : "低风险";
        } else {
            return "低风险";
        }
    }

    /**
     * 生成建议
     */
    private List<String> generateRecommendations(String changeType, List<BomChangeLogDTO.AffectedBomDTO> affectedBoms) {
        List<String> recommendations = new ArrayList<>();
        
        if (!CollectionUtils.isEmpty(affectedBoms)) {
            recommendations.add("建议通知相关产品负责人");
            recommendations.add("建议在非生产时间执行变更");
            
            if (affectedBoms.size() > 10) {
                recommendations.add("建议分批执行变更以降低风险");
            }
        }
        
        if ("STRUCTURE".equals(changeType)) {
            recommendations.add("建议进行充分的测试验证");
        }
        
        return recommendations;
    }

    /**
     * 转换为DTO列表
     */
    private List<BomChangeLogDTO> convertToChangeLogDTOList(List<BomChangeLogEntity> changeLogList) {
        if (CollectionUtils.isEmpty(changeLogList)) {
            return new ArrayList<>();
        }
        
        return changeLogList.stream()
            .map(this::convertToChangeLogDTO)
            .collect(Collectors.toList());
    }

    /**
     * 转换为DTO
     */
    private BomChangeLogDTO convertToChangeLogDTO(BomChangeLogEntity changeLog) {
        BomChangeLogDTO dto = new BomChangeLogDTO();
        BeanUtils.copyProperties(changeLog, dto);
        
        // 设置关联BOM信息
        if (changeLog.getBomId() != null) {
            BomEntity bom = bomMapper.selectById(changeLog.getBomId());
            if (bom != null) {
                BomChangeLogDTO.BomInfoDTO bomInfo = new BomChangeLogDTO.BomInfoDTO();
                BeanUtils.copyProperties(bom, bomInfo);
                dto.setBomInfo(bomInfo);
            }
        }
        
        return dto;
    }
}