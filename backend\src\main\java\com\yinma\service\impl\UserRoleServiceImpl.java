package com.yinma.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinma.entity.UserRoleEntity;
import com.yinma.mapper.UserRoleMapper;
import com.yinma.service.UserRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户角色关联Service实现类
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserRoleServiceImpl extends ServiceImpl<UserRoleMapper, UserRoleEntity> implements UserRoleService {

    private final UserRoleMapper userRoleMapper;

    @Override
    public List<Long> selectRoleIdsByUserId(Long userId) {
        return userRoleMapper.selectRoleIdsByUserId(userId);
    }

    @Override
    public List<Long> selectUserIdsByRoleId(Long roleId) {
        return userRoleMapper.selectUserIdsByRoleId(roleId);
    }

    @Override
    public Boolean checkUserRoleExists(Long userId, Long roleId) {
        return userRoleMapper.checkUserRoleExists(userId, roleId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteUserRolesByUserId(Long userId) {
        int result = userRoleMapper.deleteUserRolesByUserId(userId);
        log.info("删除用户所有角色关联成功, userId: {}, 删除数量: {}", userId, result);
        return result >= 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteUserRolesByRoleId(Long roleId) {
        int result = userRoleMapper.deleteUserRolesByRoleId(roleId);
        log.info("删除角色所有用户关联成功, roleId: {}, 删除数量: {}", roleId, result);
        return result >= 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteUserRole(Long userId, Long roleId) {
        int result = userRoleMapper.deleteUserRole(userId, roleId);
        log.info("删除用户角色关联成功, userId: {}, roleId: {}", userId, roleId);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchInsertUserRoles(Long userId, List<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return false;
        }
        
        List<UserRoleEntity> entities = new ArrayList<>();
        for (Long roleId : roleIds) {
            // 检查关联是否已存在
            if (!checkUserRoleExists(userId, roleId)) {
                UserRoleEntity entity = new UserRoleEntity();
                entity.setUserId(userId);
                entity.setRoleId(roleId);
                entity.setCreateTime(LocalDateTime.now());
                entities.add(entity);
            }
        }
        
        if (CollectionUtils.isEmpty(entities)) {
            return true;
        }
        
        int result = userRoleMapper.batchInsertUserRoles(entities);
        log.info("批量插入用户角色关联成功, userId: {}, roleIds: {}, 插入数量: {}", userId, roleIds, result);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDeleteUserRoles(Long userId, List<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return false;
        }
        
        int result = userRoleMapper.batchDeleteUserRoles(userId, roleIds);
        log.info("批量删除用户角色关联成功, userId: {}, roleIds: {}, 删除数量: {}", userId, roleIds, result);
        return result >= 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchInsertRoleUsers(Long roleId, List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return false;
        }
        
        List<UserRoleEntity> entities = new ArrayList<>();
        for (Long userId : userIds) {
            // 检查关联是否已存在
            if (!checkUserRoleExists(userId, roleId)) {
                UserRoleEntity entity = new UserRoleEntity();
                entity.setUserId(userId);
                entity.setRoleId(roleId);
                entity.setCreateTime(LocalDateTime.now());
                entities.add(entity);
            }
        }
        
        if (CollectionUtils.isEmpty(entities)) {
            return true;
        }
        
        int result = userRoleMapper.batchInsertUserRoles(entities);
        log.info("批量插入角色用户关联成功, roleId: {}, userIds: {}, 插入数量: {}", roleId, userIds, result);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDeleteRoleUsers(Long roleId, List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return false;
        }
        
        int result = userRoleMapper.batchDeleteRoleUsers(roleId, userIds);
        log.info("批量删除角色用户关联成功, roleId: {}, userIds: {}, 删除数量: {}", roleId, userIds, result);
        return result >= 0;
    }

    @Override
    public Long selectUserRoleCount() {
        return userRoleMapper.selectUserRoleCount();
    }

    @Override
    public Long selectUserCountByRoleId(Long roleId) {
        return userRoleMapper.selectUserCountByRoleId(roleId);
    }

    @Override
    public Long selectRoleCountByUserId(Long userId) {
        return userRoleMapper.selectRoleCountByUserId(userId);
    }

    @Override
    public Long selectUsersWithoutRoleCount() {
        return userRoleMapper.selectUsersWithoutRoleCount();
    }

    @Override
    public Long selectRolesWithoutUserCount() {
        return userRoleMapper.selectRolesWithoutUserCount();
    }
}