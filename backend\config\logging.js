/**
 * 日志管理配置
 * 用于配置系统日志记录、存储、轮转等功能
 */

const path = require('path');
const winston = require('winston');
require('winston-daily-rotate-file');

// 日志级别定义
const LOG_LEVELS = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  verbose: 4,
  debug: 5,
  silly: 6
};

// 日志颜色配置
const LOG_COLORS = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  verbose: 'cyan',
  debug: 'blue',
  silly: 'grey'
};

// 基础配置
const config = {
  // 日志级别
  level: process.env.LOG_LEVEL || (process.env.NODE_ENV === 'production' ? 'info' : 'debug'),
  
  // 日志格式
  format: winston.format.combine(
    winston.format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss.SSS'
    }),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  
  // 控制台格式
  consoleFormat: winston.format.combine(
    winston.format.colorize({ all: true }),
    winston.format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss.SSS'
    }),
    winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
      let log = `${timestamp} [${level}]: ${message}`;
      if (stack) {
        log += `\n${stack}`;
      }
      if (Object.keys(meta).length > 0) {
        log += `\n${JSON.stringify(meta, null, 2)}`;
      }
      return log;
    })
  ),
  
  // 日志目录
  logDir: process.env.LOG_DIR || path.join(__dirname, '../logs'),
  
  // 文件配置
  files: {
    // 应用日志
    application: {
      filename: 'application-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      maxSize: '100m',
      maxFiles: '30d',
      level: 'info'
    },
    
    // 错误日志
    error: {
      filename: 'error-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      maxSize: '100m',
      maxFiles: '90d',
      level: 'error'
    },
    
    // 访问日志
    access: {
      filename: 'access-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      maxSize: '200m',
      maxFiles: '30d',
      level: 'http'
    },
    
    // 数据库日志
    database: {
      filename: 'database-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      maxSize: '100m',
      maxFiles: '30d',
      level: 'debug'
    },
    
    // 安全日志
    security: {
      filename: 'security-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      maxSize: '50m',
      maxFiles: '180d',
      level: 'warn'
    },
    
    // 性能日志
    performance: {
      filename: 'performance-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      maxSize: '100m',
      maxFiles: '7d',
      level: 'info'
    },
    
    // 审计日志
    audit: {
      filename: 'audit-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      maxSize: '200m',
      maxFiles: '365d',
      level: 'info'
    }
  },
  
  // 控制台输出配置
  console: {
    enabled: process.env.CONSOLE_LOG_ENABLED !== 'false',
    level: process.env.CONSOLE_LOG_LEVEL || 'debug',
    colorize: process.env.NODE_ENV !== 'production'
  },
  
  // 远程日志配置
  remote: {
    // ELK Stack配置
    elasticsearch: {
      enabled: process.env.ELASTICSEARCH_LOG_ENABLED === 'true',
      host: process.env.ELASTICSEARCH_HOST || 'localhost:9200',
      index: process.env.ELASTICSEARCH_INDEX || 'yinma-logs',
      level: 'info'
    },
    
    // Syslog配置
    syslog: {
      enabled: process.env.SYSLOG_ENABLED === 'true',
      host: process.env.SYSLOG_HOST || 'localhost',
      port: parseInt(process.env.SYSLOG_PORT) || 514,
      protocol: process.env.SYSLOG_PROTOCOL || 'udp4',
      facility: process.env.SYSLOG_FACILITY || 'local0'
    },
    
    // HTTP日志服务配置
    http: {
      enabled: process.env.HTTP_LOG_ENABLED === 'true',
      url: process.env.HTTP_LOG_URL,
      timeout: parseInt(process.env.HTTP_LOG_TIMEOUT) || 5000,
      level: 'error'
    }
  },
  
  // 日志过滤配置
  filters: {
    // 敏感信息过滤
    sensitive: {
      enabled: true,
      fields: ['password', 'token', 'secret', 'key', 'authorization'],
      replacement: '[FILTERED]'
    },
    
    // IP地址脱敏
    ipMasking: {
      enabled: process.env.IP_MASKING_ENABLED === 'true',
      pattern: /\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b/g,
      replacement: 'xxx.xxx.xxx.xxx'
    },
    
    // 用户ID脱敏
    userIdMasking: {
      enabled: process.env.USER_ID_MASKING_ENABLED === 'true',
      pattern: /"userId":\s*"([^"]+)"/g,
      replacement: '"userId": "[MASKED]"'
    }
  },
  
  // 日志聚合配置
  aggregation: {
    // 错误聚合
    errorAggregation: {
      enabled: true,
      window: 300000,      // 5分钟窗口
      threshold: 10,       // 阈值
      fields: ['message', 'stack']
    },
    
    // 慢查询聚合
    slowQueryAggregation: {
      enabled: true,
      window: 600000,      // 10分钟窗口
      threshold: 5,        // 阈值
      fields: ['query', 'duration']
    }
  },
  
  // 日志压缩配置
  compression: {
    enabled: process.env.LOG_COMPRESSION_ENABLED !== 'false',
    algorithm: 'gzip',
    level: 6
  },
  
  // 日志清理配置
  cleanup: {
    enabled: true,
    schedule: '0 2 * * *',  // 每天凌晨2点执行
    retentionDays: {
      application: 30,
      error: 90,
      access: 30,
      database: 30,
      security: 180,
      performance: 7,
      audit: 365
    }
  }
};

// 创建日志传输器
function createTransports() {
  const transports = [];
  
  // 控制台传输器
  if (config.console.enabled) {
    transports.push(new winston.transports.Console({
      level: config.console.level,
      format: config.consoleFormat,
      handleExceptions: true,
      handleRejections: true
    }));
  }
  
  // 文件传输器
  Object.entries(config.files).forEach(([name, fileConfig]) => {
    transports.push(new winston.transports.DailyRotateFile({
      filename: path.join(config.logDir, fileConfig.filename),
      datePattern: fileConfig.datePattern,
      maxSize: fileConfig.maxSize,
      maxFiles: fileConfig.maxFiles,
      level: fileConfig.level,
      format: config.format,
      auditFile: path.join(config.logDir, `${name}-audit.json`),
      zippedArchive: config.compression.enabled
    }));
  });
  
  // Elasticsearch传输器
  if (config.remote.elasticsearch.enabled) {
    const ElasticsearchTransport = require('winston-elasticsearch');
    transports.push(new ElasticsearchTransport({
      level: config.remote.elasticsearch.level,
      clientOpts: {
        node: `http://${config.remote.elasticsearch.host}`
      },
      index: config.remote.elasticsearch.index,
      indexTemplate: {
        name: config.remote.elasticsearch.index,
        pattern: `${config.remote.elasticsearch.index}-*`,
        settings: {
          number_of_shards: 1,
          number_of_replicas: 0
        }
      }
    }));
  }
  
  // Syslog传输器
  if (config.remote.syslog.enabled) {
    require('winston-syslog');
    transports.push(new winston.transports.Syslog({
      host: config.remote.syslog.host,
      port: config.remote.syslog.port,
      protocol: config.remote.syslog.protocol,
      facility: config.remote.syslog.facility
    }));
  }
  
  // HTTP传输器
  if (config.remote.http.enabled) {
    transports.push(new winston.transports.Http({
      host: config.remote.http.url,
      level: config.remote.http.level,
      timeout: config.remote.http.timeout
    }));
  }
  
  return transports;
}

// 创建日志记录器
function createLogger(category = 'application') {
  winston.addColors(LOG_COLORS);
  
  const logger = winston.createLogger({
    level: config.level,
    levels: LOG_LEVELS,
    format: config.format,
    transports: createTransports(),
    exitOnError: false
  });
  
  // 添加自定义方法
  logger.audit = function(message, meta = {}) {
    this.info(message, { ...meta, category: 'audit' });
  };
  
  logger.security = function(message, meta = {}) {
    this.warn(message, { ...meta, category: 'security' });
  };
  
  logger.performance = function(message, meta = {}) {
    this.info(message, { ...meta, category: 'performance' });
  };
  
  logger.database = function(message, meta = {}) {
    this.debug(message, { ...meta, category: 'database' });
  };
  
  logger.access = function(message, meta = {}) {
    this.http(message, { ...meta, category: 'access' });
  };
  
  return logger;
}

// 日志中间件
function createLoggerMiddleware() {
  return (req, res, next) => {
    const start = Date.now();
    const originalSend = res.send;
    
    res.send = function(body) {
      const duration = Date.now() - start;
      const logger = createLogger('access');
      
      logger.access('HTTP Request', {
        method: req.method,
        url: req.url,
        statusCode: res.statusCode,
        duration,
        userAgent: req.get('User-Agent'),
        ip: req.ip,
        userId: req.user?.id,
        requestId: req.id
      });
      
      return originalSend.call(this, body);
    };
    
    next();
  };
}

// 错误日志中间件
function createErrorLoggerMiddleware() {
  return (err, req, res, next) => {
    const logger = createLogger('error');
    
    logger.error('Unhandled Error', {
      error: {
        message: err.message,
        stack: err.stack,
        code: err.code
      },
      request: {
        method: req.method,
        url: req.url,
        headers: req.headers,
        body: req.body,
        params: req.params,
        query: req.query
      },
      user: {
        id: req.user?.id,
        ip: req.ip
      },
      requestId: req.id
    });
    
    next(err);
  };
}

// 敏感信息过滤器
function filterSensitiveData(data) {
  if (!config.filters.sensitive.enabled) {
    return data;
  }
  
  const filtered = JSON.parse(JSON.stringify(data));
  
  function filterObject(obj) {
    if (typeof obj !== 'object' || obj === null) {
      return obj;
    }
    
    for (const key in obj) {
      if (config.filters.sensitive.fields.some(field => 
        key.toLowerCase().includes(field.toLowerCase())
      )) {
        obj[key] = config.filters.sensitive.replacement;
      } else if (typeof obj[key] === 'object') {
        filterObject(obj[key]);
      }
    }
  }
  
  filterObject(filtered);
  return filtered;
}

// 导出配置和工具函数
module.exports = {
  config,
  createLogger,
  createLoggerMiddleware,
  createErrorLoggerMiddleware,
  filterSensitiveData,
  LOG_LEVELS,
  LOG_COLORS
};