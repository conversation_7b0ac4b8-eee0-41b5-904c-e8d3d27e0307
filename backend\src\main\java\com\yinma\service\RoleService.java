package com.yinma.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yinma.dto.RoleDTO;
import com.yinma.entity.RoleEntity;

import java.util.List;

/**
 * 角色信息Service接口
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
public interface RoleService extends IService<RoleEntity> {

    /**
     * 分页查询角色列表
     * 
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    IPage<RoleDTO> selectRolePage(RoleDTO.RoleQueryDTO queryDTO);

    /**
     * 根据角色ID查询角色详情
     * 
     * @param roleId 角色ID
     * @return 角色详情
     */
    RoleDTO selectRoleById(Long roleId);

    /**
     * 根据角色编码查询角色
     * 
     * @param roleCode 角色编码
     * @return 角色信息
     */
    RoleEntity selectRoleByCode(String roleCode);

    /**
     * 创建角色
     * 
     * @param createDTO 创建角色DTO
     * @return 角色ID
     */
    Long createRole(RoleDTO.RoleCreateDTO createDTO);

    /**
     * 更新角色信息
     * 
     * @param updateDTO 更新角色DTO
     * @return 是否成功
     */
    Boolean updateRole(RoleDTO.RoleUpdateDTO updateDTO);

    /**
     * 删除角色
     * 
     * @param roleId 角色ID
     * @return 是否成功
     */
    Boolean deleteRole(Long roleId);

    /**
     * 批量删除角色
     * 
     * @param roleIds 角色ID列表
     * @return 是否成功
     */
    Boolean batchDeleteRoles(List<Long> roleIds);

    /**
     * 启用角色
     * 
     * @param roleId 角色ID
     * @return 是否成功
     */
    Boolean enableRole(Long roleId);

    /**
     * 禁用角色
     * 
     * @param roleId 角色ID
     * @return 是否成功
     */
    Boolean disableRole(Long roleId);

    /**
     * 批量更新角色状态
     * 
     * @param roleIds 角色ID列表
     * @param status 状态
     * @return 是否成功
     */
    Boolean batchUpdateRoleStatus(List<Long> roleIds, Integer status);

    /**
     * 检查角色编码是否存在
     * 
     * @param roleCode 角色编码
     * @return 是否存在
     */
    Boolean checkRoleCodeExists(String roleCode);

    /**
     * 检查角色名称是否存在
     * 
     * @param roleName 角色名称
     * @return 是否存在
     */
    Boolean checkRoleNameExists(String roleName);

    /**
     * 查询角色的用户列表
     * 
     * @param roleId 角色ID
     * @return 用户列表
     */
    List<RoleDTO.RoleUserDTO> selectRoleUsers(Long roleId);

    /**
     * 查询角色的权限列表
     * 
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<String> selectRolePermissions(Long roleId);

    /**
     * 分配角色权限
     * 
     * @param assignDTO 分配权限DTO
     * @return 是否成功
     */
    Boolean assignRolePermissions(RoleDTO.PermissionAssignDTO assignDTO);

    /**
     * 移除角色权限
     * 
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @return 是否成功
     */
    Boolean removeRolePermissions(Long roleId, List<Long> permissionIds);

    /**
     * 分配角色用户
     * 
     * @param roleId 角色ID
     * @param userIds 用户ID列表
     * @return 是否成功
     */
    Boolean assignRoleUsers(Long roleId, List<Long> userIds);

    /**
     * 移除角色用户
     * 
     * @param roleId 角色ID
     * @param userIds 用户ID列表
     * @return 是否成功
     */
    Boolean removeRoleUsers(Long roleId, List<Long> userIds);

    /**
     * 查询所有启用的角色
     * 
     * @return 角色列表
     */
    List<RoleDTO> selectEnabledRoles();

    /**
     * 查询系统角色
     * 
     * @return 角色列表
     */
    List<RoleDTO> selectSystemRoles();

    /**
     * 查询用户角色
     * 
     * @return 角色列表
     */
    List<RoleDTO> selectUserRoles();

    /**
     * 查询角色统计信息
     * 
     * @return 统计信息
     */
    RoleDTO.RoleStatisticsDTO selectRoleStatistics();

    /**
     * 查询角色权限统计
     * 
     * @return 统计信息
     */
    List<RoleDTO.RolePermissionStatisticsDTO> selectRolePermissionStatistics();

    /**
     * 查询角色用户统计
     * 
     * @return 统计信息
     */
    List<RoleDTO.RoleUserStatisticsDTO> selectRoleUserStatistics();

    /**
     * 导出角色数据
     * 
     * @param queryDTO 查询条件
     * @return 角色数据
     */
    List<RoleDTO> exportRoles(RoleDTO.RoleQueryDTO queryDTO);

    /**
     * 导入角色数据
     * 
     * @param roles 角色数据
     * @return 导入结果
     */
    RoleDTO.ImportResultDTO importRoles(List<RoleDTO.RoleImportDTO> roles);

    /**
     * 检查角色是否拥有指定权限
     * 
     * @param roleId 角色ID
     * @param permissionCode 权限编码
     * @return 是否拥有
     */
    Boolean checkRoleHasPermission(Long roleId, String permissionCode);

    /**
     * 检查角色是否拥有指定权限列表中的任一权限
     * 
     * @param roleId 角色ID
     * @param permissionCodes 权限编码列表
     * @return 是否拥有
     */
    Boolean checkRoleHasAnyPermission(Long roleId, List<String> permissionCodes);

    /**
     * 检查角色是否拥有指定权限列表中的所有权限
     * 
     * @param roleId 角色ID
     * @param permissionCodes 权限编码列表
     * @return 是否拥有
     */
    Boolean checkRoleHasAllPermissions(Long roleId, List<String> permissionCodes);

    /**
     * 复制角色
     * 
     * @param sourceRoleId 源角色ID
     * @param targetRoleCode 目标角色编码
     * @param targetRoleName 目标角色名称
     * @return 新角色ID
     */
    Long copyRole(Long sourceRoleId, String targetRoleCode, String targetRoleName);

    /**
     * 获取角色菜单权限
     * 
     * @param roleId 角色ID
     * @return 菜单权限列表
     */
    List<RoleDTO.MenuPermissionDTO> selectRoleMenuPermissions(Long roleId);

    /**
     * 获取角色按钮权限
     * 
     * @param roleId 角色ID
     * @return 按钮权限列表
     */
    List<String> selectRoleButtonPermissions(Long roleId);

    /**
     * 获取角色数据权限
     * 
     * @param roleId 角色ID
     * @return 数据权限信息
     */
    RoleDTO.DataPermissionDTO selectRoleDataPermissions(Long roleId);

    /**
     * 设置角色数据权限
     * 
     * @param roleId 角色ID
     * @param dataPermissionDTO 数据权限DTO
     * @return 是否成功
     */
    Boolean setRoleDataPermissions(Long roleId, RoleDTO.DataPermissionDTO dataPermissionDTO);

    /**
     * 查询角色层级关系
     * 
     * @return 角色层级列表
     */
    List<RoleDTO.RoleHierarchyDTO> selectRoleHierarchy();

    /**
     * 设置角色层级关系
     * 
     * @param parentRoleId 父角色ID
     * @param childRoleIds 子角色ID列表
     * @return 是否成功
     */
    Boolean setRoleHierarchy(Long parentRoleId, List<Long> childRoleIds);

    /**
     * 查询角色的子角色
     * 
     * @param roleId 角色ID
     * @return 子角色列表
     */
    List<RoleDTO> selectChildRoles(Long roleId);

    /**
     * 查询角色的父角色
     * 
     * @param roleId 角色ID
     * @return 父角色列表
     */
    List<RoleDTO> selectParentRoles(Long roleId);

    /**
     * 检查角色是否可以删除
     * 
     * @param roleId 角色ID
     * @return 是否可以删除
     */
    Boolean checkRoleCanDelete(Long roleId);

    /**
     * 获取角色权限树
     * 
     * @param roleId 角色ID
     * @return 权限树
     */
    List<RoleDTO.PermissionTreeDTO> selectRolePermissionTree(Long roleId);
}