package com.yinma.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.time.LocalDateTime;

/**
 * MyBatis Plus配置类
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
@Slf4j
@Configuration
public class MybatisPlusConfig {

    /**
     * MyBatis Plus拦截器
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        
        // 分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        
        // 乐观锁插件
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        
        return interceptor;
    }

    /**
     * 元数据处理器
     */
    @Bean
    public MetaObjectHandler metaObjectHandler() {
        return new MetaObjectHandler() {
            @Override
            public void insertFill(MetaObject metaObject) {
                log.debug("开始插入填充...");
                
                LocalDateTime now = LocalDateTime.now();
                String currentUser = getCurrentUser();
                
                // 创建时间
                this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, now);
                this.strictInsertFill(metaObject, "createdAt", LocalDateTime.class, now);
                
                // 更新时间
                this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, now);
                this.strictInsertFill(metaObject, "updatedAt", LocalDateTime.class, now);
                
                // 创建人
                this.strictInsertFill(metaObject, "createBy", String.class, currentUser);
                this.strictInsertFill(metaObject, "createdBy", String.class, currentUser);
                
                // 更新人
                this.strictInsertFill(metaObject, "updateBy", String.class, currentUser);
                this.strictInsertFill(metaObject, "updatedBy", String.class, currentUser);
                
                // 删除标志
                this.strictInsertFill(metaObject, "isDeleted", Integer.class, 0);
                this.strictInsertFill(metaObject, "deleted", Integer.class, 0);
                
                // 版本号
                this.strictInsertFill(metaObject, "version", Integer.class, 1);
            }

            @Override
            public void updateFill(MetaObject metaObject) {
                log.debug("开始更新填充...");
                
                LocalDateTime now = LocalDateTime.now();
                String currentUser = getCurrentUser();
                
                // 更新时间
                this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, now);
                this.strictUpdateFill(metaObject, "updatedAt", LocalDateTime.class, now);
                
                // 更新人
                this.strictUpdateFill(metaObject, "updateBy", String.class, currentUser);
                this.strictUpdateFill(metaObject, "updatedBy", String.class, currentUser);
            }
            
            /**
             * 获取当前用户
             */
            private String getCurrentUser() {
                try {
                    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
                    if (authentication != null && authentication.isAuthenticated() 
                            && !"anonymousUser".equals(authentication.getPrincipal())) {
                        return authentication.getName();
                    }
                } catch (Exception e) {
                    log.warn("获取当前用户失败: {}", e.getMessage());
                }
                return "system";
            }
        };
    }
}