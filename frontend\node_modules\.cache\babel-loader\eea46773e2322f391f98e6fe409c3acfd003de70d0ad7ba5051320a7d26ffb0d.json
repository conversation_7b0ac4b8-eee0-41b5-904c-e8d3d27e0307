{"ast": null, "code": "export default function (callback, that) {\n  let index = -1;\n  for (const node of this) {\n    callback.call(that, node, ++index, this);\n  }\n  return this;\n}", "map": {"version": 3, "names": ["callback", "that", "index", "node", "call"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/d3-hierarchy/src/hierarchy/each.js"], "sourcesContent": ["export default function(callback, that) {\n  let index = -1;\n  for (const node of this) {\n    callback.call(that, node, ++index, this);\n  }\n  return this;\n}\n"], "mappings": "AAAA,eAAe,UAASA,QAAQ,EAAEC,IAAI,EAAE;EACtC,IAAIC,KAAK,GAAG,CAAC,CAAC;EACd,KAAK,MAAMC,IAAI,IAAI,IAAI,EAAE;IACvBH,QAAQ,CAACI,IAAI,CAACH,IAAI,EAAEE,IAAI,EAAE,EAAED,KAAK,EAAE,IAAI,CAAC;EAC1C;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}