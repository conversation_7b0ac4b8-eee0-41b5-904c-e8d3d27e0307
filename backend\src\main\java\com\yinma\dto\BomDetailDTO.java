package com.yinma.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * BOM明细数据传输对象
 * 用于前后端数据交互
 * 
 * <AUTHOR> System
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class BomDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 明细主键ID
     */
    private Long id;

    /**
     * BOM主表ID
     */
    @NotNull(message = "BOM主表ID不能为空")
    private Long bomId;

    /**
     * BOM编码
     */
    private String bomCode;

    /**
     * 父级明细ID
     */
    private Long parentDetailId;

    /**
     * 物料编码
     */
    @NotBlank(message = "物料编码不能为空")
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 物料规格
     */
    private String materialSpec;

    /**
     * 物料类型
     */
    private String materialType;

    /**
     * 基本单位
     */
    private String baseUnit;

    /**
     * 需求数量
     */
    @NotNull(message = "需求数量不能为空")
    @DecimalMin(value = "0", inclusive = false, message = "需求数量必须大于0")
    private BigDecimal requiredQty;

    /**
     * 损耗率（%）
     */
    @DecimalMin(value = "0", message = "损耗率不能为负数")
    @DecimalMax(value = "100", message = "损耗率不能超过100%")
    private BigDecimal lossRate;

    /**
     * 实际需求数量（含损耗）
     */
    private BigDecimal actualQty;

    /**
     * 单价
     */
    @DecimalMin(value = "0", message = "单价不能为负数")
    private BigDecimal unitPrice;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 投料点
     */
    private String feedingPoint;

    /**
     * 工序编码
     */
    private String operationCode;

    /**
     * 工序名称
     */
    private String operationName;

    /**
     * 工位编码
     */
    private String workstationCode;

    /**
     * 工位名称
     */
    private String workstationName;

    /**
     * 是否关键物料
     */
    private Boolean isCritical;

    /**
     * 是否可替代
     */
    private Boolean isSubstitutable;

    /**
     * 替代物料组
     */
    private String substituteGroup;

    /**
     * 优先级
     */
    @Min(value = 1, message = "优先级必须大于0")
    private Integer priority;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 路径（层级路径）
     */
    private String levelPath;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 生效日期
     */
    private LocalDateTime effectiveDate;

    /**
     * 失效日期
     */
    private LocalDateTime expiryDate;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 采购提前期（天）
     */
    private Integer leadTime;

    /**
     * 最小采购量
     */
    private BigDecimal minOrderQty;

    /**
     * 包装规格
     */
    private String packageSpec;

    /**
     * 存储位置
     */
    private String storageLocation;

    /**
     * 质检要求
     */
    private String inspectionRequirement;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 物料主数据信息（关联查询）
     */
    private MaterialDTO materialInfo;

    /**
     * 子级明细列表
     */
    private List<BomDetailDTO> childDetails;

    /**
     * 替代物料列表
     */
    private List<SubstituteMaterialDTO> substituteMaterials;

    /**
     * 当前库存数量（计算字段）
     */
    private BigDecimal currentStock;

    /**
     * 可用库存数量（计算字段）
     */
    private BigDecimal availableStock;

    /**
     * 缺料数量（计算字段）
     */
    private BigDecimal shortageQty;

    /**
     * 是否缺料（计算字段）
     */
    private Boolean isShortage;

    /**
     * 成本占比（计算字段）
     */
    private BigDecimal costRatio;

    /**
     * 累计需求数量（计算字段）
     */
    private BigDecimal cumulativeQty;

    /**
     * 物料简化信息DTO
     */
    @Data
    @Accessors(chain = true)
    public static class MaterialDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 物料编码
         */
        private String materialCode;

        /**
         * 物料名称
         */
        private String materialName;

        /**
         * 物料规格
         */
        private String materialSpec;

        /**
         * 物料类型
         */
        private String materialType;

        /**
         * 基本单位
         */
        private String baseUnit;

        /**
         * 标准成本
         */
        private BigDecimal standardCost;

        /**
         * 最新采购价格
         */
        private BigDecimal latestPurchasePrice;

        /**
         * 主供应商
         */
        private String mainSupplier;

        /**
         * 采购提前期
         */
        private Integer leadTime;

        /**
         * 安全库存
         */
        private BigDecimal safetyStock;

        /**
         * 物料状态
         */
        private String materialStatus;
    }

    /**
     * 替代物料DTO
     */
    @Data
    @Accessors(chain = true)
    public static class SubstituteMaterialDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 替代物料编码
         */
        private String substituteMaterialCode;

        /**
         * 替代物料名称
         */
        private String substituteMaterialName;

        /**
         * 替代物料规格
         */
        private String substituteMaterialSpec;

        /**
         * 替代比例
         */
        private BigDecimal substituteRatio;

        /**
         * 替代优先级
         */
        private Integer substitutePriority;

        /**
         * 替代条件
         */
        private String substituteCondition;

        /**
         * 是否可用
         */
        private Boolean isAvailable;

        /**
         * 当前库存
         */
        private BigDecimal currentStock;

        /**
         * 单价差异
         */
        private BigDecimal priceDifference;
    }

    /**
     * BOM明细查询条件DTO
     */
    @Data
    @Accessors(chain = true)
    public static class BomDetailQueryDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * BOM主表ID
         */
        private Long bomId;

        /**
         * BOM编码
         */
        private String bomCode;

        /**
         * 物料编码（模糊查询）
         */
        private String materialCode;

        /**
         * 物料名称（模糊查询）
         */
        private String materialName;

        /**
         * 物料类型
         */
        private String materialType;

        /**
         * 投料点
         */
        private String feedingPoint;

        /**
         * 工序编码
         */
        private String operationCode;

        /**
         * 是否关键物料
         */
        private Boolean isCritical;

        /**
         * 是否可替代
         */
        private Boolean isSubstitutable;

        /**
         * 层级
         */
        private Integer level;

        /**
         * 最大层级（用于层级范围查询）
         */
        private Integer maxLevel;

        /**
         * 是否包含子级
         */
        private Boolean includeChildren = true;

        /**
         * 排序字段
         */
        private String orderBy = "level,sort_order";

        /**
         * 排序方向
         */
        private String orderDirection = "ASC";
    }

    /**
     * BOM明细批量操作DTO
     */
    @Data
    @Accessors(chain = true)
    public static class BomDetailBatchDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 操作类型：ADD-新增，UPDATE-更新，DELETE-删除
         */
        @NotBlank(message = "操作类型不能为空")
        private String operationType;

        /**
         * BOM主表ID
         */
        @NotNull(message = "BOM主表ID不能为空")
        private Long bomId;

        /**
         * 明细列表
         */
        @NotEmpty(message = "明细列表不能为空")
        private List<BomDetailDTO> details;

        /**
         * 批量操作原因
         */
        private String reason;

        /**
         * 是否验证库存
         */
        private Boolean validateStock = true;

        /**
         * 是否自动计算成本
         */
        private Boolean autoCalculateCost = true;
    }

    /**
     * 物料需求展开结果DTO
     */
    @Data
    @Accessors(chain = true)
    public static class MaterialRequirementExpandDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 物料编码
         */
        private String materialCode;

        /**
         * 物料名称
         */
        private String materialName;

        /**
         * 物料规格
         */
        private String materialSpec;

        /**
         * 物料类型
         */
        private String materialType;

        /**
         * 层级
         */
        private Integer level;

        /**
         * 路径
         */
        private String levelPath;

        /**
         * 单位需求数量
         */
        private BigDecimal unitRequiredQty;

        /**
         * 累计需求数量
         */
        private BigDecimal totalRequiredQty;

        /**
         * 单位
         */
        private String unit;

        /**
         * 单价
         */
        private BigDecimal unitPrice;

        /**
         * 总金额
         */
        private BigDecimal totalAmount;

        /**
         * 当前库存
         */
        private BigDecimal currentStock;

        /**
         * 缺料数量
         */
        private BigDecimal shortageQty;

        /**
         * 供应商
         */
        private String supplier;

        /**
         * 采购提前期
         */
        private Integer leadTime;

        /**
         * 投料点列表
         */
        private List<String> feedingPoints;

        /**
         * 使用路径列表（哪些上级物料使用了此物料）
         */
        private List<String> usagePaths;
    }
}