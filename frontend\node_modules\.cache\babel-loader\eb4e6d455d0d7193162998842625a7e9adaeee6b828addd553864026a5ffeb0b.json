{"ast": null, "code": "\"use client\";\n\n// @ts-ignore\nimport version from './version';\nexport default version;", "map": {"version": 3, "names": ["version"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/antd/es/version/index.js"], "sourcesContent": ["\"use client\";\n\n// @ts-ignore\nimport version from './version';\nexport default version;"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAOA,OAAO,MAAM,WAAW;AAC/B,eAAeA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}