{"ast": null, "code": "// Proxy the dom ref with `{ nativeElement, otherFn }` type\n// ref: https://github.com/ant-design/ant-design/discussions/45242\nimport { useImperativeHandle } from 'react';\nfunction fillProxy(element, handler) {\n  element._antProxy = element._antProxy || {};\n  Object.keys(handler).forEach(key => {\n    if (!(key in element._antProxy)) {\n      const ori = element[key];\n      element._antProxy[key] = ori;\n      element[key] = handler[key];\n    }\n  });\n  return element;\n}\nexport default function useProxyImperativeHandle(ref, init) {\n  return useImperativeHandle(ref, () => {\n    const refObj = init();\n    const {\n      nativeElement\n    } = refObj;\n    if (typeof Proxy !== 'undefined') {\n      return new Proxy(nativeElement, {\n        get(obj, prop) {\n          if (refObj[prop]) {\n            return refObj[prop];\n          }\n          return Reflect.get(obj, prop);\n        }\n      });\n    }\n    // Fallback of IE\n    return fillProxy(nativeElement, refObj);\n  });\n}", "map": {"version": 3, "names": ["useImperativeHandle", "fillProxy", "element", "handler", "_antProxy", "Object", "keys", "for<PERSON>ach", "key", "ori", "useProxyImperativeHandle", "ref", "init", "refObj", "nativeElement", "Proxy", "get", "obj", "prop", "Reflect"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/antd/es/_util/hooks/useProxyImperativeHandle.js"], "sourcesContent": ["// Proxy the dom ref with `{ nativeElement, otherFn }` type\n// ref: https://github.com/ant-design/ant-design/discussions/45242\nimport { useImperativeHandle } from 'react';\nfunction fillProxy(element, handler) {\n  element._antProxy = element._antProxy || {};\n  Object.keys(handler).forEach(key => {\n    if (!(key in element._antProxy)) {\n      const ori = element[key];\n      element._antProxy[key] = ori;\n      element[key] = handler[key];\n    }\n  });\n  return element;\n}\nexport default function useProxyImperativeHandle(ref, init) {\n  return useImperativeHandle(ref, () => {\n    const refObj = init();\n    const {\n      nativeElement\n    } = refObj;\n    if (typeof Proxy !== 'undefined') {\n      return new Proxy(nativeElement, {\n        get(obj, prop) {\n          if (refObj[prop]) {\n            return refObj[prop];\n          }\n          return Reflect.get(obj, prop);\n        }\n      });\n    }\n    // Fallback of IE\n    return fillProxy(nativeElement, refObj);\n  });\n}"], "mappings": "AAAA;AACA;AACA,SAASA,mBAAmB,QAAQ,OAAO;AAC3C,SAASC,SAASA,CAACC,OAAO,EAAEC,OAAO,EAAE;EACnCD,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACE,SAAS,IAAI,CAAC,CAAC;EAC3CC,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,CAACI,OAAO,CAACC,GAAG,IAAI;IAClC,IAAI,EAAEA,GAAG,IAAIN,OAAO,CAACE,SAAS,CAAC,EAAE;MAC/B,MAAMK,GAAG,GAAGP,OAAO,CAACM,GAAG,CAAC;MACxBN,OAAO,CAACE,SAAS,CAACI,GAAG,CAAC,GAAGC,GAAG;MAC5BP,OAAO,CAACM,GAAG,CAAC,GAAGL,OAAO,CAACK,GAAG,CAAC;IAC7B;EACF,CAAC,CAAC;EACF,OAAON,OAAO;AAChB;AACA,eAAe,SAASQ,wBAAwBA,CAACC,GAAG,EAAEC,IAAI,EAAE;EAC1D,OAAOZ,mBAAmB,CAACW,GAAG,EAAE,MAAM;IACpC,MAAME,MAAM,GAAGD,IAAI,CAAC,CAAC;IACrB,MAAM;MACJE;IACF,CAAC,GAAGD,MAAM;IACV,IAAI,OAAOE,KAAK,KAAK,WAAW,EAAE;MAChC,OAAO,IAAIA,KAAK,CAACD,aAAa,EAAE;QAC9BE,GAAGA,CAACC,GAAG,EAAEC,IAAI,EAAE;UACb,IAAIL,MAAM,CAACK,IAAI,CAAC,EAAE;YAChB,OAAOL,MAAM,CAACK,IAAI,CAAC;UACrB;UACA,OAAOC,OAAO,CAACH,GAAG,CAACC,GAAG,EAAEC,IAAI,CAAC;QAC/B;MACF,CAAC,CAAC;IACJ;IACA;IACA,OAAOjB,SAAS,CAACa,aAAa,EAAED,MAAM,CAAC;EACzC,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}