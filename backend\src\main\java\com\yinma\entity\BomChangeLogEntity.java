package com.yinma.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * BOM变更日志实体类
 * 记录BOM的所有变更历史，支持版本追溯和影响分析
 * 
 * <AUTHOR> System
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("bom_change_log")
public class BomChangeLogEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 变更日志主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * BOM主表ID
     */
    @TableField("bom_id")
    private Long bomId;

    /**
     * BOM编码
     */
    @TableField("bom_code")
    private String bomCode;

    /**
     * 变更类型：CREATE-新建，UPDATE-修改，DELETE-删除，VERSION-版本升级，CONVERT-转换
     */
    @TableField("change_type")
    private String changeType;

    /**
     * 变更操作：ADD-新增，MODIFY-修改，REMOVE-删除，REPLACE-替换
     */
    @TableField("change_operation")
    private String changeOperation;

    /**
     * 变更对象类型：BOM-BOM主表，DETAIL-BOM明细，MATERIAL-物料信息
     */
    @TableField("change_object_type")
    private String changeObjectType;

    /**
     * 变更对象ID
     */
    @TableField("change_object_id")
    private Long changeObjectId;

    /**
     * 变更字段名称
     */
    @TableField("change_field")
    private String changeField;

    /**
     * 变更前值
     */
    @TableField("old_value")
    private String oldValue;

    /**
     * 变更后值
     */
    @TableField("new_value")
    private String newValue;

    /**
     * 变更原因
     */
    @TableField("change_reason")
    private String changeReason;

    /**
     * 变更说明
     */
    @TableField("change_description")
    private String changeDescription;

    /**
     * 影响范围：LOW-低影响，MEDIUM-中等影响，HIGH-高影响，CRITICAL-关键影响
     */
    @TableField("impact_level")
    private String impactLevel;

    /**
     * 影响的下游BOM数量
     */
    @TableField("affected_bom_count")
    private Integer affectedBomCount;

    /**
     * 影响的在制订单数量
     */
    @TableField("affected_order_count")
    private Integer affectedOrderCount;

    /**
     * 影响的库存数量
     */
    @TableField("affected_inventory_count")
    private Integer affectedInventoryCount;

    /**
     * 变更状态：PENDING-待审批，APPROVED-已批准，REJECTED-已拒绝，IMPLEMENTED-已实施
     */
    @TableField("change_status")
    private String changeStatus;

    /**
     * 申请人
     */
    @TableField("applicant")
    private String applicant;

    /**
     * 申请时间
     */
    @TableField("apply_time")
    private LocalDateTime applyTime;

    /**
     * 审批人
     */
    @TableField("approver")
    private String approver;

    /**
     * 审批时间
     */
    @TableField("approve_time")
    private LocalDateTime approveTime;

    /**
     * 审批意见
     */
    @TableField("approve_comment")
    private String approveComment;

    /**
     * 实施人
     */
    @TableField("implementer")
    private String implementer;

    /**
     * 实施时间
     */
    @TableField("implement_time")
    private LocalDateTime implementTime;

    /**
     * 生效时间
     */
    @TableField("effective_time")
    private LocalDateTime effectiveTime;

    /**
     * 变更前版本号
     */
    @TableField("old_version")
    private String oldVersion;

    /**
     * 变更后版本号
     */
    @TableField("new_version")
    private String newVersion;

    /**
     * 关联的变更单号
     */
    @TableField("change_order_no")
    private String changeOrderNo;

    /**
     * 是否需要重新计算成本
     */
    @TableField("need_cost_recalc")
    private Boolean needCostRecalc;

    /**
     * 是否需要更新库存
     */
    @TableField("need_inventory_update")
    private Boolean needInventoryUpdate;

    /**
     * 是否需要通知相关部门
     */
    @TableField("need_notification")
    private Boolean needNotification;

    /**
     * 通知状态：PENDING-待通知，SENT-已发送，CONFIRMED-已确认
     */
    @TableField("notification_status")
    private String notificationStatus;

    /**
     * 备注说明
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 关联的BOM信息（非数据库字段）
     */
    @TableField(exist = false)
    private BomEntity bom;

    /**
     * 影响的BOM列表（非数据库字段）
     */
    @TableField(exist = false)
    private java.util.List<BomEntity> affectedBoms;

    /**
     * 变更类型枚举
     */
    public enum ChangeType {
        CREATE("CREATE", "新建"),
        UPDATE("UPDATE", "修改"),
        DELETE("DELETE", "删除"),
        VERSION("VERSION", "版本升级"),
        CONVERT("CONVERT", "转换");

        private final String code;
        private final String name;

        ChangeType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * 变更操作枚举
     */
    public enum ChangeOperation {
        ADD("ADD", "新增"),
        MODIFY("MODIFY", "修改"),
        REMOVE("REMOVE", "删除"),
        REPLACE("REPLACE", "替换");

        private final String code;
        private final String name;

        ChangeOperation(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * 变更对象类型枚举
     */
    public enum ChangeObjectType {
        BOM("BOM", "BOM主表"),
        DETAIL("DETAIL", "BOM明细"),
        MATERIAL("MATERIAL", "物料信息");

        private final String code;
        private final String name;

        ChangeObjectType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * 影响级别枚举
     */
    public enum ImpactLevel {
        LOW("LOW", "低影响"),
        MEDIUM("MEDIUM", "中等影响"),
        HIGH("HIGH", "高影响"),
        CRITICAL("CRITICAL", "关键影响");

        private final String code;
        private final String name;

        ImpactLevel(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * 变更状态枚举
     */
    public enum ChangeStatus {
        PENDING("PENDING", "待审批"),
        APPROVED("APPROVED", "已批准"),
        REJECTED("REJECTED", "已拒绝"),
        IMPLEMENTED("IMPLEMENTED", "已实施");

        private final String code;
        private final String name;

        ChangeStatus(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * 通知状态枚举
     */
    public enum NotificationStatus {
        PENDING("PENDING", "待通知"),
        SENT("SENT", "已发送"),
        CONFIRMED("CONFIRMED", "已确认");

        private final String code;
        private final String name;

        NotificationStatus(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }
}