package com.yinma.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinma.common.result.Result;
import com.yinma.dto.UserDTO;
import com.yinma.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 用户管理控制器
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/user")
@RequiredArgsConstructor
@Validated
@Tag(name = "用户管理", description = "用户信息的增删改查操作")
public class UserController {

    private final UserService userService;

    @GetMapping("/page")
    @Operation(summary = "分页查询用户列表", description = "根据条件分页查询用户信息")
    @PreAuthorize("hasAuthority('user:query')")
    public Result<IPage<UserDTO>> selectUserPage(@Valid UserDTO.UserQueryDTO queryDTO) {
        try {
            IPage<UserDTO> page = userService.selectUserPage(queryDTO);
            return Result.success(page);
        } catch (Exception e) {
            log.error("分页查询用户列表失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/{userId}")
    @Operation(summary = "查询用户详情", description = "根据用户ID查询用户详细信息")
    @PreAuthorize("hasAuthority('user:query')")
    public Result<UserDTO> selectUserById(
            @Parameter(description = "用户ID", required = true)
            @PathVariable @NotNull Long userId) {
        try {
            UserDTO user = userService.selectUserById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }
            return Result.success(user);
        } catch (Exception e) {
            log.error("查询用户详情失败, userId: {}", userId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @PostMapping
    @Operation(summary = "创建用户", description = "创建新的用户信息")
    @PreAuthorize("hasAuthority('user:create')")
    public Result<Long> createUser(@Valid @RequestBody UserDTO.UserCreateDTO createDTO) {
        try {
            Long userId = userService.createUser(createDTO);
            return Result.success(userId, "用户创建成功");
        } catch (Exception e) {
            log.error("创建用户失败", e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    @PutMapping
    @Operation(summary = "更新用户", description = "更新用户信息")
    @PreAuthorize("hasAuthority('user:update')")
    public Result<Boolean> updateUser(@Valid @RequestBody UserDTO.UserUpdateDTO updateDTO) {
        try {
            Boolean result = userService.updateUser(updateDTO);
            return Result.success(result, "用户更新成功");
        } catch (Exception e) {
            log.error("更新用户失败", e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/{userId}")
    @Operation(summary = "删除用户", description = "根据用户ID删除用户")
    @PreAuthorize("hasAuthority('user:delete')")
    public Result<Boolean> deleteUser(
            @Parameter(description = "用户ID", required = true)
            @PathVariable @NotNull Long userId) {
        try {
            Boolean result = userService.deleteUser(userId);
            return Result.success(result, "用户删除成功");
        } catch (Exception e) {
            log.error("删除用户失败, userId: {}", userId, e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/batch")
    @Operation(summary = "批量删除用户", description = "根据用户ID列表批量删除用户")
    @PreAuthorize("hasAuthority('user:delete')")
    public Result<Boolean> batchDeleteUsers(
            @Parameter(description = "用户ID列表", required = true)
            @RequestBody @NotEmpty List<Long> userIds) {
        try {
            Boolean result = userService.batchDeleteUsers(userIds);
            return Result.success(result, "批量删除成功");
        } catch (Exception e) {
            log.error("批量删除用户失败, userIds: {}", userIds, e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }

    @PutMapping("/{userId}/enable")
    @Operation(summary = "启用用户", description = "启用指定用户")
    @PreAuthorize("hasAuthority('user:update')")
    public Result<Boolean> enableUser(
            @Parameter(description = "用户ID", required = true)
            @PathVariable @NotNull Long userId) {
        try {
            Boolean result = userService.enableUser(userId);
            return Result.success(result, "用户启用成功");
        } catch (Exception e) {
            log.error("启用用户失败, userId: {}", userId, e);
            return Result.error("启用失败: " + e.getMessage());
        }
    }

    @PutMapping("/{userId}/disable")
    @Operation(summary = "禁用用户", description = "禁用指定用户")
    @PreAuthorize("hasAuthority('user:update')")
    public Result<Boolean> disableUser(
            @Parameter(description = "用户ID", required = true)
            @PathVariable @NotNull Long userId) {
        try {
            Boolean result = userService.disableUser(userId);
            return Result.success(result, "用户禁用成功");
        } catch (Exception e) {
            log.error("禁用用户失败, userId: {}", userId, e);
            return Result.error("禁用失败: " + e.getMessage());
        }
    }

    @PutMapping("/batch/status")
    @Operation(summary = "批量更新用户状态", description = "批量更新用户状态")
    @PreAuthorize("hasAuthority('user:update')")
    public Result<Boolean> batchUpdateUserStatus(
            @Valid @RequestBody UserDTO.BatchStatusUpdateDTO updateDTO) {
        try {
            Boolean result = userService.batchUpdateUserStatus(updateDTO.getUserIds(), updateDTO.getStatus());
            return Result.success(result, "批量更新状态成功");
        } catch (Exception e) {
            log.error("批量更新用户状态失败", e);
            return Result.error("批量更新失败: " + e.getMessage());
        }
    }

    @PutMapping("/{userId}/reset-password")
    @Operation(summary = "重置用户密码", description = "重置指定用户的密码")
    @PreAuthorize("hasAuthority('user:reset-password')")
    public Result<Boolean> resetPassword(
            @Parameter(description = "用户ID", required = true)
            @PathVariable @NotNull Long userId,
            @Valid @RequestBody UserDTO.PasswordResetDTO resetDTO) {
        try {
            Boolean result = userService.resetPassword(userId, resetDTO.getNewPassword());
            return Result.success(result, "密码重置成功");
        } catch (Exception e) {
            log.error("重置用户密码失败, userId: {}", userId, e);
            return Result.error("重置失败: " + e.getMessage());
        }
    }

    @PutMapping("/{userId}/change-password")
    @Operation(summary = "修改用户密码", description = "用户修改自己的密码")
    @PreAuthorize("hasAuthority('user:change-password')")
    public Result<Boolean> changePassword(
            @Parameter(description = "用户ID", required = true)
            @PathVariable @NotNull Long userId,
            @Valid @RequestBody UserDTO.PasswordChangeDTO changeDTO) {
        try {
            Boolean result = userService.changePassword(userId, changeDTO.getOldPassword(), changeDTO.getNewPassword());
            return Result.success(result, "密码修改成功");
        } catch (Exception e) {
            log.error("修改用户密码失败, userId: {}", userId, e);
            return Result.error("修改失败: " + e.getMessage());
        }
    }

    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户登录认证")
    public Result<UserDTO.LoginResultDTO> login(@Valid @RequestBody UserDTO.LoginDTO loginDTO) {
        try {
            UserDTO.LoginResultDTO result = userService.login(loginDTO);
            return Result.success(result, "登录成功");
        } catch (Exception e) {
            log.error("用户登录失败, username: {}", loginDTO.getUsername(), e);
            return Result.error("登录失败: " + e.getMessage());
        }
    }

    @PostMapping("/logout")
    @Operation(summary = "用户登出", description = "用户登出")
    @PreAuthorize("isAuthenticated()")
    public Result<Boolean> logout(
            @Parameter(description = "用户ID", required = true)
            @RequestParam @NotNull Long userId) {
        try {
            Boolean result = userService.logout(userId);
            return Result.success(result, "登出成功");
        } catch (Exception e) {
            log.error("用户登出失败, userId: {}", userId, e);
            return Result.error("登出失败: " + e.getMessage());
        }
    }

    @PostMapping("/refresh-token")
    @Operation(summary = "刷新Token", description = "使用刷新Token获取新的访问Token")
    public Result<UserDTO.LoginResultDTO> refreshToken(
            @Parameter(description = "刷新Token", required = true)
            @RequestParam @NotEmpty String refreshToken) {
        try {
            UserDTO.LoginResultDTO result = userService.refreshToken(refreshToken);
            return Result.success(result, "Token刷新成功");
        } catch (Exception e) {
            log.error("Token刷新失败", e);
            return Result.error("刷新失败: " + e.getMessage());
        }
    }

    @GetMapping("/check-username")
    @Operation(summary = "检查用户名是否存在", description = "检查用户名是否已被使用")
    public Result<Boolean> checkUsernameExists(
            @Parameter(description = "用户名", required = true)
            @RequestParam @NotEmpty String username) {
        try {
            Boolean exists = userService.checkUsernameExists(username);
            return Result.success(exists);
        } catch (Exception e) {
            log.error("检查用户名失败, username: {}", username, e);
            return Result.error("检查失败: " + e.getMessage());
        }
    }

    @GetMapping("/check-email")
    @Operation(summary = "检查邮箱是否存在", description = "检查邮箱是否已被使用")
    public Result<Boolean> checkEmailExists(
            @Parameter(description = "邮箱", required = true)
            @RequestParam @NotEmpty String email) {
        try {
            Boolean exists = userService.checkEmailExists(email);
            return Result.success(exists);
        } catch (Exception e) {
            log.error("检查邮箱失败, email: {}", email, e);
            return Result.error("检查失败: " + e.getMessage());
        }
    }

    @GetMapping("/check-mobile")
    @Operation(summary = "检查手机号是否存在", description = "检查手机号是否已被使用")
    public Result<Boolean> checkMobileExists(
            @Parameter(description = "手机号", required = true)
            @RequestParam @NotEmpty String mobile) {
        try {
            Boolean exists = userService.checkMobileExists(mobile);
            return Result.success(exists);
        } catch (Exception e) {
            log.error("检查手机号失败, mobile: {}", mobile, e);
            return Result.error("检查失败: " + e.getMessage());
        }
    }

    @GetMapping("/dept/{deptId}")
    @Operation(summary = "查询部门用户", description = "根据部门ID查询用户列表")
    @PreAuthorize("hasAuthority('user:query')")
    public Result<List<UserDTO>> selectUsersByDeptId(
            @Parameter(description = "部门ID", required = true)
            @PathVariable @NotNull Long deptId) {
        try {
            List<UserDTO> users = userService.selectUsersByDeptId(deptId);
            return Result.success(users);
        } catch (Exception e) {
            log.error("查询部门用户失败, deptId: {}", deptId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/role/{roleId}")
    @Operation(summary = "查询角色用户", description = "根据角色ID查询用户列表")
    @PreAuthorize("hasAuthority('user:query')")
    public Result<List<UserDTO>> selectUsersByRoleId(
            @Parameter(description = "角色ID", required = true)
            @PathVariable @NotNull Long roleId) {
        try {
            List<UserDTO> users = userService.selectUsersByRoleId(roleId);
            return Result.success(users);
        } catch (Exception e) {
            log.error("查询角色用户失败, roleId: {}", roleId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/{userId}/roles")
    @Operation(summary = "查询用户角色", description = "查询用户拥有的角色列表")
    @PreAuthorize("hasAuthority('user:query')")
    public Result<List<String>> selectUserRoles(
            @Parameter(description = "用户ID", required = true)
            @PathVariable @NotNull Long userId) {
        try {
            List<String> roles = userService.selectUserRoles(userId);
            return Result.success(roles);
        } catch (Exception e) {
            log.error("查询用户角色失败, userId: {}", userId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/{userId}/permissions")
    @Operation(summary = "查询用户权限", description = "查询用户拥有的权限列表")
    @PreAuthorize("hasAuthority('user:query')")
    public Result<List<String>> selectUserPermissions(
            @Parameter(description = "用户ID", required = true)
            @PathVariable @NotNull Long userId) {
        try {
            List<String> permissions = userService.selectUserPermissions(userId);
            return Result.success(permissions);
        } catch (Exception e) {
            log.error("查询用户权限失败, userId: {}", userId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @PutMapping("/{userId}/roles")
    @Operation(summary = "分配用户角色", description = "为用户分配角色")
    @PreAuthorize("hasAuthority('user:assign-role')")
    public Result<Boolean> assignUserRoles(
            @Parameter(description = "用户ID", required = true)
            @PathVariable @NotNull Long userId,
            @Parameter(description = "角色ID列表", required = true)
            @RequestBody @NotEmpty List<Long> roleIds) {
        try {
            Boolean result = userService.assignUserRoles(userId, roleIds);
            return Result.success(result, "角色分配成功");
        } catch (Exception e) {
            log.error("分配用户角色失败, userId: {}, roleIds: {}", userId, roleIds, e);
            return Result.error("分配失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/{userId}/roles")
    @Operation(summary = "移除用户角色", description = "移除用户的角色")
    @PreAuthorize("hasAuthority('user:remove-role')")
    public Result<Boolean> removeUserRoles(
            @Parameter(description = "用户ID", required = true)
            @PathVariable @NotNull Long userId,
            @Parameter(description = "角色ID列表", required = true)
            @RequestBody @NotEmpty List<Long> roleIds) {
        try {
            Boolean result = userService.removeUserRoles(userId, roleIds);
            return Result.success(result, "角色移除成功");
        } catch (Exception e) {
            log.error("移除用户角色失败, userId: {}, roleIds: {}", userId, roleIds, e);
            return Result.error("移除失败: " + e.getMessage());
        }
    }

    @GetMapping("/statistics")
    @Operation(summary = "查询用户统计", description = "查询用户统计信息")
    @PreAuthorize("hasAuthority('user:statistics')")
    public Result<UserDTO.UserStatisticsDTO> selectUserStatistics() {
        try {
            UserDTO.UserStatisticsDTO statistics = userService.selectUserStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("查询用户统计失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/online-count")
    @Operation(summary = "查询在线用户数", description = "查询当前在线用户数量")
    @PreAuthorize("hasAuthority('user:statistics')")
    public Result<Long> selectOnlineUserCount() {
        try {
            Long count = userService.selectOnlineUserCount();
            return Result.success(count);
        } catch (Exception e) {
            log.error("查询在线用户数失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/export")
    @Operation(summary = "导出用户数据", description = "导出用户数据")
    @PreAuthorize("hasAuthority('user:export')")
    public Result<List<UserDTO>> exportUsers(@Valid UserDTO.UserQueryDTO queryDTO) {
        try {
            List<UserDTO> users = userService.exportUsers(queryDTO);
            return Result.success(users);
        } catch (Exception e) {
            log.error("导出用户数据失败", e);
            return Result.error("导出失败: " + e.getMessage());
        }
    }

    @PostMapping("/import")
    @Operation(summary = "导入用户数据", description = "导入用户数据")
    @PreAuthorize("hasAuthority('user:import')")
    public Result<UserDTO.ImportResultDTO> importUsers(
            @Parameter(description = "用户数据列表", required = true)
            @RequestBody @NotEmpty List<UserDTO.UserImportDTO> users) {
        try {
            UserDTO.ImportResultDTO result = userService.importUsers(users);
            return Result.success(result, "导入完成");
        } catch (Exception e) {
            log.error("导入用户数据失败", e);
            return Result.error("导入失败: " + e.getMessage());
        }
    }
}