package com.yinma.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * BOM变更日志实体类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("bom_change_log")
public class BomChangeLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 变更单号
     */
    @NotBlank(message = "变更单号不能为空")
    @TableField("change_no")
    private String changeNo;

    /**
     * BOM编码
     */
    @NotBlank(message = "BOM编码不能为空")
    @TableField("bom_code")
    private String bomCode;

    /**
     * BOM名称
     */
    @NotBlank(message = "BOM名称不能为空")
    @TableField("bom_name")
    private String bomName;

    /**
     * 变更类型：ADD-新增，UPDATE-修改，DELETE-删除
     */
    @NotBlank(message = "变更类型不能为空")
    @TableField("change_type")
    private String changeType;

    /**
     * 变更对象：BOM-BOM主表，DETAIL-BOM明细
     */
    @NotBlank(message = "变更对象不能为空")
    @TableField("change_object")
    private String changeObject;

    /**
     * 变更字段
     */
    @TableField("change_field")
    private String changeField;

    /**
     * 变更前值
     */
    @TableField("old_value")
    private String oldValue;

    /**
     * 变更后值
     */
    @TableField("new_value")
    private String newValue;

    /**
     * 变更原因
     */
    @NotBlank(message = "变更原因不能为空")
    @TableField("change_reason")
    private String changeReason;

    /**
     * 变更描述
     */
    @TableField("change_description")
    private String changeDescription;

    /**
     * 影响分析
     */
    @TableField("impact_analysis")
    private String impactAnalysis;

    /**
     * 变更状态：PENDING-待审核，APPROVED-已批准，REJECTED-已拒绝，IMPLEMENTED-已实施
     */
    @TableField("change_status")
    private String changeStatus;

    /**
     * 申请人
     */
    @NotBlank(message = "申请人不能为空")
    @TableField("applicant")
    private String applicant;

    /**
     * 申请时间
     */
    @NotNull(message = "申请时间不能为空")
    @TableField("apply_time")
    private LocalDateTime applyTime;

    /**
     * 审核人
     */
    @TableField("reviewer")
    private String reviewer;

    /**
     * 审核时间
     */
    @TableField("review_time")
    private LocalDateTime reviewTime;

    /**
     * 审核意见
     */
    @TableField("review_comment")
    private String reviewComment;

    /**
     * 实施人
     */
    @TableField("implementer")
    private String implementer;

    /**
     * 实施时间
     */
    @TableField("implement_time")
    private LocalDateTime implementTime;

    /**
     * 实施结果
     */
    @TableField("implement_result")
    private String implementResult;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}