{"ast": null, "code": "import { atan, cos, sin } from \"../math.js\";\nimport { azimuthalInvert } from \"./azimuthal.js\";\nimport projection from \"./index.js\";\nexport function stereographicRaw(x, y) {\n  var cy = cos(y),\n    k = 1 + cos(x) * cy;\n  return [cy * sin(x) / k, sin(y) / k];\n}\nstereographicRaw.invert = azimuthalInvert(function (z) {\n  return 2 * atan(z);\n});\nexport default function () {\n  return projection(stereographicRaw).scale(250).clipAngle(142);\n}", "map": {"version": 3, "names": ["atan", "cos", "sin", "azimuthalInvert", "projection", "stereographicRaw", "x", "y", "cy", "k", "invert", "z", "scale", "clipAngle"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/d3-geo/src/projection/stereographic.js"], "sourcesContent": ["import {atan, cos, sin} from \"../math.js\";\nimport {azimuthalInvert} from \"./azimuthal.js\";\nimport projection from \"./index.js\";\n\nexport function stereographicRaw(x, y) {\n  var cy = cos(y), k = 1 + cos(x) * cy;\n  return [cy * sin(x) / k, sin(y) / k];\n}\n\nstereographicRaw.invert = azimuthalInvert(function(z) {\n  return 2 * atan(z);\n});\n\nexport default function() {\n  return projection(stereographicRaw)\n      .scale(250)\n      .clipAngle(142);\n}\n"], "mappings": "AAAA,SAAQA,IAAI,EAAEC,GAAG,EAAEC,GAAG,QAAO,YAAY;AACzC,SAAQC,eAAe,QAAO,gBAAgB;AAC9C,OAAOC,UAAU,MAAM,YAAY;AAEnC,OAAO,SAASC,gBAAgBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACrC,IAAIC,EAAE,GAAGP,GAAG,CAACM,CAAC,CAAC;IAAEE,CAAC,GAAG,CAAC,GAAGR,GAAG,CAACK,CAAC,CAAC,GAAGE,EAAE;EACpC,OAAO,CAACA,EAAE,GAAGN,GAAG,CAACI,CAAC,CAAC,GAAGG,CAAC,EAAEP,GAAG,CAACK,CAAC,CAAC,GAAGE,CAAC,CAAC;AACtC;AAEAJ,gBAAgB,CAACK,MAAM,GAAGP,eAAe,CAAC,UAASQ,CAAC,EAAE;EACpD,OAAO,CAAC,GAAGX,IAAI,CAACW,CAAC,CAAC;AACpB,CAAC,CAAC;AAEF,eAAe,YAAW;EACxB,OAAOP,UAAU,CAACC,gBAAgB,CAAC,CAC9BO,KAAK,CAAC,GAAG,CAAC,CACVC,SAAS,CAAC,GAAG,CAAC;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}