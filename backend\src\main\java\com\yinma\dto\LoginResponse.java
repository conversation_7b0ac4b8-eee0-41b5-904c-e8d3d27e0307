package com.yinma.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 登录响应DTO
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "登录响应")
public class LoginResponse {

    @Schema(description = "访问令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String accessToken;

    @Schema(description = "刷新令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String refreshToken;

    @Schema(description = "令牌类型", example = "Bearer")
    private String tokenType;

    @Schema(description = "访问令牌过期时间（秒）", example = "3600")
    private Long expiresIn;

    @Schema(description = "用户ID", example = "1")
    private Long userId;

    @Schema(description = "用户名", example = "admin")
    private String username;

    @Schema(description = "真实姓名", example = "管理员")
    private String realName;

    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "手机号", example = "13800138000")
    private String phone;

    @Schema(description = "头像", example = "https://example.com/avatar.jpg")
    private String avatar;

    @Schema(description = "权限列表", example = "ROLE_ADMIN,user:read,user:write")
    private String authorities;

    @Schema(description = "部门ID", example = "1")
    private Long deptId;

    @Schema(description = "部门名称", example = "技术部")
    private String deptName;

    @Schema(description = "职位", example = "系统管理员")
    private String position;

    @Schema(description = "登录时间", example = "2024-01-01 12:00:00")
    private String loginTime;

    @Schema(description = "登录IP", example = "*************")
    private String loginIp;

    @Schema(description = "登录地点", example = "北京市")
    private String loginLocation;

    @Schema(description = "是否首次登录", example = "false")
    private Boolean firstLogin;

    @Schema(description = "密码是否需要修改", example = "false")
    private Boolean needChangePassword;

    @Schema(description = "账户状态", example = "1")
    private Integer status;
}