{"ast": null, "code": "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { useMemo } from 'react';\nimport { devUseWarning } from '../../_util/warning';\n// Calculate the sum of span in a row\nfunction getCalcRows(rowItems, mergedColumn) {\n  let rows = [];\n  let tmpRow = [];\n  let exceed = false;\n  let count = 0;\n  rowItems.filter(n => n).forEach(rowItem => {\n    const {\n        filled\n      } = rowItem,\n      restItem = __rest(rowItem, [\"filled\"]);\n    if (filled) {\n      tmpRow.push(restItem);\n      rows.push(tmpRow);\n      // reset\n      tmpRow = [];\n      count = 0;\n      return;\n    }\n    const restSpan = mergedColumn - count;\n    count += rowItem.span || 1;\n    if (count >= mergedColumn) {\n      if (count > mergedColumn) {\n        exceed = true;\n        tmpRow.push(Object.assign(Object.assign({}, restItem), {\n          span: restSpan\n        }));\n      } else {\n        tmpRow.push(restItem);\n      }\n      rows.push(tmpRow);\n      // reset\n      tmpRow = [];\n      count = 0;\n    } else {\n      tmpRow.push(restItem);\n    }\n  });\n  if (tmpRow.length > 0) {\n    rows.push(tmpRow);\n  }\n  rows = rows.map(rows => {\n    const count = rows.reduce((acc, item) => acc + (item.span || 1), 0);\n    if (count < mergedColumn) {\n      // If the span of the last element in the current row is less than the column, then add its span to the remaining columns\n      const last = rows[rows.length - 1];\n      last.span = mergedColumn - (count - (last.span || 1));\n      return rows;\n    }\n    return rows;\n  });\n  return [rows, exceed];\n}\nconst useRow = (mergedColumn, items) => {\n  const [rows, exceed] = useMemo(() => getCalcRows(items, mergedColumn), [items, mergedColumn]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Descriptions');\n    process.env.NODE_ENV !== \"production\" ? warning(!exceed, 'usage', 'Sum of column `span` in a line not match `column` of Descriptions.') : void 0;\n  }\n  return rows;\n};\nexport default useRow;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "useMemo", "devUseW<PERSON>ning", "getCalcRows", "rowItems", "mergedColumn", "rows", "tmpRow", "exceed", "count", "filter", "n", "for<PERSON>ach", "rowItem", "filled", "restItem", "push", "restSpan", "span", "assign", "map", "reduce", "acc", "item", "last", "useRow", "items", "process", "env", "NODE_ENV", "warning"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/antd/es/descriptions/hooks/useRow.js"], "sourcesContent": ["var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { useMemo } from 'react';\nimport { devUseWarning } from '../../_util/warning';\n// Calculate the sum of span in a row\nfunction getCalcRows(rowItems, mergedColumn) {\n  let rows = [];\n  let tmpRow = [];\n  let exceed = false;\n  let count = 0;\n  rowItems.filter(n => n).forEach(rowItem => {\n    const {\n        filled\n      } = rowItem,\n      restItem = __rest(rowItem, [\"filled\"]);\n    if (filled) {\n      tmpRow.push(restItem);\n      rows.push(tmpRow);\n      // reset\n      tmpRow = [];\n      count = 0;\n      return;\n    }\n    const restSpan = mergedColumn - count;\n    count += rowItem.span || 1;\n    if (count >= mergedColumn) {\n      if (count > mergedColumn) {\n        exceed = true;\n        tmpRow.push(Object.assign(Object.assign({}, restItem), {\n          span: restSpan\n        }));\n      } else {\n        tmpRow.push(restItem);\n      }\n      rows.push(tmpRow);\n      // reset\n      tmpRow = [];\n      count = 0;\n    } else {\n      tmpRow.push(restItem);\n    }\n  });\n  if (tmpRow.length > 0) {\n    rows.push(tmpRow);\n  }\n  rows = rows.map(rows => {\n    const count = rows.reduce((acc, item) => acc + (item.span || 1), 0);\n    if (count < mergedColumn) {\n      // If the span of the last element in the current row is less than the column, then add its span to the remaining columns\n      const last = rows[rows.length - 1];\n      last.span = mergedColumn - (count - (last.span || 1));\n      return rows;\n    }\n    return rows;\n  });\n  return [rows, exceed];\n}\nconst useRow = (mergedColumn, items) => {\n  const [rows, exceed] = useMemo(() => getCalcRows(items, mergedColumn), [items, mergedColumn]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Descriptions');\n    process.env.NODE_ENV !== \"production\" ? warning(!exceed, 'usage', 'Sum of column `span` in a line not match `column` of Descriptions.') : void 0;\n  }\n  return rows;\n};\nexport default useRow;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,SAASW,OAAO,QAAQ,OAAO;AAC/B,SAASC,aAAa,QAAQ,qBAAqB;AACnD;AACA,SAASC,WAAWA,CAACC,QAAQ,EAAEC,YAAY,EAAE;EAC3C,IAAIC,IAAI,GAAG,EAAE;EACb,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,MAAM,GAAG,KAAK;EAClB,IAAIC,KAAK,GAAG,CAAC;EACbL,QAAQ,CAACM,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC,CAACC,OAAO,CAACC,OAAO,IAAI;IACzC,MAAM;QACFC;MACF,CAAC,GAAGD,OAAO;MACXE,QAAQ,GAAG5B,MAAM,CAAC0B,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAC;IACxC,IAAIC,MAAM,EAAE;MACVP,MAAM,CAACS,IAAI,CAACD,QAAQ,CAAC;MACrBT,IAAI,CAACU,IAAI,CAACT,MAAM,CAAC;MACjB;MACAA,MAAM,GAAG,EAAE;MACXE,KAAK,GAAG,CAAC;MACT;IACF;IACA,MAAMQ,QAAQ,GAAGZ,YAAY,GAAGI,KAAK;IACrCA,KAAK,IAAII,OAAO,CAACK,IAAI,IAAI,CAAC;IAC1B,IAAIT,KAAK,IAAIJ,YAAY,EAAE;MACzB,IAAII,KAAK,GAAGJ,YAAY,EAAE;QACxBG,MAAM,GAAG,IAAI;QACbD,MAAM,CAACS,IAAI,CAACxB,MAAM,CAAC2B,MAAM,CAAC3B,MAAM,CAAC2B,MAAM,CAAC,CAAC,CAAC,EAAEJ,QAAQ,CAAC,EAAE;UACrDG,IAAI,EAAED;QACR,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLV,MAAM,CAACS,IAAI,CAACD,QAAQ,CAAC;MACvB;MACAT,IAAI,CAACU,IAAI,CAACT,MAAM,CAAC;MACjB;MACAA,MAAM,GAAG,EAAE;MACXE,KAAK,GAAG,CAAC;IACX,CAAC,MAAM;MACLF,MAAM,CAACS,IAAI,CAACD,QAAQ,CAAC;IACvB;EACF,CAAC,CAAC;EACF,IAAIR,MAAM,CAACR,MAAM,GAAG,CAAC,EAAE;IACrBO,IAAI,CAACU,IAAI,CAACT,MAAM,CAAC;EACnB;EACAD,IAAI,GAAGA,IAAI,CAACc,GAAG,CAACd,IAAI,IAAI;IACtB,MAAMG,KAAK,GAAGH,IAAI,CAACe,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,IAAIC,IAAI,CAACL,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACnE,IAAIT,KAAK,GAAGJ,YAAY,EAAE;MACxB;MACA,MAAMmB,IAAI,GAAGlB,IAAI,CAACA,IAAI,CAACP,MAAM,GAAG,CAAC,CAAC;MAClCyB,IAAI,CAACN,IAAI,GAAGb,YAAY,IAAII,KAAK,IAAIe,IAAI,CAACN,IAAI,IAAI,CAAC,CAAC,CAAC;MACrD,OAAOZ,IAAI;IACb;IACA,OAAOA,IAAI;EACb,CAAC,CAAC;EACF,OAAO,CAACA,IAAI,EAAEE,MAAM,CAAC;AACvB;AACA,MAAMiB,MAAM,GAAGA,CAACpB,YAAY,EAAEqB,KAAK,KAAK;EACtC,MAAM,CAACpB,IAAI,EAAEE,MAAM,CAAC,GAAGP,OAAO,CAAC,MAAME,WAAW,CAACuB,KAAK,EAAErB,YAAY,CAAC,EAAE,CAACqB,KAAK,EAAErB,YAAY,CAAC,CAAC;EAC7F,IAAIsB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAG5B,aAAa,CAAC,cAAc,CAAC;IAC7CyB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGC,OAAO,CAAC,CAACtB,MAAM,EAAE,OAAO,EAAE,oEAAoE,CAAC,GAAG,KAAK,CAAC;EAClJ;EACA,OAAOF,IAAI;AACb,CAAC;AACD,eAAemB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}