{"ast": null, "code": "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { Path, AABB } from '@antv/g';\nimport { path as d3Path } from '@antv/vendor/d3-path';\nimport { sort, bisector } from '@antv/vendor/d3-array';\nimport { filter } from '@antv/util';\nimport { select } from '../utils/selection';\nimport { mapObject } from '../utils/array';\nimport { ELEMENT_CLASS_NAME, PLOT_CLASS_NAME } from '../runtime';\nimport { isOrdinalScale } from '../utils/scale';\nimport { rect } from '../shape/interval/color';\nimport { isPolar, isTranspose } from '../utils/coordinate';\nimport { getStyle } from '../utils/style';\nimport { reorder } from '../shape/utils';\nimport { angle, angleBetween, sub } from '../utils/vector';\nimport { traverseElements } from '../utils/traverse-elements';\n/**\n * Given root of chart returns elements to be manipulated\n */\nexport function selectG2Elements(root) {\n  return select(root).selectAll(`.${ELEMENT_CLASS_NAME}`).nodes().filter(d => !d.__removed__);\n}\nexport function selectFacetG2Elements(target, viewInstances) {\n  return selectFacetViews(target, viewInstances).flatMap(({\n    container\n  }) => selectG2Elements(container));\n}\nexport function selectFacetViews(target, viewInstances) {\n  return viewInstances.filter(d => d !== target && d.options.parentKey === target.options.key);\n}\nexport function selectPlotArea(root) {\n  return select(root).select(`.${PLOT_CLASS_NAME}`).node();\n}\nexport function bboxOf(element) {\n  // The geometry bounds of a group is empty, so return the render bounds.\n  if (element.tagName === 'g') return element.getRenderBounds();\n  // Compute the geometry bounds related to the parent.\n  const bounds = element.getGeometryBounds();\n  const aabb = new AABB();\n  aabb.setFromTransformedAABB(bounds, element.getWorldTransform());\n  return aabb;\n}\nexport function mousePosition(target, event) {\n  const {\n    offsetX,\n    offsetY\n  } = event;\n  const bbox = bboxOf(target);\n  const {\n    min: [x, y],\n    max: [x1, y1]\n  } = bbox;\n  const isOutX = offsetX < x || offsetX > x1;\n  const isOutY = offsetY < y || offsetY > y1;\n  if (isOutX || isOutY) return null;\n  return [offsetX - x, offsetY - y];\n}\n/**\n * @todo Pass bbox rather than calc it here.\n */\nexport function brushMousePosition(target, event) {\n  const {\n    offsetX,\n    offsetY\n  } = event;\n  const [x, y, x1, y1] = boundsOfBrushArea(target);\n  return [Math.min(x1, Math.max(x, offsetX)) - x, Math.min(y1, Math.max(y, offsetY)) - y];\n}\nexport function boundsOfBrushArea(target) {\n  // Calc bbox after clipping.\n  const bbox = target.getRenderBounds();\n  const {\n    min: [x0, y0],\n    max: [x1, y1]\n  } = bbox;\n  return [x0, y0, x1, y1];\n}\nexport function createColorKey(view) {\n  return element => element.__data__.color;\n}\nexport function createXKey(view) {\n  return element => element.__data__.x;\n}\nexport function createDatumof(view) {\n  const views = Array.isArray(view) ? view : [view];\n  const keyData = new Map(views.flatMap(view => {\n    const marks = Array.from(view.markState.keys());\n    return marks.map(mark => [keyed(view.key, mark.key), mark.data]);\n  }));\n  return element => {\n    const {\n      index,\n      markKey,\n      viewKey\n    } = element.__data__;\n    const data = keyData.get(keyed(viewKey, markKey));\n    return data[index];\n  };\n}\n/**\n * A state manager for G2Element.\n * The keys for each state's style start with the state name.\n * { selectedFill, selectedStroke } is for selected state.\n * { unselectedFill, unselectedStroke } is for unselected state.\n */\n/**\n * Define state priorities, higher number means higher priority.\n */\nconst STATE_PRIORITIES = {\n  selected: 3,\n  unselected: 3,\n  active: 2,\n  inactive: 2,\n  default: 1\n};\n/**\n * Define state groups, states in the same group are mutually exclusive.\n */\nconst STATE_GROUPS = {\n  selection: ['selected', 'unselected'],\n  highlight: ['active', 'inactive']\n};\nconst setElementAttribute = (element, k, v) => {\n  traverseElements(element, el => {\n    if ('setAttribute' in el && typeof el.setAttribute === 'function') {\n      el.setAttribute(k, v);\n    }\n  });\n};\nexport function createUseState(style, elements) {\n  // Apply interaction style to all elements.\n  elements.forEach(element => {\n    // @ts-ignore\n    const currentStyle = element.__interactionStyle__;\n    if (currentStyle) {\n      // @ts-ignore\n      element.__interactionStyle__ = Object.assign(Object.assign({}, currentStyle), style);\n    } else {\n      // @ts-ignore\n      element.__interactionStyle__ = style;\n    }\n  });\n  return (valueof = (d, element) => d, setAttribute = setElementAttribute) => useState(undefined, valueof, setAttribute);\n}\nexport function useState(style, valueof = (d, element) => d, setAttribute = setElementAttribute) {\n  const STATES = '__states__';\n  const ORIGINAL = '__ordinal__';\n  // Get state priority.\n  const getStatePriority = stateName => STATE_PRIORITIES[stateName] || STATE_PRIORITIES.default;\n  // Get the group that a state belongs to.\n  const getStateGroup = stateName => {\n    var _a;\n    return (_a = Object.entries(STATE_GROUPS).find(([_, states]) => states.includes(stateName))) === null || _a === void 0 ? void 0 : _a[0];\n  };\n  // Mix style for each state and apply it to element.\n  const applyState = element => {\n    var _a;\n    const {\n      [STATES]: states = [],\n      [ORIGINAL]: original = {}\n    } = element;\n    // Sort states by priority.\n    const sortedStates = [...states].sort((a, b) => getStatePriority(b) - getStatePriority(a));\n    // Create a Map to track the highest priority state for each style attribute.\n    const styleAttributeMap = new Map();\n    // Iterate through all states to find the highest priority state for each style attribute.\n    for (const state of sortedStates) {\n      // If style exists, use it directly, else use interaction style on element.\n      const stateStyles = ((_a = style !== null && style !== void 0 ? style : element.__interactionStyle__) === null || _a === void 0 ? void 0 : _a[state]) || {};\n      for (const [key, value] of Object.entries(stateStyles)) {\n        if (!styleAttributeMap.has(key)) {\n          styleAttributeMap.set(key, value);\n        }\n      }\n    }\n    // Apply styles including original styles.\n    const finalStyle = Object.assign({}, original);\n    for (const [key, value] of styleAttributeMap.entries()) {\n      finalStyle[key] = value;\n    }\n    if (Object.keys(finalStyle).length === 0) return;\n    // Apply final styles to the element.\n    for (const [key, value] of Object.entries(finalStyle)) {\n      const currentValue = getStyle(element, key);\n      const v = valueof(value, element);\n      setAttribute(element, key, v);\n      // Store the attribute if it does not exist in original.\n      if (!(key in original)) original[key] = currentValue;\n    }\n    element[ORIGINAL] = original;\n  };\n  const initState = element => {\n    if (element[STATES]) return;\n    element[STATES] = [];\n    return;\n  };\n  /**\n   * Update states and update element, handle conflict states automatically.\n   */\n  const updateState = (element, ...states) => {\n    initState(element);\n    const currentStates = element[STATES];\n    // Collect all new state groups.\n    const newStateGroups = new Set(states.map(state => getStateGroup(state)).filter(group => group !== undefined));\n    // Exclude old states that are in the new state group.\n    const remainingStates = currentStates.filter(existingState => !newStateGroups.has(getStateGroup(existingState)));\n    element[STATES] = [...remainingStates, ...states];\n    applyState(element);\n  };\n  /**\n   * Set the states and update element.\n   */\n  const setState = (element, ...states) => {\n    initState(element);\n    element[STATES] = [...states];\n    applyState(element);\n  };\n  /**\n   * Remove the states and update element.\n   */\n  const removeState = (element, ...states) => {\n    initState(element);\n    for (const state of states) {\n      const index = element[STATES].indexOf(state);\n      if (index !== -1) {\n        element[STATES].splice(index, 1);\n      }\n    }\n    applyState(element);\n  };\n  const hasState = (element, state) => {\n    initState(element);\n    return element[STATES].indexOf(state) !== -1;\n  };\n  return {\n    setState,\n    updateState,\n    removeState,\n    hasState\n  };\n}\nfunction isEmptyObject(obj) {\n  if (obj === undefined) return true;\n  if (typeof obj !== 'object') return false;\n  return Object.keys(obj).length === 0;\n}\n// A function to generate key for mark each view.\nfunction keyed(viewKey, markKey) {\n  return `${viewKey},${markKey}`;\n}\nexport function mergeState(options, states) {\n  // Index state by mark key and view key.\n  const views = Array.isArray(options) ? options : [options];\n  const markState = views.flatMap(view => view.marks.map(mark => [keyed(view.key, mark.key), mark.state]));\n  const state = {};\n  // Update each specified state.\n  for (const descriptor of states) {\n    const [key, defaults] = Array.isArray(descriptor) ? descriptor : [descriptor, {}];\n    // Update each specified mark state.\n    state[key] = markState.reduce((merged, mark) => {\n      // Normalize state.\n      const [markKey, markState = {}] = mark;\n      const selectedState = isEmptyObject(markState[key]) ? defaults : markState[key];\n      // Update each state attribute.\n      for (const [attr, value] of Object.entries(selectedState)) {\n        const oldValue = merged[attr];\n        const newValue = (data, index, array, element) => {\n          const k = keyed(element.__data__.viewKey, element.__data__.markKey);\n          if (markKey !== k) return oldValue === null || oldValue === void 0 ? void 0 : oldValue(data, index, array, element);\n          if (typeof value !== 'function') return value;\n          return value(data, index, array, element);\n        };\n        merged[attr] = newValue;\n      }\n      return merged;\n    }, {});\n  }\n  return state;\n}\n// @todo Support elements from different view.\nexport function createValueof(elements, datum) {\n  const elementIndex = new Map(elements.map((d, i) => [d, i]));\n  const fa = datum ? elements.map(datum) : elements;\n  return (d, e) => {\n    if (typeof d !== 'function') return d;\n    const i = elementIndex.get(e);\n    const fe = datum ? datum(e) : e;\n    return d(fe, i, fa, e);\n  };\n}\nexport function renderLink(_a) {\n  var {\n      link = false,\n      valueof = (d, element) => d,\n      coordinate\n    } = _a,\n    style = __rest(_a, [\"link\", \"valueof\", \"coordinate\"]);\n  const LINK_CLASS_NAME = 'element-link';\n  if (!link) return [() => {}, () => {}];\n  const pointsOf = element => element.__data__.points;\n  const pathPointsOf = (P0, P1) => {\n    const [, p1, p2] = P0;\n    const [p0,,, p3] = P1;\n    const P = [p1, p0, p3, p2];\n    return P;\n  };\n  const append = elements => {\n    var _a;\n    if (elements.length <= 1) return;\n    // Sort elements by normalized x to avoid cross.\n    const sortedElements = sort(elements, (e0, e1) => {\n      const {\n        x: x0\n      } = e0.__data__;\n      const {\n        x: x1\n      } = e1.__data__;\n      const dx = x0 - x1;\n      return dx;\n    });\n    for (let i = 1; i < sortedElements.length; i++) {\n      const p = d3Path();\n      const e0 = sortedElements[i - 1];\n      const e1 = sortedElements[i];\n      const [p0, p1, p2, p3] = pathPointsOf(pointsOf(e0), pointsOf(e1));\n      p.moveTo(...p0);\n      p.lineTo(...p1);\n      p.lineTo(...p2);\n      p.lineTo(...p3);\n      p.closePath();\n      const _b = mapObject(style, d => valueof(d, e0)),\n        {\n          fill = e0.getAttribute('fill')\n        } = _b,\n        rest = __rest(_b, [\"fill\"]);\n      const link = new Path({\n        className: LINK_CLASS_NAME,\n        style: Object.assign({\n          d: p.toString(),\n          fill,\n          zIndex: -2\n        }, rest)\n      });\n      // @ts-ignore\n      (_a = e0.link) === null || _a === void 0 ? void 0 : _a.remove();\n      e0.parentNode.appendChild(link);\n      // @ts-ignore\n      e0.link = link;\n    }\n  };\n  const remove = element => {\n    var _a;\n    (_a = element.link) === null || _a === void 0 ? void 0 : _a.remove();\n    element.link = null;\n  };\n  return [append, remove];\n}\n// Apply translate to mock slice out.\nexport function offsetTransform(element, offset, coordinate) {\n  const append = t => {\n    const {\n      transform\n    } = element.style;\n    return transform ? `${transform} ${t}` : t;\n  };\n  if (isPolar(coordinate)) {\n    const {\n      points\n    } = element.__data__;\n    const [p0, p1] = isTranspose(coordinate) ? reorder(points) : points;\n    const center = coordinate.getCenter();\n    const v0 = sub(p0, center);\n    const v1 = sub(p1, center);\n    const a0 = angle(v0);\n    const da = angleBetween(v0, v1);\n    const amid = a0 + da / 2;\n    const dx = offset * Math.cos(amid);\n    const dy = offset * Math.sin(amid);\n    return append(`translate(${dx}, ${dy})`);\n  }\n  if (isTranspose(coordinate)) return append(`translate(${offset}, 0)`);\n  return append(`translate(0, ${-offset})`);\n}\nexport function renderBackground(_a) {\n  var {\n      document,\n      background,\n      scale,\n      coordinate,\n      valueof\n    } = _a,\n    rest = __rest(_a, [\"document\", \"background\", \"scale\", \"coordinate\", \"valueof\"]);\n  const BACKGROUND_CLASS_NAME = 'element-background';\n  // Don't have background.\n  if (!background) return [() => {}, () => {}];\n  const extentOf = (scale, x, padding) => {\n    const ax = scale.invert(x);\n    const mid = x + scale.getBandWidth(ax) / 2;\n    const half = scale.getStep(ax) / 2;\n    const offset = half * padding;\n    return [mid - half + offset, mid + half - offset];\n  };\n  const sizeXOf = (element, padding) => {\n    const {\n      x: scaleX\n    } = scale;\n    if (!isOrdinalScale(scaleX)) return [0, 1];\n    const {\n      __data__: data\n    } = element;\n    const {\n      x\n    } = data;\n    const [e1, e2] = extentOf(scaleX, x, padding);\n    return [e1, e2];\n  };\n  const sizeYOf = (element, padding) => {\n    const {\n      y: scaleY\n    } = scale;\n    if (!isOrdinalScale(scaleY)) return [0, 1];\n    const {\n      __data__: data\n    } = element;\n    const {\n      y\n    } = data;\n    const [e1, e2] = extentOf(scaleY, y, padding);\n    return [e1, e2];\n  };\n  const bandShapeOf = (element, style) => {\n    const {\n      padding\n    } = style;\n    const [x1, x2] = sizeXOf(element, padding);\n    const [y1, y2] = sizeYOf(element, padding);\n    const points = [[x1, y1], [x2, y1], [x2, y2], [x1, y2]].map(d => coordinate.map(d));\n    const {\n      __data__: data\n    } = element;\n    const {\n      y: dy,\n      y1: dy1\n    } = data;\n    return rect(document, points, {\n      y: dy,\n      y1: dy1\n    }, coordinate, style);\n  };\n  // Shape without ordinal style.\n  // Clone and scale it.\n  const cloneShapeOf = (element, style) => {\n    const {\n        transform = 'scale(1.2, 1.2)',\n        transformOrigin = 'center center',\n        stroke = ''\n      } = style,\n      rest = __rest(style, [\"transform\", \"transformOrigin\", \"stroke\"]);\n    const finalStyle = Object.assign({\n      transform,\n      transformOrigin,\n      stroke\n    }, rest);\n    const shape = element.cloneNode(true);\n    for (const [key, value] of Object.entries(finalStyle)) {\n      shape.style[key] = value;\n    }\n    return shape;\n  };\n  const isOrdinalShape = () => {\n    const {\n      x,\n      y\n    } = scale;\n    return [x, y].some(isOrdinalScale);\n  };\n  const append = element => {\n    if (element.background) element.background.remove();\n    const _a = mapObject(rest, d => valueof(d, element)),\n      {\n        fill = '#CCD6EC',\n        fillOpacity = 0.3,\n        zIndex = -2,\n        padding = 0.001,\n        lineWidth = 0\n      } = _a,\n      style = __rest(_a, [\"fill\", \"fillOpacity\", \"zIndex\", \"padding\", \"lineWidth\"]);\n    const finalStyle = Object.assign(Object.assign({}, style), {\n      fill,\n      fillOpacity,\n      zIndex,\n      padding,\n      lineWidth\n    });\n    const shapeOf = isOrdinalShape() ? bandShapeOf : cloneShapeOf;\n    const shape = shapeOf(element, finalStyle);\n    shape.className = BACKGROUND_CLASS_NAME;\n    element.parentNode.parentNode.appendChild(shape);\n    element.background = shape;\n  };\n  const remove = element => {\n    var _a;\n    (_a = element.background) === null || _a === void 0 ? void 0 : _a.remove();\n    element.background = null;\n  };\n  const is = element => {\n    return element.className === BACKGROUND_CLASS_NAME;\n  };\n  return [append, remove, is];\n}\nexport function setCursor(root, cursor) {\n  // @ts-ignore\n  const canvas = root.getRootNode().defaultView;\n  const dom = canvas.getContextService().getDomElement();\n  if (dom === null || dom === void 0 ? void 0 : dom.style) {\n    root.cursor = dom.style.cursor;\n    dom.style.cursor = cursor;\n  }\n}\nexport function restoreCursor(root) {\n  setCursor(root, root.cursor);\n}\nexport function selectElementByData(elements, data, datum) {\n  return elements.find(d => Object.entries(data).every(([key, value]) => datum(d)[key] === value));\n}\nexport function getPointsR(point, nextPoint) {\n  return Math.sqrt(Math.pow(point[0] - nextPoint[0], 2) + Math.pow(point[1] - nextPoint[1], 2));\n}\n// Points create path.\nexport function getPointsPath(points, isClose = false) {\n  const path = filter(points, d => !!d).map((d, i) => {\n    return [i === 0 ? 'M' : 'L', ...d];\n  });\n  if (isClose) {\n    path.push(['Z']);\n  }\n  return path;\n}\n// Get element.\nexport function getElements(plot) {\n  return plot.querySelectorAll('.element');\n}\n// Get Theta coordinate round path.\nexport function getThetaPath(center, points, isBig = 0) {\n  const path = [['M', ...points[1]]];\n  const innerRadius = getPointsR(center, points[1]);\n  const outerRadius = getPointsR(center, points[0]);\n  if (innerRadius === 0) {\n    path.push(['L', ...points[3]], ['A', outerRadius, outerRadius, 0, isBig, 1, ...points[0]], ['Z']);\n  } else {\n    path.push(['A', innerRadius, innerRadius, 0, isBig, 0, ...points[2]], ['L', ...points[3]], ['A', outerRadius, outerRadius, 0, isBig, 1, ...points[0]], ['Z']);\n  }\n  return path;\n}\nexport function maybeRoot(node, rootOf) {\n  if (rootOf(node)) return node;\n  let root = node.parent;\n  while (root && !rootOf(root)) root = root.parent;\n  return root;\n}\nexport const VALID_FIND_BY_X_MARKS = ['interval', 'point', 'density'];\n/**\n * @description Create function that can find element by event.\n * @returns Element find function.\n */\nexport function createFindElementByEvent({\n  elementsof,\n  root,\n  coordinate,\n  scale,\n  validFindByXMarks = VALID_FIND_BY_X_MARKS\n}) {\n  var _a, _b;\n  let elements = elementsof(root);\n  const getValidFindByXMarks = d => validFindByXMarks.includes(d.markType);\n  const hasValidFindByXMarks = elements.find(getValidFindByXMarks);\n  // Try to find element by x position.\n  if (hasValidFindByXMarks) {\n    elements = elements.filter(getValidFindByXMarks);\n    const scaleX = scale.x;\n    const scaleSeries = scale.series;\n    const bandWidth = (_b = (_a = scaleX === null || scaleX === void 0 ? void 0 : scaleX.getBandWidth) === null || _a === void 0 ? void 0 : _a.call(scaleX)) !== null && _b !== void 0 ? _b : 0;\n    const xof = scaleSeries ? d => {\n      var _a, _b;\n      const seriesCount = Math.round(1 / ((_a = scaleSeries.valueBandWidth) !== null && _a !== void 0 ? _a : 1));\n      return d.__data__.x + ((_b = d.__data__.series) !== null && _b !== void 0 ? _b : 0) * bandWidth + bandWidth / (seriesCount * 2);\n    } : d => d.__data__.x + bandWidth / 2;\n    // Sort for bisector search.\n    elements.sort((a, b) => xof(a) - xof(b));\n    return event => {\n      const mouse = mousePosition(root, event);\n      if (!mouse) return;\n      const [abstractX] = coordinate.invert(mouse);\n      const search = bisector(xof).center;\n      const i = search(elements, abstractX);\n      const target = elements[i];\n      return target;\n    };\n  }\n  // If there is no valid element find by x, just return the target element.\n  return event => {\n    const {\n      target\n    } = event;\n    return maybeRoot(target, node => {\n      if (!node.classList) return false;\n      return node.classList.includes('element');\n    });\n  };\n}", "map": {"version": 3, "names": ["Path", "AABB", "path", "d3Path", "sort", "bisector", "filter", "select", "mapObject", "ELEMENT_CLASS_NAME", "PLOT_CLASS_NAME", "isOrdinalScale", "rect", "isPolar", "isTranspose", "getStyle", "reorder", "angle", "angleBetween", "sub", "traverseElements", "selectG2Elements", "root", "selectAll", "nodes", "d", "__removed__", "selectFacetG2Elements", "target", "viewInstances", "selectFacetViews", "flatMap", "container", "options", "parent<PERSON><PERSON>", "key", "selectPlotArea", "node", "bboxOf", "element", "tagName", "getRenderBounds", "bounds", "getGeometryBounds", "aabb", "setFromTransformedAABB", "getWorldTransform", "mousePosition", "event", "offsetX", "offsetY", "bbox", "min", "x", "y", "max", "x1", "y1", "isOutX", "isOutY", "brushMousePosition", "boundsOfBrushArea", "Math", "x0", "y0", "createColorKey", "view", "__data__", "color", "createXKey", "createDatumof", "views", "Array", "isArray", "keyData", "Map", "marks", "from", "markState", "keys", "map", "mark", "keyed", "data", "index", "<PERSON><PERSON><PERSON>", "viewKey", "get", "STATE_PRIORITIES", "selected", "unselected", "active", "inactive", "default", "STATE_GROUPS", "selection", "highlight", "setElementAttribute", "k", "v", "el", "setAttribute", "createUseState", "style", "elements", "for<PERSON>ach", "currentStyle", "__interactionStyle__", "Object", "assign", "valueof", "useState", "undefined", "STATES", "ORIGINAL", "getStatePriority", "stateName", "getStateGroup", "_a", "entries", "find", "_", "states", "includes", "applyState", "original", "sortedStates", "a", "b", "styleAttributeMap", "state", "stateStyles", "value", "has", "set", "finalStyle", "length", "currentValue", "initState", "updateState", "currentStates", "newStateGroups", "Set", "group", "remainingStates", "existingState", "setState", "removeState", "indexOf", "splice", "hasState", "isEmptyObject", "obj", "mergeState", "descriptor", "defaults", "reduce", "merged", "selectedState", "attr", "oldValue", "newValue", "array", "createValueof", "datum", "elementIndex", "i", "fa", "e", "fe", "renderLink", "link", "coordinate", "__rest", "LINK_CLASS_NAME", "pointsOf", "points", "pathPointsOf", "P0", "P1", "p1", "p2", "p0", "p3", "P", "append", "sortedElements", "e0", "e1", "dx", "p", "moveTo", "lineTo", "closePath", "_b", "fill", "getAttribute", "rest", "className", "toString", "zIndex", "remove", "parentNode", "append<PERSON><PERSON><PERSON>", "offsetTransform", "offset", "t", "transform", "center", "getCenter", "v0", "v1", "a0", "da", "amid", "cos", "dy", "sin", "renderBackground", "document", "background", "scale", "BACKGROUND_CLASS_NAME", "extentOf", "padding", "ax", "invert", "mid", "getBandWidth", "half", "getStep", "sizeXOf", "scaleX", "e2", "sizeYOf", "scaleY", "bandShapeOf", "x2", "y2", "dy1", "cloneShapeOf", "transform<PERSON><PERSON>in", "stroke", "shape", "cloneNode", "isOrdinalShape", "some", "fillOpacity", "lineWidth", "shapeOf", "is", "setCursor", "cursor", "canvas", "getRootNode", "defaultView", "dom", "getContextService", "getDomElement", "restoreCursor", "selectElementByData", "every", "getPointsR", "point", "nextPoint", "sqrt", "pow", "getPointsPath", "isClose", "push", "getElements", "plot", "querySelectorAll", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "isBig", "innerRadius", "outerRadius", "maybeRoot", "rootOf", "parent", "VALID_FIND_BY_X_MARKS", "createFindElementByEvent", "elementsof", "validFindByXMarks", "getValidFindByXMarks", "markType", "hasValidFindByXMarks", "scaleSeries", "series", "bandWidth", "call", "xof", "seriesCount", "round", "valueBandWidth", "mouse", "abstractX", "search", "classList"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\g2\\src\\interaction\\utils.ts"], "sourcesContent": ["import { DisplayObject, Path, AABB } from '@antv/g';\nimport { path as d3Path } from '@antv/vendor/d3-path';\nimport { sort, bisector } from '@antv/vendor/d3-array';\nimport { Vector2 } from '@antv/coord';\nimport { filter } from '@antv/util';\nimport type { PathArray } from '@antv/util';\nimport { G2Element, select } from '../utils/selection';\nimport { mapObject } from '../utils/array';\nimport {\n  G2ViewDescriptor,\n  ELEMENT_CLASS_NAME,\n  PLOT_CLASS_NAME,\n} from '../runtime';\nimport { isOrdinalScale } from '../utils/scale';\nimport { rect } from '../shape/interval/color';\nimport { isPolar, isTranspose } from '../utils/coordinate';\nimport { getStyle } from '../utils/style';\nimport { reorder } from '../shape/utils';\nimport { angle, angleBetween, sub } from '../utils/vector';\nimport { traverseElements } from '../utils/traverse-elements';\n\n/**\n * Given root of chart returns elements to be manipulated\n */\nexport function selectG2Elements(root: DisplayObject): DisplayObject[] {\n  return select(root)\n    .selectAll(`.${ELEMENT_CLASS_NAME}`)\n    .nodes()\n    .filter((d) => !d.__removed__);\n}\n\nexport function selectFacetG2Elements(target, viewInstances): DisplayObject[] {\n  return selectFacetViews(target, viewInstances).flatMap(({ container }) =>\n    selectG2Elements(container),\n  );\n}\n\nexport function selectFacetViews(target, viewInstances) {\n  return viewInstances.filter(\n    (d) => d !== target && d.options.parentKey === target.options.key,\n  );\n}\n\nexport function selectPlotArea(root: DisplayObject): DisplayObject {\n  return select(root).select(`.${PLOT_CLASS_NAME}`).node();\n}\n\nexport function bboxOf(element: DisplayObject) {\n  // The geometry bounds of a group is empty, so return the render bounds.\n  if (element.tagName === 'g') return element.getRenderBounds();\n\n  // Compute the geometry bounds related to the parent.\n  const bounds = element.getGeometryBounds();\n  const aabb = new AABB();\n  aabb.setFromTransformedAABB(bounds, element.getWorldTransform());\n  return aabb;\n}\n\nexport function mousePosition(target, event) {\n  const { offsetX, offsetY } = event;\n  const bbox = bboxOf(target);\n  const {\n    min: [x, y],\n    max: [x1, y1],\n  } = bbox;\n  const isOutX = offsetX < x || offsetX > x1;\n  const isOutY = offsetY < y || offsetY > y1;\n  if (isOutX || isOutY) return null;\n  return [offsetX - x, offsetY - y];\n}\n\n/**\n * @todo Pass bbox rather than calc it here.\n */\nexport function brushMousePosition(target, event) {\n  const { offsetX, offsetY } = event;\n  const [x, y, x1, y1] = boundsOfBrushArea(target);\n  return [\n    Math.min(x1, Math.max(x, offsetX)) - x,\n    Math.min(y1, Math.max(y, offsetY)) - y,\n  ];\n}\n\nexport function boundsOfBrushArea(target) {\n  // Calc bbox after clipping.\n  const bbox = target.getRenderBounds();\n  const {\n    min: [x0, y0],\n    max: [x1, y1],\n  } = bbox;\n  return [x0, y0, x1, y1];\n}\n\nexport function createColorKey(view) {\n  return (element) => element.__data__.color;\n}\n\nexport function createXKey(view) {\n  return (element) => element.__data__.x;\n}\n\nexport function createDatumof(view: G2ViewDescriptor | G2ViewDescriptor[]) {\n  const views = Array.isArray(view) ? view : [view];\n  const keyData = new Map(\n    views.flatMap((view) => {\n      const marks = Array.from(view.markState.keys());\n      return marks.map((mark) => [keyed(view.key, mark.key), mark.data]);\n    }),\n  );\n  return (element) => {\n    const { index, markKey, viewKey } = element.__data__;\n    const data = keyData.get(keyed(viewKey, markKey));\n    return data[index];\n  };\n}\n\n/**\n * A state manager for G2Element.\n * The keys for each state's style start with the state name.\n * { selectedFill, selectedStroke } is for selected state.\n * { unselectedFill, unselectedStroke } is for unselected state.\n */\n\n/**\n * Define state priorities, higher number means higher priority.\n */\nconst STATE_PRIORITIES = {\n  selected: 3,\n  unselected: 3,\n  active: 2,\n  inactive: 2,\n  default: 1,\n};\n\n/**\n * Define state groups, states in the same group are mutually exclusive.\n */\nconst STATE_GROUPS = {\n  selection: ['selected', 'unselected'],\n  highlight: ['active', 'inactive'],\n};\n\nconst setElementAttribute = (element: DisplayObject, k: string, v: string) => {\n  traverseElements(element, (el) => {\n    if ('setAttribute' in el && typeof el.setAttribute === 'function') {\n      (el as DisplayObject).setAttribute(k, v);\n    }\n  });\n};\n\nexport function createUseState(\n  style: Record<string, any>,\n  elements: Element[],\n) {\n  // Apply interaction style to all elements.\n  elements.forEach((element) => {\n    // @ts-ignore\n    const currentStyle = element.__interactionStyle__;\n\n    if (currentStyle) {\n      // @ts-ignore\n      element.__interactionStyle__ = { ...currentStyle, ...style };\n    } else {\n      // @ts-ignore\n      element.__interactionStyle__ = style;\n    }\n  });\n\n  return (valueof = (d, element) => d, setAttribute = setElementAttribute) =>\n    useState(undefined, valueof, setAttribute);\n}\n\nexport function useState(\n  style: Record<string, any> | undefined,\n  valueof = (d, element) => d,\n  setAttribute = setElementAttribute,\n) {\n  const STATES = '__states__';\n  const ORIGINAL = '__ordinal__';\n\n  // Get state priority.\n  const getStatePriority = (stateName) =>\n    STATE_PRIORITIES[stateName] || STATE_PRIORITIES.default;\n\n  // Get the group that a state belongs to.\n  const getStateGroup = (stateName) => {\n    return Object.entries(STATE_GROUPS).find(([_, states]) =>\n      states.includes(stateName),\n    )?.[0];\n  };\n\n  // Mix style for each state and apply it to element.\n  const applyState = (element) => {\n    const { [STATES]: states = [], [ORIGINAL]: original = {} } = element;\n\n    // Sort states by priority.\n    const sortedStates = [...states].sort(\n      (a, b) => getStatePriority(b) - getStatePriority(a),\n    );\n\n    // Create a Map to track the highest priority state for each style attribute.\n    const styleAttributeMap = new Map();\n\n    // Iterate through all states to find the highest priority state for each style attribute.\n    for (const state of sortedStates) {\n      // If style exists, use it directly, else use interaction style on element.\n      const stateStyles =\n        (style ?? element.__interactionStyle__)?.[state] || {};\n      for (const [key, value] of Object.entries(stateStyles)) {\n        if (!styleAttributeMap.has(key)) {\n          styleAttributeMap.set(key, value);\n        }\n      }\n    }\n\n    // Apply styles including original styles.\n    const finalStyle = { ...original };\n    for (const [key, value] of styleAttributeMap.entries()) {\n      finalStyle[key] = value;\n    }\n\n    if (Object.keys(finalStyle).length === 0) return;\n\n    // Apply final styles to the element.\n    for (const [key, value] of Object.entries(finalStyle)) {\n      const currentValue = getStyle(element, key);\n      const v = valueof(value, element);\n      setAttribute(element, key, v);\n      // Store the attribute if it does not exist in original.\n      if (!(key in original)) original[key] = currentValue;\n    }\n    element[ORIGINAL] = original;\n  };\n\n  const initState = (element) => {\n    if (element[STATES]) return;\n    element[STATES] = [];\n    return;\n  };\n\n  /**\n   * Update states and update element, handle conflict states automatically.\n   */\n  const updateState = (element, ...states) => {\n    initState(element);\n    const currentStates = element[STATES];\n\n    // Collect all new state groups.\n    const newStateGroups = new Set(\n      states\n        .map((state) => getStateGroup(state))\n        .filter((group) => group !== undefined),\n    );\n\n    // Exclude old states that are in the new state group.\n    const remainingStates = currentStates.filter(\n      (existingState) => !newStateGroups.has(getStateGroup(existingState)),\n    );\n\n    element[STATES] = [...remainingStates, ...states];\n    applyState(element);\n  };\n\n  /**\n   * Set the states and update element.\n   */\n  const setState = (element, ...states) => {\n    initState(element);\n    element[STATES] = [...states];\n    applyState(element);\n  };\n\n  /**\n   * Remove the states and update element.\n   */\n  const removeState = (element, ...states) => {\n    initState(element);\n    for (const state of states) {\n      const index = element[STATES].indexOf(state);\n      if (index !== -1) {\n        element[STATES].splice(index, 1);\n      }\n    }\n    applyState(element);\n  };\n\n  const hasState = (element, state) => {\n    initState(element);\n    return element[STATES].indexOf(state) !== -1;\n  };\n\n  return {\n    setState,\n    updateState,\n    removeState,\n    hasState,\n  };\n}\n\nfunction isEmptyObject(obj: any): boolean {\n  if (obj === undefined) return true;\n  if (typeof obj !== 'object') return false;\n  return Object.keys(obj).length === 0;\n}\n\n// A function to generate key for mark each view.\nfunction keyed(viewKey, markKey) {\n  return `${viewKey},${markKey}`;\n}\n\nexport function mergeState(options, states) {\n  // Index state by mark key and view key.\n  const views = Array.isArray(options) ? options : [options];\n  const markState = views.flatMap((view) =>\n    view.marks.map((mark) => [keyed(view.key, mark.key), mark.state]),\n  );\n\n  const state = {};\n  // Update each specified state.\n  for (const descriptor of states) {\n    const [key, defaults] = Array.isArray(descriptor)\n      ? descriptor\n      : [descriptor, {}];\n\n    // Update each specified mark state.\n    state[key] = markState.reduce((merged, mark) => {\n      // Normalize state.\n      const [markKey, markState = {}] = mark;\n      const selectedState = isEmptyObject(markState[key])\n        ? defaults\n        : markState[key];\n\n      // Update each state attribute.\n      for (const [attr, value] of Object.entries(selectedState)) {\n        const oldValue = merged[attr];\n        const newValue = (data, index, array, element) => {\n          const k = keyed(element.__data__.viewKey, element.__data__.markKey);\n          if (markKey !== k) return oldValue?.(data, index, array, element);\n          if (typeof value !== 'function') return value;\n          return value(data, index, array, element);\n        };\n        merged[attr] = newValue;\n      }\n      return merged;\n    }, {});\n  }\n  return state;\n}\n\n// @todo Support elements from different view.\nexport function createValueof(elements, datum) {\n  const elementIndex = new Map(elements.map((d, i) => [d, i]));\n  const fa = datum ? elements.map(datum) : elements;\n  return (d, e) => {\n    if (typeof d !== 'function') return d;\n    const i = elementIndex.get(e);\n    const fe = datum ? datum(e) : e;\n    return d(fe, i, fa, e);\n  };\n}\n\nexport function renderLink({\n  link = false,\n  valueof = (d, element) => d,\n  coordinate,\n  ...style\n}) {\n  const LINK_CLASS_NAME = 'element-link';\n\n  if (!link) return [() => {}, () => {}];\n\n  const pointsOf = (element) => element.__data__.points;\n\n  const pathPointsOf = (P0, P1) => {\n    const [, p1, p2] = P0;\n    const [p0, , , p3] = P1;\n    const P: Vector2[] = [p1, p0, p3, p2];\n    return P;\n  };\n\n  const append = (elements) => {\n    if (elements.length <= 1) return;\n\n    // Sort elements by normalized x to avoid cross.\n    const sortedElements = sort<G2Element>(elements, (e0, e1) => {\n      const { x: x0 } = e0.__data__;\n      const { x: x1 } = e1.__data__;\n      const dx = x0 - x1;\n      return dx;\n    });\n\n    for (let i = 1; i < sortedElements.length; i++) {\n      const p = d3Path();\n      const e0 = sortedElements[i - 1];\n      const e1 = sortedElements[i];\n      const [p0, p1, p2, p3] = pathPointsOf(pointsOf(e0), pointsOf(e1));\n      p.moveTo(...p0);\n      p.lineTo(...p1);\n      p.lineTo(...p2);\n      p.lineTo(...p3);\n      p.closePath();\n      const { fill = e0.getAttribute('fill'), ...rest } = mapObject(\n        style,\n        (d) => valueof(d, e0),\n      );\n      const link = new Path({\n        className: LINK_CLASS_NAME,\n        style: {\n          d: p.toString(),\n          fill,\n          zIndex: -2,\n          ...rest,\n        },\n      });\n      // @ts-ignore\n      e0.link?.remove();\n      e0.parentNode.appendChild(link);\n      // @ts-ignore\n      e0.link = link;\n    }\n  };\n\n  const remove = (element) => {\n    element.link?.remove();\n    element.link = null;\n  };\n\n  return [append, remove] as const;\n}\n\n// Apply translate to mock slice out.\nexport function offsetTransform(element, offset, coordinate) {\n  const append = (t) => {\n    const { transform } = element.style;\n    return transform ? `${transform} ${t}` : t;\n  };\n  if (isPolar(coordinate)) {\n    const { points } = element.__data__;\n    const [p0, p1] = isTranspose(coordinate) ? reorder(points) : points;\n    const center = coordinate.getCenter();\n    const v0 = sub(p0, center);\n    const v1 = sub(p1, center);\n    const a0 = angle(v0);\n    const da = angleBetween(v0, v1);\n    const amid = a0 + da / 2;\n    const dx = offset * Math.cos(amid);\n    const dy = offset * Math.sin(amid);\n    return append(`translate(${dx}, ${dy})`);\n  }\n  if (isTranspose(coordinate)) return append(`translate(${offset}, 0)`);\n  return append(`translate(0, ${-offset})`);\n}\n\nexport function renderBackground({\n  document,\n  background,\n  scale,\n  coordinate,\n  valueof,\n  ...rest\n}) {\n  const BACKGROUND_CLASS_NAME = 'element-background';\n\n  // Don't have background.\n  if (!background) return [() => {}, () => {}];\n\n  const extentOf = (scale, x, padding) => {\n    const ax = scale.invert(x);\n    const mid = x + scale.getBandWidth(ax) / 2;\n    const half = scale.getStep(ax) / 2;\n    const offset = half * padding;\n    return [mid - half + offset, mid + half - offset];\n  };\n\n  const sizeXOf = (element, padding) => {\n    const { x: scaleX } = scale;\n    if (!isOrdinalScale(scaleX)) return [0, 1];\n    const { __data__: data } = element;\n    const { x } = data;\n    const [e1, e2] = extentOf(scaleX, x, padding);\n    return [e1, e2];\n  };\n\n  const sizeYOf = (element, padding) => {\n    const { y: scaleY } = scale;\n    if (!isOrdinalScale(scaleY)) return [0, 1];\n    const { __data__: data } = element;\n    const { y } = data;\n    const [e1, e2] = extentOf(scaleY, y, padding);\n    return [e1, e2];\n  };\n\n  const bandShapeOf = (element, style) => {\n    const { padding } = style;\n    const [x1, x2] = sizeXOf(element, padding);\n    const [y1, y2] = sizeYOf(element, padding);\n    const points = [\n      [x1, y1],\n      [x2, y1],\n      [x2, y2],\n      [x1, y2],\n    ].map((d) => coordinate.map(d));\n    const { __data__: data } = element;\n    const { y: dy, y1: dy1 } = data;\n    return rect(document, points, { y: dy, y1: dy1 }, coordinate, style);\n  };\n\n  // Shape without ordinal style.\n  // Clone and scale it.\n  const cloneShapeOf = (element, style) => {\n    const {\n      transform = 'scale(1.2, 1.2)',\n      transformOrigin = 'center center',\n      stroke = '',\n      ...rest\n    } = style;\n    const finalStyle = { transform, transformOrigin, stroke, ...rest };\n    const shape = element.cloneNode(true);\n    for (const [key, value] of Object.entries(finalStyle)) {\n      shape.style[key] = value;\n    }\n    return shape;\n  };\n\n  const isOrdinalShape = () => {\n    const { x, y } = scale;\n    return [x, y].some(isOrdinalScale);\n  };\n\n  const append = (element) => {\n    if (element.background) element.background.remove();\n    const {\n      fill = '#CCD6EC',\n      fillOpacity = 0.3,\n      zIndex = -2,\n      padding = 0.001,\n      lineWidth = 0,\n      ...style\n    } = mapObject(rest, (d) => valueof(d, element));\n    const finalStyle = {\n      ...style,\n      fill,\n      fillOpacity,\n      zIndex,\n      padding,\n      lineWidth,\n    };\n    const shapeOf = isOrdinalShape() ? bandShapeOf : cloneShapeOf;\n    const shape = shapeOf(element, finalStyle);\n    shape.className = BACKGROUND_CLASS_NAME;\n    element.parentNode.parentNode.appendChild(shape);\n    element.background = shape;\n  };\n\n  const remove = (element) => {\n    element.background?.remove();\n    element.background = null;\n  };\n\n  const is = (element) => {\n    return element.className === BACKGROUND_CLASS_NAME;\n  };\n\n  return [append, remove, is] as const;\n}\n\nexport function setCursor(root, cursor) {\n  // @ts-ignore\n  const canvas = root.getRootNode().defaultView;\n  const dom = canvas.getContextService().getDomElement();\n  if (dom?.style) {\n    root.cursor = dom.style.cursor;\n    dom.style.cursor = cursor;\n  }\n}\n\nexport function restoreCursor(root) {\n  setCursor(root, root.cursor);\n}\n\nexport function selectElementByData(elements, data, datum) {\n  return elements.find((d) =>\n    Object.entries(data).every(([key, value]) => datum(d)[key] === value),\n  );\n}\n\nexport function getPointsR(point: number[], nextPoint: number[]) {\n  return Math.sqrt(\n    Math.pow(point[0] - nextPoint[0], 2) + Math.pow(point[1] - nextPoint[1], 2),\n  );\n}\n\n// Points create path.\nexport function getPointsPath(points: number[][], isClose = false) {\n  const path = filter(points, (d) => !!d).map((d, i) => {\n    return [i === 0 ? 'M' : 'L', ...d];\n  }) as PathArray;\n\n  if (isClose) {\n    path.push(['Z']);\n  }\n  return path;\n}\n\n// Get element.\nexport function getElements(plot) {\n  return plot.querySelectorAll('.element');\n}\n\n// Get Theta coordinate round path.\nexport function getThetaPath(\n  center: number[],\n  points: number[][],\n  isBig = 0,\n): PathArray {\n  const path = [['M', ...points[1]]];\n  const innerRadius = getPointsR(center, points[1]);\n  const outerRadius = getPointsR(center, points[0]);\n\n  if (innerRadius === 0) {\n    path.push(\n      ['L', ...points[3]],\n      ['A', outerRadius, outerRadius, 0, isBig, 1, ...points[0]],\n      ['Z'],\n    );\n  } else {\n    path.push(\n      ['A', innerRadius, innerRadius, 0, isBig, 0, ...points[2]],\n      ['L', ...points[3]],\n      ['A', outerRadius, outerRadius, 0, isBig, 1, ...points[0]],\n      ['Z'],\n    );\n  }\n  return path as PathArray;\n}\n\nexport function maybeRoot(node, rootOf) {\n  if (rootOf(node)) return node;\n  let root = node.parent;\n  while (root && !rootOf(root)) root = root.parent;\n  return root;\n}\n\nexport const VALID_FIND_BY_X_MARKS = ['interval', 'point', 'density'];\n/**\n * @description Create function that can find element by event.\n * @returns Element find function.\n */\nexport function createFindElementByEvent({\n  elementsof,\n  root,\n  coordinate,\n  scale,\n  validFindByXMarks = VALID_FIND_BY_X_MARKS,\n}) {\n  let elements = elementsof(root);\n  const getValidFindByXMarks = (d) => validFindByXMarks.includes(d.markType);\n  const hasValidFindByXMarks = elements.find(getValidFindByXMarks);\n\n  // Try to find element by x position.\n  if (hasValidFindByXMarks) {\n    elements = elements.filter(getValidFindByXMarks);\n\n    const scaleX = scale.x;\n    const scaleSeries = scale.series;\n    const bandWidth = scaleX?.getBandWidth?.() ?? 0;\n    const xof = scaleSeries\n      ? (d) => {\n          const seriesCount = Math.round(1 / (scaleSeries.valueBandWidth ?? 1));\n          return (\n            d.__data__.x +\n            (d.__data__.series ?? 0) * bandWidth +\n            bandWidth / (seriesCount * 2)\n          );\n        }\n      : (d) => d.__data__.x + bandWidth / 2;\n\n    // Sort for bisector search.\n    elements.sort((a, b) => xof(a) - xof(b));\n\n    return (event) => {\n      const mouse = mousePosition(root, event);\n      if (!mouse) return;\n      const [abstractX] = coordinate.invert(mouse);\n      const search = bisector(xof).center;\n      const i = search(elements, abstractX);\n      const target = elements[i];\n\n      return target;\n    };\n  }\n\n  // If there is no valid element find by x, just return the target element.\n  return (event) => {\n    const { target } = event;\n    return maybeRoot(target, (node) => {\n      if (!node.classList) return false;\n      return node.classList.includes('element');\n    });\n  };\n}\n"], "mappings": ";;;;;;;;AAAA,SAAwBA,IAAI,EAAEC,IAAI,QAAQ,SAAS;AACnD,SAASC,IAAI,IAAIC,MAAM,QAAQ,sBAAsB;AACrD,SAASC,IAAI,EAAEC,QAAQ,QAAQ,uBAAuB;AAEtD,SAASC,MAAM,QAAQ,YAAY;AAEnC,SAAoBC,MAAM,QAAQ,oBAAoB;AACtD,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAEEC,kBAAkB,EAClBC,eAAe,QACV,YAAY;AACnB,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,IAAI,QAAQ,yBAAyB;AAC9C,SAASC,OAAO,EAAEC,WAAW,QAAQ,qBAAqB;AAC1D,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,OAAO,QAAQ,gBAAgB;AACxC,SAASC,KAAK,EAAEC,YAAY,EAAEC,GAAG,QAAQ,iBAAiB;AAC1D,SAASC,gBAAgB,QAAQ,4BAA4B;AAE7D;;;AAGA,OAAM,SAAUC,gBAAgBA,CAACC,IAAmB;EAClD,OAAOf,MAAM,CAACe,IAAI,CAAC,CAChBC,SAAS,CAAC,IAAId,kBAAkB,EAAE,CAAC,CACnCe,KAAK,EAAE,CACPlB,MAAM,CAAEmB,CAAC,IAAK,CAACA,CAAC,CAACC,WAAW,CAAC;AAClC;AAEA,OAAM,SAAUC,qBAAqBA,CAACC,MAAM,EAAEC,aAAa;EACzD,OAAOC,gBAAgB,CAACF,MAAM,EAAEC,aAAa,CAAC,CAACE,OAAO,CAAC,CAAC;IAAEC;EAAS,CAAE,KACnEX,gBAAgB,CAACW,SAAS,CAAC,CAC5B;AACH;AAEA,OAAM,SAAUF,gBAAgBA,CAACF,MAAM,EAAEC,aAAa;EACpD,OAAOA,aAAa,CAACvB,MAAM,CACxBmB,CAAC,IAAKA,CAAC,KAAKG,MAAM,IAAIH,CAAC,CAACQ,OAAO,CAACC,SAAS,KAAKN,MAAM,CAACK,OAAO,CAACE,GAAG,CAClE;AACH;AAEA,OAAM,SAAUC,cAAcA,CAACd,IAAmB;EAChD,OAAOf,MAAM,CAACe,IAAI,CAAC,CAACf,MAAM,CAAC,IAAIG,eAAe,EAAE,CAAC,CAAC2B,IAAI,EAAE;AAC1D;AAEA,OAAM,SAAUC,MAAMA,CAACC,OAAsB;EAC3C;EACA,IAAIA,OAAO,CAACC,OAAO,KAAK,GAAG,EAAE,OAAOD,OAAO,CAACE,eAAe,EAAE;EAE7D;EACA,MAAMC,MAAM,GAAGH,OAAO,CAACI,iBAAiB,EAAE;EAC1C,MAAMC,IAAI,GAAG,IAAI3C,IAAI,EAAE;EACvB2C,IAAI,CAACC,sBAAsB,CAACH,MAAM,EAAEH,OAAO,CAACO,iBAAiB,EAAE,CAAC;EAChE,OAAOF,IAAI;AACb;AAEA,OAAM,SAAUG,aAAaA,CAACnB,MAAM,EAAEoB,KAAK;EACzC,MAAM;IAAEC,OAAO;IAAEC;EAAO,CAAE,GAAGF,KAAK;EAClC,MAAMG,IAAI,GAAGb,MAAM,CAACV,MAAM,CAAC;EAC3B,MAAM;IACJwB,GAAG,EAAE,CAACC,CAAC,EAAEC,CAAC,CAAC;IACXC,GAAG,EAAE,CAACC,EAAE,EAAEC,EAAE;EAAC,CACd,GAAGN,IAAI;EACR,MAAMO,MAAM,GAAGT,OAAO,GAAGI,CAAC,IAAIJ,OAAO,GAAGO,EAAE;EAC1C,MAAMG,MAAM,GAAGT,OAAO,GAAGI,CAAC,IAAIJ,OAAO,GAAGO,EAAE;EAC1C,IAAIC,MAAM,IAAIC,MAAM,EAAE,OAAO,IAAI;EACjC,OAAO,CAACV,OAAO,GAAGI,CAAC,EAAEH,OAAO,GAAGI,CAAC,CAAC;AACnC;AAEA;;;AAGA,OAAM,SAAUM,kBAAkBA,CAAChC,MAAM,EAAEoB,KAAK;EAC9C,MAAM;IAAEC,OAAO;IAAEC;EAAO,CAAE,GAAGF,KAAK;EAClC,MAAM,CAACK,CAAC,EAAEC,CAAC,EAAEE,EAAE,EAAEC,EAAE,CAAC,GAAGI,iBAAiB,CAACjC,MAAM,CAAC;EAChD,OAAO,CACLkC,IAAI,CAACV,GAAG,CAACI,EAAE,EAAEM,IAAI,CAACP,GAAG,CAACF,CAAC,EAAEJ,OAAO,CAAC,CAAC,GAAGI,CAAC,EACtCS,IAAI,CAACV,GAAG,CAACK,EAAE,EAAEK,IAAI,CAACP,GAAG,CAACD,CAAC,EAAEJ,OAAO,CAAC,CAAC,GAAGI,CAAC,CACvC;AACH;AAEA,OAAM,SAAUO,iBAAiBA,CAACjC,MAAM;EACtC;EACA,MAAMuB,IAAI,GAAGvB,MAAM,CAACa,eAAe,EAAE;EACrC,MAAM;IACJW,GAAG,EAAE,CAACW,EAAE,EAAEC,EAAE,CAAC;IACbT,GAAG,EAAE,CAACC,EAAE,EAAEC,EAAE;EAAC,CACd,GAAGN,IAAI;EACR,OAAO,CAACY,EAAE,EAAEC,EAAE,EAAER,EAAE,EAAEC,EAAE,CAAC;AACzB;AAEA,OAAM,SAAUQ,cAAcA,CAACC,IAAI;EACjC,OAAQ3B,OAAO,IAAKA,OAAO,CAAC4B,QAAQ,CAACC,KAAK;AAC5C;AAEA,OAAM,SAAUC,UAAUA,CAACH,IAAI;EAC7B,OAAQ3B,OAAO,IAAKA,OAAO,CAAC4B,QAAQ,CAACd,CAAC;AACxC;AAEA,OAAM,SAAUiB,aAAaA,CAACJ,IAA2C;EACvE,MAAMK,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACP,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;EACjD,MAAMQ,OAAO,GAAG,IAAIC,GAAG,CACrBJ,KAAK,CAACxC,OAAO,CAAEmC,IAAI,IAAI;IACrB,MAAMU,KAAK,GAAGJ,KAAK,CAACK,IAAI,CAACX,IAAI,CAACY,SAAS,CAACC,IAAI,EAAE,CAAC;IAC/C,OAAOH,KAAK,CAACI,GAAG,CAAEC,IAAI,IAAK,CAACC,KAAK,CAAChB,IAAI,CAAC/B,GAAG,EAAE8C,IAAI,CAAC9C,GAAG,CAAC,EAAE8C,IAAI,CAACE,IAAI,CAAC,CAAC;EACpE,CAAC,CAAC,CACH;EACD,OAAQ5C,OAAO,IAAI;IACjB,MAAM;MAAE6C,KAAK;MAAEC,OAAO;MAAEC;IAAO,CAAE,GAAG/C,OAAO,CAAC4B,QAAQ;IACpD,MAAMgB,IAAI,GAAGT,OAAO,CAACa,GAAG,CAACL,KAAK,CAACI,OAAO,EAAED,OAAO,CAAC,CAAC;IACjD,OAAOF,IAAI,CAACC,KAAK,CAAC;EACpB,CAAC;AACH;AAEA;;;;;;AAOA;;;AAGA,MAAMI,gBAAgB,GAAG;EACvBC,QAAQ,EAAE,CAAC;EACXC,UAAU,EAAE,CAAC;EACbC,MAAM,EAAE,CAAC;EACTC,QAAQ,EAAE,CAAC;EACXC,OAAO,EAAE;CACV;AAED;;;AAGA,MAAMC,YAAY,GAAG;EACnBC,SAAS,EAAE,CAAC,UAAU,EAAE,YAAY,CAAC;EACrCC,SAAS,EAAE,CAAC,QAAQ,EAAE,UAAU;CACjC;AAED,MAAMC,mBAAmB,GAAGA,CAAC1D,OAAsB,EAAE2D,CAAS,EAAEC,CAAS,KAAI;EAC3E/E,gBAAgB,CAACmB,OAAO,EAAG6D,EAAE,IAAI;IAC/B,IAAI,cAAc,IAAIA,EAAE,IAAI,OAAOA,EAAE,CAACC,YAAY,KAAK,UAAU,EAAE;MAChED,EAAoB,CAACC,YAAY,CAACH,CAAC,EAAEC,CAAC,CAAC;;EAE5C,CAAC,CAAC;AACJ,CAAC;AAED,OAAM,SAAUG,cAAcA,CAC5BC,KAA0B,EAC1BC,QAAmB;EAEnB;EACAA,QAAQ,CAACC,OAAO,CAAElE,OAAO,IAAI;IAC3B;IACA,MAAMmE,YAAY,GAAGnE,OAAO,CAACoE,oBAAoB;IAEjD,IAAID,YAAY,EAAE;MAChB;MACAnE,OAAO,CAACoE,oBAAoB,GAAAC,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAQH,YAAY,GAAKH,KAAK,CAAE;KAC7D,MAAM;MACL;MACAhE,OAAO,CAACoE,oBAAoB,GAAGJ,KAAK;;EAExC,CAAC,CAAC;EAEF,OAAO,CAACO,OAAO,GAAGA,CAACrF,CAAC,EAAEc,OAAO,KAAKd,CAAC,EAAE4E,YAAY,GAAGJ,mBAAmB,KACrEc,QAAQ,CAACC,SAAS,EAAEF,OAAO,EAAET,YAAY,CAAC;AAC9C;AAEA,OAAM,SAAUU,QAAQA,CACtBR,KAAsC,EACtCO,OAAO,GAAGA,CAACrF,CAAC,EAAEc,OAAO,KAAKd,CAAC,EAC3B4E,YAAY,GAAGJ,mBAAmB;EAElC,MAAMgB,MAAM,GAAG,YAAY;EAC3B,MAAMC,QAAQ,GAAG,aAAa;EAE9B;EACA,MAAMC,gBAAgB,GAAIC,SAAS,IACjC5B,gBAAgB,CAAC4B,SAAS,CAAC,IAAI5B,gBAAgB,CAACK,OAAO;EAEzD;EACA,MAAMwB,aAAa,GAAID,SAAS,IAAI;;IAClC,OAAO,CAAAE,EAAA,GAAAV,MAAM,CAACW,OAAO,CAACzB,YAAY,CAAC,CAAC0B,IAAI,CAAC,CAAC,CAACC,CAAC,EAAEC,MAAM,CAAC,KACnDA,MAAM,CAACC,QAAQ,CAACP,SAAS,CAAC,CAC3B,cAAAE,EAAA,uBAAAA,EAAA,CAAG,CAAC,CAAC;EACR,CAAC;EAED;EACA,MAAMM,UAAU,GAAIrF,OAAO,IAAI;;IAC7B,MAAM;MAAE,CAAC0E,MAAM,GAAGS,MAAM,GAAG,EAAE;MAAE,CAACR,QAAQ,GAAGW,QAAQ,GAAG;IAAE,CAAE,GAAGtF,OAAO;IAEpE;IACA,MAAMuF,YAAY,GAAG,CAAC,GAAGJ,MAAM,CAAC,CAACtH,IAAI,CACnC,CAAC2H,CAAC,EAAEC,CAAC,KAAKb,gBAAgB,CAACa,CAAC,CAAC,GAAGb,gBAAgB,CAACY,CAAC,CAAC,CACpD;IAED;IACA,MAAME,iBAAiB,GAAG,IAAItD,GAAG,EAAE;IAEnC;IACA,KAAK,MAAMuD,KAAK,IAAIJ,YAAY,EAAE;MAChC;MACA,MAAMK,WAAW,GACf,EAAAb,EAAA,GAACf,KAAK,aAALA,KAAK,cAALA,KAAK,GAAIhE,OAAO,CAACoE,oBAAqB,cAAAW,EAAA,uBAAAA,EAAA,CAAGY,KAAK,CAAC,KAAI,EAAE;MACxD,KAAK,MAAM,CAAC/F,GAAG,EAAEiG,KAAK,CAAC,IAAIxB,MAAM,CAACW,OAAO,CAACY,WAAW,CAAC,EAAE;QACtD,IAAI,CAACF,iBAAiB,CAACI,GAAG,CAAClG,GAAG,CAAC,EAAE;UAC/B8F,iBAAiB,CAACK,GAAG,CAACnG,GAAG,EAAEiG,KAAK,CAAC;;;;IAKvC;IACA,MAAMG,UAAU,GAAA3B,MAAA,CAAAC,MAAA,KAAQgB,QAAQ,CAAE;IAClC,KAAK,MAAM,CAAC1F,GAAG,EAAEiG,KAAK,CAAC,IAAIH,iBAAiB,CAACV,OAAO,EAAE,EAAE;MACtDgB,UAAU,CAACpG,GAAG,CAAC,GAAGiG,KAAK;;IAGzB,IAAIxB,MAAM,CAAC7B,IAAI,CAACwD,UAAU,CAAC,CAACC,MAAM,KAAK,CAAC,EAAE;IAE1C;IACA,KAAK,MAAM,CAACrG,GAAG,EAAEiG,KAAK,CAAC,IAAIxB,MAAM,CAACW,OAAO,CAACgB,UAAU,CAAC,EAAE;MACrD,MAAME,YAAY,GAAG1H,QAAQ,CAACwB,OAAO,EAAEJ,GAAG,CAAC;MAC3C,MAAMgE,CAAC,GAAGW,OAAO,CAACsB,KAAK,EAAE7F,OAAO,CAAC;MACjC8D,YAAY,CAAC9D,OAAO,EAAEJ,GAAG,EAAEgE,CAAC,CAAC;MAC7B;MACA,IAAI,EAAEhE,GAAG,IAAI0F,QAAQ,CAAC,EAAEA,QAAQ,CAAC1F,GAAG,CAAC,GAAGsG,YAAY;;IAEtDlG,OAAO,CAAC2E,QAAQ,CAAC,GAAGW,QAAQ;EAC9B,CAAC;EAED,MAAMa,SAAS,GAAInG,OAAO,IAAI;IAC5B,IAAIA,OAAO,CAAC0E,MAAM,CAAC,EAAE;IACrB1E,OAAO,CAAC0E,MAAM,CAAC,GAAG,EAAE;IACpB;EACF,CAAC;EAED;;;EAGA,MAAM0B,WAAW,GAAGA,CAACpG,OAAO,EAAE,GAAGmF,MAAM,KAAI;IACzCgB,SAAS,CAACnG,OAAO,CAAC;IAClB,MAAMqG,aAAa,GAAGrG,OAAO,CAAC0E,MAAM,CAAC;IAErC;IACA,MAAM4B,cAAc,GAAG,IAAIC,GAAG,CAC5BpB,MAAM,CACH1C,GAAG,CAAEkD,KAAK,IAAKb,aAAa,CAACa,KAAK,CAAC,CAAC,CACpC5H,MAAM,CAAEyI,KAAK,IAAKA,KAAK,KAAK/B,SAAS,CAAC,CAC1C;IAED;IACA,MAAMgC,eAAe,GAAGJ,aAAa,CAACtI,MAAM,CACzC2I,aAAa,IAAK,CAACJ,cAAc,CAACR,GAAG,CAAChB,aAAa,CAAC4B,aAAa,CAAC,CAAC,CACrE;IAED1G,OAAO,CAAC0E,MAAM,CAAC,GAAG,CAAC,GAAG+B,eAAe,EAAE,GAAGtB,MAAM,CAAC;IACjDE,UAAU,CAACrF,OAAO,CAAC;EACrB,CAAC;EAED;;;EAGA,MAAM2G,QAAQ,GAAGA,CAAC3G,OAAO,EAAE,GAAGmF,MAAM,KAAI;IACtCgB,SAAS,CAACnG,OAAO,CAAC;IAClBA,OAAO,CAAC0E,MAAM,CAAC,GAAG,CAAC,GAAGS,MAAM,CAAC;IAC7BE,UAAU,CAACrF,OAAO,CAAC;EACrB,CAAC;EAED;;;EAGA,MAAM4G,WAAW,GAAGA,CAAC5G,OAAO,EAAE,GAAGmF,MAAM,KAAI;IACzCgB,SAAS,CAACnG,OAAO,CAAC;IAClB,KAAK,MAAM2F,KAAK,IAAIR,MAAM,EAAE;MAC1B,MAAMtC,KAAK,GAAG7C,OAAO,CAAC0E,MAAM,CAAC,CAACmC,OAAO,CAAClB,KAAK,CAAC;MAC5C,IAAI9C,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB7C,OAAO,CAAC0E,MAAM,CAAC,CAACoC,MAAM,CAACjE,KAAK,EAAE,CAAC,CAAC;;;IAGpCwC,UAAU,CAACrF,OAAO,CAAC;EACrB,CAAC;EAED,MAAM+G,QAAQ,GAAGA,CAAC/G,OAAO,EAAE2F,KAAK,KAAI;IAClCQ,SAAS,CAACnG,OAAO,CAAC;IAClB,OAAOA,OAAO,CAAC0E,MAAM,CAAC,CAACmC,OAAO,CAAClB,KAAK,CAAC,KAAK,CAAC,CAAC;EAC9C,CAAC;EAED,OAAO;IACLgB,QAAQ;IACRP,WAAW;IACXQ,WAAW;IACXG;GACD;AACH;AAEA,SAASC,aAAaA,CAACC,GAAQ;EAC7B,IAAIA,GAAG,KAAKxC,SAAS,EAAE,OAAO,IAAI;EAClC,IAAI,OAAOwC,GAAG,KAAK,QAAQ,EAAE,OAAO,KAAK;EACzC,OAAO5C,MAAM,CAAC7B,IAAI,CAACyE,GAAG,CAAC,CAAChB,MAAM,KAAK,CAAC;AACtC;AAEA;AACA,SAAStD,KAAKA,CAACI,OAAO,EAAED,OAAO;EAC7B,OAAO,GAAGC,OAAO,IAAID,OAAO,EAAE;AAChC;AAEA,OAAM,SAAUoE,UAAUA,CAACxH,OAAO,EAAEyF,MAAM;EACxC;EACA,MAAMnD,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACxC,OAAO,CAAC,GAAGA,OAAO,GAAG,CAACA,OAAO,CAAC;EAC1D,MAAM6C,SAAS,GAAGP,KAAK,CAACxC,OAAO,CAAEmC,IAAI,IACnCA,IAAI,CAACU,KAAK,CAACI,GAAG,CAAEC,IAAI,IAAK,CAACC,KAAK,CAAChB,IAAI,CAAC/B,GAAG,EAAE8C,IAAI,CAAC9C,GAAG,CAAC,EAAE8C,IAAI,CAACiD,KAAK,CAAC,CAAC,CAClE;EAED,MAAMA,KAAK,GAAG,EAAE;EAChB;EACA,KAAK,MAAMwB,UAAU,IAAIhC,MAAM,EAAE;IAC/B,MAAM,CAACvF,GAAG,EAAEwH,QAAQ,CAAC,GAAGnF,KAAK,CAACC,OAAO,CAACiF,UAAU,CAAC,GAC7CA,UAAU,GACV,CAACA,UAAU,EAAE,EAAE,CAAC;IAEpB;IACAxB,KAAK,CAAC/F,GAAG,CAAC,GAAG2C,SAAS,CAAC8E,MAAM,CAAC,CAACC,MAAM,EAAE5E,IAAI,KAAI;MAC7C;MACA,MAAM,CAACI,OAAO,EAAEP,SAAS,GAAG,EAAE,CAAC,GAAGG,IAAI;MACtC,MAAM6E,aAAa,GAAGP,aAAa,CAACzE,SAAS,CAAC3C,GAAG,CAAC,CAAC,GAC/CwH,QAAQ,GACR7E,SAAS,CAAC3C,GAAG,CAAC;MAElB;MACA,KAAK,MAAM,CAAC4H,IAAI,EAAE3B,KAAK,CAAC,IAAIxB,MAAM,CAACW,OAAO,CAACuC,aAAa,CAAC,EAAE;QACzD,MAAME,QAAQ,GAAGH,MAAM,CAACE,IAAI,CAAC;QAC7B,MAAME,QAAQ,GAAGA,CAAC9E,IAAI,EAAEC,KAAK,EAAE8E,KAAK,EAAE3H,OAAO,KAAI;UAC/C,MAAM2D,CAAC,GAAGhB,KAAK,CAAC3C,OAAO,CAAC4B,QAAQ,CAACmB,OAAO,EAAE/C,OAAO,CAAC4B,QAAQ,CAACkB,OAAO,CAAC;UACnE,IAAIA,OAAO,KAAKa,CAAC,EAAE,OAAO8D,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG7E,IAAI,EAAEC,KAAK,EAAE8E,KAAK,EAAE3H,OAAO,CAAC;UACjE,IAAI,OAAO6F,KAAK,KAAK,UAAU,EAAE,OAAOA,KAAK;UAC7C,OAAOA,KAAK,CAACjD,IAAI,EAAEC,KAAK,EAAE8E,KAAK,EAAE3H,OAAO,CAAC;QAC3C,CAAC;QACDsH,MAAM,CAACE,IAAI,CAAC,GAAGE,QAAQ;;MAEzB,OAAOJ,MAAM;IACf,CAAC,EAAE,EAAE,CAAC;;EAER,OAAO3B,KAAK;AACd;AAEA;AACA,OAAM,SAAUiC,aAAaA,CAAC3D,QAAQ,EAAE4D,KAAK;EAC3C,MAAMC,YAAY,GAAG,IAAI1F,GAAG,CAAC6B,QAAQ,CAACxB,GAAG,CAAC,CAACvD,CAAC,EAAE6I,CAAC,KAAK,CAAC7I,CAAC,EAAE6I,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAMC,EAAE,GAAGH,KAAK,GAAG5D,QAAQ,CAACxB,GAAG,CAACoF,KAAK,CAAC,GAAG5D,QAAQ;EACjD,OAAO,CAAC/E,CAAC,EAAE+I,CAAC,KAAI;IACd,IAAI,OAAO/I,CAAC,KAAK,UAAU,EAAE,OAAOA,CAAC;IACrC,MAAM6I,CAAC,GAAGD,YAAY,CAAC9E,GAAG,CAACiF,CAAC,CAAC;IAC7B,MAAMC,EAAE,GAAGL,KAAK,GAAGA,KAAK,CAACI,CAAC,CAAC,GAAGA,CAAC;IAC/B,OAAO/I,CAAC,CAACgJ,EAAE,EAAEH,CAAC,EAAEC,EAAE,EAAEC,CAAC,CAAC;EACxB,CAAC;AACH;AAEA,OAAM,SAAUE,UAAUA,CAACpD,EAK1B;MAL0B;MACzBqD,IAAI,GAAG,KAAK;MACZ7D,OAAO,GAAGA,CAACrF,CAAC,EAAEc,OAAO,KAAKd,CAAC;MAC3BmJ;IAAU,IAAAtD,EAEX;IADIf,KAAK,GAAAsE,MAAA,CAAAvD,EAAA,EAJiB,iCAK1B,CADS;EAER,MAAMwD,eAAe,GAAG,cAAc;EAEtC,IAAI,CAACH,IAAI,EAAE,OAAO,CAAC,MAAK,CAAE,CAAC,EAAE,MAAK,CAAE,CAAC,CAAC;EAEtC,MAAMI,QAAQ,GAAIxI,OAAO,IAAKA,OAAO,CAAC4B,QAAQ,CAAC6G,MAAM;EAErD,MAAMC,YAAY,GAAGA,CAACC,EAAE,EAAEC,EAAE,KAAI;IAC9B,MAAM,GAAGC,EAAE,EAAEC,EAAE,CAAC,GAAGH,EAAE;IACrB,MAAM,CAACI,EAAE,IAAMC,EAAE,CAAC,GAAGJ,EAAE;IACvB,MAAMK,CAAC,GAAc,CAACJ,EAAE,EAAEE,EAAE,EAAEC,EAAE,EAAEF,EAAE,CAAC;IACrC,OAAOG,CAAC;EACV,CAAC;EAED,MAAMC,MAAM,GAAIjF,QAAQ,IAAI;;IAC1B,IAAIA,QAAQ,CAACgC,MAAM,IAAI,CAAC,EAAE;IAE1B;IACA,MAAMkD,cAAc,GAAGtL,IAAI,CAAYoG,QAAQ,EAAE,CAACmF,EAAE,EAAEC,EAAE,KAAI;MAC1D,MAAM;QAAEvI,CAAC,EAAEU;MAAE,CAAE,GAAG4H,EAAE,CAACxH,QAAQ;MAC7B,MAAM;QAAEd,CAAC,EAAEG;MAAE,CAAE,GAAGoI,EAAE,CAACzH,QAAQ;MAC7B,MAAM0H,EAAE,GAAG9H,EAAE,GAAGP,EAAE;MAClB,OAAOqI,EAAE;IACX,CAAC,CAAC;IAEF,KAAK,IAAIvB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoB,cAAc,CAAClD,MAAM,EAAE8B,CAAC,EAAE,EAAE;MAC9C,MAAMwB,CAAC,GAAG3L,MAAM,EAAE;MAClB,MAAMwL,EAAE,GAAGD,cAAc,CAACpB,CAAC,GAAG,CAAC,CAAC;MAChC,MAAMsB,EAAE,GAAGF,cAAc,CAACpB,CAAC,CAAC;MAC5B,MAAM,CAACgB,EAAE,EAAEF,EAAE,EAAEC,EAAE,EAAEE,EAAE,CAAC,GAAGN,YAAY,CAACF,QAAQ,CAACY,EAAE,CAAC,EAAEZ,QAAQ,CAACa,EAAE,CAAC,CAAC;MACjEE,CAAC,CAACC,MAAM,CAAC,GAAGT,EAAE,CAAC;MACfQ,CAAC,CAACE,MAAM,CAAC,GAAGZ,EAAE,CAAC;MACfU,CAAC,CAACE,MAAM,CAAC,GAAGX,EAAE,CAAC;MACfS,CAAC,CAACE,MAAM,CAAC,GAAGT,EAAE,CAAC;MACfO,CAAC,CAACG,SAAS,EAAE;MACb,MAAMC,EAAA,GAA8C1L,SAAS,CAC3D+F,KAAK,EACJ9E,CAAC,IAAKqF,OAAO,CAACrF,CAAC,EAAEkK,EAAE,CAAC,CACtB;QAHK;UAAEQ,IAAI,GAAGR,EAAE,CAACS,YAAY,CAAC,MAAM;QAAC,IAAAF,EAGrC;QAH0CG,IAAI,GAAAxB,MAAA,CAAAqB,EAAA,EAAzC,QAA2C,CAGhD;MACD,MAAMvB,IAAI,GAAG,IAAI3K,IAAI,CAAC;QACpBsM,SAAS,EAAExB,eAAe;QAC1BvE,KAAK,EAAAK,MAAA,CAAAC,MAAA;UACHpF,CAAC,EAAEqK,CAAC,CAACS,QAAQ,EAAE;UACfJ,IAAI;UACJK,MAAM,EAAE,CAAC;QAAC,GACPH,IAAI;OAEV,CAAC;MACF;MACA,CAAA/E,EAAA,GAAAqE,EAAE,CAAChB,IAAI,cAAArD,EAAA,uBAAAA,EAAA,CAAEmF,MAAM,EAAE;MACjBd,EAAE,CAACe,UAAU,CAACC,WAAW,CAAChC,IAAI,CAAC;MAC/B;MACAgB,EAAE,CAAChB,IAAI,GAAGA,IAAI;;EAElB,CAAC;EAED,MAAM8B,MAAM,GAAIlK,OAAO,IAAI;;IACzB,CAAA+E,EAAA,GAAA/E,OAAO,CAACoI,IAAI,cAAArD,EAAA,uBAAAA,EAAA,CAAEmF,MAAM,EAAE;IACtBlK,OAAO,CAACoI,IAAI,GAAG,IAAI;EACrB,CAAC;EAED,OAAO,CAACc,MAAM,EAAEgB,MAAM,CAAU;AAClC;AAEA;AACA,OAAM,SAAUG,eAAeA,CAACrK,OAAO,EAAEsK,MAAM,EAAEjC,UAAU;EACzD,MAAMa,MAAM,GAAIqB,CAAC,IAAI;IACnB,MAAM;MAAEC;IAAS,CAAE,GAAGxK,OAAO,CAACgE,KAAK;IACnC,OAAOwG,SAAS,GAAG,GAAGA,SAAS,IAAID,CAAC,EAAE,GAAGA,CAAC;EAC5C,CAAC;EACD,IAAIjM,OAAO,CAAC+J,UAAU,CAAC,EAAE;IACvB,MAAM;MAAEI;IAAM,CAAE,GAAGzI,OAAO,CAAC4B,QAAQ;IACnC,MAAM,CAACmH,EAAE,EAAEF,EAAE,CAAC,GAAGtK,WAAW,CAAC8J,UAAU,CAAC,GAAG5J,OAAO,CAACgK,MAAM,CAAC,GAAGA,MAAM;IACnE,MAAMgC,MAAM,GAAGpC,UAAU,CAACqC,SAAS,EAAE;IACrC,MAAMC,EAAE,GAAG/L,GAAG,CAACmK,EAAE,EAAE0B,MAAM,CAAC;IAC1B,MAAMG,EAAE,GAAGhM,GAAG,CAACiK,EAAE,EAAE4B,MAAM,CAAC;IAC1B,MAAMI,EAAE,GAAGnM,KAAK,CAACiM,EAAE,CAAC;IACpB,MAAMG,EAAE,GAAGnM,YAAY,CAACgM,EAAE,EAAEC,EAAE,CAAC;IAC/B,MAAMG,IAAI,GAAGF,EAAE,GAAGC,EAAE,GAAG,CAAC;IACxB,MAAMxB,EAAE,GAAGgB,MAAM,GAAG/I,IAAI,CAACyJ,GAAG,CAACD,IAAI,CAAC;IAClC,MAAME,EAAE,GAAGX,MAAM,GAAG/I,IAAI,CAAC2J,GAAG,CAACH,IAAI,CAAC;IAClC,OAAO7B,MAAM,CAAC,aAAaI,EAAE,KAAK2B,EAAE,GAAG,CAAC;;EAE1C,IAAI1M,WAAW,CAAC8J,UAAU,CAAC,EAAE,OAAOa,MAAM,CAAC,aAAaoB,MAAM,MAAM,CAAC;EACrE,OAAOpB,MAAM,CAAC,gBAAgB,CAACoB,MAAM,GAAG,CAAC;AAC3C;AAEA,OAAM,SAAUa,gBAAgBA,CAACpG,EAOhC;MAPgC;MAC/BqG,QAAQ;MACRC,UAAU;MACVC,KAAK;MACLjD,UAAU;MACV9D;IAAO,IAAAQ,EAER;IADI+E,IAAI,GAAAxB,MAAA,CAAAvD,EAAA,EANwB,4DAOhC,CADQ;EAEP,MAAMwG,qBAAqB,GAAG,oBAAoB;EAElD;EACA,IAAI,CAACF,UAAU,EAAE,OAAO,CAAC,MAAK,CAAE,CAAC,EAAE,MAAK,CAAE,CAAC,CAAC;EAE5C,MAAMG,QAAQ,GAAGA,CAACF,KAAK,EAAExK,CAAC,EAAE2K,OAAO,KAAI;IACrC,MAAMC,EAAE,GAAGJ,KAAK,CAACK,MAAM,CAAC7K,CAAC,CAAC;IAC1B,MAAM8K,GAAG,GAAG9K,CAAC,GAAGwK,KAAK,CAACO,YAAY,CAACH,EAAE,CAAC,GAAG,CAAC;IAC1C,MAAMI,IAAI,GAAGR,KAAK,CAACS,OAAO,CAACL,EAAE,CAAC,GAAG,CAAC;IAClC,MAAMpB,MAAM,GAAGwB,IAAI,GAAGL,OAAO;IAC7B,OAAO,CAACG,GAAG,GAAGE,IAAI,GAAGxB,MAAM,EAAEsB,GAAG,GAAGE,IAAI,GAAGxB,MAAM,CAAC;EACnD,CAAC;EAED,MAAM0B,OAAO,GAAGA,CAAChM,OAAO,EAAEyL,OAAO,KAAI;IACnC,MAAM;MAAE3K,CAAC,EAAEmL;IAAM,CAAE,GAAGX,KAAK;IAC3B,IAAI,CAAClN,cAAc,CAAC6N,MAAM,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;IAC1C,MAAM;MAAErK,QAAQ,EAAEgB;IAAI,CAAE,GAAG5C,OAAO;IAClC,MAAM;MAAEc;IAAC,CAAE,GAAG8B,IAAI;IAClB,MAAM,CAACyG,EAAE,EAAE6C,EAAE,CAAC,GAAGV,QAAQ,CAACS,MAAM,EAAEnL,CAAC,EAAE2K,OAAO,CAAC;IAC7C,OAAO,CAACpC,EAAE,EAAE6C,EAAE,CAAC;EACjB,CAAC;EAED,MAAMC,OAAO,GAAGA,CAACnM,OAAO,EAAEyL,OAAO,KAAI;IACnC,MAAM;MAAE1K,CAAC,EAAEqL;IAAM,CAAE,GAAGd,KAAK;IAC3B,IAAI,CAAClN,cAAc,CAACgO,MAAM,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;IAC1C,MAAM;MAAExK,QAAQ,EAAEgB;IAAI,CAAE,GAAG5C,OAAO;IAClC,MAAM;MAAEe;IAAC,CAAE,GAAG6B,IAAI;IAClB,MAAM,CAACyG,EAAE,EAAE6C,EAAE,CAAC,GAAGV,QAAQ,CAACY,MAAM,EAAErL,CAAC,EAAE0K,OAAO,CAAC;IAC7C,OAAO,CAACpC,EAAE,EAAE6C,EAAE,CAAC;EACjB,CAAC;EAED,MAAMG,WAAW,GAAGA,CAACrM,OAAO,EAAEgE,KAAK,KAAI;IACrC,MAAM;MAAEyH;IAAO,CAAE,GAAGzH,KAAK;IACzB,MAAM,CAAC/C,EAAE,EAAEqL,EAAE,CAAC,GAAGN,OAAO,CAAChM,OAAO,EAAEyL,OAAO,CAAC;IAC1C,MAAM,CAACvK,EAAE,EAAEqL,EAAE,CAAC,GAAGJ,OAAO,CAACnM,OAAO,EAAEyL,OAAO,CAAC;IAC1C,MAAMhD,MAAM,GAAG,CACb,CAACxH,EAAE,EAAEC,EAAE,CAAC,EACR,CAACoL,EAAE,EAAEpL,EAAE,CAAC,EACR,CAACoL,EAAE,EAAEC,EAAE,CAAC,EACR,CAACtL,EAAE,EAAEsL,EAAE,CAAC,CACT,CAAC9J,GAAG,CAAEvD,CAAC,IAAKmJ,UAAU,CAAC5F,GAAG,CAACvD,CAAC,CAAC,CAAC;IAC/B,MAAM;MAAE0C,QAAQ,EAAEgB;IAAI,CAAE,GAAG5C,OAAO;IAClC,MAAM;MAAEe,CAAC,EAAEkK,EAAE;MAAE/J,EAAE,EAAEsL;IAAG,CAAE,GAAG5J,IAAI;IAC/B,OAAOvE,IAAI,CAAC+M,QAAQ,EAAE3C,MAAM,EAAE;MAAE1H,CAAC,EAAEkK,EAAE;MAAE/J,EAAE,EAAEsL;IAAG,CAAE,EAAEnE,UAAU,EAAErE,KAAK,CAAC;EACtE,CAAC;EAED;EACA;EACA,MAAMyI,YAAY,GAAGA,CAACzM,OAAO,EAAEgE,KAAK,KAAI;IACtC,MAAM;QACJwG,SAAS,GAAG,iBAAiB;QAC7BkC,eAAe,GAAG,eAAe;QACjCC,MAAM,GAAG;MAAE,IAET3I,KAAK;MADJ8F,IAAI,GAAAxB,MAAA,CACLtE,KAAK,EALH,0CAKL,CAAQ;IACT,MAAMgC,UAAU,GAAA3B,MAAA,CAAAC,MAAA;MAAKkG,SAAS;MAAEkC,eAAe;MAAEC;IAAM,GAAK7C,IAAI,CAAE;IAClE,MAAM8C,KAAK,GAAG5M,OAAO,CAAC6M,SAAS,CAAC,IAAI,CAAC;IACrC,KAAK,MAAM,CAACjN,GAAG,EAAEiG,KAAK,CAAC,IAAIxB,MAAM,CAACW,OAAO,CAACgB,UAAU,CAAC,EAAE;MACrD4G,KAAK,CAAC5I,KAAK,CAACpE,GAAG,CAAC,GAAGiG,KAAK;;IAE1B,OAAO+G,KAAK;EACd,CAAC;EAED,MAAME,cAAc,GAAGA,CAAA,KAAK;IAC1B,MAAM;MAAEhM,CAAC;MAAEC;IAAC,CAAE,GAAGuK,KAAK;IACtB,OAAO,CAACxK,CAAC,EAAEC,CAAC,CAAC,CAACgM,IAAI,CAAC3O,cAAc,CAAC;EACpC,CAAC;EAED,MAAM8K,MAAM,GAAIlJ,OAAO,IAAI;IACzB,IAAIA,OAAO,CAACqL,UAAU,EAAErL,OAAO,CAACqL,UAAU,CAACnB,MAAM,EAAE;IACnD,MAAMnF,EAAA,GAOF9G,SAAS,CAAC6L,IAAI,EAAG5K,CAAC,IAAKqF,OAAO,CAACrF,CAAC,EAAEc,OAAO,CAAC,CAAC;MAPzC;QACJ4J,IAAI,GAAG,SAAS;QAChBoD,WAAW,GAAG,GAAG;QACjB/C,MAAM,GAAG,CAAC,CAAC;QACXwB,OAAO,GAAG,KAAK;QACfwB,SAAS,GAAG;MAAC,IAAAlI,EAEgC;MAD1Cf,KAAK,GAAAsE,MAAA,CAAAvD,EAAA,EANJ,yDAOL,CAA8C;IAC/C,MAAMiB,UAAU,GAAA3B,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACXN,KAAK;MACR4F,IAAI;MACJoD,WAAW;MACX/C,MAAM;MACNwB,OAAO;MACPwB;IAAS,EACV;IACD,MAAMC,OAAO,GAAGJ,cAAc,EAAE,GAAGT,WAAW,GAAGI,YAAY;IAC7D,MAAMG,KAAK,GAAGM,OAAO,CAAClN,OAAO,EAAEgG,UAAU,CAAC;IAC1C4G,KAAK,CAAC7C,SAAS,GAAGwB,qBAAqB;IACvCvL,OAAO,CAACmK,UAAU,CAACA,UAAU,CAACC,WAAW,CAACwC,KAAK,CAAC;IAChD5M,OAAO,CAACqL,UAAU,GAAGuB,KAAK;EAC5B,CAAC;EAED,MAAM1C,MAAM,GAAIlK,OAAO,IAAI;;IACzB,CAAA+E,EAAA,GAAA/E,OAAO,CAACqL,UAAU,cAAAtG,EAAA,uBAAAA,EAAA,CAAEmF,MAAM,EAAE;IAC5BlK,OAAO,CAACqL,UAAU,GAAG,IAAI;EAC3B,CAAC;EAED,MAAM8B,EAAE,GAAInN,OAAO,IAAI;IACrB,OAAOA,OAAO,CAAC+J,SAAS,KAAKwB,qBAAqB;EACpD,CAAC;EAED,OAAO,CAACrC,MAAM,EAAEgB,MAAM,EAAEiD,EAAE,CAAU;AACtC;AAEA,OAAM,SAAUC,SAASA,CAACrO,IAAI,EAAEsO,MAAM;EACpC;EACA,MAAMC,MAAM,GAAGvO,IAAI,CAACwO,WAAW,EAAE,CAACC,WAAW;EAC7C,MAAMC,GAAG,GAAGH,MAAM,CAACI,iBAAiB,EAAE,CAACC,aAAa,EAAE;EACtD,IAAIF,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEzJ,KAAK,EAAE;IACdjF,IAAI,CAACsO,MAAM,GAAGI,GAAG,CAACzJ,KAAK,CAACqJ,MAAM;IAC9BI,GAAG,CAACzJ,KAAK,CAACqJ,MAAM,GAAGA,MAAM;;AAE7B;AAEA,OAAM,SAAUO,aAAaA,CAAC7O,IAAI;EAChCqO,SAAS,CAACrO,IAAI,EAAEA,IAAI,CAACsO,MAAM,CAAC;AAC9B;AAEA,OAAM,SAAUQ,mBAAmBA,CAAC5J,QAAQ,EAAErB,IAAI,EAAEiF,KAAK;EACvD,OAAO5D,QAAQ,CAACgB,IAAI,CAAE/F,CAAC,IACrBmF,MAAM,CAACW,OAAO,CAACpC,IAAI,CAAC,CAACkL,KAAK,CAAC,CAAC,CAAClO,GAAG,EAAEiG,KAAK,CAAC,KAAKgC,KAAK,CAAC3I,CAAC,CAAC,CAACU,GAAG,CAAC,KAAKiG,KAAK,CAAC,CACtE;AACH;AAEA,OAAM,SAAUkI,UAAUA,CAACC,KAAe,EAAEC,SAAmB;EAC7D,OAAO1M,IAAI,CAAC2M,IAAI,CACd3M,IAAI,CAAC4M,GAAG,CAACH,KAAK,CAAC,CAAC,CAAC,GAAGC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG1M,IAAI,CAAC4M,GAAG,CAACH,KAAK,CAAC,CAAC,CAAC,GAAGC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAC5E;AACH;AAEA;AACA,OAAM,SAAUG,aAAaA,CAAC3F,MAAkB,EAAE4F,OAAO,GAAG,KAAK;EAC/D,MAAM1Q,IAAI,GAAGI,MAAM,CAAC0K,MAAM,EAAGvJ,CAAC,IAAK,CAAC,CAACA,CAAC,CAAC,CAACuD,GAAG,CAAC,CAACvD,CAAC,EAAE6I,CAAC,KAAI;IACnD,OAAO,CAACA,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,GAAG7I,CAAC,CAAC;EACpC,CAAC,CAAc;EAEf,IAAImP,OAAO,EAAE;IACX1Q,IAAI,CAAC2Q,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;;EAElB,OAAO3Q,IAAI;AACb;AAEA;AACA,OAAM,SAAU4Q,WAAWA,CAACC,IAAI;EAC9B,OAAOA,IAAI,CAACC,gBAAgB,CAAC,UAAU,CAAC;AAC1C;AAEA;AACA,OAAM,SAAUC,YAAYA,CAC1BjE,MAAgB,EAChBhC,MAAkB,EAClBkG,KAAK,GAAG,CAAC;EAET,MAAMhR,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG8K,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;EAClC,MAAMmG,WAAW,GAAGb,UAAU,CAACtD,MAAM,EAAEhC,MAAM,CAAC,CAAC,CAAC,CAAC;EACjD,MAAMoG,WAAW,GAAGd,UAAU,CAACtD,MAAM,EAAEhC,MAAM,CAAC,CAAC,CAAC,CAAC;EAEjD,IAAImG,WAAW,KAAK,CAAC,EAAE;IACrBjR,IAAI,CAAC2Q,IAAI,CACP,CAAC,GAAG,EAAE,GAAG7F,MAAM,CAAC,CAAC,CAAC,CAAC,EACnB,CAAC,GAAG,EAAEoG,WAAW,EAAEA,WAAW,EAAE,CAAC,EAAEF,KAAK,EAAE,CAAC,EAAE,GAAGlG,MAAM,CAAC,CAAC,CAAC,CAAC,EAC1D,CAAC,GAAG,CAAC,CACN;GACF,MAAM;IACL9K,IAAI,CAAC2Q,IAAI,CACP,CAAC,GAAG,EAAEM,WAAW,EAAEA,WAAW,EAAE,CAAC,EAAED,KAAK,EAAE,CAAC,EAAE,GAAGlG,MAAM,CAAC,CAAC,CAAC,CAAC,EAC1D,CAAC,GAAG,EAAE,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,EACnB,CAAC,GAAG,EAAEoG,WAAW,EAAEA,WAAW,EAAE,CAAC,EAAEF,KAAK,EAAE,CAAC,EAAE,GAAGlG,MAAM,CAAC,CAAC,CAAC,CAAC,EAC1D,CAAC,GAAG,CAAC,CACN;;EAEH,OAAO9K,IAAiB;AAC1B;AAEA,OAAM,SAAUmR,SAASA,CAAChP,IAAI,EAAEiP,MAAM;EACpC,IAAIA,MAAM,CAACjP,IAAI,CAAC,EAAE,OAAOA,IAAI;EAC7B,IAAIf,IAAI,GAAGe,IAAI,CAACkP,MAAM;EACtB,OAAOjQ,IAAI,IAAI,CAACgQ,MAAM,CAAChQ,IAAI,CAAC,EAAEA,IAAI,GAAGA,IAAI,CAACiQ,MAAM;EAChD,OAAOjQ,IAAI;AACb;AAEA,OAAO,MAAMkQ,qBAAqB,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,SAAS,CAAC;AACrE;;;;AAIA,OAAM,SAAUC,wBAAwBA,CAAC;EACvCC,UAAU;EACVpQ,IAAI;EACJsJ,UAAU;EACViD,KAAK;EACL8D,iBAAiB,GAAGH;AAAqB,CAC1C;;EACC,IAAIhL,QAAQ,GAAGkL,UAAU,CAACpQ,IAAI,CAAC;EAC/B,MAAMsQ,oBAAoB,GAAInQ,CAAC,IAAKkQ,iBAAiB,CAAChK,QAAQ,CAAClG,CAAC,CAACoQ,QAAQ,CAAC;EAC1E,MAAMC,oBAAoB,GAAGtL,QAAQ,CAACgB,IAAI,CAACoK,oBAAoB,CAAC;EAEhE;EACA,IAAIE,oBAAoB,EAAE;IACxBtL,QAAQ,GAAGA,QAAQ,CAAClG,MAAM,CAACsR,oBAAoB,CAAC;IAEhD,MAAMpD,MAAM,GAAGX,KAAK,CAACxK,CAAC;IACtB,MAAM0O,WAAW,GAAGlE,KAAK,CAACmE,MAAM;IAChC,MAAMC,SAAS,GAAG,CAAA/F,EAAA,IAAA5E,EAAA,GAAAkH,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEJ,YAAY,cAAA9G,EAAA,uBAAAA,EAAA,CAAA4K,IAAA,CAAA1D,MAAA,CAAI,cAAAtC,EAAA,cAAAA,EAAA,GAAI,CAAC;IAC/C,MAAMiG,GAAG,GAAGJ,WAAW,GAClBtQ,CAAC,IAAI;;MACJ,MAAM2Q,WAAW,GAAGtO,IAAI,CAACuO,KAAK,CAAC,CAAC,IAAI,CAAA/K,EAAA,GAAAyK,WAAW,CAACO,cAAc,cAAAhL,EAAA,cAAAA,EAAA,GAAI,CAAC,CAAC,CAAC;MACrE,OACE7F,CAAC,CAAC0C,QAAQ,CAACd,CAAC,GACZ,CAAC,CAAA6I,EAAA,GAAAzK,CAAC,CAAC0C,QAAQ,CAAC6N,MAAM,cAAA9F,EAAA,cAAAA,EAAA,GAAI,CAAC,IAAI+F,SAAS,GACpCA,SAAS,IAAIG,WAAW,GAAG,CAAC,CAAC;IAEjC,CAAC,GACA3Q,CAAC,IAAKA,CAAC,CAAC0C,QAAQ,CAACd,CAAC,GAAG4O,SAAS,GAAG,CAAC;IAEvC;IACAzL,QAAQ,CAACpG,IAAI,CAAC,CAAC2H,CAAC,EAAEC,CAAC,KAAKmK,GAAG,CAACpK,CAAC,CAAC,GAAGoK,GAAG,CAACnK,CAAC,CAAC,CAAC;IAExC,OAAQhF,KAAK,IAAI;MACf,MAAMuP,KAAK,GAAGxP,aAAa,CAACzB,IAAI,EAAE0B,KAAK,CAAC;MACxC,IAAI,CAACuP,KAAK,EAAE;MACZ,MAAM,CAACC,SAAS,CAAC,GAAG5H,UAAU,CAACsD,MAAM,CAACqE,KAAK,CAAC;MAC5C,MAAME,MAAM,GAAGpS,QAAQ,CAAC8R,GAAG,CAAC,CAACnF,MAAM;MACnC,MAAM1C,CAAC,GAAGmI,MAAM,CAACjM,QAAQ,EAAEgM,SAAS,CAAC;MACrC,MAAM5Q,MAAM,GAAG4E,QAAQ,CAAC8D,CAAC,CAAC;MAE1B,OAAO1I,MAAM;IACf,CAAC;;EAGH;EACA,OAAQoB,KAAK,IAAI;IACf,MAAM;MAAEpB;IAAM,CAAE,GAAGoB,KAAK;IACxB,OAAOqO,SAAS,CAACzP,MAAM,EAAGS,IAAI,IAAI;MAChC,IAAI,CAACA,IAAI,CAACqQ,SAAS,EAAE,OAAO,KAAK;MACjC,OAAOrQ,IAAI,CAACqQ,SAAS,CAAC/K,QAAQ,CAAC,SAAS,CAAC;IAC3C,CAAC,CAAC;EACJ,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}