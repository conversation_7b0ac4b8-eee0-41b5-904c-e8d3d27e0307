{"ast": null, "code": "var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nimport { flow, transformOptions, isArray, set, get, fieldAdapter, isFunction } from '../../utils';\n/**\n * @param chart\n * @param options\n */\nexport function adaptor(params) {\n  /**\n   * @description 当 angleField 总算为 0 时，设置默认样式\n   * @link https://github.com/ant-design/ant-design-charts/issues/2324\n   */\n  var emptyData = function (params) {\n    var options = params.options;\n    var angleField = options.angleField,\n      data = options.data,\n      label = options.label,\n      tooltip = options.tooltip,\n      colorField = options.colorField;\n    var getColorValue = fieldAdapter(colorField);\n    if (isArray(data) && data.length > 0) {\n      var sum = data.reduce(function (a, b) {\n        return a + b[angleField];\n      }, 0);\n      if (sum === 0) {\n        var normalization = data.map(function (item) {\n          var _a;\n          return __assign(__assign({}, item), (_a = {}, _a[angleField] = 1, _a));\n        });\n        set(options, 'data', normalization);\n        if (label) {\n          var isColorField = colorField === get(label, 'text');\n          set(options, 'label', __assign(__assign({}, label), isColorField ? {} : {\n            formatter: function () {\n              return 0;\n            }\n          }));\n        }\n        if (tooltip !== false) {\n          if (isFunction(tooltip)) {\n            set(options, 'tooltip', function (arg, index, items) {\n              var _a;\n              return tooltip(__assign(__assign({}, arg), (_a = {}, _a[angleField] = 0, _a)), index, items.map(function (item) {\n                var _a;\n                return __assign(__assign({}, item), (_a = {}, _a[angleField] = 0, _a));\n              }));\n            });\n          } else {\n            set(options, 'tooltip', __assign(__assign({}, tooltip), {\n              items: [function (arg, i, d) {\n                return {\n                  name: getColorValue(arg, i, d),\n                  value: 0\n                };\n              }]\n            }));\n          }\n        }\n      }\n    }\n    return params;\n  };\n  return flow(emptyData, transformOptions)(params);\n}", "map": {"version": 3, "names": ["__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "flow", "transformOptions", "isArray", "set", "get", "fieldAdapter", "isFunction", "adaptor", "params", "emptyData", "options", "angleField", "data", "label", "tooltip", "colorField", "getColorValue", "sum", "reduce", "a", "b", "normalization", "map", "item", "_a", "isColorField", "formatter", "arg", "index", "items", "d", "name", "value"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/@ant-design/plots/es/core/plots/pie/adaptor.js"], "sourcesContent": ["var __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nimport { flow, transformOptions, isArray, set, get, fieldAdapter, isFunction } from '../../utils';\n/**\n * @param chart\n * @param options\n */\nexport function adaptor(params) {\n    /**\n     * @description 当 angleField 总算为 0 时，设置默认样式\n     * @link https://github.com/ant-design/ant-design-charts/issues/2324\n     */\n    var emptyData = function (params) {\n        var options = params.options;\n        var angleField = options.angleField, data = options.data, label = options.label, tooltip = options.tooltip, colorField = options.colorField;\n        var getColorValue = fieldAdapter(colorField);\n        if (isArray(data) && data.length > 0) {\n            var sum = data.reduce(function (a, b) { return a + b[angleField]; }, 0);\n            if (sum === 0) {\n                var normalization = data.map(function (item) {\n                    var _a;\n                    return (__assign(__assign({}, item), (_a = {}, _a[angleField] = 1, _a)));\n                });\n                set(options, 'data', normalization);\n                if (label) {\n                    var isColorField = colorField === get(label, 'text');\n                    set(options, 'label', __assign(__assign({}, label), (isColorField ? {} : { formatter: function () { return 0; } })));\n                }\n                if (tooltip !== false) {\n                    if (isFunction(tooltip)) {\n                        set(options, 'tooltip', function (arg, index, items) {\n                            var _a;\n                            return tooltip(__assign(__assign({}, arg), (_a = {}, _a[angleField] = 0, _a)), index, items.map(function (item) {\n                                var _a;\n                                return (__assign(__assign({}, item), (_a = {}, _a[angleField] = 0, _a)));\n                            }));\n                        });\n                    }\n                    else {\n                        set(options, 'tooltip', __assign(__assign({}, tooltip), { items: [\n                                function (arg, i, d) {\n                                    return {\n                                        name: getColorValue(arg, i, d),\n                                        value: 0,\n                                    };\n                                },\n                            ] }));\n                    }\n                }\n            }\n        }\n        return params;\n    };\n    return flow(emptyData, transformOptions)(params);\n}\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,YAAY;EAClDA,QAAQ,GAAGC,MAAM,CAACC,MAAM,IAAI,UAASC,CAAC,EAAE;IACpC,KAAK,IAAIC,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEH,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACjDD,CAAC,GAAGG,SAAS,CAACF,CAAC,CAAC;MAChB,KAAK,IAAII,CAAC,IAAIL,CAAC,EAAE,IAAIH,MAAM,CAACS,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,CAAC,EAAEK,CAAC,CAAC,EAC3DN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IACnB;IACA,OAAON,CAAC;EACZ,CAAC;EACD,OAAOH,QAAQ,CAACa,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAC1C,CAAC;AACD,SAASO,IAAI,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,GAAG,EAAEC,GAAG,EAAEC,YAAY,EAAEC,UAAU,QAAQ,aAAa;AACjG;AACA;AACA;AACA;AACA,OAAO,SAASC,OAAOA,CAACC,MAAM,EAAE;EAC5B;AACJ;AACA;AACA;EACI,IAAIC,SAAS,GAAG,SAAAA,CAAUD,MAAM,EAAE;IAC9B,IAAIE,OAAO,GAAGF,MAAM,CAACE,OAAO;IAC5B,IAAIC,UAAU,GAAGD,OAAO,CAACC,UAAU;MAAEC,IAAI,GAAGF,OAAO,CAACE,IAAI;MAAEC,KAAK,GAAGH,OAAO,CAACG,KAAK;MAAEC,OAAO,GAAGJ,OAAO,CAACI,OAAO;MAAEC,UAAU,GAAGL,OAAO,CAACK,UAAU;IAC3I,IAAIC,aAAa,GAAGX,YAAY,CAACU,UAAU,CAAC;IAC5C,IAAIb,OAAO,CAACU,IAAI,CAAC,IAAIA,IAAI,CAAClB,MAAM,GAAG,CAAC,EAAE;MAClC,IAAIuB,GAAG,GAAGL,IAAI,CAACM,MAAM,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;QAAE,OAAOD,CAAC,GAAGC,CAAC,CAACT,UAAU,CAAC;MAAE,CAAC,EAAE,CAAC,CAAC;MACvE,IAAIM,GAAG,KAAK,CAAC,EAAE;QACX,IAAII,aAAa,GAAGT,IAAI,CAACU,GAAG,CAAC,UAAUC,IAAI,EAAE;UACzC,IAAIC,EAAE;UACN,OAAQtC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEqC,IAAI,CAAC,GAAGC,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,CAACb,UAAU,CAAC,GAAG,CAAC,EAAEa,EAAE,CAAC,CAAC;QAC3E,CAAC,CAAC;QACFrB,GAAG,CAACO,OAAO,EAAE,MAAM,EAAEW,aAAa,CAAC;QACnC,IAAIR,KAAK,EAAE;UACP,IAAIY,YAAY,GAAGV,UAAU,KAAKX,GAAG,CAACS,KAAK,EAAE,MAAM,CAAC;UACpDV,GAAG,CAACO,OAAO,EAAE,OAAO,EAAExB,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE2B,KAAK,CAAC,EAAGY,YAAY,GAAG,CAAC,CAAC,GAAG;YAAEC,SAAS,EAAE,SAAAA,CAAA,EAAY;cAAE,OAAO,CAAC;YAAE;UAAE,CAAE,CAAC,CAAC;QACxH;QACA,IAAIZ,OAAO,KAAK,KAAK,EAAE;UACnB,IAAIR,UAAU,CAACQ,OAAO,CAAC,EAAE;YACrBX,GAAG,CAACO,OAAO,EAAE,SAAS,EAAE,UAAUiB,GAAG,EAAEC,KAAK,EAAEC,KAAK,EAAE;cACjD,IAAIL,EAAE;cACN,OAAOV,OAAO,CAAC5B,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEyC,GAAG,CAAC,GAAGH,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,CAACb,UAAU,CAAC,GAAG,CAAC,EAAEa,EAAE,CAAC,CAAC,EAAEI,KAAK,EAAEC,KAAK,CAACP,GAAG,CAAC,UAAUC,IAAI,EAAE;gBAC5G,IAAIC,EAAE;gBACN,OAAQtC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEqC,IAAI,CAAC,GAAGC,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,CAACb,UAAU,CAAC,GAAG,CAAC,EAAEa,EAAE,CAAC,CAAC;cAC3E,CAAC,CAAC,CAAC;YACP,CAAC,CAAC;UACN,CAAC,MACI;YACDrB,GAAG,CAACO,OAAO,EAAE,SAAS,EAAExB,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE4B,OAAO,CAAC,EAAE;cAAEe,KAAK,EAAE,CACzD,UAAUF,GAAG,EAAEpC,CAAC,EAAEuC,CAAC,EAAE;gBACjB,OAAO;kBACHC,IAAI,EAAEf,aAAa,CAACW,GAAG,EAAEpC,CAAC,EAAEuC,CAAC,CAAC;kBAC9BE,KAAK,EAAE;gBACX,CAAC;cACL,CAAC;YACH,CAAC,CAAC,CAAC;UACb;QACJ;MACJ;IACJ;IACA,OAAOxB,MAAM;EACjB,CAAC;EACD,OAAOR,IAAI,CAACS,SAAS,EAAER,gBAAgB,CAAC,CAACO,MAAM,CAAC;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}