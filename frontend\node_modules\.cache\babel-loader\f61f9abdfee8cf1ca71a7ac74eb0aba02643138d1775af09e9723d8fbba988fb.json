{"ast": null, "code": "import { isOverlap, parseAABB } from '../utils/bounds';\nimport { hide, show } from '../utils/style';\n/**\n * Hide the label when overlap.\n */\nexport const OverlapHide = options => {\n  const {\n    priority\n  } = options;\n  return labels => {\n    const displayLabels = [];\n    // When overlap, will hide the next label.\n    if (priority) labels.sort(priority);\n    labels.forEach(l => {\n      show(l);\n      const b1 = l.getLocalBounds();\n      const overlaping = displayLabels.some(dl => isOverlap(parseAABB(b1), parseAABB(dl.getLocalBounds())));\n      if (overlaping) hide(l);else displayLabels.push(l);\n    });\n    return labels;\n  };\n};", "map": {"version": 3, "names": ["isOverlap", "parseAABB", "hide", "show", "OverlapHide", "options", "priority", "labels", "displayLabels", "sort", "for<PERSON>ach", "l", "b1", "getLocalBounds", "overlaping", "some", "dl", "push"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\g2\\src\\label-transform\\overlapHide.ts"], "sourcesContent": ["import { DisplayObject } from '@antv/g';\nimport { LabelTransformComponent as LLC } from '../runtime';\nimport { OverlapHideLabelTransform } from '../spec';\nimport { isOverlap, parseAABB } from '../utils/bounds';\nimport { hide, show } from '../utils/style';\n\nexport type OverlapHideOptions = Omit<OverlapHideLabelTransform, 'type'>;\n\n/**\n * Hide the label when overlap.\n */\nexport const OverlapHide: LLC<OverlapHideOptions> = (options) => {\n  const { priority } = options;\n  return (labels: DisplayObject[]) => {\n    const displayLabels = [];\n    // When overlap, will hide the next label.\n    if (priority) labels.sort(priority);\n\n    labels.forEach((l) => {\n      show(l);\n\n      const b1 = l.getLocalBounds();\n      const overlaping = displayLabels.some((dl) =>\n        isOverlap(parseAABB(b1), parseAABB(dl.getLocalBounds())),\n      );\n\n      if (overlaping) hide(l);\n      else displayLabels.push(l);\n    });\n\n    return labels;\n  };\n};\n"], "mappings": "AAGA,SAASA,SAAS,EAAEC,SAAS,QAAQ,iBAAiB;AACtD,SAASC,IAAI,EAAEC,IAAI,QAAQ,gBAAgB;AAI3C;;;AAGA,OAAO,MAAMC,WAAW,GAA6BC,OAAO,IAAI;EAC9D,MAAM;IAAEC;EAAQ,CAAE,GAAGD,OAAO;EAC5B,OAAQE,MAAuB,IAAI;IACjC,MAAMC,aAAa,GAAG,EAAE;IACxB;IACA,IAAIF,QAAQ,EAAEC,MAAM,CAACE,IAAI,CAACH,QAAQ,CAAC;IAEnCC,MAAM,CAACG,OAAO,CAAEC,CAAC,IAAI;MACnBR,IAAI,CAACQ,CAAC,CAAC;MAEP,MAAMC,EAAE,GAAGD,CAAC,CAACE,cAAc,EAAE;MAC7B,MAAMC,UAAU,GAAGN,aAAa,CAACO,IAAI,CAAEC,EAAE,IACvChB,SAAS,CAACC,SAAS,CAACW,EAAE,CAAC,EAAEX,SAAS,CAACe,EAAE,CAACH,cAAc,EAAE,CAAC,CAAC,CACzD;MAED,IAAIC,UAAU,EAAEZ,IAAI,CAACS,CAAC,CAAC,CAAC,KACnBH,aAAa,CAACS,IAAI,CAACN,CAAC,CAAC;IAC5B,CAAC,CAAC;IAEF,OAAOJ,MAAM;EACf,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}