package com.yinma.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 物料主数据实体类
 * 管理企业所有物料的基础信息
 * 
 * <AUTHOR> System
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("material_master")
public class MaterialEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 物料主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 物料编码（唯一标识）
     */
    @TableField("material_code")
    private String materialCode;

    /**
     * 物料名称
     */
    @TableField("material_name")
    private String materialName;

    /**
     * 物料简称
     */
    @TableField("material_short_name")
    private String materialShortName;

    /**
     * 物料规格型号
     */
    @TableField("material_spec")
    private String materialSpec;

    /**
     * 物料类型：RAW-原材料，SEMI-半成品，FINISHED-成品，PURCHASE-外购件，TOOL-工具，SPARE-备件
     */
    @TableField("material_type")
    private String materialType;

    /**
     * 物料分类编码
     */
    @TableField("category_code")
    private String categoryCode;

    /**
     * 物料分类名称
     */
    @TableField("category_name")
    private String categoryName;

    /**
     * 物料组
     */
    @TableField("material_group")
    private String materialGroup;

    /**
     * 基本单位
     */
    @TableField("base_unit")
    private String baseUnit;

    /**
     * 采购单位
     */
    @TableField("purchase_unit")
    private String purchaseUnit;

    /**
     * 销售单位
     */
    @TableField("sales_unit")
    private String salesUnit;

    /**
     * 库存单位
     */
    @TableField("stock_unit")
    private String stockUnit;

    /**
     * 单位换算系数（采购单位到基本单位）
     */
    @TableField("conversion_factor")
    private BigDecimal conversionFactor;

    /**
     * 标准成本
     */
    @TableField("standard_cost")
    private BigDecimal standardCost;

    /**
     * 最新采购价格
     */
    @TableField("latest_purchase_price")
    private BigDecimal latestPurchasePrice;

    /**
     * 平均成本
     */
    @TableField("average_cost")
    private BigDecimal averageCost;

    /**
     * 销售价格
     */
    @TableField("sales_price")
    private BigDecimal salesPrice;

    /**
     * 重量（KG）
     */
    @TableField("weight")
    private BigDecimal weight;

    /**
     * 体积（立方米）
     */
    @TableField("volume")
    private BigDecimal volume;

    /**
     * 长度（MM）
     */
    @TableField("length")
    private BigDecimal length;

    /**
     * 宽度（MM）
     */
    @TableField("width")
    private BigDecimal width;

    /**
     * 高度（MM）
     */
    @TableField("height")
    private BigDecimal height;

    /**
     * 主供应商编码
     */
    @TableField("main_supplier_code")
    private String mainSupplierCode;

    /**
     * 主供应商名称
     */
    @TableField("main_supplier_name")
    private String mainSupplierName;

    /**
     * 采购提前期（天）
     */
    @TableField("lead_time")
    private Integer leadTime;

    /**
     * 最小采购量
     */
    @TableField("min_order_qty")
    private BigDecimal minOrderQty;

    /**
     * 最大库存量
     */
    @TableField("max_stock_qty")
    private BigDecimal maxStockQty;

    /**
     * 最小库存量
     */
    @TableField("min_stock_qty")
    private BigDecimal minStockQty;

    /**
     * 安全库存
     */
    @TableField("safety_stock")
    private BigDecimal safetyStock;

    /**
     * 再订货点
     */
    @TableField("reorder_point")
    private BigDecimal reorderPoint;

    /**
     * ABC分类：A-重要，B-一般，C-次要
     */
    @TableField("abc_category")
    private String abcCategory;

    /**
     * 是否批次管理
     */
    @TableField("is_batch_managed")
    private Boolean isBatchManaged;

    /**
     * 是否序列号管理
     */
    @TableField("is_serial_managed")
    private Boolean isSerialManaged;

    /**
     * 是否保质期管理
     */
    @TableField("is_expiry_managed")
    private Boolean isExpiryManaged;

    /**
     * 保质期（天）
     */
    @TableField("shelf_life")
    private Integer shelfLife;

    /**
     * 是否危险品
     */
    @TableField("is_hazardous")
    private Boolean isHazardous;

    /**
     * 存储条件
     */
    @TableField("storage_condition")
    private String storageCondition;

    /**
     * 质检类型：NONE-免检，SAMPLING-抽检，FULL-全检
     */
    @TableField("inspection_type")
    private String inspectionType;

    /**
     * 质检标准
     */
    @TableField("inspection_standard")
    private String inspectionStandard;

    /**
     * 物料状态：ACTIVE-激活，INACTIVE-停用，OBSOLETE-淘汰
     */
    @TableField("material_status")
    private String materialStatus;

    /**
     * 生命周期阶段：DESIGN-设计，PILOT-试产，PRODUCTION-量产，PHASEOUT-淘汰
     */
    @TableField("lifecycle_stage")
    private String lifecycleStage;

    /**
     * 图片URL
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * 技术文档URL
     */
    @TableField("document_url")
    private String documentUrl;

    /**
     * 备注说明
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 当前库存数量（非数据库字段）
     */
    @TableField(exist = false)
    private BigDecimal currentStock;

    /**
     * 可用库存数量（非数据库字段）
     */
    @TableField(exist = false)
    private BigDecimal availableStock;

    /**
     * 在途数量（非数据库字段）
     */
    @TableField(exist = false)
    private BigDecimal inTransitQty;

    /**
     * 预留数量（非数据库字段）
     */
    @TableField(exist = false)
    private BigDecimal reservedQty;

    /**
     * 物料类型枚举
     */
    public enum MaterialType {
        RAW("RAW", "原材料"),
        SEMI("SEMI", "半成品"),
        FINISHED("FINISHED", "成品"),
        PURCHASE("PURCHASE", "外购件"),
        TOOL("TOOL", "工具"),
        SPARE("SPARE", "备件");

        private final String code;
        private final String name;

        MaterialType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * ABC分类枚举
     */
    public enum AbcCategory {
        A("A", "重要"),
        B("B", "一般"),
        C("C", "次要");

        private final String code;
        private final String name;

        AbcCategory(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * 质检类型枚举
     */
    public enum InspectionType {
        NONE("NONE", "免检"),
        SAMPLING("SAMPLING", "抽检"),
        FULL("FULL", "全检");

        private final String code;
        private final String name;

        InspectionType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * 物料状态枚举
     */
    public enum MaterialStatus {
        ACTIVE("ACTIVE", "激活"),
        INACTIVE("INACTIVE", "停用"),
        OBSOLETE("OBSOLETE", "淘汰");

        private final String code;
        private final String name;

        MaterialStatus(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * 生命周期阶段枚举
     */
    public enum LifecycleStage {
        DESIGN("DESIGN", "设计"),
        PILOT("PILOT", "试产"),
        PRODUCTION("PRODUCTION", "量产"),
        PHASEOUT("PHASEOUT", "淘汰");

        private final String code;
        private final String name;

        LifecycleStage(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }
}