package com.yinma.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 物料主数据实体类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("material")
public class Material implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 物料编码
     */
    @NotBlank(message = "物料编码不能为空")
    @TableField("material_code")
    private String materialCode;

    /**
     * 物料名称
     */
    @NotBlank(message = "物料名称不能为空")
    @TableField("material_name")
    private String materialName;

    /**
     * 物料简称
     */
    @TableField("short_name")
    private String shortName;

    /**
     * 物料分类
     */
    @NotBlank(message = "物料分类不能为空")
    @TableField("category")
    private String category;

    /**
     * 物料类型：RAW-原材料，SEMI-半成品，FINISHED-成品，SPARE-备件
     */
    @NotBlank(message = "物料类型不能为空")
    @TableField("material_type")
    private String materialType;

    /**
     * 规格型号
     */
    @TableField("specification")
    private String specification;

    /**
     * 型号
     */
    @TableField("model")
    private String model;

    /**
     * 品牌
     */
    @TableField("brand")
    private String brand;

    /**
     * 基本计量单位
     */
    @NotBlank(message = "基本计量单位不能为空")
    @TableField("base_unit")
    private String baseUnit;

    /**
     * 采购计量单位
     */
    @TableField("purchase_unit")
    private String purchaseUnit;

    /**
     * 库存计量单位
     */
    @TableField("stock_unit")
    private String stockUnit;

    /**
     * 销售计量单位
     */
    @TableField("sales_unit")
    private String salesUnit;

    /**
     * 标准成本
     */
    @TableField("standard_cost")
    private BigDecimal standardCost;

    /**
     * 最新采购价
     */
    @TableField("latest_purchase_price")
    private BigDecimal latestPurchasePrice;

    /**
     * 平均采购价
     */
    @TableField("average_purchase_price")
    private BigDecimal averagePurchasePrice;

    /**
     * 销售价格
     */
    @TableField("sales_price")
    private BigDecimal salesPrice;

    /**
     * 安全库存
     */
    @TableField("safety_stock")
    private BigDecimal safetyStock;

    /**
     * 最小库存
     */
    @TableField("min_stock")
    private BigDecimal minStock;

    /**
     * 最大库存
     */
    @TableField("max_stock")
    private BigDecimal maxStock;

    /**
     * 采购提前期(天)
     */
    @TableField("lead_time")
    private Integer leadTime;

    /**
     * ABC分类：A-重要，B-一般，C-次要
     */
    @TableField("abc_category")
    private String abcCategory;

    /**
     * 主供应商编码
     */
    @TableField("main_supplier_code")
    private String mainSupplierCode;

    /**
     * 主供应商名称
     */
    @TableField("main_supplier_name")
    private String mainSupplierName;

    /**
     * 质量等级
     */
    @TableField("quality_grade")
    private String qualityGrade;

    /**
     * 保质期(天)
     */
    @TableField("shelf_life")
    private Integer shelfLife;

    /**
     * 存储条件
     */
    @TableField("storage_condition")
    private String storageCondition;

    /**
     * 是否批次管理：0-否，1-是
     */
    @TableField("batch_management")
    private Integer batchManagement;

    /**
     * 是否序列号管理：0-否，1-是
     */
    @TableField("serial_management")
    private Integer serialManagement;

    /**
     * 状态：ACTIVE-启用，INACTIVE-停用
     */
    @TableField("status")
    private String status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}