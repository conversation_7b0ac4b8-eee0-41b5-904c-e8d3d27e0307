{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-YinMa\\\\frontend\\\\src\\\\components\\\\Layout\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Outlet, useNavigate, useLocation } from 'react-router-dom';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { Layout as AntLayout, Menu, Avatar, Dropdown, Button, theme, Breadcrumb, Space, Badge } from 'antd';\nimport { MenuFoldOutlined, MenuUnfoldOutlined, DashboardOutlined, PartitionOutlined, SettingOutlined, ProjectOutlined, CustomerServiceOutlined, DollarOutlined, TeamOutlined, UserOutlined, LogoutOutlined, BellOutlined, InboxOutlined } from '@ant-design/icons';\nimport { logout, selectUserInfo, selectIsAuthenticated } from '../../store/slices/authSlice';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Sider,\n  Content\n} = AntLayout;\n\n/**\n * 主布局组件\n */\nconst Layout = () => {\n  _s();\n  const [collapsed, setCollapsed] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const userInfo = useSelector(selectUserInfo);\n  const isAuthenticated = useSelector(selectIsAuthenticated);\n  const {\n    token: {\n      colorBgContainer\n    }\n  } = theme.useToken();\n\n  // 检查认证状态\n  useEffect(() => {\n    if (!isAuthenticated) {\n      navigate('/login');\n    }\n  }, [isAuthenticated, navigate]);\n\n  // 菜单配置\n  const menuItems = [{\n    key: '/dashboard',\n    icon: /*#__PURE__*/_jsxDEV(DashboardOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 13\n    }, this),\n    label: '数据看板'\n  }, {\n    key: '/bom',\n    icon: /*#__PURE__*/_jsxDEV(PartitionOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 13\n    }, this),\n    label: '设备BOM管理',\n    children: [{\n      key: '/bom',\n      label: 'BOM管理中心'\n    }, {\n      key: '/bom-detail',\n      label: 'BOM明细管理'\n    }, {\n      key: '/bom-change-log',\n      label: 'BOM变更日志'\n    }]\n  }, {\n    key: '/material',\n    icon: /*#__PURE__*/_jsxDEV(InboxOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 13\n    }, this),\n    label: '物料主数据管理'\n  }, {\n    key: '/manufacturing',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 13\n    }, this),\n    label: '智能制造',\n    children: [{\n      key: '/manufacturing/equipment',\n      label: '设备管理'\n    }]\n  }, {\n    key: '/project',\n    icon: /*#__PURE__*/_jsxDEV(ProjectOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 13\n    }, this),\n    label: '项目交付',\n    children: [{\n      key: '/project/management',\n      label: '项目管理'\n    }]\n  }, {\n    key: '/service',\n    icon: /*#__PURE__*/_jsxDEV(CustomerServiceOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 13\n    }, this),\n    label: '智慧服务',\n    children: [{\n      key: '/service/maintenance',\n      label: '维护管理'\n    }, {\n      key: '/service/monitoring',\n      label: '远程监控'\n    }]\n  }, {\n    key: '/financial',\n    icon: /*#__PURE__*/_jsxDEV(DollarOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 13\n    }, this),\n    label: '财务管控'\n  }, {\n    key: '/decision',\n    icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 13\n    }, this),\n    label: '协同决策'\n  }];\n\n  // 获取当前选中的菜单项\n  const getSelectedKeys = () => {\n    const pathname = location.pathname;\n    // 找到最匹配的菜单项\n    for (const item of menuItems) {\n      if (item.children) {\n        for (const child of item.children) {\n          if (pathname.startsWith(child.key)) {\n            return [child.key];\n          }\n        }\n      } else if (pathname.startsWith(item.key)) {\n        return [item.key];\n      }\n    }\n    return ['/dashboard'];\n  };\n\n  // 获取当前展开的菜单项\n  const getOpenKeys = () => {\n    const pathname = location.pathname;\n    for (const item of menuItems) {\n      if (item.children) {\n        for (const child of item.children) {\n          if (pathname.startsWith(child.key)) {\n            return [item.key];\n          }\n        }\n      }\n    }\n    return [];\n  };\n\n  // 处理菜单点击\n  const handleMenuClick = ({\n    key\n  }) => {\n    navigate(key);\n  };\n\n  // 处理登出\n  const handleLogout = () => {\n    dispatch(logout());\n  };\n\n  // 用户下拉菜单\n  const userMenuItems = [{\n    key: 'profile',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 13\n    }, this),\n    label: '个人信息'\n  }, {\n    key: 'settings',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 13\n    }, this),\n    label: '系统设置'\n  }, {\n    type: 'divider'\n  }, {\n    key: 'logout',\n    icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 13\n    }, this),\n    label: '退出登录',\n    onClick: handleLogout\n  }];\n\n  // 生成面包屑\n  const generateBreadcrumb = () => {\n    const pathname = location.pathname;\n    const pathSegments = pathname.split('/').filter(Boolean);\n    const breadcrumbItems = [{\n      title: '首页',\n      href: '/dashboard'\n    }];\n\n    // 根据路径生成面包屑\n    let currentPath = '';\n    pathSegments.forEach((segment, index) => {\n      currentPath += `/${segment}`;\n\n      // 查找对应的菜单项\n      let title = segment;\n      for (const item of menuItems) {\n        if (item.key === currentPath) {\n          title = item.label;\n          break;\n        }\n        if (item.children) {\n          for (const child of item.children) {\n            if (child.key === currentPath) {\n              title = child.label;\n              break;\n            }\n          }\n        }\n      }\n      breadcrumbItems.push({\n        title,\n        href: index === pathSegments.length - 1 ? undefined : currentPath\n      });\n    });\n    return breadcrumbItems;\n  };\n  if (!isAuthenticated) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(AntLayout, {\n    className: \"layout-container\",\n    children: [/*#__PURE__*/_jsxDEV(Sider, {\n      trigger: null,\n      collapsible: true,\n      collapsed: collapsed,\n      className: \"layout-sider\",\n      width: 256,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"layout-logo\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"/logo.png\",\n          alt: \"\\u94F6\\u9A6C\\u5B9E\\u4E1A\",\n          className: \"logo-image\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), !collapsed && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"logo-text\",\n          children: \"\\u94F6\\u9A6C\\u6570\\u5B57\\u5316\\u7BA1\\u7406\\u7CFB\\u7EDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Menu, {\n        theme: \"dark\",\n        mode: \"inline\",\n        selectedKeys: getSelectedKeys(),\n        defaultOpenKeys: getOpenKeys(),\n        items: menuItems,\n        onClick: handleMenuClick\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AntLayout, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        className: \"layout-header\",\n        style: {\n          background: colorBgContainer\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-left\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            icon: collapsed ? /*#__PURE__*/_jsxDEV(MenuUnfoldOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(MenuFoldOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 58\n            }, this),\n            onClick: () => setCollapsed(!collapsed),\n            className: \"trigger-button\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Breadcrumb, {\n            items: generateBreadcrumb(),\n            className: \"breadcrumb\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-right\",\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            size: \"middle\",\n            children: [/*#__PURE__*/_jsxDEV(Badge, {\n              count: 5,\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"text\",\n                icon: /*#__PURE__*/_jsxDEV(BellOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 25\n                }, this),\n                className: \"notification-button\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n              menu: {\n                items: userMenuItems\n              },\n              placement: \"bottomRight\",\n              arrow: true,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                className: \"user-info\",\n                children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                  size: \"small\",\n                  icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 27\n                  }, this),\n                  src: userInfo === null || userInfo === void 0 ? void 0 : userInfo.avatar\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"username\",\n                  children: (userInfo === null || userInfo === void 0 ? void 0 : userInfo.name) || '用户'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Content, {\n        className: \"layout-content\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content-wrapper\",\n          children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 251,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"dd9E7ZvlPtxg15NpTOOsqHIdFic=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useSelector, useSelector, theme.useToken];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Outlet", "useNavigate", "useLocation", "useSelector", "useDispatch", "Layout", "AntLayout", "<PERSON><PERSON>", "Avatar", "Dropdown", "<PERSON><PERSON>", "theme", "Breadcrumb", "Space", "Badge", "MenuFoldOutlined", "MenuUnfoldOutlined", "DashboardOutlined", "PartitionOutlined", "SettingOutlined", "ProjectOutlined", "CustomerServiceOutlined", "DollarOutlined", "TeamOutlined", "UserOutlined", "LogoutOutlined", "BellOutlined", "InboxOutlined", "logout", "selectUserInfo", "selectIsAuthenticated", "jsxDEV", "_jsxDEV", "Header", "<PERSON><PERSON>", "Content", "_s", "collapsed", "setCollapsed", "navigate", "location", "dispatch", "userInfo", "isAuthenticated", "token", "colorBgContainer", "useToken", "menuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "children", "getSelectedKeys", "pathname", "item", "child", "startsWith", "get<PERSON><PERSON><PERSON><PERSON>s", "handleMenuClick", "handleLogout", "userMenuItems", "type", "onClick", "generateBreadcrumb", "pathSegments", "split", "filter", "Boolean", "breadcrumbItems", "title", "href", "currentPath", "for<PERSON>ach", "segment", "index", "push", "length", "undefined", "className", "trigger", "collapsible", "width", "src", "alt", "mode", "<PERSON><PERSON><PERSON><PERSON>", "defaultOpenKeys", "items", "style", "background", "size", "count", "menu", "placement", "arrow", "avatar", "name", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-YinMa/frontend/src/components/Layout/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Outlet, useNavigate, useLocation } from 'react-router-dom';\nimport { useSelector, useDispatch } from 'react-redux';\nimport {\n  Layout as AntLayout,\n  Menu,\n  Avatar,\n  Dropdown,\n  Button,\n  theme,\n  Breadcrumb,\n  Space,\n  Badge,\n} from 'antd';\nimport {\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  DashboardOutlined,\n  PartitionOutlined,\n  SettingOutlined,\n  ProjectOutlined,\n  CustomerServiceOutlined,\n  DollarOutlined,\n  TeamOutlined,\n  UserOutlined,\n  LogoutOutlined,\n  BellOutlined,\n  InboxOutlined,\n} from '@ant-design/icons';\nimport { logout, selectUserInfo, selectIsAuthenticated } from '../../store/slices/authSlice';\nimport './index.css';\n\nconst { Header, Sider, Content } = AntLayout;\n\n/**\n * 主布局组件\n */\nconst Layout = () => {\n  const [collapsed, setCollapsed] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const userInfo = useSelector(selectUserInfo);\n  const isAuthenticated = useSelector(selectIsAuthenticated);\n  \n  const {\n    token: { colorBgContainer },\n  } = theme.useToken();\n\n  // 检查认证状态\n  useEffect(() => {\n    if (!isAuthenticated) {\n      navigate('/login');\n    }\n  }, [isAuthenticated, navigate]);\n\n  // 菜单配置\n  const menuItems = [\n    {\n      key: '/dashboard',\n      icon: <DashboardOutlined />,\n      label: '数据看板',\n    },\n    {\n      key: '/bom',\n      icon: <PartitionOutlined />,\n      label: '设备BOM管理',\n      children: [\n        {\n          key: '/bom',\n          label: 'BOM管理中心',\n        },\n        {\n          key: '/bom-detail',\n          label: 'BOM明细管理',\n        },\n        {\n          key: '/bom-change-log',\n          label: 'BOM变更日志',\n        },\n      ],\n    },\n    {\n      key: '/material',\n      icon: <InboxOutlined />,\n      label: '物料主数据管理',\n    },\n    {\n      key: '/manufacturing',\n      icon: <SettingOutlined />,\n      label: '智能制造',\n      children: [\n        {\n          key: '/manufacturing/equipment',\n          label: '设备管理',\n        },\n      ],\n    },\n    {\n      key: '/project',\n      icon: <ProjectOutlined />,\n      label: '项目交付',\n      children: [\n        {\n          key: '/project/management',\n          label: '项目管理',\n        },\n      ],\n    },\n    {\n      key: '/service',\n      icon: <CustomerServiceOutlined />,\n      label: '智慧服务',\n      children: [\n        {\n          key: '/service/maintenance',\n          label: '维护管理',\n        },\n        {\n          key: '/service/monitoring',\n          label: '远程监控',\n        },\n      ],\n    },\n    {\n      key: '/financial',\n      icon: <DollarOutlined />,\n      label: '财务管控',\n    },\n    {\n      key: '/decision',\n      icon: <TeamOutlined />,\n      label: '协同决策',\n    },\n  ];\n\n  // 获取当前选中的菜单项\n  const getSelectedKeys = () => {\n    const pathname = location.pathname;\n    // 找到最匹配的菜单项\n    for (const item of menuItems) {\n      if (item.children) {\n        for (const child of item.children) {\n          if (pathname.startsWith(child.key)) {\n            return [child.key];\n          }\n        }\n      } else if (pathname.startsWith(item.key)) {\n        return [item.key];\n      }\n    }\n    return ['/dashboard'];\n  };\n\n  // 获取当前展开的菜单项\n  const getOpenKeys = () => {\n    const pathname = location.pathname;\n    for (const item of menuItems) {\n      if (item.children) {\n        for (const child of item.children) {\n          if (pathname.startsWith(child.key)) {\n            return [item.key];\n          }\n        }\n      }\n    }\n    return [];\n  };\n\n  // 处理菜单点击\n  const handleMenuClick = ({ key }) => {\n    navigate(key);\n  };\n\n  // 处理登出\n  const handleLogout = () => {\n    dispatch(logout());\n  };\n\n  // 用户下拉菜单\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: '个人信息',\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: '系统设置',\n    },\n    {\n      type: 'divider',\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: '退出登录',\n      onClick: handleLogout,\n    },\n  ];\n\n  // 生成面包屑\n  const generateBreadcrumb = () => {\n    const pathname = location.pathname;\n    const pathSegments = pathname.split('/').filter(Boolean);\n    \n    const breadcrumbItems = [\n      {\n        title: '首页',\n        href: '/dashboard',\n      },\n    ];\n\n    // 根据路径生成面包屑\n    let currentPath = '';\n    pathSegments.forEach((segment, index) => {\n      currentPath += `/${segment}`;\n      \n      // 查找对应的菜单项\n      let title = segment;\n      for (const item of menuItems) {\n        if (item.key === currentPath) {\n          title = item.label;\n          break;\n        }\n        if (item.children) {\n          for (const child of item.children) {\n            if (child.key === currentPath) {\n              title = child.label;\n              break;\n            }\n          }\n        }\n      }\n      \n      breadcrumbItems.push({\n        title,\n        href: index === pathSegments.length - 1 ? undefined : currentPath,\n      });\n    });\n\n    return breadcrumbItems;\n  };\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <AntLayout className=\"layout-container\">\n      <Sider \n        trigger={null} \n        collapsible \n        collapsed={collapsed}\n        className=\"layout-sider\"\n        width={256}\n      >\n        <div className=\"layout-logo\">\n          <img src=\"/logo.png\" alt=\"银马实业\" className=\"logo-image\" />\n          {!collapsed && (\n            <span className=\"logo-text\">银马数字化管理系统</span>\n          )}\n        </div>\n        <Menu\n          theme=\"dark\"\n          mode=\"inline\"\n          selectedKeys={getSelectedKeys()}\n          defaultOpenKeys={getOpenKeys()}\n          items={menuItems}\n          onClick={handleMenuClick}\n        />\n      </Sider>\n      \n      <AntLayout>\n        <Header \n          className=\"layout-header\"\n          style={{ background: colorBgContainer }}\n        >\n          <div className=\"header-left\">\n            <Button\n              type=\"text\"\n              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n              onClick={() => setCollapsed(!collapsed)}\n              className=\"trigger-button\"\n            />\n            \n            <Breadcrumb\n              items={generateBreadcrumb()}\n              className=\"breadcrumb\"\n            />\n          </div>\n          \n          <div className=\"header-right\">\n            <Space size=\"middle\">\n              <Badge count={5} size=\"small\">\n                <Button \n                  type=\"text\" \n                  icon={<BellOutlined />} \n                  className=\"notification-button\"\n                />\n              </Badge>\n              \n              <Dropdown\n                menu={{ items: userMenuItems }}\n                placement=\"bottomRight\"\n                arrow\n              >\n                <Space className=\"user-info\">\n                  <Avatar \n                    size=\"small\" \n                    icon={<UserOutlined />}\n                    src={userInfo?.avatar}\n                  />\n                  <span className=\"username\">\n                    {userInfo?.name || '用户'}\n                  </span>\n                </Space>\n              </Dropdown>\n            </Space>\n          </div>\n        </Header>\n        \n        <Content className=\"layout-content\">\n          <div className=\"content-wrapper\">\n            <Outlet />\n          </div>\n        </Content>\n      </AntLayout>\n    </AntLayout>\n  );\n};\n\nexport default Layout;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACnE,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,MAAM,IAAIC,SAAS,EACnBC,IAAI,EACJC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,KAAK,EACLC,KAAK,QACA,MAAM;AACb,SACEC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,EACjBC,iBAAiB,EACjBC,eAAe,EACfC,eAAe,EACfC,uBAAuB,EACvBC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,aAAa,QACR,mBAAmB;AAC1B,SAASC,MAAM,EAAEC,cAAc,EAAEC,qBAAqB,QAAQ,8BAA8B;AAC5F,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAM;EAAEC,MAAM;EAAEC,KAAK;EAAEC;AAAQ,CAAC,GAAG7B,SAAS;;AAE5C;AACA;AACA;AACA,MAAMD,MAAM,GAAGA,CAAA,KAAM;EAAA+B,EAAA;EACnB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAMyC,QAAQ,GAAGtC,WAAW,CAAC,CAAC;EAC9B,MAAMuC,QAAQ,GAAGtC,WAAW,CAAC,CAAC;EAC9B,MAAMuC,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAC9B,MAAMsC,QAAQ,GAAGvC,WAAW,CAAC0B,cAAc,CAAC;EAC5C,MAAMc,eAAe,GAAGxC,WAAW,CAAC2B,qBAAqB,CAAC;EAE1D,MAAM;IACJc,KAAK,EAAE;MAAEC;IAAiB;EAC5B,CAAC,GAAGlC,KAAK,CAACmC,QAAQ,CAAC,CAAC;;EAEpB;EACA/C,SAAS,CAAC,MAAM;IACd,IAAI,CAAC4C,eAAe,EAAE;MACpBJ,QAAQ,CAAC,QAAQ,CAAC;IACpB;EACF,CAAC,EAAE,CAACI,eAAe,EAAEJ,QAAQ,CAAC,CAAC;;EAE/B;EACA,MAAMQ,SAAS,GAAG,CAChB;IACEC,GAAG,EAAE,YAAY;IACjBC,IAAI,eAAEjB,OAAA,CAACf,iBAAiB;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,MAAM;IACXC,IAAI,eAAEjB,OAAA,CAACd,iBAAiB;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3BC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CACR;MACEP,GAAG,EAAE,MAAM;MACXM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,aAAa;MAClBM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,iBAAiB;MACtBM,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,EACD;IACEN,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAEjB,OAAA,CAACL,aAAa;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,gBAAgB;IACrBC,IAAI,eAAEjB,OAAA,CAACb,eAAe;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,CACR;MACEP,GAAG,EAAE,0BAA0B;MAC/BM,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,EACD;IACEN,GAAG,EAAE,UAAU;IACfC,IAAI,eAAEjB,OAAA,CAACZ,eAAe;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,CACR;MACEP,GAAG,EAAE,qBAAqB;MAC1BM,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,EACD;IACEN,GAAG,EAAE,UAAU;IACfC,IAAI,eAAEjB,OAAA,CAACX,uBAAuB;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjCC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,CACR;MACEP,GAAG,EAAE,sBAAsB;MAC3BM,KAAK,EAAE;IACT,CAAC,EACD;MACEN,GAAG,EAAE,qBAAqB;MAC1BM,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,EACD;IACEN,GAAG,EAAE,YAAY;IACjBC,IAAI,eAAEjB,OAAA,CAACV,cAAc;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAEjB,OAAA,CAACT,YAAY;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,QAAQ,GAAGjB,QAAQ,CAACiB,QAAQ;IAClC;IACA,KAAK,MAAMC,IAAI,IAAIX,SAAS,EAAE;MAC5B,IAAIW,IAAI,CAACH,QAAQ,EAAE;QACjB,KAAK,MAAMI,KAAK,IAAID,IAAI,CAACH,QAAQ,EAAE;UACjC,IAAIE,QAAQ,CAACG,UAAU,CAACD,KAAK,CAACX,GAAG,CAAC,EAAE;YAClC,OAAO,CAACW,KAAK,CAACX,GAAG,CAAC;UACpB;QACF;MACF,CAAC,MAAM,IAAIS,QAAQ,CAACG,UAAU,CAACF,IAAI,CAACV,GAAG,CAAC,EAAE;QACxC,OAAO,CAACU,IAAI,CAACV,GAAG,CAAC;MACnB;IACF;IACA,OAAO,CAAC,YAAY,CAAC;EACvB,CAAC;;EAED;EACA,MAAMa,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMJ,QAAQ,GAAGjB,QAAQ,CAACiB,QAAQ;IAClC,KAAK,MAAMC,IAAI,IAAIX,SAAS,EAAE;MAC5B,IAAIW,IAAI,CAACH,QAAQ,EAAE;QACjB,KAAK,MAAMI,KAAK,IAAID,IAAI,CAACH,QAAQ,EAAE;UACjC,IAAIE,QAAQ,CAACG,UAAU,CAACD,KAAK,CAACX,GAAG,CAAC,EAAE;YAClC,OAAO,CAACU,IAAI,CAACV,GAAG,CAAC;UACnB;QACF;MACF;IACF;IACA,OAAO,EAAE;EACX,CAAC;;EAED;EACA,MAAMc,eAAe,GAAGA,CAAC;IAAEd;EAAI,CAAC,KAAK;IACnCT,QAAQ,CAACS,GAAG,CAAC;EACf,CAAC;;EAED;EACA,MAAMe,YAAY,GAAGA,CAAA,KAAM;IACzBtB,QAAQ,CAACb,MAAM,CAAC,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAMoC,aAAa,GAAG,CACpB;IACEhB,GAAG,EAAE,SAAS;IACdC,IAAI,eAAEjB,OAAA,CAACR,YAAY;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,UAAU;IACfC,IAAI,eAAEjB,OAAA,CAACb,eAAe;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEW,IAAI,EAAE;EACR,CAAC,EACD;IACEjB,GAAG,EAAE,QAAQ;IACbC,IAAI,eAAEjB,OAAA,CAACP,cAAc;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE,MAAM;IACbY,OAAO,EAAEH;EACX,CAAC,CACF;;EAED;EACA,MAAMI,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMV,QAAQ,GAAGjB,QAAQ,CAACiB,QAAQ;IAClC,MAAMW,YAAY,GAAGX,QAAQ,CAACY,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;IAExD,MAAMC,eAAe,GAAG,CACtB;MACEC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE;IACR,CAAC,CACF;;IAED;IACA,IAAIC,WAAW,GAAG,EAAE;IACpBP,YAAY,CAACQ,OAAO,CAAC,CAACC,OAAO,EAAEC,KAAK,KAAK;MACvCH,WAAW,IAAI,IAAIE,OAAO,EAAE;;MAE5B;MACA,IAAIJ,KAAK,GAAGI,OAAO;MACnB,KAAK,MAAMnB,IAAI,IAAIX,SAAS,EAAE;QAC5B,IAAIW,IAAI,CAACV,GAAG,KAAK2B,WAAW,EAAE;UAC5BF,KAAK,GAAGf,IAAI,CAACJ,KAAK;UAClB;QACF;QACA,IAAII,IAAI,CAACH,QAAQ,EAAE;UACjB,KAAK,MAAMI,KAAK,IAAID,IAAI,CAACH,QAAQ,EAAE;YACjC,IAAII,KAAK,CAACX,GAAG,KAAK2B,WAAW,EAAE;cAC7BF,KAAK,GAAGd,KAAK,CAACL,KAAK;cACnB;YACF;UACF;QACF;MACF;MAEAkB,eAAe,CAACO,IAAI,CAAC;QACnBN,KAAK;QACLC,IAAI,EAAEI,KAAK,KAAKV,YAAY,CAACY,MAAM,GAAG,CAAC,GAAGC,SAAS,GAAGN;MACxD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAOH,eAAe;EACxB,CAAC;EAED,IAAI,CAAC7B,eAAe,EAAE;IACpB,OAAO,IAAI;EACb;EAEA,oBACEX,OAAA,CAAC1B,SAAS;IAAC4E,SAAS,EAAC,kBAAkB;IAAA3B,QAAA,gBACrCvB,OAAA,CAACE,KAAK;MACJiD,OAAO,EAAE,IAAK;MACdC,WAAW;MACX/C,SAAS,EAAEA,SAAU;MACrB6C,SAAS,EAAC,cAAc;MACxBG,KAAK,EAAE,GAAI;MAAA9B,QAAA,gBAEXvB,OAAA;QAAKkD,SAAS,EAAC,aAAa;QAAA3B,QAAA,gBAC1BvB,OAAA;UAAKsD,GAAG,EAAC,WAAW;UAACC,GAAG,EAAC,0BAAM;UAACL,SAAS,EAAC;QAAY;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACxD,CAAChB,SAAS,iBACTL,OAAA;UAAMkD,SAAS,EAAC,WAAW;UAAA3B,QAAA,EAAC;QAAS;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAC5C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNrB,OAAA,CAACzB,IAAI;QACHI,KAAK,EAAC,MAAM;QACZ6E,IAAI,EAAC,QAAQ;QACbC,YAAY,EAAEjC,eAAe,CAAC,CAAE;QAChCkC,eAAe,EAAE7B,WAAW,CAAC,CAAE;QAC/B8B,KAAK,EAAE5C,SAAU;QACjBmB,OAAO,EAAEJ;MAAgB;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAERrB,OAAA,CAAC1B,SAAS;MAAAiD,QAAA,gBACRvB,OAAA,CAACC,MAAM;QACLiD,SAAS,EAAC,eAAe;QACzBU,KAAK,EAAE;UAAEC,UAAU,EAAEhD;QAAiB,CAAE;QAAAU,QAAA,gBAExCvB,OAAA;UAAKkD,SAAS,EAAC,aAAa;UAAA3B,QAAA,gBAC1BvB,OAAA,CAACtB,MAAM;YACLuD,IAAI,EAAC,MAAM;YACXhB,IAAI,EAAEZ,SAAS,gBAAGL,OAAA,CAAChB,kBAAkB;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGrB,OAAA,CAACjB,gBAAgB;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChEa,OAAO,EAAEA,CAAA,KAAM5B,YAAY,CAAC,CAACD,SAAS,CAAE;YACxC6C,SAAS,EAAC;UAAgB;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAEFrB,OAAA,CAACpB,UAAU;YACT+E,KAAK,EAAExB,kBAAkB,CAAC,CAAE;YAC5Be,SAAS,EAAC;UAAY;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENrB,OAAA;UAAKkD,SAAS,EAAC,cAAc;UAAA3B,QAAA,eAC3BvB,OAAA,CAACnB,KAAK;YAACiF,IAAI,EAAC,QAAQ;YAAAvC,QAAA,gBAClBvB,OAAA,CAAClB,KAAK;cAACiF,KAAK,EAAE,CAAE;cAACD,IAAI,EAAC,OAAO;cAAAvC,QAAA,eAC3BvB,OAAA,CAACtB,MAAM;gBACLuD,IAAI,EAAC,MAAM;gBACXhB,IAAI,eAAEjB,OAAA,CAACN,YAAY;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACvB6B,SAAS,EAAC;cAAqB;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eAERrB,OAAA,CAACvB,QAAQ;cACPuF,IAAI,EAAE;gBAAEL,KAAK,EAAE3B;cAAc,CAAE;cAC/BiC,SAAS,EAAC,aAAa;cACvBC,KAAK;cAAA3C,QAAA,eAELvB,OAAA,CAACnB,KAAK;gBAACqE,SAAS,EAAC,WAAW;gBAAA3B,QAAA,gBAC1BvB,OAAA,CAACxB,MAAM;kBACLsF,IAAI,EAAC,OAAO;kBACZ7C,IAAI,eAAEjB,OAAA,CAACR,YAAY;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACvBiC,GAAG,EAAE5C,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEyD;gBAAO;kBAAAjD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACFrB,OAAA;kBAAMkD,SAAS,EAAC,UAAU;kBAAA3B,QAAA,EACvB,CAAAb,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE0D,IAAI,KAAI;gBAAI;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAETrB,OAAA,CAACG,OAAO;QAAC+C,SAAS,EAAC,gBAAgB;QAAA3B,QAAA,eACjCvB,OAAA;UAAKkD,SAAS,EAAC,iBAAiB;UAAA3B,QAAA,eAC9BvB,OAAA,CAAChC,MAAM;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEhB,CAAC;AAACjB,EAAA,CAtSI/B,MAAM;EAAA,QAEOJ,WAAW,EACXC,WAAW,EACXE,WAAW,EACXD,WAAW,EACJA,WAAW,EAI/BQ,KAAK,CAACmC,QAAQ;AAAA;AAAAuD,EAAA,GAVdhG,MAAM;AAwSZ,eAAeA,MAAM;AAAC,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}