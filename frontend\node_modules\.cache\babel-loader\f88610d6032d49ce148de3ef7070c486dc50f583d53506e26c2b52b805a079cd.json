{"ast": null, "code": "/**\n * Classic palette of AntV for ordinal data with 10 colors.\n */\nexport const Category10 = () => {\n  return ['#5B8FF9', '#5AD8A6', '#5D7092', '#F6BD16', '#6F5EF9', '#6DC8EC', '#945FB9', '#FF9845', '#1E9493', '#FF99C3'];\n};\nCategory10.props = {};", "map": {"version": 3, "names": ["Category10", "props"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\g2\\src\\palette\\category10.ts"], "sourcesContent": ["import { PaletteComponent } from '../runtime';\nimport { Category10Palette } from '../spec/palette';\n\nexport type Category10Options = Omit<Category10Palette, 'type'>;\n\n/**\n * Classic palette of AntV for ordinal data with 10 colors.\n */\nexport const Category10: PaletteComponent<Category10Options> = () => {\n  return [\n    '#5B8FF9',\n    '#5AD8A6',\n    '#5D7092',\n    '#F6BD16',\n    '#6F5EF9',\n    '#6DC8EC',\n    '#945FB9',\n    '#FF9845',\n    '#1E9493',\n    '#FF99C3',\n  ];\n};\n\nCategory10.props = {};\n"], "mappings": "AAKA;;;AAGA,OAAO,MAAMA,UAAU,GAAwCA,CAAA,KAAK;EAClE,OAAO,CACL,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;AACH,CAAC;AAEDA,UAAU,CAACC,KAAK,GAAG,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}