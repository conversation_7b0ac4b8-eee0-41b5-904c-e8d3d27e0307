package com.yinma.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yinma.entity.UserRoleEntity;

import java.util.List;

/**
 * 用户角色关联Service接口
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
public interface UserRoleService extends IService<UserRoleEntity> {

    /**
     * 查询用户的角色ID列表
     * 
     * @param userId 用户ID
     * @return 角色ID列表
     */
    List<Long> selectRoleIdsByUserId(Long userId);

    /**
     * 查询角色的用户ID列表
     * 
     * @param roleId 角色ID
     * @return 用户ID列表
     */
    List<Long> selectUserIdsByRoleId(Long roleId);

    /**
     * 检查用户角色关联是否存在
     * 
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 是否存在
     */
    Boolean checkUserRoleExists(Long userId, Long roleId);

    /**
     * 删除用户的所有角色关联
     * 
     * @param userId 用户ID
     * @return 是否成功
     */
    Boolean deleteUserRolesByUserId(Long userId);

    /**
     * 删除角色的所有用户关联
     * 
     * @param roleId 角色ID
     * @return 是否成功
     */
    Boolean deleteUserRolesByRoleId(Long roleId);

    /**
     * 删除用户角色关联
     * 
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 是否成功
     */
    Boolean deleteUserRole(Long userId, Long roleId);

    /**
     * 批量插入用户角色关联
     * 
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 是否成功
     */
    Boolean batchInsertUserRoles(Long userId, List<Long> roleIds);

    /**
     * 批量删除用户角色关联
     * 
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 是否成功
     */
    Boolean batchDeleteUserRoles(Long userId, List<Long> roleIds);

    /**
     * 批量插入角色用户关联
     * 
     * @param roleId 角色ID
     * @param userIds 用户ID列表
     * @return 是否成功
     */
    Boolean batchInsertRoleUsers(Long roleId, List<Long> userIds);

    /**
     * 批量删除角色用户关联
     * 
     * @param roleId 角色ID
     * @param userIds 用户ID列表
     * @return 是否成功
     */
    Boolean batchDeleteRoleUsers(Long roleId, List<Long> userIds);

    /**
     * 查询用户角色关联统计信息
     * 
     * @return 统计信息
     */
    Long selectUserRoleCount();

    /**
     * 查询拥有指定角色的用户数量
     * 
     * @param roleId 角色ID
     * @return 用户数量
     */
    Long selectUserCountByRoleId(Long roleId);

    /**
     * 查询用户拥有的角色数量
     * 
     * @param userId 用户ID
     * @return 角色数量
     */
    Long selectRoleCountByUserId(Long userId);

    /**
     * 查询没有分配角色的用户数量
     * 
     * @return 用户数量
     */
    Long selectUsersWithoutRoleCount();

    /**
     * 查询没有分配用户的角色数量
     * 
     * @return 角色数量
     */
    Long selectRolesWithoutUserCount();
}