package com.yinma.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinma.common.Result;
import com.yinma.entity.Component;
import com.yinma.dto.ComponentDTO;
import com.yinma.vo.ComponentVO;
import com.yinma.service.ComponentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 核心部件管理控制器
 * 银马实业设备核心部件管理系统
 * 
 * <AUTHOR>
 * @since 2024-05-20
 */
@Slf4j
@RestController
@RequestMapping("/api/components")
@Api(tags = "核心部件管理")
public class ComponentController {

    @Autowired
    private ComponentService componentService;

    /**
     * 分页查询核心部件
     */
    @GetMapping("/page")
    @ApiOperation("分页查询核心部件")
    public Result<IPage<ComponentVO>> getComponentPage(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("部件名称") @RequestParam(required = false) String componentName,
            @ApiParam("部件编码") @RequestParam(required = false) String componentCode,
            @ApiParam("部件分类") @RequestParam(required = false) String category,
            @ApiParam("部件类型") @RequestParam(required = false) String componentType,
            @ApiParam("供应商") @RequestParam(required = false) String supplier,
            @ApiParam("状态") @RequestParam(required = false) String status) {
        
        Page<Component> page = new Page<>(current, size);
        IPage<ComponentVO> result = componentService.getComponentPage(page, componentName, 
                componentCode, category, componentType, supplier, status);
        
        return Result.success(result);
    }

    /**
     * 根据ID查询核心部件详情
     */
    @GetMapping("/{id}")
    @ApiOperation("查询核心部件详情")
    public Result<ComponentVO> getComponentById(
            @ApiParam("部件ID") @PathVariable Long id) {
        
        ComponentVO component = componentService.getComponentDetailById(id);
        return Result.success(component);
    }

    /**
     * 创建核心部件
     */
    @PostMapping
    @ApiOperation("创建核心部件")
    public Result<Component> createComponent(
            @ApiParam("部件信息") @Valid @RequestBody ComponentDTO componentDTO) {
        
        Component component = componentService.createComponent(componentDTO);
        return Result.success(component);
    }

    /**
     * 更新核心部件
     */
    @PutMapping("/{id}")
    @ApiOperation("更新核心部件")
    public Result<Component> updateComponent(
            @ApiParam("部件ID") @PathVariable Long id,
            @ApiParam("部件信息") @Valid @RequestBody ComponentDTO componentDTO) {
        
        Component component = componentService.updateComponent(id, componentDTO);
        return Result.success(component);
    }

    /**
     * 删除核心部件
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除核心部件")
    public Result<Boolean> deleteComponent(
            @ApiParam("部件ID") @PathVariable Long id) {
        
        boolean result = componentService.deleteComponent(id);
        return Result.success(result);
    }

    /**
     * 批量删除核心部件
     */
    @DeleteMapping("/batch")
    @ApiOperation("批量删除核心部件")
    public Result<Boolean> batchDeleteComponents(
            @ApiParam("部件ID列表") @RequestBody List<Long> ids) {
        
        boolean result = componentService.batchDeleteComponents(ids);
        return Result.success(result);
    }

    /**
     * 更新核心部件状态
     */
    @PutMapping("/{id}/status")
    @ApiOperation("更新核心部件状态")
    public Result<Component> updateComponentStatus(
            @ApiParam("部件ID") @PathVariable Long id,
            @ApiParam("状态") @RequestParam String status,
            @ApiParam("备注") @RequestParam(required = false) String remark) {
        
        Component component = componentService.updateComponentStatus(id, status, remark);
        return Result.success(component);
    }

    /**
     * 复制核心部件
     */
    @PostMapping("/{id}/copy")
    @ApiOperation("复制核心部件")
    public Result<Component> copyComponent(
            @ApiParam("原部件ID") @PathVariable Long id,
            @ApiParam("新部件名称") @RequestParam String newComponentName,
            @ApiParam("新部件编码") @RequestParam String newComponentCode) {
        
        Component component = componentService.copyComponent(id, newComponentName, newComponentCode);
        return Result.success(component);
    }

    /**
     * 获取核心部件技术规格
     */
    @GetMapping("/{id}/specifications")
    @ApiOperation("获取核心部件技术规格")
    public Result<Map<String, Object>> getComponentSpecifications(
            @ApiParam("部件ID") @PathVariable Long id) {
        
        Map<String, Object> specifications = componentService.getComponentSpecifications(id);
        return Result.success(specifications);
    }

    /**
     * 更新核心部件技术规格
     */
    @PutMapping("/{id}/specifications")
    @ApiOperation("更新核心部件技术规格")
    public Result<Void> updateComponentSpecifications(
            @ApiParam("部件ID") @PathVariable Long id,
            @ApiParam("技术规格") @RequestBody Map<String, Object> specifications) {
        
        componentService.updateComponentSpecifications(id, specifications);
        return Result.success();
    }

    /**
     * 获取核心部件供应商信息
     */
    @GetMapping("/{id}/suppliers")
    @ApiOperation("获取核心部件供应商信息")
    public Result<List<Map<String, Object>>> getComponentSuppliers(
            @ApiParam("部件ID") @PathVariable Long id) {
        
        List<Map<String, Object>> suppliers = componentService.getComponentSuppliers(id);
        return Result.success(suppliers);
    }

    /**
     * 添加核心部件供应商
     */
    @PostMapping("/{id}/suppliers")
    @ApiOperation("添加核心部件供应商")
    public Result<Void> addComponentSupplier(
            @ApiParam("部件ID") @PathVariable Long id,
            @ApiParam("供应商信息") @RequestBody Map<String, Object> supplierInfo) {
        
        componentService.addComponentSupplier(id, supplierInfo);
        return Result.success();
    }

    /**
     * 移除核心部件供应商
     */
    @DeleteMapping("/{id}/suppliers/{supplierId}")
    @ApiOperation("移除核心部件供应商")
    public Result<Boolean> removeComponentSupplier(
            @ApiParam("部件ID") @PathVariable Long id,
            @ApiParam("供应商ID") @PathVariable Long supplierId) {
        
        boolean result = componentService.removeComponentSupplier(id, supplierId);
        return Result.success(result);
    }

    /**
     * 获取核心部件价格历史
     */
    @GetMapping("/{id}/price-history")
    @ApiOperation("获取核心部件价格历史")
    public Result<List<Map<String, Object>>> getComponentPriceHistory(
            @ApiParam("部件ID") @PathVariable Long id,
            @ApiParam("开始日期") @RequestParam(required = false) String startDate,
            @ApiParam("结束日期") @RequestParam(required = false) String endDate) {
        
        List<Map<String, Object>> priceHistory = componentService.getComponentPriceHistory(id, startDate, endDate);
        return Result.success(priceHistory);
    }

    /**
     * 更新核心部件价格
     */
    @PostMapping("/{id}/price")
    @ApiOperation("更新核心部件价格")
    public Result<Void> updateComponentPrice(
            @ApiParam("部件ID") @PathVariable Long id,
            @ApiParam("价格信息") @RequestBody Map<String, Object> priceInfo) {
        
        componentService.updateComponentPrice(id, priceInfo);
        return Result.success();
    }

    /**
     * 获取核心部件库存信息
     */
    @GetMapping("/{id}/inventory")
    @ApiOperation("获取核心部件库存信息")
    public Result<Map<String, Object>> getComponentInventory(
            @ApiParam("部件ID") @PathVariable Long id) {
        
        Map<String, Object> inventory = componentService.getComponentInventory(id);
        return Result.success(inventory);
    }

    /**
     * 更新核心部件库存
     */
    @PutMapping("/{id}/inventory")
    @ApiOperation("更新核心部件库存")
    public Result<Void> updateComponentInventory(
            @ApiParam("部件ID") @PathVariable Long id,
            @ApiParam("库存信息") @RequestBody Map<String, Object> inventoryInfo) {
        
        componentService.updateComponentInventory(id, inventoryInfo);
        return Result.success();
    }

    /**
     * 获取核心部件质量检测记录
     */
    @GetMapping("/{id}/quality-records")
    @ApiOperation("获取核心部件质量检测记录")
    public Result<List<Map<String, Object>>> getComponentQualityRecords(
            @ApiParam("部件ID") @PathVariable Long id) {
        
        List<Map<String, Object>> qualityRecords = componentService.getComponentQualityRecords(id);
        return Result.success(qualityRecords);
    }

    /**
     * 添加核心部件质量检测记录
     */
    @PostMapping("/{id}/quality-records")
    @ApiOperation("添加核心部件质量检测记录")
    public Result<Void> addComponentQualityRecord(
            @ApiParam("部件ID") @PathVariable Long id,
            @ApiParam("质量检测记录") @RequestBody Map<String, Object> qualityRecord) {
        
        componentService.addComponentQualityRecord(id, qualityRecord);
        return Result.success();
    }

    /**
     * 获取核心部件使用记录
     */
    @GetMapping("/{id}/usage-records")
    @ApiOperation("获取核心部件使用记录")
    public Result<List<Map<String, Object>>> getComponentUsageRecords(
            @ApiParam("部件ID") @PathVariable Long id) {
        
        List<Map<String, Object>> usageRecords = componentService.getComponentUsageRecords(id);
        return Result.success(usageRecords);
    }

    /**
     * 记录核心部件使用情况
     */
    @PostMapping("/{id}/usage-records")
    @ApiOperation("记录核心部件使用情况")
    public Result<Void> recordComponentUsage(
            @ApiParam("部件ID") @PathVariable Long id,
            @ApiParam("使用记录") @RequestBody Map<String, Object> usageRecord) {
        
        componentService.recordComponentUsage(id, usageRecord);
        return Result.success();
    }

    /**
     * 获取核心部件维护计划
     */
    @GetMapping("/{id}/maintenance-plan")
    @ApiOperation("获取核心部件维护计划")
    public Result<List<Map<String, Object>>> getComponentMaintenancePlan(
            @ApiParam("部件ID") @PathVariable Long id) {
        
        List<Map<String, Object>> maintenancePlan = componentService.getComponentMaintenancePlan(id);
        return Result.success(maintenancePlan);
    }

    /**
     * 创建核心部件维护计划
     */
    @PostMapping("/{id}/maintenance-plan")
    @ApiOperation("创建核心部件维护计划")
    public Result<Map<String, Object>> createComponentMaintenancePlan(
            @ApiParam("部件ID") @PathVariable Long id,
            @ApiParam("维护计划") @RequestBody Map<String, Object> maintenancePlan) {
        
        Map<String, Object> result = componentService.createComponentMaintenancePlan(id, maintenancePlan);
        return Result.success(result);
    }

    /**
     * 上传核心部件图片
     */
    @PostMapping("/{id}/images")
    @ApiOperation("上传核心部件图片")
    public Result<String> uploadComponentImage(
            @ApiParam("部件ID") @PathVariable Long id,
            @ApiParam("图片文件") @RequestParam("file") MultipartFile file,
            @ApiParam("图片类型") @RequestParam String imageType) {
        
        String imageUrl = componentService.uploadComponentImage(id, file, imageType);
        return Result.success(imageUrl);
    }

    /**
     * 获取核心部件图片列表
     */
    @GetMapping("/{id}/images")
    @ApiOperation("获取核心部件图片列表")
    public Result<List<Map<String, Object>>> getComponentImages(
            @ApiParam("部件ID") @PathVariable Long id) {
        
        List<Map<String, Object>> images = componentService.getComponentImages(id);
        return Result.success(images);
    }

    /**
     * 删除核心部件图片
     */
    @DeleteMapping("/{id}/images/{imageId}")
    @ApiOperation("删除核心部件图片")
    public Result<Boolean> deleteComponentImage(
            @ApiParam("部件ID") @PathVariable Long id,
            @ApiParam("图片ID") @PathVariable Long imageId) {
        
        boolean result = componentService.deleteComponentImage(id, imageId);
        return Result.success(result);
    }

    /**
     * 上传核心部件技术文档
     */
    @PostMapping("/{id}/documents")
    @ApiOperation("上传核心部件技术文档")
    public Result<String> uploadComponentDocument(
            @ApiParam("部件ID") @PathVariable Long id,
            @ApiParam("文档文件") @RequestParam("file") MultipartFile file,
            @ApiParam("文档类型") @RequestParam String docType) {
        
        String docUrl = componentService.uploadComponentDocument(id, file, docType);
        return Result.success(docUrl);
    }

    /**
     * 获取核心部件技术文档列表
     */
    @GetMapping("/{id}/documents")
    @ApiOperation("获取核心部件技术文档列表")
    public Result<List<Map<String, Object>>> getComponentDocuments(
            @ApiParam("部件ID") @PathVariable Long id) {
        
        List<Map<String, Object>> documents = componentService.getComponentDocuments(id);
        return Result.success(documents);
    }

    /**
     * 删除核心部件技术文档
     */
    @DeleteMapping("/{id}/documents/{docId}")
    @ApiOperation("删除核心部件技术文档")
    public Result<Boolean> deleteComponentDocument(
            @ApiParam("部件ID") @PathVariable Long id,
            @ApiParam("文档ID") @PathVariable Long docId) {
        
        boolean result = componentService.deleteComponentDocument(id, docId);
        return Result.success(result);
    }

    /**
     * 导出核心部件数据
     */
    @GetMapping("/export")
    @ApiOperation("导出核心部件数据")
    public Result<String> exportComponents(
            @ApiParam("部件名称") @RequestParam(required = false) String componentName,
            @ApiParam("部件分类") @RequestParam(required = false) String category,
            @ApiParam("状态") @RequestParam(required = false) String status,
            @ApiParam("导出格式") @RequestParam(defaultValue = "excel") String format) {
        
        String fileUrl = componentService.exportComponents(componentName, category, status, format);
        return Result.success(fileUrl);
    }

    /**
     * 导入核心部件数据
     */
    @PostMapping("/import")
    @ApiOperation("导入核心部件数据")
    public Result<Map<String, Object>> importComponents(
            @ApiParam("导入文件") @RequestParam("file") MultipartFile file) {
        
        Map<String, Object> result = componentService.importComponents(file);
        return Result.success(result);
    }

    /**
     * 获取核心部件统计数据
     */
    @GetMapping("/statistics")
    @ApiOperation("获取核心部件统计数据")
    public Result<Map<String, Object>> getComponentStatistics() {
        
        Map<String, Object> statistics = componentService.getComponentStatistics();
        return Result.success(statistics);
    }

    /**
     * 获取核心部件选项列表
     */
    @GetMapping("/options")
    @ApiOperation("获取核心部件选项列表")
    public Result<List<Map<String, Object>>> getComponentOptions(
            @ApiParam("部件分类") @RequestParam(required = false) String category,
            @ApiParam("部件类型") @RequestParam(required = false) String componentType) {
        
        List<Map<String, Object>> options = componentService.getComponentOptions(category, componentType);
        return Result.success(options);
    }

    /**
     * 验证核心部件编码唯一性
     */
    @GetMapping("/validate-code")
    @ApiOperation("验证核心部件编码唯一性")
    public Result<Boolean> validateComponentCode(
            @ApiParam("部件编码") @RequestParam String componentCode,
            @ApiParam("排除的ID") @RequestParam(required = false) Long excludeId) {
        
        boolean isUnique = componentService.validateComponentCode(componentCode, excludeId);
        return Result.success(isUnique);
    }

    /**
     * 获取核心部件分类树
     */
    @GetMapping("/category-tree")
    @ApiOperation("获取核心部件分类树")
    public Result<List<Map<String, Object>>> getComponentCategoryTree() {
        
        List<Map<String, Object>> categoryTree = componentService.getComponentCategoryTree();
        return Result.success(categoryTree);
    }

    /**
     * 获取核心部件兼容性信息
     */
    @GetMapping("/{id}/compatibility")
    @ApiOperation("获取核心部件兼容性信息")
    public Result<Map<String, Object>> getComponentCompatibility(
            @ApiParam("部件ID") @PathVariable Long id) {
        
        Map<String, Object> compatibility = componentService.getComponentCompatibility(id);
        return Result.success(compatibility);
    }

    /**
     * 更新核心部件兼容性信息
     */
    @PutMapping("/{id}/compatibility")
    @ApiOperation("更新核心部件兼容性信息")
    public Result<Void> updateComponentCompatibility(
            @ApiParam("部件ID") @PathVariable Long id,
            @ApiParam("兼容性信息") @RequestBody Map<String, Object> compatibility) {
        
        componentService.updateComponentCompatibility(id, compatibility);
        return Result.success();
    }

    /**
     * 获取核心部件替代方案
     */
    @GetMapping("/{id}/alternatives")
    @ApiOperation("获取核心部件替代方案")
    public Result<List<Map<String, Object>>> getComponentAlternatives(
            @ApiParam("部件ID") @PathVariable Long id) {
        
        List<Map<String, Object>> alternatives = componentService.getComponentAlternatives(id);
        return Result.success(alternatives);
    }

    /**
     * 添加核心部件替代方案
     */
    @PostMapping("/{id}/alternatives")
    @ApiOperation("添加核心部件替代方案")
    public Result<Void> addComponentAlternative(
            @ApiParam("部件ID") @PathVariable Long id,
            @ApiParam("替代部件ID") @RequestParam Long alternativeId,
            @ApiParam("替代原因") @RequestParam String reason) {
        
        componentService.addComponentAlternative(id, alternativeId, reason);
        return Result.success();
    }

    /**
     * 移除核心部件替代方案
     */
    @DeleteMapping("/{id}/alternatives/{alternativeId}")
    @ApiOperation("移除核心部件替代方案")
    public Result<Boolean> removeComponentAlternative(
            @ApiParam("部件ID") @PathVariable Long id,
            @ApiParam("替代部件ID") @PathVariable Long alternativeId) {
        
        boolean result = componentService.removeComponentAlternative(id, alternativeId);
        return Result.success(result);
    }

    /**
     * 获取核心部件生命周期信息
     */
    @GetMapping("/{id}/lifecycle")
    @ApiOperation("获取核心部件生命周期信息")
    public Result<Map<String, Object>> getComponentLifecycle(
            @ApiParam("部件ID") @PathVariable Long id) {
        
        Map<String, Object> lifecycle = componentService.getComponentLifecycle(id);
        return Result.success(lifecycle);
    }

    /**
     * 更新核心部件生命周期信息
     */
    @PutMapping("/{id}/lifecycle")
    @ApiOperation("更新核心部件生命周期信息")
    public Result<Void> updateComponentLifecycle(
            @ApiParam("部件ID") @PathVariable Long id,
            @ApiParam("生命周期信息") @RequestBody Map<String, Object> lifecycle) {
        
        componentService.updateComponentLifecycle(id, lifecycle);
        return Result.success();
    }

    /**
     * 生成核心部件二维码
     */
    @PostMapping("/{id}/qrcode")
    @ApiOperation("生成核心部件二维码")
    public Result<String> generateComponentQRCode(
            @ApiParam("部件ID") @PathVariable Long id) {
        
        String qrCodeUrl = componentService.generateComponentQRCode(id);
        return Result.success(qrCodeUrl);
    }

    /**
     * 获取核心部件性能指标
     */
    @GetMapping("/{id}/performance")
    @ApiOperation("获取核心部件性能指标")
    public Result<Map<String, Object>> getComponentPerformance(
            @ApiParam("部件ID") @PathVariable Long id) {
        
        Map<String, Object> performance = componentService.getComponentPerformance(id);
        return Result.success(performance);
    }

    /**
     * 更新核心部件性能指标
     */
    @PutMapping("/{id}/performance")
    @ApiOperation("更新核心部件性能指标")
    public Result<Void> updateComponentPerformance(
            @ApiParam("部件ID") @PathVariable Long id,
            @ApiParam("性能指标") @RequestBody Map<String, Object> performance) {
        
        componentService.updateComponentPerformance(id, performance);
        return Result.success();
    }

    /**
     * 获取核心部件成本分析
     */
    @GetMapping("/{id}/cost-analysis")
    @ApiOperation("获取核心部件成本分析")
    public Result<Map<String, Object>> getComponentCostAnalysis(
            @ApiParam("部件ID") @PathVariable Long id) {
        
        Map<String, Object> costAnalysis = componentService.getComponentCostAnalysis(id);
        return Result.success(costAnalysis);
    }

    /**
     * 更新核心部件成本信息
     */
    @PutMapping("/{id}/cost")
    @ApiOperation("更新核心部件成本信息")
    public Result<Void> updateComponentCost(
            @ApiParam("部件ID") @PathVariable Long id,
            @ApiParam("成本信息") @RequestBody Map<String, Object> costInfo) {
        
        componentService.updateComponentCost(id, costInfo);
        return Result.success();
    }

    /**
     * 获取核心部件变更历史
     */
    @GetMapping("/{id}/change-history")
    @ApiOperation("获取核心部件变更历史")
    public Result<List<Map<String, Object>>> getComponentChangeHistory(
            @ApiParam("部件ID") @PathVariable Long id) {
        
        List<Map<String, Object>> changeHistory = componentService.getComponentChangeHistory(id);
        return Result.success(changeHistory);
    }

    /**
     * 记录核心部件变更
     */
    @PostMapping("/{id}/change-history")
    @ApiOperation("记录核心部件变更")
    public Result<Void> recordComponentChange(
            @ApiParam("部件ID") @PathVariable Long id,
            @ApiParam("变更类型") @RequestParam String changeType,
            @ApiParam("变更内容") @RequestParam String changeContent,
            @ApiParam("操作人") @RequestParam String operator) {
        
        componentService.recordComponentChange(id, changeType, changeContent, operator);
        return Result.success();
    }
}