package com.yinma.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinma.common.Result;
import com.yinma.dto.MaterialDTO;
import com.yinma.entity.MaterialEntity;
import com.yinma.service.MaterialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 物料主数据管理Controller
 * 
 * <AUTHOR>
 * @since 2024-01-20
 */
@Api(tags = "物料主数据管理")
@RestController
@RequestMapping("/api/material")
public class MaterialController {

    @Autowired
    private MaterialService materialService;

    @ApiOperation("分页查询物料列表")
    @GetMapping("/page")
    public Result<IPage<MaterialDTO>> queryPage(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("物料编码") @RequestParam(required = false) String materialCode,
            @ApiParam("物料名称") @RequestParam(required = false) String materialName,
            @ApiParam("物料分类") @RequestParam(required = false) String category,
            @ApiParam("物料类型") @RequestParam(required = false) String materialType,
            @ApiParam("规格型号") @RequestParam(required = false) String specification,
            @ApiParam("状态") @RequestParam(required = false) String status,
            @ApiParam("ABC分类") @RequestParam(required = false) String abcCategory,
            @ApiParam("是否关键物料") @RequestParam(required = false) Boolean isKeyMaterial) {
        
        Page<MaterialEntity> page = new Page<>(current, size);
        MaterialDTO.MaterialQueryDTO queryDTO = new MaterialDTO.MaterialQueryDTO();
        queryDTO.setMaterialCode(materialCode);
        queryDTO.setMaterialName(materialName);
        queryDTO.setCategory(category);
        queryDTO.setMaterialType(materialType);
        queryDTO.setSpecification(specification);
        queryDTO.setStatus(status);
        queryDTO.setAbcCategory(abcCategory);
        queryDTO.setIsKeyMaterial(isKeyMaterial);
        
        IPage<MaterialDTO> result = materialService.queryPage(page, queryDTO);
        return Result.success(result);
    }

    @ApiOperation("获取物料详情")
    @GetMapping("/{materialId}")
    public Result<MaterialDTO> getMaterialDetail(
            @ApiParam("物料ID") @PathVariable Long materialId) {
        
        MaterialDTO material = materialService.getMaterialDetail(materialId);
        if (material == null) {
            return Result.error("物料不存在");
        }
        return Result.success(material);
    }

    @ApiOperation("获取物料完整信息")
    @GetMapping("/full-info/{materialId}")
    public Result<MaterialDTO> getMaterialFullInfo(
            @ApiParam("物料ID") @PathVariable Long materialId) {
        
        MaterialDTO material = materialService.getMaterialFullInfo(materialId);
        if (material == null) {
            return Result.error("物料不存在");
        }
        return Result.success(material);
    }

    @ApiOperation("创建物料")
    @PostMapping
    public Result<MaterialDTO> createMaterial(@Valid @RequestBody MaterialDTO materialDTO) {
        MaterialDTO result = materialService.createMaterial(materialDTO);
        return Result.success(result);
    }

    @ApiOperation("更新物料")
    @PutMapping("/{materialId}")
    public Result<MaterialDTO> updateMaterial(
            @ApiParam("物料ID") @PathVariable Long materialId,
            @Valid @RequestBody MaterialDTO materialDTO) {
        
        materialDTO.setMaterialId(materialId);
        MaterialDTO result = materialService.updateMaterial(materialDTO);
        return Result.success(result);
    }

    @ApiOperation("删除物料")
    @DeleteMapping("/{materialId}")
    public Result<Boolean> deleteMaterial(
            @ApiParam("物料ID") @PathVariable Long materialId) {
        
        boolean success = materialService.deleteMaterial(materialId);
        return Result.success(success);
    }

    @ApiOperation("启用物料")
    @PutMapping("/enable/{materialId}")
    public Result<Boolean> enableMaterial(
            @ApiParam("物料ID") @PathVariable Long materialId) {
        
        boolean success = materialService.enableMaterial(materialId);
        return Result.success(success);
    }

    @ApiOperation("禁用物料")
    @PutMapping("/disable/{materialId}")
    public Result<Boolean> disableMaterial(
            @ApiParam("物料ID") @PathVariable Long materialId) {
        
        boolean success = materialService.disableMaterial(materialId);
        return Result.success(success);
    }

    @ApiOperation("获取物料库存信息")
    @GetMapping("/stock/{materialId}")
    public Result<List<MaterialDTO.MaterialStockDTO>> getMaterialStock(
            @ApiParam("物料ID") @PathVariable Long materialId) {
        
        List<MaterialDTO.MaterialStockDTO> stockList = materialService.getMaterialStock(materialId);
        return Result.success(stockList);
    }

    @ApiOperation("获取物料供应商列表")
    @GetMapping("/suppliers/{materialId}")
    public Result<List<MaterialDTO.MaterialSupplierDTO>> getMaterialSuppliers(
            @ApiParam("物料ID") @PathVariable Long materialId) {
        
        List<MaterialDTO.MaterialSupplierDTO> suppliers = materialService.getMaterialSuppliers(materialId);
        return Result.success(suppliers);
    }

    @ApiOperation("获取物料价格历史")
    @GetMapping("/price-history/{materialId}")
    public Result<List<MaterialDTO.MaterialPriceHistoryDTO>> getMaterialPriceHistory(
            @ApiParam("物料ID") @PathVariable Long materialId) {
        
        List<MaterialDTO.MaterialPriceHistoryDTO> priceHistory = materialService.getMaterialPriceHistory(materialId);
        return Result.success(priceHistory);
    }

    @ApiOperation("获取库存明细")
    @GetMapping("/stock-details")
    public Result<List<MaterialDTO.MaterialStockDetailDTO>> getStockDetails(
            @ApiParam("物料ID") @RequestParam(required = false) Long materialId,
            @ApiParam("仓库ID") @RequestParam(required = false) Long warehouseId,
            @ApiParam("库位ID") @RequestParam(required = false) Long locationId,
            @ApiParam("批次号") @RequestParam(required = false) String batchNumber,
            @ApiParam("序列号") @RequestParam(required = false) String serialNumber) {
        
        MaterialDTO.StockQueryDTO queryDTO = new MaterialDTO.StockQueryDTO();
        queryDTO.setMaterialId(materialId);
        queryDTO.setWarehouseId(warehouseId);
        queryDTO.setLocationId(locationId);
        queryDTO.setBatchNumber(batchNumber);
        queryDTO.setSerialNumber(serialNumber);
        
        List<MaterialDTO.MaterialStockDetailDTO> stockDetails = materialService.getStockDetails(queryDTO);
        return Result.success(stockDetails);
    }

    @ApiOperation("获取物料替代关系")
    @GetMapping("/substitutes/{materialId}")
    public Result<List<MaterialDTO.MaterialSubstituteDTO>> getMaterialSubstitutes(
            @ApiParam("物料ID") @PathVariable Long materialId) {
        
        List<MaterialDTO.MaterialSubstituteDTO> substitutes = materialService.getMaterialSubstitutes(materialId);
        return Result.success(substitutes);
    }

    @ApiOperation("获取低库存物料")
    @GetMapping("/low-stock")
    public Result<List<MaterialDTO>> getLowStockMaterials(
            @ApiParam("库存阈值") @RequestParam(defaultValue = "10") BigDecimal threshold) {
        
        List<MaterialDTO> lowStockMaterials = materialService.getLowStockMaterials(threshold);
        return Result.success(lowStockMaterials);
    }

    @ApiOperation("获取超期物料")
    @GetMapping("/expired")
    public Result<List<MaterialDTO>> getExpiredMaterials(
            @ApiParam("超期天数") @RequestParam(defaultValue = "30") Integer days) {
        
        List<MaterialDTO> expiredMaterials = materialService.getExpiredMaterials(days);
        return Result.success(expiredMaterials);
    }

    @ApiOperation("获取物料分类统计")
    @GetMapping("/category-statistics")
    public Result<List<MaterialDTO.MaterialCategoryStatDTO>> getCategoryStatistics() {
        List<MaterialDTO.MaterialCategoryStatDTO> statistics = materialService.getCategoryStatistics();
        return Result.success(statistics);
    }

    @ApiOperation("获取ABC分析数据")
    @GetMapping("/abc-analysis")
    public Result<List<MaterialDTO.MaterialAbcAnalysisDTO>> getAbcAnalysisData() {
        List<MaterialDTO.MaterialAbcAnalysisDTO> analysisData = materialService.getAbcAnalysisData();
        return Result.success(analysisData);
    }

    @ApiOperation("批量更新ABC分类")
    @PutMapping("/batch-update-abc")
    public Result<Boolean> batchUpdateAbcCategory(
            @ApiParam("更新列表") @RequestBody List<MaterialDTO.MaterialAbcUpdateDTO> updateList) {
        
        boolean success = materialService.batchUpdateAbcCategory(updateList);
        return Result.success(success);
    }

    @ApiOperation("批量更新价格")
    @PutMapping("/batch-update-price")
    public Result<Boolean> batchUpdatePrice(
            @ApiParam("更新列表") @RequestBody List<MaterialDTO.MaterialPriceUpdateDTO> updateList) {
        
        boolean success = materialService.batchUpdatePrice(updateList);
        return Result.success(success);
    }

    @ApiOperation("获取物料使用情况")
    @GetMapping("/usage/{materialId}")
    public Result<List<MaterialDTO.MaterialUsageDTO>> getMaterialUsage(
            @ApiParam("物料ID") @PathVariable Long materialId) {
        
        List<MaterialDTO.MaterialUsageDTO> usage = materialService.getMaterialUsage(materialId);
        return Result.success(usage);
    }

    @ApiOperation("检查物料编码是否存在")
    @GetMapping("/check-code")
    public Result<Boolean> checkCodeExists(
            @ApiParam("物料编码") @RequestParam String materialCode,
            @ApiParam("排除的物料ID") @RequestParam(required = false) Long excludeId) {
        
        boolean exists = materialService.checkCodeExists(materialCode, excludeId);
        return Result.success(exists);
    }

    @ApiOperation("生成物料编码建议")
    @GetMapping("/code-suggestions")
    public Result<List<String>> generateCodeSuggestions(
            @ApiParam("编码前缀") @RequestParam(required = false) String prefix,
            @ApiParam("物料分类") @RequestParam(required = false) String category) {
        
        List<String> suggestions = materialService.generateCodeSuggestions(prefix, category);
        return Result.success(suggestions);
    }

    @ApiOperation("校验物料数据")
    @PostMapping("/validate")
    public Result<MaterialDTO.MaterialValidationResultDTO> validateMaterialData(
            @ApiParam("物料数据") @RequestBody MaterialDTO materialDTO) {
        
        MaterialDTO.MaterialValidationResultDTO result = materialService.validateMaterialData(materialDTO);
        return Result.success(result);
    }

    @ApiOperation("导入物料数据")
    @PostMapping("/import")
    public Result<MaterialDTO.MaterialImportResultDTO> importMaterials(
            @ApiParam("物料数据列表") @RequestBody List<MaterialDTO> materialList) {
        
        try {
            MaterialDTO.MaterialImportResultDTO result = materialService.importMaterials(materialList);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("导入失败: " + e.getMessage());
        }
    }

    @ApiOperation("导出物料数据")
    @GetMapping("/export")
    public Result<String> exportMaterials(
            @ApiParam("物料编码") @RequestParam(required = false) String materialCode,
            @ApiParam("物料名称") @RequestParam(required = false) String materialName,
            @ApiParam("物料分类") @RequestParam(required = false) String category,
            @ApiParam("物料类型") @RequestParam(required = false) String materialType,
            @ApiParam("规格型号") @RequestParam(required = false) String specification,
            @ApiParam("状态") @RequestParam(required = false) String status,
            @ApiParam("ABC分类") @RequestParam(required = false) String abcCategory,
            @ApiParam("是否关键物料") @RequestParam(required = false) Boolean isKeyMaterial,
            @ApiParam("导出格式") @RequestParam(defaultValue = "EXCEL") String format) {
        
        try {
            MaterialDTO.MaterialQueryDTO queryDTO = new MaterialDTO.MaterialQueryDTO();
            queryDTO.setMaterialCode(materialCode);
            queryDTO.setMaterialName(materialName);
            queryDTO.setCategory(category);
            queryDTO.setMaterialType(materialType);
            queryDTO.setSpecification(specification);
            queryDTO.setStatus(status);
            queryDTO.setAbcCategory(abcCategory);
            queryDTO.setIsKeyMaterial(isKeyMaterial);
            
            byte[] data = materialService.exportMaterials(queryDTO);
            // TODO: 返回文件下载链接或直接返回文件流
            return Result.success("导出成功");
        } catch (Exception e) {
            return Result.error("导出失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取物料分类选项")
    @GetMapping("/categories")
    public Result<List<String>> getMaterialCategories() {
        List<String> categories = List.of(
            "原材料",
            "半成品",
            "成品",
            "包装材料",
            "辅助材料",
            "工具",
            "设备",
            "备件",
            "消耗品",
            "其他"
        );
        return Result.success(categories);
    }

    @ApiOperation("获取物料类型选项")
    @GetMapping("/types")
    public Result<List<String>> getMaterialTypes() {
        List<String> types = List.of(
            "采购件",
            "自制件",
            "外协件",
            "虚拟件",
            "配置件",
            "选项件",
            "标准件",
            "通用件",
            "专用件"
        );
        return Result.success(types);
    }

    @ApiOperation("获取计量单位选项")
    @GetMapping("/units")
    public Result<List<String>> getMaterialUnits() {
        List<String> units = List.of(
            "个", "件", "套", "台", "只",
            "米", "厘米", "毫米", "千米",
            "千克", "克", "吨", "磅",
            "升", "毫升", "立方米",
            "平方米", "平方厘米",
            "小时", "天", "月", "年",
            "批", "箱", "包", "袋"
        );
        return Result.success(units);
    }

    @ApiOperation("获取ABC分类选项")
    @GetMapping("/abc-categories")
    public Result<List<String>> getAbcCategories() {
        List<String> abcCategories = List.of(
            "A",  // 重要物料
            "B",  // 一般物料
            "C"   // 普通物料
        );
        return Result.success(abcCategories);
    }

    @ApiOperation("获取物料状态选项")
    @GetMapping("/statuses")
    public Result<List<String>> getMaterialStatuses() {
        List<String> statuses = List.of(
            "ACTIVE",    // 启用
            "INACTIVE",  // 禁用
            "DRAFT",     // 草稿
            "OBSOLETE"   // 废弃
        );
        return Result.success(statuses);
    }

    @ApiOperation("搜索物料")
    @GetMapping("/search")
    public Result<List<MaterialDTO>> searchMaterials(
            @ApiParam("搜索关键词") @RequestParam String keyword,
            @ApiParam("搜索类型") @RequestParam(defaultValue = "ALL") String searchType,
            @ApiParam("限制数量") @RequestParam(defaultValue = "20") Integer limit) {
        
        MaterialDTO.MaterialQueryDTO queryDTO = new MaterialDTO.MaterialQueryDTO();
        
        switch (searchType) {
            case "CODE":
                queryDTO.setMaterialCode(keyword);
                break;
            case "NAME":
                queryDTO.setMaterialName(keyword);
                break;
            case "SPECIFICATION":
                queryDTO.setSpecification(keyword);
                break;
            default:
                // 全文搜索
                queryDTO.setKeyword(keyword);
                break;
        }
        
        Page<MaterialEntity> page = new Page<>(1, limit);
        IPage<MaterialDTO> result = materialService.queryPage(page, queryDTO);
        return Result.success(result.getRecords());
    }

    @ApiOperation("获取热门物料")
    @GetMapping("/popular")
    public Result<List<MaterialDTO>> getPopularMaterials(
            @ApiParam("限制数量") @RequestParam(defaultValue = "10") Integer limit) {
        
        // TODO: 根据使用频率、访问次数等指标获取热门物料
        MaterialDTO.MaterialQueryDTO queryDTO = new MaterialDTO.MaterialQueryDTO();
        queryDTO.setIsKeyMaterial(true);
        
        Page<MaterialEntity> page = new Page<>(1, limit);
        IPage<MaterialDTO> result = materialService.queryPage(page, queryDTO);
        return Result.success(result.getRecords());
    }

    @ApiOperation("获取最近创建的物料")
    @GetMapping("/recent")
    public Result<List<MaterialDTO>> getRecentMaterials(
            @ApiParam("限制数量") @RequestParam(defaultValue = "10") Integer limit) {
        
        MaterialDTO.MaterialQueryDTO queryDTO = new MaterialDTO.MaterialQueryDTO();
        queryDTO.setOrderBy("create_time");
        queryDTO.setOrderDirection("DESC");
        
        Page<MaterialEntity> page = new Page<>(1, limit);
        IPage<MaterialDTO> result = materialService.queryPage(page, queryDTO);
        return Result.success(result.getRecords());
    }
}