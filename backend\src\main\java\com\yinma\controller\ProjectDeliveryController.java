package com.yinma.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinma.common.Result;
import com.yinma.entity.ProjectDelivery;
import com.yinma.dto.ProjectDeliveryDTO;
import com.yinma.vo.ProjectDeliveryVO;
import com.yinma.service.ProjectDeliveryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 项目交付管理控制器
 * 银马实业项目交付管理系统
 * 
 * <AUTHOR>
 * @since 2024-05-20
 */
@Slf4j
@RestController
@RequestMapping("/api/project-delivery")
@RequiredArgsConstructor
@Api(tags = "项目交付管理")
public class ProjectDeliveryController {

    private final ProjectDeliveryService projectDeliveryService;

    @GetMapping("/page")
    @ApiOperation("分页查询项目交付")
    public Result<IPage<ProjectDeliveryVO>> getProjectDeliveryPage(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Long current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Long size,
            @ApiParam("项目编号") @RequestParam(required = false) String projectCode,
            @ApiParam("项目名称") @RequestParam(required = false) String projectName,
            @ApiParam("客户名称") @RequestParam(required = false) String customerName,
            @ApiParam("交付状态") @RequestParam(required = false) String deliveryStatus,
            @ApiParam("项目经理") @RequestParam(required = false) String projectManager,
            @ApiParam("计划交付日期开始") @RequestParam(required = false) String planDeliveryDateStart,
            @ApiParam("计划交付日期结束") @RequestParam(required = false) String planDeliveryDateEnd) {
        
        Page<ProjectDelivery> page = new Page<>(current, size);
        IPage<ProjectDeliveryVO> result = projectDeliveryService.getProjectDeliveryPage(
                page, projectCode, projectName, customerName, deliveryStatus, 
                projectManager, planDeliveryDateStart, planDeliveryDateEnd);
        
        return Result.success(result);
    }

    @GetMapping("/{id}")
    @ApiOperation("根据ID查询项目交付详情")
    public Result<ProjectDeliveryVO> getProjectDeliveryById(
            @ApiParam("项目交付ID") @PathVariable Long id) {
        
        ProjectDeliveryVO projectDelivery = projectDeliveryService.getProjectDeliveryDetailById(id);
        return Result.success(projectDelivery);
    }

    @PostMapping
    @ApiOperation("创建项目交付")
    public Result<ProjectDelivery> createProjectDelivery(
            @ApiParam("项目交付信息") @Valid @RequestBody ProjectDeliveryDTO projectDeliveryDTO) {
        
        ProjectDelivery projectDelivery = projectDeliveryService.createProjectDelivery(projectDeliveryDTO);
        return Result.success(projectDelivery);
    }

    @PutMapping("/{id}")
    @ApiOperation("更新项目交付")
    public Result<ProjectDelivery> updateProjectDelivery(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("项目交付信息") @Valid @RequestBody ProjectDeliveryDTO projectDeliveryDTO) {
        
        ProjectDelivery projectDelivery = projectDeliveryService.updateProjectDelivery(id, projectDeliveryDTO);
        return Result.success(projectDelivery);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除项目交付")
    public Result<Boolean> deleteProjectDelivery(
            @ApiParam("项目交付ID") @PathVariable Long id) {
        
        boolean result = projectDeliveryService.deleteProjectDelivery(id);
        return Result.success(result);
    }

    @DeleteMapping("/batch")
    @ApiOperation("批量删除项目交付")
    public Result<Boolean> batchDeleteProjectDelivery(
            @ApiParam("项目交付ID列表") @RequestBody List<Long> ids) {
        
        boolean result = projectDeliveryService.batchDeleteProjectDelivery(ids);
        return Result.success(result);
    }

    @PutMapping("/{id}/status")
    @ApiOperation("更新项目交付状态")
    public Result<ProjectDelivery> updateProjectDeliveryStatus(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("新状态") @RequestParam String status,
            @ApiParam("备注") @RequestParam(required = false) String remark) {
        
        ProjectDelivery projectDelivery = projectDeliveryService.updateProjectDeliveryStatus(id, status, remark);
        return Result.success(projectDelivery);
    }

    @GetMapping("/{id}/deliverables")
    @ApiOperation("获取项目交付物清单")
    public Result<List<Map<String, Object>>> getProjectDeliverables(
            @ApiParam("项目交付ID") @PathVariable Long id) {
        
        List<Map<String, Object>> deliverables = projectDeliveryService.getProjectDeliverables(id);
        return Result.success(deliverables);
    }

    @PutMapping("/{id}/deliverables")
    @ApiOperation("更新项目交付物清单")
    public Result<Void> updateProjectDeliverables(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("交付物清单") @RequestBody List<Map<String, Object>> deliverablesList) {
        
        projectDeliveryService.updateProjectDeliverables(id, deliverablesList);
        return Result.success();
    }

    @GetMapping("/{id}/milestones")
    @ApiOperation("获取项目里程碑")
    public Result<List<Map<String, Object>>> getProjectMilestones(
            @ApiParam("项目交付ID") @PathVariable Long id) {
        
        List<Map<String, Object>> milestones = projectDeliveryService.getProjectMilestones(id);
        return Result.success(milestones);
    }

    @PutMapping("/{id}/milestones")
    @ApiOperation("更新项目里程碑")
    public Result<Void> updateProjectMilestones(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("里程碑列表") @RequestBody List<Map<String, Object>> milestonesList) {
        
        projectDeliveryService.updateProjectMilestones(id, milestonesList);
        return Result.success();
    }

    @GetMapping("/{id}/progress")
    @ApiOperation("获取项目进度")
    public Result<Map<String, Object>> getProjectProgress(
            @ApiParam("项目交付ID") @PathVariable Long id) {
        
        Map<String, Object> progress = projectDeliveryService.getProjectProgress(id);
        return Result.success(progress);
    }

    @PutMapping("/{id}/progress")
    @ApiOperation("更新项目进度")
    public Result<Void> updateProjectProgress(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("进度信息") @RequestBody Map<String, Object> progressInfo) {
        
        projectDeliveryService.updateProjectProgress(id, progressInfo);
        return Result.success();
    }

    @GetMapping("/{id}/team")
    @ApiOperation("获取项目团队")
    public Result<List<Map<String, Object>>> getProjectTeam(
            @ApiParam("项目交付ID") @PathVariable Long id) {
        
        List<Map<String, Object>> team = projectDeliveryService.getProjectTeam(id);
        return Result.success(team);
    }

    @PutMapping("/{id}/team")
    @ApiOperation("更新项目团队")
    public Result<Void> updateProjectTeam(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("团队成员列表") @RequestBody List<Map<String, Object>> teamList) {
        
        projectDeliveryService.updateProjectTeam(id, teamList);
        return Result.success();
    }

    @GetMapping("/{id}/risks")
    @ApiOperation("获取项目风险")
    public Result<List<Map<String, Object>>> getProjectRisks(
            @ApiParam("项目交付ID") @PathVariable Long id) {
        
        List<Map<String, Object>> risks = projectDeliveryService.getProjectRisks(id);
        return Result.success(risks);
    }

    @PostMapping("/{id}/risks")
    @ApiOperation("添加项目风险")
    public Result<Void> addProjectRisk(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("风险信息") @RequestBody Map<String, Object> riskInfo) {
        
        projectDeliveryService.addProjectRisk(id, riskInfo);
        return Result.success();
    }

    @PutMapping("/{id}/risks/{riskId}")
    @ApiOperation("更新项目风险")
    public Result<Void> updateProjectRisk(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("风险ID") @PathVariable Long riskId,
            @ApiParam("风险信息") @RequestBody Map<String, Object> riskInfo) {
        
        projectDeliveryService.updateProjectRisk(id, riskId, riskInfo);
        return Result.success();
    }

    @DeleteMapping("/{id}/risks/{riskId}")
    @ApiOperation("删除项目风险")
    public Result<Boolean> deleteProjectRisk(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("风险ID") @PathVariable Long riskId) {
        
        boolean result = projectDeliveryService.deleteProjectRisk(id, riskId);
        return Result.success(result);
    }

    @GetMapping("/{id}/issues")
    @ApiOperation("获取项目问题")
    public Result<List<Map<String, Object>>> getProjectIssues(
            @ApiParam("项目交付ID") @PathVariable Long id) {
        
        List<Map<String, Object>> issues = projectDeliveryService.getProjectIssues(id);
        return Result.success(issues);
    }

    @PostMapping("/{id}/issues")
    @ApiOperation("添加项目问题")
    public Result<Void> addProjectIssue(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("问题信息") @RequestBody Map<String, Object> issueInfo) {
        
        projectDeliveryService.addProjectIssue(id, issueInfo);
        return Result.success();
    }

    @PutMapping("/{id}/issues/{issueId}")
    @ApiOperation("更新项目问题")
    public Result<Void> updateProjectIssue(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("问题ID") @PathVariable Long issueId,
            @ApiParam("问题信息") @RequestBody Map<String, Object> issueInfo) {
        
        projectDeliveryService.updateProjectIssue(id, issueId, issueInfo);
        return Result.success();
    }

    @DeleteMapping("/{id}/issues/{issueId}")
    @ApiOperation("删除项目问题")
    public Result<Boolean> deleteProjectIssue(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("问题ID") @PathVariable Long issueId) {
        
        boolean result = projectDeliveryService.deleteProjectIssue(id, issueId);
        return Result.success(result);
    }

    @GetMapping("/{id}/budget")
    @ApiOperation("获取项目预算")
    public Result<Map<String, Object>> getProjectBudget(
            @ApiParam("项目交付ID") @PathVariable Long id) {
        
        Map<String, Object> budget = projectDeliveryService.getProjectBudget(id);
        return Result.success(budget);
    }

    @PutMapping("/{id}/budget")
    @ApiOperation("更新项目预算")
    public Result<Void> updateProjectBudget(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("预算信息") @RequestBody Map<String, Object> budgetInfo) {
        
        projectDeliveryService.updateProjectBudget(id, budgetInfo);
        return Result.success();
    }

    @GetMapping("/{id}/costs")
    @ApiOperation("获取项目成本")
    public Result<List<Map<String, Object>>> getProjectCosts(
            @ApiParam("项目交付ID") @PathVariable Long id) {
        
        List<Map<String, Object>> costs = projectDeliveryService.getProjectCosts(id);
        return Result.success(costs);
    }

    @PostMapping("/{id}/costs")
    @ApiOperation("记录项目成本")
    public Result<Void> recordProjectCost(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("成本记录") @RequestBody Map<String, Object> costRecord) {
        
        projectDeliveryService.recordProjectCost(id, costRecord);
        return Result.success();
    }

    @GetMapping("/{id}/quality")
    @ApiOperation("获取项目质量检查")
    public Result<List<Map<String, Object>>> getProjectQuality(
            @ApiParam("项目交付ID") @PathVariable Long id) {
        
        List<Map<String, Object>> quality = projectDeliveryService.getProjectQuality(id);
        return Result.success(quality);
    }

    @PostMapping("/{id}/quality")
    @ApiOperation("添加质量检查记录")
    public Result<Void> addProjectQuality(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("质量检查记录") @RequestBody Map<String, Object> qualityRecord) {
        
        projectDeliveryService.addProjectQuality(id, qualityRecord);
        return Result.success();
    }

    @GetMapping("/{id}/acceptance")
    @ApiOperation("获取项目验收信息")
    public Result<Map<String, Object>> getProjectAcceptance(
            @ApiParam("项目交付ID") @PathVariable Long id) {
        
        Map<String, Object> acceptance = projectDeliveryService.getProjectAcceptance(id);
        return Result.success(acceptance);
    }

    @PutMapping("/{id}/acceptance")
    @ApiOperation("更新项目验收信息")
    public Result<Void> updateProjectAcceptance(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("验收信息") @RequestBody Map<String, Object> acceptanceInfo) {
        
        projectDeliveryService.updateProjectAcceptance(id, acceptanceInfo);
        return Result.success();
    }

    @PostMapping("/{id}/acceptance/submit")
    @ApiOperation("提交项目验收")
    public Result<Void> submitProjectAcceptance(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("验收提交信息") @RequestBody Map<String, Object> submitInfo) {
        
        projectDeliveryService.submitProjectAcceptance(id, submitInfo);
        return Result.success();
    }

    @PostMapping("/{id}/acceptance/approve")
    @ApiOperation("审批项目验收")
    public Result<Void> approveProjectAcceptance(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("审批信息") @RequestBody Map<String, Object> approveInfo) {
        
        projectDeliveryService.approveProjectAcceptance(id, approveInfo);
        return Result.success();
    }

    @PostMapping("/{id}/acceptance/reject")
    @ApiOperation("拒绝项目验收")
    public Result<Void> rejectProjectAcceptance(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("拒绝原因") @RequestParam String reason) {
        
        projectDeliveryService.rejectProjectAcceptance(id, reason);
        return Result.success();
    }

    @PostMapping("/{id}/documents")
    @ApiOperation("上传项目文档")
    public Result<String> uploadProjectDocument(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("文档文件") @RequestParam("file") MultipartFile file,
            @ApiParam("文档类型") @RequestParam String docType,
            @ApiParam("文档描述") @RequestParam(required = false) String description) {
        
        String docUrl = projectDeliveryService.uploadProjectDocument(id, file, docType, description);
        return Result.success(docUrl);
    }

    @GetMapping("/{id}/documents")
    @ApiOperation("获取项目文档列表")
    public Result<List<Map<String, Object>>> getProjectDocuments(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("文档类型") @RequestParam(required = false) String docType) {
        
        List<Map<String, Object>> documents = projectDeliveryService.getProjectDocuments(id, docType);
        return Result.success(documents);
    }

    @DeleteMapping("/{id}/documents/{docId}")
    @ApiOperation("删除项目文档")
    public Result<Boolean> deleteProjectDocument(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("文档ID") @PathVariable Long docId) {
        
        boolean result = projectDeliveryService.deleteProjectDocument(id, docId);
        return Result.success(result);
    }

    @GetMapping("/{id}/communications")
    @ApiOperation("获取项目沟通记录")
    public Result<List<Map<String, Object>>> getProjectCommunications(
            @ApiParam("项目交付ID") @PathVariable Long id) {
        
        List<Map<String, Object>> communications = projectDeliveryService.getProjectCommunications(id);
        return Result.success(communications);
    }

    @PostMapping("/{id}/communications")
    @ApiOperation("添加沟通记录")
    public Result<Void> addProjectCommunication(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("沟通记录") @RequestBody Map<String, Object> communicationRecord) {
        
        projectDeliveryService.addProjectCommunication(id, communicationRecord);
        return Result.success();
    }

    @GetMapping("/{id}/timeline")
    @ApiOperation("获取项目时间线")
    public Result<List<Map<String, Object>>> getProjectTimeline(
            @ApiParam("项目交付ID") @PathVariable Long id) {
        
        List<Map<String, Object>> timeline = projectDeliveryService.getProjectTimeline(id);
        return Result.success(timeline);
    }

    @PostMapping("/{id}/timeline")
    @ApiOperation("添加时间线事件")
    public Result<Void> addProjectTimelineEvent(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("时间线事件") @RequestBody Map<String, Object> timelineEvent) {
        
        projectDeliveryService.addProjectTimelineEvent(id, timelineEvent);
        return Result.success();
    }

    @GetMapping("/{id}/reports")
    @ApiOperation("获取项目报告")
    public Result<List<Map<String, Object>>> getProjectReports(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("报告类型") @RequestParam(required = false) String reportType) {
        
        List<Map<String, Object>> reports = projectDeliveryService.getProjectReports(id, reportType);
        return Result.success(reports);
    }

    @PostMapping("/{id}/reports")
    @ApiOperation("生成项目报告")
    public Result<String> generateProjectReport(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("报告类型") @RequestParam String reportType,
            @ApiParam("报告参数") @RequestBody(required = false) Map<String, Object> reportParams) {
        
        String reportUrl = projectDeliveryService.generateProjectReport(id, reportType, reportParams);
        return Result.success(reportUrl);
    }

    @GetMapping("/export")
    @ApiOperation("导出项目交付数据")
    public Result<String> exportProjectDelivery(
            @ApiParam("项目编号") @RequestParam(required = false) String projectCode,
            @ApiParam("客户名称") @RequestParam(required = false) String customerName,
            @ApiParam("交付状态") @RequestParam(required = false) String deliveryStatus,
            @ApiParam("导出格式") @RequestParam(defaultValue = "excel") String format) {
        
        String exportUrl = projectDeliveryService.exportProjectDelivery(projectCode, customerName, deliveryStatus, format);
        return Result.success(exportUrl);
    }

    @PostMapping("/import")
    @ApiOperation("导入项目交付数据")
    public Result<Map<String, Object>> importProjectDelivery(
            @ApiParam("导入文件") @RequestParam("file") MultipartFile file) {
        
        Map<String, Object> result = projectDeliveryService.importProjectDelivery(file);
        return Result.success(result);
    }

    @GetMapping("/statistics")
    @ApiOperation("获取项目交付统计数据")
    public Result<Map<String, Object>> getProjectDeliveryStatistics(
            @ApiParam("时间范围") @RequestParam(required = false) String timeRange) {
        
        Map<String, Object> statistics = projectDeliveryService.getProjectDeliveryStatistics(timeRange);
        return Result.success(statistics);
    }

    @GetMapping("/dashboard")
    @ApiOperation("获取项目交付看板数据")
    public Result<Map<String, Object>> getProjectDeliveryDashboard(
            @ApiParam("时间范围") @RequestParam(required = false) String timeRange) {
        
        Map<String, Object> dashboard = projectDeliveryService.getProjectDeliveryDashboard(timeRange);
        return Result.success(dashboard);
    }

    @GetMapping("/options")
    @ApiOperation("获取项目交付选项列表")
    public Result<Map<String, List<Map<String, Object>>>> getProjectDeliveryOptions() {
        Map<String, List<Map<String, Object>>> options = projectDeliveryService.getProjectDeliveryOptions();
        return Result.success(options);
    }

    @GetMapping("/validate-project-code")
    @ApiOperation("验证项目编号唯一性")
    public Result<Boolean> validateProjectCode(
            @ApiParam("项目编号") @RequestParam String projectCode,
            @ApiParam("排除的ID") @RequestParam(required = false) Long excludeId) {
        
        boolean isUnique = projectDeliveryService.validateProjectCode(projectCode, excludeId);
        return Result.success(isUnique);
    }

    @PostMapping("/{id}/start")
    @ApiOperation("启动项目")
    public Result<Void> startProject(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("启动信息") @RequestBody Map<String, Object> startInfo) {
        
        projectDeliveryService.startProject(id, startInfo);
        return Result.success();
    }

    @PostMapping("/{id}/pause")
    @ApiOperation("暂停项目")
    public Result<Void> pauseProject(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("暂停原因") @RequestParam String reason) {
        
        projectDeliveryService.pauseProject(id, reason);
        return Result.success();
    }

    @PostMapping("/{id}/resume")
    @ApiOperation("恢复项目")
    public Result<Void> resumeProject(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("恢复信息") @RequestBody Map<String, Object> resumeInfo) {
        
        projectDeliveryService.resumeProject(id, resumeInfo);
        return Result.success();
    }

    @PostMapping("/{id}/complete")
    @ApiOperation("完成项目")
    public Result<Void> completeProject(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("完成信息") @RequestBody Map<String, Object> completeInfo) {
        
        projectDeliveryService.completeProject(id, completeInfo);
        return Result.success();
    }

    @PostMapping("/{id}/cancel")
    @ApiOperation("取消项目")
    public Result<Void> cancelProject(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("取消原因") @RequestParam String reason) {
        
        projectDeliveryService.cancelProject(id, reason);
        return Result.success();
    }

    @PostMapping("/{id}/archive")
    @ApiOperation("归档项目")
    public Result<Void> archiveProject(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("归档信息") @RequestBody Map<String, Object> archiveInfo) {
        
        projectDeliveryService.archiveProject(id, archiveInfo);
        return Result.success();
    }

    @GetMapping("/{id}/performance")
    @ApiOperation("获取项目绩效分析")
    public Result<Map<String, Object>> getProjectPerformance(
            @ApiParam("项目交付ID") @PathVariable Long id) {
        
        Map<String, Object> performance = projectDeliveryService.getProjectPerformance(id);
        return Result.success(performance);
    }

    @GetMapping("/{id}/lessons")
    @ApiOperation("获取项目经验教训")
    public Result<List<Map<String, Object>>> getProjectLessons(
            @ApiParam("项目交付ID") @PathVariable Long id) {
        
        List<Map<String, Object>> lessons = projectDeliveryService.getProjectLessons(id);
        return Result.success(lessons);
    }

    @PostMapping("/{id}/lessons")
    @ApiOperation("添加项目经验教训")
    public Result<Void> addProjectLesson(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("经验教训") @RequestBody Map<String, Object> lessonInfo) {
        
        projectDeliveryService.addProjectLesson(id, lessonInfo);
        return Result.success();
    }

    @GetMapping("/{id}/satisfaction")
    @ApiOperation("获取客户满意度")
    public Result<Map<String, Object>> getCustomerSatisfaction(
            @ApiParam("项目交付ID") @PathVariable Long id) {
        
        Map<String, Object> satisfaction = projectDeliveryService.getCustomerSatisfaction(id);
        return Result.success(satisfaction);
    }

    @PostMapping("/{id}/satisfaction")
    @ApiOperation("记录客户满意度")
    public Result<Void> recordCustomerSatisfaction(
            @ApiParam("项目交付ID") @PathVariable Long id,
            @ApiParam("满意度信息") @RequestBody Map<String, Object> satisfactionInfo) {
        
        projectDeliveryService.recordCustomerSatisfaction(id, satisfactionInfo);
        return Result.success();
    }
}