{"ast": null, "code": "function isPlainObject(object) {\n  if (typeof object !== 'object') {\n    return false;\n  }\n  if (object == null) {\n    return false;\n  }\n  if (Object.getPrototypeOf(object) === null) {\n    return true;\n  }\n  if (Object.prototype.toString.call(object) !== '[object Object]') {\n    const tag = object[Symbol.toStringTag];\n    if (tag == null) {\n      return false;\n    }\n    const isTagReadonly = !Object.getOwnPropertyDescriptor(object, Symbol.toStringTag)?.writable;\n    if (isTagReadonly) {\n      return false;\n    }\n    return object.toString() === `[object ${tag}]`;\n  }\n  let proto = object;\n  while (Object.getPrototypeOf(proto) !== null) {\n    proto = Object.getPrototypeOf(proto);\n  }\n  return Object.getPrototypeOf(object) === proto;\n}\nexport { isPlainObject };", "map": {"version": 3, "names": ["isPlainObject", "object", "Object", "getPrototypeOf", "prototype", "toString", "call", "tag", "Symbol", "toStringTag", "isTagReadonly", "getOwnPropertyDescriptor", "writable", "proto"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/es-toolkit/dist/compat/predicate/isPlainObject.mjs"], "sourcesContent": ["function isPlainObject(object) {\n    if (typeof object !== 'object') {\n        return false;\n    }\n    if (object == null) {\n        return false;\n    }\n    if (Object.getPrototypeOf(object) === null) {\n        return true;\n    }\n    if (Object.prototype.toString.call(object) !== '[object Object]') {\n        const tag = object[Symbol.toStringTag];\n        if (tag == null) {\n            return false;\n        }\n        const isTagReadonly = !Object.getOwnPropertyDescriptor(object, Symbol.toStringTag)?.writable;\n        if (isTagReadonly) {\n            return false;\n        }\n        return object.toString() === `[object ${tag}]`;\n    }\n    let proto = object;\n    while (Object.getPrototypeOf(proto) !== null) {\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(object) === proto;\n}\n\nexport { isPlainObject };\n"], "mappings": "AAAA,SAASA,aAAaA,CAACC,MAAM,EAAE;EAC3B,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC5B,OAAO,KAAK;EAChB;EACA,IAAIA,MAAM,IAAI,IAAI,EAAE;IAChB,OAAO,KAAK;EAChB;EACA,IAAIC,MAAM,CAACC,cAAc,CAACF,MAAM,CAAC,KAAK,IAAI,EAAE;IACxC,OAAO,IAAI;EACf;EACA,IAAIC,MAAM,CAACE,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,MAAM,CAAC,KAAK,iBAAiB,EAAE;IAC9D,MAAMM,GAAG,GAAGN,MAAM,CAACO,MAAM,CAACC,WAAW,CAAC;IACtC,IAAIF,GAAG,IAAI,IAAI,EAAE;MACb,OAAO,KAAK;IAChB;IACA,MAAMG,aAAa,GAAG,CAACR,MAAM,CAACS,wBAAwB,CAACV,MAAM,EAAEO,MAAM,CAACC,WAAW,CAAC,EAAEG,QAAQ;IAC5F,IAAIF,aAAa,EAAE;MACf,OAAO,KAAK;IAChB;IACA,OAAOT,MAAM,CAACI,QAAQ,CAAC,CAAC,KAAK,WAAWE,GAAG,GAAG;EAClD;EACA,IAAIM,KAAK,GAAGZ,MAAM;EAClB,OAAOC,MAAM,CAACC,cAAc,CAACU,KAAK,CAAC,KAAK,IAAI,EAAE;IAC1CA,KAAK,GAAGX,MAAM,CAACC,cAAc,CAACU,KAAK,CAAC;EACxC;EACA,OAAOX,MAAM,CAACC,cAAc,CAACF,MAAM,CAAC,KAAKY,KAAK;AAClD;AAEA,SAASb,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}