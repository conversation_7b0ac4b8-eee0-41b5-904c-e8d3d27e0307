package com.yinma.service.impl;

import com.yinma.dto.LoginRequest;
import com.yinma.dto.LoginResponse;
import com.yinma.security.UserPrincipal;
import com.yinma.service.AuthService;
import com.yinma.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

/**
 * 认证服务实现类
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {

    private final AuthenticationManager authenticationManager;
    private final UserDetailsService userDetailsService;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;

    @Override
    public LoginResponse login(LoginRequest request) {
        try {
            // 进行身份认证
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            request.getUsername(),
                            request.getPassword()
                    )
            );

            // 获取用户主体
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            
            // 生成令牌
            String accessToken = jwtUtil.generateAccessToken(userPrincipal);
            String refreshToken = jwtUtil.generateRefreshToken(userPrincipal.getUsername());
            
            // 记录登录日志
            recordLoginLog(request.getUsername(), true, request.getIp(), request.getUserAgent());
            
            return LoginResponse.builder()
                    .accessToken(accessToken)
                    .refreshToken(refreshToken)
                    .tokenType("Bearer")
                    .expiresIn(jwtUtil.getAccessTokenExpiration())
                    .userId(userPrincipal.getUserId())
                    .username(userPrincipal.getUsername())
                    .realName(userPrincipal.getRealName())
                    .email(userPrincipal.getEmail())
                    .phone(userPrincipal.getPhone())
                    .avatar(userPrincipal.getAvatar())
                    .authorities(userPrincipal.getAuthoritiesString())
                    .build();
                    
        } catch (AuthenticationException e) {
            log.error("用户 {} 认证失败: {}", request.getUsername(), e.getMessage());
            recordLoginLog(request.getUsername(), false, request.getIp(), request.getUserAgent());
            throw new BadCredentialsException("用户名或密码错误");
        }
    }

    @Override
    public LoginResponse refreshToken(String refreshToken) {
        try {
            // 验证刷新令牌
            if (!jwtUtil.validateToken(refreshToken)) {
                throw new BadCredentialsException("刷新令牌无效或已过期");
            }
            
            // 从刷新令牌中获取用户名
            String username = jwtUtil.getUsernameFromToken(refreshToken);
            
            // 重新加载用户信息
            UserPrincipal userPrincipal = (UserPrincipal) userDetailsService.loadUserByUsername(username);
            
            // 生成新的访问令牌
            String newAccessToken = jwtUtil.generateAccessToken(userPrincipal);
            String newRefreshToken = jwtUtil.refreshToken(refreshToken);
            
            return LoginResponse.builder()
                    .accessToken(newAccessToken)
                    .refreshToken(newRefreshToken)
                    .tokenType("Bearer")
                    .expiresIn(jwtUtil.getAccessTokenExpiration())
                    .userId(userPrincipal.getUserId())
                    .username(userPrincipal.getUsername())
                    .realName(userPrincipal.getRealName())
                    .email(userPrincipal.getEmail())
                    .phone(userPrincipal.getPhone())
                    .avatar(userPrincipal.getAvatar())
                    .authorities(userPrincipal.getAuthoritiesString())
                    .build();
                    
        } catch (Exception e) {
            log.error("刷新令牌失败: {}", e.getMessage());
            throw new BadCredentialsException("刷新令牌失败: " + e.getMessage());
        }
    }

    @Override
    public boolean validateCredentials(String username, String password) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) userDetailsService.loadUserByUsername(username);
            return passwordEncoder.matches(password, userPrincipal.getPassword());
        } catch (Exception e) {
            log.error("验证用户凭证失败: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public LoginResponse generateTokens(String username) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) userDetailsService.loadUserByUsername(username);
            
            String accessToken = jwtUtil.generateAccessToken(userPrincipal);
            String refreshToken = jwtUtil.generateRefreshToken(username);
            
            return LoginResponse.builder()
                    .accessToken(accessToken)
                    .refreshToken(refreshToken)
                    .tokenType("Bearer")
                    .expiresIn(jwtUtil.getAccessTokenExpiration())
                    .userId(userPrincipal.getUserId())
                    .username(userPrincipal.getUsername())
                    .realName(userPrincipal.getRealName())
                    .email(userPrincipal.getEmail())
                    .phone(userPrincipal.getPhone())
                    .avatar(userPrincipal.getAvatar())
                    .authorities(userPrincipal.getAuthoritiesString())
                    .build();
                    
        } catch (Exception e) {
            log.error("生成令牌失败: {}", e.getMessage());
            throw new RuntimeException("生成令牌失败: " + e.getMessage());
        }
    }

    @Override
    public void recordLoginLog(String username, boolean success, String ip, String userAgent) {
        try {
            // TODO: 实现登录日志记录
            log.info("登录日志 - 用户: {}, 成功: {}, IP: {}, UserAgent: {}", username, success, ip, userAgent);
        } catch (Exception e) {
            log.error("记录登录日志失败: {}", e.getMessage());
        }
    }
}