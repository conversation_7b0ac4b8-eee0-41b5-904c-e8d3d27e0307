package com.yinma.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yinma.dto.UserDTO;
import com.yinma.entity.UserEntity;

import java.util.List;

/**
 * 用户信息Service接口
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
public interface UserService extends IService<UserEntity> {

    /**
     * 分页查询用户列表
     * 
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    IPage<UserDTO> selectUserPage(UserDTO.UserQueryDTO queryDTO);

    /**
     * 根据用户ID查询用户详情
     * 
     * @param userId 用户ID
     * @return 用户详情
     */
    UserDTO selectUserById(Long userId);

    /**
     * 根据用户名查询用户
     * 
     * @param username 用户名
     * @return 用户信息
     */
    UserEntity selectUserByUsername(String username);

    /**
     * 根据邮箱查询用户
     * 
     * @param email 邮箱
     * @return 用户信息
     */
    UserEntity selectUserByEmail(String email);

    /**
     * 根据手机号查询用户
     * 
     * @param mobile 手机号
     * @return 用户信息
     */
    UserEntity selectUserByMobile(String mobile);

    /**
     * 创建用户
     * 
     * @param createDTO 创建用户DTO
     * @return 用户ID
     */
    Long createUser(UserDTO.UserCreateDTO createDTO);

    /**
     * 更新用户信息
     * 
     * @param updateDTO 更新用户DTO
     * @return 是否成功
     */
    Boolean updateUser(UserDTO.UserUpdateDTO updateDTO);

    /**
     * 删除用户
     * 
     * @param userId 用户ID
     * @return 是否成功
     */
    Boolean deleteUser(Long userId);

    /**
     * 批量删除用户
     * 
     * @param userIds 用户ID列表
     * @return 是否成功
     */
    Boolean batchDeleteUsers(List<Long> userIds);

    /**
     * 启用用户
     * 
     * @param userId 用户ID
     * @return 是否成功
     */
    Boolean enableUser(Long userId);

    /**
     * 禁用用户
     * 
     * @param userId 用户ID
     * @return 是否成功
     */
    Boolean disableUser(Long userId);

    /**
     * 批量更新用户状态
     * 
     * @param userIds 用户ID列表
     * @param status 状态
     * @return 是否成功
     */
    Boolean batchUpdateUserStatus(List<Long> userIds, Integer status);

    /**
     * 重置用户密码
     * 
     * @param userId 用户ID
     * @param newPassword 新密码
     * @return 是否成功
     */
    Boolean resetPassword(Long userId, String newPassword);

    /**
     * 修改用户密码
     * 
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 是否成功
     */
    Boolean changePassword(Long userId, String oldPassword, String newPassword);

    /**
     * 用户登录
     * 
     * @param loginDTO 登录DTO
     * @return 登录结果
     */
    UserDTO.LoginResultDTO login(UserDTO.LoginDTO loginDTO);

    /**
     * 用户登出
     * 
     * @param userId 用户ID
     * @return 是否成功
     */
    Boolean logout(Long userId);

    /**
     * 刷新Token
     * 
     * @param refreshToken 刷新Token
     * @return 新的Token信息
     */
    UserDTO.LoginResultDTO refreshToken(String refreshToken);

    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @return 是否存在
     */
    Boolean checkUsernameExists(String username);

    /**
     * 检查邮箱是否存在
     * 
     * @param email 邮箱
     * @return 是否存在
     */
    Boolean checkEmailExists(String email);

    /**
     * 检查手机号是否存在
     * 
     * @param mobile 手机号
     * @return 是否存在
     */
    Boolean checkMobileExists(String mobile);

    /**
     * 根据部门ID查询用户列表
     * 
     * @param deptId 部门ID
     * @return 用户列表
     */
    List<UserDTO> selectUsersByDeptId(Long deptId);

    /**
     * 根据角色ID查询用户列表
     * 
     * @param roleId 角色ID
     * @return 用户列表
     */
    List<UserDTO> selectUsersByRoleId(Long roleId);

    /**
     * 查询用户的角色列表
     * 
     * @param userId 用户ID
     * @return 角色列表
     */
    List<String> selectUserRoles(Long userId);

    /**
     * 查询用户的权限列表
     * 
     * @param userId 用户ID
     * @return 权限列表
     */
    List<String> selectUserPermissions(Long userId);

    /**
     * 分配用户角色
     * 
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 是否成功
     */
    Boolean assignUserRoles(Long userId, List<Long> roleIds);

    /**
     * 移除用户角色
     * 
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 是否成功
     */
    Boolean removeUserRoles(Long userId, List<Long> roleIds);

    /**
     * 更新用户最后登录时间
     * 
     * @param userId 用户ID
     * @return 是否成功
     */
    Boolean updateLastLoginTime(Long userId);

    /**
     * 查询用户统计信息
     * 
     * @return 统计信息
     */
    UserDTO.UserStatisticsDTO selectUserStatistics();

    /**
     * 查询在线用户数量
     * 
     * @return 在线用户数量
     */
    Long selectOnlineUserCount();

    /**
     * 查询密码即将过期的用户
     * 
     * @param days 天数
     * @return 用户列表
     */
    List<UserDTO> selectPasswordExpiredUsers(Integer days);

    /**
     * 查询长期未登录的用户
     * 
     * @param days 天数
     * @return 用户列表
     */
    List<UserDTO> selectLongTimeNoLoginUsers(Integer days);

    /**
     * 查询部门用户统计
     * 
     * @return 统计信息
     */
    List<UserDTO.DeptUserStatisticsDTO> selectDeptUserStatistics();

    /**
     * 查询角色用户统计
     * 
     * @return 统计信息
     */
    List<UserDTO.RoleUserStatisticsDTO> selectRoleUserStatistics();

    /**
     * 导出用户数据
     * 
     * @param queryDTO 查询条件
     * @return 用户数据
     */
    List<UserDTO> exportUsers(UserDTO.UserQueryDTO queryDTO);

    /**
     * 导入用户数据
     * 
     * @param users 用户数据
     * @return 导入结果
     */
    UserDTO.ImportResultDTO importUsers(List<UserDTO.UserImportDTO> users);

    /**
     * 检查用户是否拥有指定角色
     * 
     * @param userId 用户ID
     * @param roleCode 角色编码
     * @return 是否拥有
     */
    Boolean checkUserHasRole(Long userId, String roleCode);

    /**
     * 检查用户是否拥有指定权限
     * 
     * @param userId 用户ID
     * @param permissionCode 权限编码
     * @return 是否拥有
     */
    Boolean checkUserHasPermission(Long userId, String permissionCode);

    /**
     * 检查用户是否拥有指定角色列表中的任一角色
     * 
     * @param userId 用户ID
     * @param roleCodes 角色编码列表
     * @return 是否拥有
     */
    Boolean checkUserHasAnyRole(Long userId, List<String> roleCodes);

    /**
     * 检查用户是否拥有指定权限列表中的任一权限
     * 
     * @param userId 用户ID
     * @param permissionCodes 权限编码列表
     * @return 是否拥有
     */
    Boolean checkUserHasAnyPermission(Long userId, List<String> permissionCodes);

    /**
     * 检查用户是否拥有指定角色列表中的所有角色
     * 
     * @param userId 用户ID
     * @param roleCodes 角色编码列表
     * @return 是否拥有
     */
    Boolean checkUserHasAllRoles(Long userId, List<String> roleCodes);

    /**
     * 检查用户是否拥有指定权限列表中的所有权限
     * 
     * @param userId 用户ID
     * @param permissionCodes 权限编码列表
     * @return 是否拥有
     */
    Boolean checkUserHasAllPermissions(Long userId, List<String> permissionCodes);

    /**
     * 获取用户菜单权限
     * 
     * @param userId 用户ID
     * @return 菜单权限列表
     */
    List<UserDTO.MenuPermissionDTO> selectUserMenuPermissions(Long userId);

    /**
     * 获取用户按钮权限
     * 
     * @param userId 用户ID
     * @return 按钮权限列表
     */
    List<String> selectUserButtonPermissions(Long userId);

    /**
     * 获取用户数据权限
     * 
     * @param userId 用户ID
     * @return 数据权限信息
     */
    UserDTO.DataPermissionDTO selectUserDataPermissions(Long userId);
}