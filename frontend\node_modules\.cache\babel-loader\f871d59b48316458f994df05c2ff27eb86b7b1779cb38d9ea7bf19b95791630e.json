{"ast": null, "code": "var _this = this;\n/**\n * 计算不同方向的 poptip 在目标盒子上的位置，配置 POPTIP_STYLE 达到需要的 样式效果\n * @param position PoptipPosition\n * @returns { x: number, y: number }\n */\nexport function getPositionXY(clientX, clientY, target, position, arrowPointAtCenter, follow) {\n  if (arrowPointAtCenter === void 0) {\n    arrowPointAtCenter = false;\n  }\n  if (follow === void 0) {\n    follow = false;\n  }\n  if (follow) return [clientX, clientY];\n  // @ts-ignore\n  var _a = target.getBoundingClientRect(),\n    x = _a.x,\n    y = _a.y,\n    width = _a.width,\n    height = _a.height;\n  switch (position) {\n    case 'top':\n      return arrowPointAtCenter ? [x + width / 2, y] : [clientX, y];\n    case 'left':\n      return arrowPointAtCenter ? [x, y + height / 2] : [x, clientY];\n    case 'bottom':\n      return arrowPointAtCenter ? [x + width / 2, y + height] : [clientX, y + height];\n    case 'right':\n      return arrowPointAtCenter ? [x + width, y + height / 2] : [x + width, clientY];\n    case 'top-right':\n    case 'right-top':\n      return [x + width, y];\n    case 'left-bottom':\n    case 'bottom-left':\n      return [x, y + height];\n    case 'right-bottom':\n    case 'bottom-right':\n      return [x + width, y + height];\n    case 'top-left':\n    case 'left-top':\n    default:\n      return [x, y];\n  }\n}\nvar getSingleTon = function (fn) {\n  var instance;\n  return function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    if (!instance) instance = fn.apply(_this, args);\n    return instance;\n  };\n};\nfunction createElement(id) {\n  var div = id && document.getElementById(id);\n  if (!div) {\n    div = document.createElement('div');\n    div.setAttribute('id', id);\n    document.body.appendChild(div);\n  }\n  return div;\n}\n/**\n * 获取全局唯一的 dom 元素\n */\nexport function getSingleTonElement(id) {\n  var element = getSingleTon(createElement)(id);\n  return element;\n}", "map": {"version": 3, "names": ["_this", "getPositionXY", "clientX", "clientY", "target", "position", "arrowPointAtCenter", "follow", "_a", "getBoundingClientRect", "x", "y", "width", "height", "getSingleTon", "fn", "instance", "args", "_i", "arguments", "length", "apply", "createElement", "id", "div", "document", "getElementById", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "getSingleTonElement", "element"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\component\\src\\ui\\poptip\\utils.ts"], "sourcesContent": ["import { DisplayObject } from '../../shapes';\nimport type { PoptipPosition } from './types';\n\n/**\n * 计算不同方向的 poptip 在目标盒子上的位置，配置 POPTIP_STYLE 达到需要的 样式效果\n * @param position PoptipPosition\n * @returns { x: number, y: number }\n */\nexport function getPositionXY(\n  clientX: number,\n  clientY: number,\n  target: HTMLElement | DisplayObject,\n  position: PoptipPosition,\n  arrowPointAtCenter: boolean = false,\n  follow: boolean = false\n): [number, number] {\n  if (follow) return [clientX, clientY];\n  // @ts-ignore\n  const { x, y, width, height } = target.getBoundingClientRect();\n  switch (position) {\n    case 'top':\n      return arrowPointAtCenter ? [x + width / 2, y] : [clientX, y];\n    case 'left':\n      return arrowPointAtCenter ? [x, y + height / 2] : [x, clientY];\n    case 'bottom':\n      return arrowPointAtCenter ? [x + width / 2, y + height] : [clientX, y + height];\n    case 'right':\n      return arrowPointAtCenter ? [x + width, y + height / 2] : [x + width, clientY];\n    case 'top-right':\n    case 'right-top':\n      return [x + width, y];\n    case 'left-bottom':\n    case 'bottom-left':\n      return [x, y + height];\n    case 'right-bottom':\n    case 'bottom-right':\n      return [x + width, y + height];\n    case 'top-left':\n    case 'left-top':\n    default:\n      return [x, y];\n  }\n}\n\nconst getSingleTon = <T, P>(fn: (...args: P[]) => T) => {\n  let instance: T;\n  return (...args: P[]) => {\n    if (!instance) instance = fn.apply(this, args);\n    return instance;\n  };\n};\n\nfunction createElement(id: string) {\n  let div = id && document.getElementById(id);\n  if (!div) {\n    div = document.createElement('div');\n    div.setAttribute('id', id);\n    document.body.appendChild(div);\n  }\n  return div;\n}\n\n/**\n * 获取全局唯一的 dom 元素\n */\nexport function getSingleTonElement(id: string): HTMLElement {\n  const element = getSingleTon(createElement)(id);\n  return element;\n}\n"], "mappings": "AAAA,IAAAA,KAAA;AAGA;;;;;AAKA,OAAM,SAAUC,aAAaA,CAC3BC,OAAe,EACfC,OAAe,EACfC,MAAmC,EACnCC,QAAwB,EACxBC,kBAAmC,EACnCC,MAAuB;EADvB,IAAAD,kBAAA;IAAAA,kBAAA,QAAmC;EAAA;EACnC,IAAAC,MAAA;IAAAA,MAAA,QAAuB;EAAA;EAEvB,IAAIA,MAAM,EAAE,OAAO,CAACL,OAAO,EAAEC,OAAO,CAAC;EACrC;EACM,IAAAK,EAAA,GAA0BJ,MAAM,CAACK,qBAAqB,EAAE;IAAtDC,CAAC,GAAAF,EAAA,CAAAE,CAAA;IAAEC,CAAC,GAAAH,EAAA,CAAAG,CAAA;IAAEC,KAAK,GAAAJ,EAAA,CAAAI,KAAA;IAAEC,MAAM,GAAAL,EAAA,CAAAK,MAAmC;EAC9D,QAAQR,QAAQ;IACd,KAAK,KAAK;MACR,OAAOC,kBAAkB,GAAG,CAACI,CAAC,GAAGE,KAAK,GAAG,CAAC,EAAED,CAAC,CAAC,GAAG,CAACT,OAAO,EAAES,CAAC,CAAC;IAC/D,KAAK,MAAM;MACT,OAAOL,kBAAkB,GAAG,CAACI,CAAC,EAAEC,CAAC,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAG,CAACH,CAAC,EAAEP,OAAO,CAAC;IAChE,KAAK,QAAQ;MACX,OAAOG,kBAAkB,GAAG,CAACI,CAAC,GAAGE,KAAK,GAAG,CAAC,EAAED,CAAC,GAAGE,MAAM,CAAC,GAAG,CAACX,OAAO,EAAES,CAAC,GAAGE,MAAM,CAAC;IACjF,KAAK,OAAO;MACV,OAAOP,kBAAkB,GAAG,CAACI,CAAC,GAAGE,KAAK,EAAED,CAAC,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAG,CAACH,CAAC,GAAGE,KAAK,EAAET,OAAO,CAAC;IAChF,KAAK,WAAW;IAChB,KAAK,WAAW;MACd,OAAO,CAACO,CAAC,GAAGE,KAAK,EAAED,CAAC,CAAC;IACvB,KAAK,aAAa;IAClB,KAAK,aAAa;MAChB,OAAO,CAACD,CAAC,EAAEC,CAAC,GAAGE,MAAM,CAAC;IACxB,KAAK,cAAc;IACnB,KAAK,cAAc;MACjB,OAAO,CAACH,CAAC,GAAGE,KAAK,EAAED,CAAC,GAAGE,MAAM,CAAC;IAChC,KAAK,UAAU;IACf,KAAK,UAAU;IACf;MACE,OAAO,CAACH,CAAC,EAAEC,CAAC,CAAC;EACjB;AACF;AAEA,IAAMG,YAAY,GAAG,SAAAA,CAAOC,EAAuB;EACjD,IAAIC,QAAW;EACf,OAAO;IAAC,IAAAC,IAAA;SAAA,IAAAC,EAAA,IAAY,EAAZA,EAAA,GAAAC,SAAA,CAAAC,MAAY,EAAZF,EAAA,EAAY;MAAZD,IAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;IACN,IAAI,CAACF,QAAQ,EAAEA,QAAQ,GAAGD,EAAE,CAACM,KAAK,CAACrB,KAAI,EAAEiB,IAAI,CAAC;IAC9C,OAAOD,QAAQ;EACjB,CAAC;AACH,CAAC;AAED,SAASM,aAAaA,CAACC,EAAU;EAC/B,IAAIC,GAAG,GAAGD,EAAE,IAAIE,QAAQ,CAACC,cAAc,CAACH,EAAE,CAAC;EAC3C,IAAI,CAACC,GAAG,EAAE;IACRA,GAAG,GAAGC,QAAQ,CAACH,aAAa,CAAC,KAAK,CAAC;IACnCE,GAAG,CAACG,YAAY,CAAC,IAAI,EAAEJ,EAAE,CAAC;IAC1BE,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,GAAG,CAAC;EAChC;EACA,OAAOA,GAAG;AACZ;AAEA;;;AAGA,OAAM,SAAUM,mBAAmBA,CAACP,EAAU;EAC5C,IAAMQ,OAAO,GAAGjB,YAAY,CAACQ,aAAa,CAAC,CAACC,EAAE,CAAC;EAC/C,OAAOQ,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}