@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM 西安银马实业数字化管理系统
REM Windows数据库部署脚本
REM 支持PostgreSQL数据库的创建、初始化、迁移和备份

REM 设置默认配置
set "DEFAULT_DB_HOST=localhost"
set "DEFAULT_DB_PORT=5432"
set "DEFAULT_DB_NAME=yinma_management"
set "DEFAULT_DB_USER=postgres"
set "DEFAULT_ADMIN_DB=postgres"

REM 从环境变量或使用默认值
if not defined DB_HOST set "DB_HOST=%DEFAULT_DB_HOST%"
if not defined DB_PORT set "DB_PORT=%DEFAULT_DB_PORT%"
if not defined DB_NAME set "DB_NAME=%DEFAULT_DB_NAME%"
if not defined DB_USER set "DB_USER=%DEFAULT_DB_USER%"
if not defined ADMIN_DB set "ADMIN_DB=%DEFAULT_ADMIN_DB%"

REM 脚本目录
set "SCRIPT_DIR=%~dp0"
set "MIGRATION_DIR=%SCRIPT_DIR%migration"
set "BACKUP_DIR=%SCRIPT_DIR%backup"

REM 创建备份目录
if not exist "%BACKUP_DIR%" mkdir "%BACKUP_DIR%"

REM 颜色定义（Windows 10+）
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

REM 日志函数
:log_info
echo %BLUE%[INFO]%NC% %~1
goto :eof

:log_success
echo %GREEN%[SUCCESS]%NC% %~1
goto :eof

:log_warning
echo %YELLOW%[WARNING]%NC% %~1
goto :eof

:log_error
echo %RED%[ERROR]%NC% %~1
goto :eof

REM 显示帮助信息
:show_help
echo.
echo 西安银马实业数字化管理系统 - Windows数据库部署脚本
echo.
echo 用法: %~nx0 [选项] ^<命令^>
echo.
echo 命令:
echo   init        初始化数据库（创建数据库和用户）
echo   migrate     执行数据库迁移
echo   seed        插入初始化数据
echo   backup      备份数据库
echo   restore     恢复数据库
echo   reset       重置数据库（删除并重新创建）
echo   status      检查数据库状态
echo   help        显示此帮助信息
echo.
echo 选项:
echo   --host      数据库主机 (默认: %DEFAULT_DB_HOST%)
echo   --port      数据库端口 (默认: %DEFAULT_DB_PORT%)
echo   --database  数据库名称 (默认: %DEFAULT_DB_NAME%)
echo   --user      数据库用户 (默认: %DEFAULT_DB_USER%)
echo   --password  数据库密码 (提示输入)
echo   --admin-db  管理员数据库 (默认: %DEFAULT_ADMIN_DB%)
echo   --force     强制执行（跳过确认）
echo   --verbose   详细输出
echo   --help      显示帮助信息
echo.
echo 环境变量:
echo   DB_HOST         数据库主机
echo   DB_PORT         数据库端口
echo   DB_NAME         数据库名称
echo   DB_USER         数据库用户
echo   DB_PASSWORD     数据库密码
echo   ADMIN_DB        管理员数据库
echo.
echo 示例:
echo   %~nx0 init                           # 初始化数据库
echo   %~nx0 migrate                        # 执行迁移
echo   %~nx0 backup                         # 备份数据库
echo   %~nx0 --host ************* --database yinma init # 指定主机和数据库名初始化
echo   %~nx0 --force reset                  # 强制重置数据库
echo.
goto :eof

REM 检查PostgreSQL客户端工具
:check_postgresql_tools
call :log_info "检查PostgreSQL客户端工具..."

psql --version >nul 2>&1
if errorlevel 1 (
    call :log_error "psql 命令未找到，请安装PostgreSQL客户端工具"
    exit /b 1
)

pg_dump --version >nul 2>&1
if errorlevel 1 (
    call :log_error "pg_dump 命令未找到，请安装PostgreSQL客户端工具"
    exit /b 1
)

call :log_success "PostgreSQL客户端工具检查通过"
goto :eof

REM 检查数据库连接
:check_database_connection
call :log_info "检查数据库连接..."

set "PGPASSWORD=%DB_PASSWORD%"
psql -h "%DB_HOST%" -p "%DB_PORT%" -U "%DB_USER%" -d "%ADMIN_DB%" -c "SELECT 1;" >nul 2>&1
if errorlevel 1 (
    call :log_error "无法连接到数据库服务器"
    exit /b 1
) else (
    call :log_success "数据库连接成功"
)
goto :eof

REM 检查数据库是否存在
:check_database_exists
set "db_name=%~1"
set "PGPASSWORD=%DB_PASSWORD%"
for /f "usebackq tokens=*" %%i in (`psql -h "%DB_HOST%" -p "%DB_PORT%" -U "%DB_USER%" -d "%ADMIN_DB%" -tAc "SELECT 1 FROM pg_database WHERE datname='%db_name%';" 2^>nul`) do (
    if "%%i"=="1" exit /b 0
)
exit /b 1

REM 创建数据库
:create_database
call :log_info "创建数据库 '%DB_NAME%'..."

call :check_database_exists "%DB_NAME%"
if not errorlevel 1 (
    call :log_warning "数据库 '%DB_NAME%' 已存在"
    goto :eof
)

set "PGPASSWORD=%DB_PASSWORD%"
psql -h "%DB_HOST%" -p "%DB_PORT%" -U "%DB_USER%" -d "%ADMIN_DB%" -c "CREATE DATABASE \"%DB_NAME%\" WITH ENCODING='UTF8';"
if errorlevel 1 (
    call :log_error "数据库 '%DB_NAME%' 创建失败"
    exit /b 1
) else (
    call :log_success "数据库 '%DB_NAME%' 创建成功"
)
goto :eof

REM 删除数据库
:drop_database
call :log_info "删除数据库 '%DB_NAME%'..."

call :check_database_exists "%DB_NAME%"
if errorlevel 1 (
    call :log_warning "数据库 '%DB_NAME%' 不存在"
    goto :eof
)

REM 终止所有连接
set "PGPASSWORD=%DB_PASSWORD%"
psql -h "%DB_HOST%" -p "%DB_PORT%" -U "%DB_USER%" -d "%ADMIN_DB%" -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = '%DB_NAME%';" >nul 2>&1

REM 删除数据库
psql -h "%DB_HOST%" -p "%DB_PORT%" -U "%DB_USER%" -d "%ADMIN_DB%" -c "DROP DATABASE IF EXISTS \"%DB_NAME%\";"
if errorlevel 1 (
    call :log_error "数据库 '%DB_NAME%' 删除失败"
    exit /b 1
) else (
    call :log_success "数据库 '%DB_NAME%' 删除成功"
)
goto :eof

REM 执行SQL文件
:execute_sql_file
set "sql_file=%~1"
set "description=%~2"

if not exist "%sql_file%" (
    call :log_error "SQL文件不存在: %sql_file%"
    exit /b 1
)

call :log_info "执行 %description%: %~nx1"

set "PGPASSWORD=%DB_PASSWORD%"
psql -h "%DB_HOST%" -p "%DB_PORT%" -U "%DB_USER%" -d "%DB_NAME%" -f "%sql_file%"
if errorlevel 1 (
    call :log_error "%description% 执行失败"
    exit /b 1
) else (
    call :log_success "%description% 执行成功"
)
goto :eof

REM 初始化数据库
:init_database
call :log_info "开始初始化数据库..."

call :check_postgresql_tools
if errorlevel 1 exit /b 1

call :check_database_connection
if errorlevel 1 exit /b 1

call :create_database
if errorlevel 1 exit /b 1

call :log_success "数据库初始化完成"
goto :eof

REM 执行数据库迁移
:migrate_database
call :log_info "开始执行数据库迁移..."

if not exist "%MIGRATION_DIR%" (
    call :log_error "迁移目录不存在: %MIGRATION_DIR%"
    exit /b 1
)

REM 按文件名排序执行迁移文件
for /f "tokens=*" %%f in ('dir /b /on "%MIGRATION_DIR%\V*.sql" 2^>nul') do (
    call :execute_sql_file "%MIGRATION_DIR%\%%f" "数据库迁移"
    if errorlevel 1 exit /b 1
)

call :log_success "数据库迁移完成"
goto :eof

REM 插入初始化数据
:seed_database
call :log_info "开始插入初始化数据..."

if exist "%SCRIPT_DIR%init.sql" (
    call :execute_sql_file "%SCRIPT_DIR%init.sql" "初始化数据"
    if errorlevel 1 exit /b 1
) else (
    call :log_warning "初始化数据文件不存在: %SCRIPT_DIR%init.sql"
)

call :log_success "初始化数据插入完成"
goto :eof

REM 备份数据库
:backup_database
for /f "tokens=2 delims==" %%i in ('wmic OS Get localdatetime /value') do set datetime=%%i
set "backup_file=%BACKUP_DIR%\yinma_backup_%datetime:~0,8%_%datetime:~8,6%.sql"

call :log_info "开始备份数据库到: %backup_file%"

set "PGPASSWORD=%DB_PASSWORD%"
pg_dump -h "%DB_HOST%" -p "%DB_PORT%" -U "%DB_USER%" -d "%DB_NAME%" --verbose --clean --no-owner --no-privileges > "%backup_file%"
if errorlevel 1 (
    call :log_error "数据库备份失败"
    exit /b 1
) else (
    call :log_success "数据库备份完成: %backup_file%"
)

REM 清理旧备份（保留最近30个）
set count=0
for /f "tokens=*" %%f in ('dir /b /o-d "%BACKUP_DIR%\yinma_backup_*.sql" 2^>nul') do (
    set /a count+=1
    if !count! gtr 30 (
        del "%BACKUP_DIR%\%%f" >nul 2>&1
    )
)
if !count! gtr 30 call :log_info "已清理旧备份文件"

goto :eof

REM 恢复数据库
:restore_database
set "backup_file=%~1"

if "%backup_file%"=="" (
    call :log_error "请指定备份文件路径"
    exit /b 1
)

if not exist "%backup_file%" (
    call :log_error "备份文件不存在: %backup_file%"
    exit /b 1
)

call :log_info "开始从备份文件恢复数据库: %backup_file%"

set "PGPASSWORD=%DB_PASSWORD%"
psql -h "%DB_HOST%" -p "%DB_PORT%" -U "%DB_USER%" -d "%DB_NAME%" -f "%backup_file%"
if errorlevel 1 (
    call :log_error "数据库恢复失败"
    exit /b 1
) else (
    call :log_success "数据库恢复完成"
)
goto :eof

REM 重置数据库
:reset_database
call :log_warning "即将重置数据库 '%DB_NAME%'，所有数据将被删除！"

if not "%FORCE%"=="true" (
    set /p "confirm=确认继续？(y/N): "
    if not "!confirm!"=="y" if not "!confirm!"=="Y" (
        call :log_info "操作已取消"
        exit /b 0
    )
)

call :drop_database
if errorlevel 1 exit /b 1

call :init_database
if errorlevel 1 exit /b 1

call :migrate_database
if errorlevel 1 exit /b 1

call :seed_database
if errorlevel 1 exit /b 1

call :log_success "数据库重置完成"
goto :eof

REM 检查数据库状态
:check_database_status
call :log_info "检查数据库状态..."

call :check_postgresql_tools
if errorlevel 1 exit /b 1

call :check_database_connection
if errorlevel 1 exit /b 1

call :check_database_exists "%DB_NAME%"
if not errorlevel 1 (
    call :log_success "数据库 '%DB_NAME%' 存在"
    
    REM 检查表数量
    set "PGPASSWORD=%DB_PASSWORD%"
    for /f "usebackq tokens=*" %%i in (`psql -h "%DB_HOST%" -p "%DB_PORT%" -U "%DB_USER%" -d "%DB_NAME%" -tAc "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" 2^>nul`) do (
        call :log_info "数据库中共有 %%i 个表"
        set "table_count=%%i"
    )
    
    REM 检查主要表数据量
    if !table_count! gtr 0 (
        call :log_info "主要表数据量:"
        for %%t in (sys_user material bom bom_detail bom_change_log) do (
            for /f "usebackq tokens=*" %%c in (`psql -h "%DB_HOST%" -p "%DB_PORT%" -U "%DB_USER%" -d "%DB_NAME%" -tAc "SELECT COUNT(*) FROM %%t WHERE deleted = 0;" 2^>nul`) do (
                call :log_info "  %%t: %%c 条记录"
            )
        )
    )
) else (
    call :log_warning "数据库 '%DB_NAME%' 不存在"
)
goto :eof

REM 解析命令行参数
set "FORCE=false"
set "VERBOSE=false"
set "COMMAND=help"

:parse_args
if "%~1"=="" goto :args_done

if "%~1"=="--host" (
    set "DB_HOST=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="--port" (
    set "DB_PORT=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="--database" (
    set "DB_NAME=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="--user" (
    set "DB_USER=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="--password" (
    set /p "DB_PASSWORD=请输入数据库密码: "
    shift
    goto :parse_args
)
if "%~1"=="--admin-db" (
    set "ADMIN_DB=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="--force" (
    set "FORCE=true"
    shift
    goto :parse_args
)
if "%~1"=="--verbose" (
    set "VERBOSE=true"
    shift
    goto :parse_args
)
if "%~1"=="--help" (
    call :show_help
    exit /b 0
)
if "%~1"=="help" (
    set "COMMAND=help"
    shift
    goto :parse_args
)
if "%~1"=="init" (
    set "COMMAND=init"
    shift
    goto :parse_args
)
if "%~1"=="migrate" (
    set "COMMAND=migrate"
    shift
    goto :parse_args
)
if "%~1"=="seed" (
    set "COMMAND=seed"
    shift
    goto :parse_args
)
if "%~1"=="backup" (
    set "COMMAND=backup"
    shift
    goto :parse_args
)
if "%~1"=="restore" (
    set "COMMAND=restore"
    set "RESTORE_FILE=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="reset" (
    set "COMMAND=reset"
    shift
    goto :parse_args
)
if "%~1"=="status" (
    set "COMMAND=status"
    shift
    goto :parse_args
)

REM 未知参数
call :log_error "未知参数: %~1"
call :show_help
exit /b 1

:args_done

REM 如果没有提供密码，尝试从环境变量获取
if not defined DB_PASSWORD (
    if defined PGPASSWORD (
        set "DB_PASSWORD=%PGPASSWORD%"
    ) else (
        set /p "DB_PASSWORD=请输入数据库密码: "
    )
)

REM 显示配置信息
if "%VERBOSE%"=="true" (
    call :log_info "配置信息:"
    call :log_info "  数据库主机: %DB_HOST%"
    call :log_info "  数据库端口: %DB_PORT%"
    call :log_info "  数据库名称: %DB_NAME%"
    call :log_info "  数据库用户: %DB_USER%"
    call :log_info "  管理员数据库: %ADMIN_DB%"
    call :log_info "  脚本目录: %SCRIPT_DIR%"
    call :log_info "  迁移目录: %MIGRATION_DIR%"
    call :log_info "  备份目录: %BACKUP_DIR%"
)

REM 执行命令
if "%COMMAND%"=="init" (
    call :init_database
) else if "%COMMAND%"=="migrate" (
    call :migrate_database
) else if "%COMMAND%"=="seed" (
    call :seed_database
) else if "%COMMAND%"=="backup" (
    call :backup_database
) else if "%COMMAND%"=="restore" (
    call :restore_database "%RESTORE_FILE%"
) else if "%COMMAND%"=="reset" (
    call :reset_database
) else if "%COMMAND%"=="status" (
    call :check_database_status
) else if "%COMMAND%"=="help" (
    call :show_help
) else (
    call :log_error "未知命令: %COMMAND%"
    call :show_help
    exit /b 1
)

if errorlevel 1 (
    call :log_error "操作失败！"
    exit /b 1
) else (
    call :log_success "操作完成！"
)

endlocal