package com.yinma.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinma.entity.BomChangeLogEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * BOM变更日志Mapper接口
 * 提供BOM变更日志相关的数据库操作
 * 
 * <AUTHOR> System
 * @since 2024-01-15
 */
@Repository
@Mapper
public interface BomChangeLogMapper extends BaseMapper<BomChangeLogEntity> {

    /**
     * 分页查询BOM变更日志
     * 
     * @param page 分页参数
     * @param bomId BOM主表ID
     * @param changeType 变更类型
     * @param changeOperation 变更操作
     * @param changeObjectType 变更对象类型
     * @param approvalStatus 审批状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param changeBy 变更人
     * @return 分页结果
     */
    @Select({
        "<script>",
        "SELECT bcl.*, ",
        "       b.bom_code, b.bom_name, b.bom_type, b.product_code,",
        "       m.material_name as product_name,",
        "       u1.real_name as change_by_name,",
        "       u2.real_name as approve_by_name",
        "FROM bom_change_log bcl",
        "LEFT JOIN bom_master b ON bcl.bom_id = b.id AND b.deleted = 0",
        "LEFT JOIN material_master m ON b.product_code = m.material_code AND m.deleted = 0",
        "LEFT JOIN sys_user u1 ON bcl.change_by = u1.user_code AND u1.deleted = 0",
        "LEFT JOIN sys_user u2 ON bcl.approve_by = u2.user_code AND u2.deleted = 0",
        "WHERE bcl.deleted = 0",
        "<if test='bomId != null'>",
        "  AND bcl.bom_id = #{bomId}",
        "</if>",
        "<if test='changeType != null and changeType != ""'>",
        "  AND bcl.change_type = #{changeType}",
        "</if>",
        "<if test='changeOperation != null and changeOperation != ""'>",
        "  AND bcl.change_operation = #{changeOperation}",
        "</if>",
        "<if test='changeObjectType != null and changeObjectType != ""'>",
        "  AND bcl.change_object_type = #{changeObjectType}",
        "</if>",
        "<if test='approvalStatus != null and approvalStatus != ""'>",
        "  AND bcl.approval_status = #{approvalStatus}",
        "</if>",
        "<if test='startDate != null'>",
        "  AND bcl.change_time >= #{startDate}",
        "</if>",
        "<if test='endDate != null'>",
        "  AND bcl.change_time <= #{endDate}",
        "</if>",
        "<if test='changeBy != null and changeBy != ""'>",
        "  AND bcl.change_by = #{changeBy}",
        "</if>",
        "ORDER BY bcl.change_time DESC, bcl.id DESC",
        "</script>"
    })
    IPage<BomChangeLogEntity> selectChangeLogPage(Page<BomChangeLogEntity> page,
                                                  @Param("bomId") Long bomId,
                                                  @Param("changeType") String changeType,
                                                  @Param("changeOperation") String changeOperation,
                                                  @Param("changeObjectType") String changeObjectType,
                                                  @Param("approvalStatus") String approvalStatus,
                                                  @Param("startDate") LocalDateTime startDate,
                                                  @Param("endDate") LocalDateTime endDate,
                                                  @Param("changeBy") String changeBy);

    /**
     * 查询BOM变更历史
     * 
     * @param bomId BOM主表ID
     * @param limit 限制数量
     * @return 变更历史列表
     */
    @Select({
        "SELECT bcl.*, ",
        "       b.bom_code, b.bom_name, b.bom_type,",
        "       u.real_name as change_by_name",
        "FROM bom_change_log bcl",
        "LEFT JOIN bom_master b ON bcl.bom_id = b.id AND b.deleted = 0",
        "LEFT JOIN sys_user u ON bcl.change_by = u.user_code AND u.deleted = 0",
        "WHERE bcl.bom_id = #{bomId} AND bcl.deleted = 0",
        "ORDER BY bcl.change_time DESC, bcl.id DESC",
        "<if test='limit != null and limit > 0'>",
        "  LIMIT #{limit}",
        "</if>"
    })
    List<BomChangeLogEntity> selectBomChangeHistory(@Param("bomId") Long bomId, @Param("limit") Integer limit);

    /**
     * 查询变更影响的BOM列表
     * 
     * @param changeLogId 变更日志ID
     * @return 影响的BOM列表
     */
    @Select({
        "SELECT DISTINCT b.*, m.material_name as product_name",
        "FROM bom_change_impact bci",
        "INNER JOIN bom_master b ON bci.affected_bom_id = b.id AND b.deleted = 0",
        "LEFT JOIN material_master m ON b.product_code = m.material_code AND m.deleted = 0",
        "WHERE bci.change_log_id = #{changeLogId} AND bci.deleted = 0",
        "ORDER BY bci.impact_level DESC, b.bom_code"
    })
    List<Map<String, Object>> selectAffectedBoms(@Param("changeLogId") Long changeLogId);

    /**
     * 查询变更详情
     * 
     * @param changeLogId 变更日志ID
     * @return 变更详情
     */
    @Select({
        "SELECT bcd.*",
        "FROM bom_change_detail bcd",
        "WHERE bcd.change_log_id = #{changeLogId} AND bcd.deleted = 0",
        "ORDER BY bcd.change_sequence"
    })
    List<Map<String, Object>> selectChangeDetails(@Param("changeLogId") Long changeLogId);

    /**
     * 查询审批记录
     * 
     * @param changeLogId 变更日志ID
     * @return 审批记录列表
     */
    @Select({
        "SELECT bar.*, u.real_name as approver_name",
        "FROM bom_approval_record bar",
        "LEFT JOIN sys_user u ON bar.approver = u.user_code AND u.deleted = 0",
        "WHERE bar.change_log_id = #{changeLogId} AND bar.deleted = 0",
        "ORDER BY bar.approval_time ASC"
    })
    List<Map<String, Object>> selectApprovalRecords(@Param("changeLogId") Long changeLogId);

    /**
     * 查询变更附件
     * 
     * @param changeLogId 变更日志ID
     * @return 附件列表
     */
    @Select({
        "SELECT bca.*",
        "FROM bom_change_attachment bca",
        "WHERE bca.change_log_id = #{changeLogId} AND bca.deleted = 0",
        "ORDER BY bca.upload_time DESC"
    })
    List<Map<String, Object>> selectChangeAttachments(@Param("changeLogId") Long changeLogId);

    /**
     * 查询物料变更历史
     * 
     * @param materialCode 物料编码
     * @param limit 限制数量
     * @return 变更历史列表
     */
    @Select({
        "SELECT bcl.*, ",
        "       b.bom_code, b.bom_name, b.bom_type,",
        "       u.real_name as change_by_name",
        "FROM bom_change_log bcl",
        "LEFT JOIN bom_master b ON bcl.bom_id = b.id AND b.deleted = 0",
        "LEFT JOIN sys_user u ON bcl.change_by = u.user_code AND u.deleted = 0",
        "WHERE bcl.deleted = 0",
        "  AND (bcl.change_content LIKE CONCAT('%', #{materialCode}, '%')",
        "       OR EXISTS (",
        "         SELECT 1 FROM bom_change_detail bcd",
        "         WHERE bcd.change_log_id = bcl.id ",
        "           AND bcd.material_code = #{materialCode}",
        "           AND bcd.deleted = 0",
        "       ))",
        "ORDER BY bcl.change_time DESC, bcl.id DESC",
        "<if test='limit != null and limit > 0'>",
        "  LIMIT #{limit}",
        "</if>"
    })
    List<BomChangeLogEntity> selectMaterialChangeHistory(@Param("materialCode") String materialCode, @Param("limit") Integer limit);

    /**
     * 查询变更统计信息
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计信息
     */
    @Select({
        "SELECT ",
        "  change_type,",
        "  change_operation,",
        "  COUNT(*) as change_count,",
        "  SUM(CASE WHEN approval_status = 'APPROVED' THEN 1 ELSE 0 END) as approved_count,",
        "  SUM(CASE WHEN approval_status = 'REJECTED' THEN 1 ELSE 0 END) as rejected_count,",
        "  SUM(CASE WHEN approval_status = 'PENDING' THEN 1 ELSE 0 END) as pending_count",
        "FROM bom_change_log ",
        "WHERE deleted = 0",
        "<if test='startDate != null'>",
        "  AND change_time >= #{startDate}",
        "</if>",
        "<if test='endDate != null'>",
        "  AND change_time <= #{endDate}",
        "</if>",
        "GROUP BY change_type, change_operation",
        "ORDER BY change_count DESC"
    })
    List<Map<String, Object>> selectChangeStatistics(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * 查询待审批的变更
     * 
     * @param approver 审批人
     * @return 待审批变更列表
     */
    @Select({
        "SELECT bcl.*, ",
        "       b.bom_code, b.bom_name, b.bom_type, b.product_code,",
        "       m.material_name as product_name,",
        "       u.real_name as change_by_name",
        "FROM bom_change_log bcl",
        "LEFT JOIN bom_master b ON bcl.bom_id = b.id AND b.deleted = 0",
        "LEFT JOIN material_master m ON b.product_code = m.material_code AND m.deleted = 0",
        "LEFT JOIN sys_user u ON bcl.change_by = u.user_code AND u.deleted = 0",
        "WHERE bcl.approval_status = 'PENDING' AND bcl.deleted = 0",
        "  AND (bcl.approve_by = #{approver} ",
        "       OR EXISTS (",
        "         SELECT 1 FROM bom_approval_flow baf",
        "         WHERE baf.change_log_id = bcl.id ",
        "           AND baf.approver = #{approver}",
        "           AND baf.approval_status = 'PENDING'",
        "           AND baf.deleted = 0",
        "       ))",
        "ORDER BY bcl.change_time ASC"
    })
    List<BomChangeLogEntity> selectPendingApprovals(@Param("approver") String approver);

    /**
     * 查询变更趋势数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param groupBy 分组方式（DAY/WEEK/MONTH）
     * @return 趋势数据
     */
    @Select({
        "<script>",
        "SELECT ",
        "<choose>",
        "  <when test='groupBy == "DAY"'>",
        "    DATE(change_time) as time_period,",
        "  </when>",
        "  <when test='groupBy == "WEEK"'>",
        "    DATE_FORMAT(change_time, '%Y-%u') as time_period,",
        "  </when>",
        "  <otherwise>",
        "    DATE_FORMAT(change_time, '%Y-%m') as time_period,",
        "  </otherwise>",
        "</choose>",
        "  COUNT(*) as total_changes,",
        "  SUM(CASE WHEN change_type = 'STRUCTURE' THEN 1 ELSE 0 END) as structure_changes,",
        "  SUM(CASE WHEN change_type = 'MATERIAL' THEN 1 ELSE 0 END) as material_changes,",
        "  SUM(CASE WHEN change_type = 'QUANTITY' THEN 1 ELSE 0 END) as quantity_changes,",
        "  SUM(CASE WHEN change_type = 'PROCESS' THEN 1 ELSE 0 END) as process_changes,",
        "  SUM(CASE WHEN approval_status = 'APPROVED' THEN 1 ELSE 0 END) as approved_changes",
        "FROM bom_change_log ",
        "WHERE deleted = 0",
        "<if test='startDate != null'>",
        "  AND change_time >= #{startDate}",
        "</if>",
        "<if test='endDate != null'>",
        "  AND change_time <= #{endDate}",
        "</if>",
        "GROUP BY time_period",
        "ORDER BY time_period ASC",
        "</script>"
    })
    List<Map<String, Object>> selectChangeTrend(@Param("startDate") LocalDateTime startDate, 
                                               @Param("endDate") LocalDateTime endDate,
                                               @Param("groupBy") String groupBy);

    /**
     * 查询变更影响分析
     * 
     * @param changeLogId 变更日志ID
     * @return 影响分析结果
     */
    @Select({
        "SELECT ",
        "  bci.impact_level,",
        "  COUNT(*) as affected_count,",
        "  GROUP_CONCAT(DISTINCT b.bom_code ORDER BY b.bom_code) as affected_boms",
        "FROM bom_change_impact bci",
        "INNER JOIN bom_master b ON bci.affected_bom_id = b.id AND b.deleted = 0",
        "WHERE bci.change_log_id = #{changeLogId} AND bci.deleted = 0",
        "GROUP BY bci.impact_level",
        "ORDER BY ",
        "  CASE bci.impact_level ",
        "    WHEN 'HIGH' THEN 1",
        "    WHEN 'MEDIUM' THEN 2",
        "    WHEN 'LOW' THEN 3",
        "    ELSE 4",
        "  END"
    })
    List<Map<String, Object>> selectChangeImpactAnalysis(@Param("changeLogId") Long changeLogId);

    /**
     * 批量插入变更影响记录
     * 
     * @param impacts 影响记录列表
     * @return 插入数量
     */
    @Insert({
        "<script>",
        "INSERT INTO bom_change_impact (",
        "  change_log_id, affected_bom_id, impact_level, impact_description,",
        "  create_by, create_time",
        ") VALUES ",
        "<foreach collection='impacts' item='impact' separator=','>",
        "  (",
        "    #{impact.changeLogId}, #{impact.affectedBomId}, #{impact.impactLevel},",
        "    #{impact.impactDescription}, #{impact.createBy}, #{impact.createTime}",
        "  )",
        "</foreach>",
        "</script>"
    })
    int batchInsertChangeImpacts(@Param("impacts") List<Map<String, Object>> impacts);

    /**
     * 批量插入变更详情
     * 
     * @param details 变更详情列表
     * @return 插入数量
     */
    @Insert({
        "<script>",
        "INSERT INTO bom_change_detail (",
        "  change_log_id, change_sequence, material_code, field_name, old_value, new_value,",
        "  change_reason, create_by, create_time",
        ") VALUES ",
        "<foreach collection='details' item='detail' separator=','>",
        "  (",
        "    #{detail.changeLogId}, #{detail.changeSequence}, #{detail.materialCode},",
        "    #{detail.fieldName}, #{detail.oldValue}, #{detail.newValue},",
        "    #{detail.changeReason}, #{detail.createBy}, #{detail.createTime}",
        "  )",
        "</foreach>",
        "</script>"
    })
    int batchInsertChangeDetails(@Param("details") List<Map<String, Object>> details);

    /**
     * 更新变更审批状态
     * 
     * @param changeLogId 变更日志ID
     * @param approvalStatus 审批状态
     * @param approveBy 审批人
     * @param approveTime 审批时间
     * @param approveComment 审批意见
     * @return 更新数量
     */
    @Update({
        "UPDATE bom_change_log SET ",
        "approval_status = #{approvalStatus},",
        "approve_by = #{approveBy},",
        "approve_time = #{approveTime},",
        "approve_comment = #{approveComment},",
        "update_by = #{approveBy},",
        "update_time = #{approveTime}",
        "WHERE id = #{changeLogId} AND deleted = 0"
    })
    int updateApprovalStatus(@Param("changeLogId") Long changeLogId,
                           @Param("approvalStatus") String approvalStatus,
                           @Param("approveBy") String approveBy,
                           @Param("approveTime") LocalDateTime approveTime,
                           @Param("approveComment") String approveComment);

    /**
     * 查询变更日志详细信息
     * 
     * @param changeLogId 变更日志ID
     * @return 详细信息
     */
    @Select({
        "SELECT bcl.*, ",
        "       b.bom_code, b.bom_name, b.bom_type, b.product_code,",
        "       m.material_name as product_name,",
        "       u1.real_name as change_by_name,",
        "       u2.real_name as approve_by_name",
        "FROM bom_change_log bcl",
        "LEFT JOIN bom_master b ON bcl.bom_id = b.id AND b.deleted = 0",
        "LEFT JOIN material_master m ON b.product_code = m.material_code AND m.deleted = 0",
        "LEFT JOIN sys_user u1 ON bcl.change_by = u1.user_code AND u1.deleted = 0",
        "LEFT JOIN sys_user u2 ON bcl.approve_by = u2.user_code AND u2.deleted = 0",
        "WHERE bcl.id = #{changeLogId} AND bcl.deleted = 0"
    })
    BomChangeLogEntity selectChangeLogDetail(@Param("changeLogId") Long changeLogId);

    /**
     * 删除变更相关数据
     * 
     * @param changeLogId 变更日志ID
     * @param updateBy 更新人
     * @param updateTime 更新时间
     * @return 删除数量
     */
    @Update({
        "UPDATE bom_change_log SET deleted = 1, update_by = #{updateBy}, update_time = #{updateTime} WHERE id = #{changeLogId};",
        "UPDATE bom_change_detail SET deleted = 1, update_by = #{updateBy}, update_time = #{updateTime} WHERE change_log_id = #{changeLogId};",
        "UPDATE bom_change_impact SET deleted = 1, update_by = #{updateBy}, update_time = #{updateTime} WHERE change_log_id = #{changeLogId};",
        "UPDATE bom_change_attachment SET deleted = 1, update_by = #{updateBy}, update_time = #{updateTime} WHERE change_log_id = #{changeLogId};"
    })
    int deleteChangeLogAndRelated(@Param("changeLogId") Long changeLogId,
                                 @Param("updateBy") String updateBy,
                                 @Param("updateTime") LocalDateTime updateTime);
}