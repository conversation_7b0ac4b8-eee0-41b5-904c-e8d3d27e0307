package com.yinma.security;

import com.yinma.entity.PermissionEntity;
import com.yinma.entity.RoleEntity;
import com.yinma.entity.UserEntity;
import com.yinma.mapper.PermissionMapper;
import com.yinma.mapper.RoleMapper;
import com.yinma.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 用户详情服务实现类
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserDetailsServiceImpl implements UserDetailsService {

    private final UserMapper userMapper;
    private final RoleMapper roleMapper;
    private final PermissionMapper permissionMapper;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        log.debug("加载用户信息: {}", username);
        
        // 查询用户信息
        UserEntity user = userMapper.selectUserByUsername(username);
        if (user == null) {
            log.warn("用户不存在: {}", username);
            throw new UsernameNotFoundException("用户不存在: " + username);
        }
        
        // 检查用户状态
        if (user.getStatus() == null || user.getStatus() != 1) {
            log.warn("用户已被禁用: {}", username);
            throw new UsernameNotFoundException("用户已被禁用: " + username);
        }
        
        // 查询用户角色
        List<RoleEntity> roles = roleMapper.selectRolesByUserId(user.getUserId());
        
        // 查询用户权限
        List<PermissionEntity> permissions = permissionMapper.selectUserPermissions(user.getUserId());
        
        // 构建权限列表
        List<GrantedAuthority> authorities = buildAuthorities(roles, permissions);
        
        log.debug("用户 {} 拥有权限: {}", username, authorities);
        
        return UserPrincipal.builder()
                .userId(user.getUserId())
                .username(user.getUsername())
                .password(user.getPassword())
                .email(user.getEmail())
                .phone(user.getPhone())
                .realName(user.getRealName())
                .avatar(user.getAvatar())
                .status(user.getStatus())
                .enabled(user.getStatus() != null && user.getStatus() == 1)
                .accountNonExpired(true)
                .accountNonLocked(user.getStatus() != null && user.getStatus() == 1)
                .credentialsNonExpired(true)
                .authorities(authorities)
                .build();
    }

    /**
     * 构建用户权限列表
     */
    private List<GrantedAuthority> buildAuthorities(List<RoleEntity> roles, List<PermissionEntity> permissions) {
        Set<String> authoritySet = new HashSet<>();
        
        // 添加角色权限（以ROLE_前缀标识）
        if (!CollectionUtils.isEmpty(roles)) {
            for (RoleEntity role : roles) {
                if (role.getStatus() != null && role.getStatus() == 1) {
                    authoritySet.add("ROLE_" + role.getRoleCode());
                }
            }
        }
        
        // 添加功能权限
        if (!CollectionUtils.isEmpty(permissions)) {
            for (PermissionEntity permission : permissions) {
                if (permission.getStatus() != null && permission.getStatus() == 1) {
                    authoritySet.add(permission.getPermissionCode());
                }
            }
        }
        
        // 转换为GrantedAuthority列表
        List<GrantedAuthority> authorities = new ArrayList<>();
        for (String authority : authoritySet) {
            authorities.add(new SimpleGrantedAuthority(authority));
        }
        
        return authorities;
    }
}