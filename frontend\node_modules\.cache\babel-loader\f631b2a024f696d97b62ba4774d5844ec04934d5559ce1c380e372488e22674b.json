{"ast": null, "code": "function isUndefined(x) {\n  return x === undefined;\n}\nexport { isUndefined };", "map": {"version": 3, "names": ["isUndefined", "x", "undefined"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/es-toolkit/dist/predicate/isUndefined.mjs"], "sourcesContent": ["function isUndefined(x) {\n    return x === undefined;\n}\n\nexport { isUndefined };\n"], "mappings": "AAAA,SAASA,WAAWA,CAACC,CAAC,EAAE;EACpB,OAAOA,CAAC,KAAKC,SAAS;AAC1B;AAEA,SAASF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}