{"ast": null, "code": "import { random } from './random.mjs';\nfunction randomInt(minimum, maximum) {\n  return Math.floor(random(minimum, maximum));\n}\nexport { randomInt };", "map": {"version": 3, "names": ["random", "randomInt", "minimum", "maximum", "Math", "floor"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/es-toolkit/dist/math/randomInt.mjs"], "sourcesContent": ["import { random } from './random.mjs';\n\nfunction randomInt(minimum, maximum) {\n    return Math.floor(random(minimum, maximum));\n}\n\nexport { randomInt };\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;AAErC,SAASC,SAASA,CAACC,OAAO,EAAEC,OAAO,EAAE;EACjC,OAAOC,IAAI,CAACC,KAAK,CAACL,MAAM,CAACE,OAAO,EAAEC,OAAO,CAAC,CAAC;AAC/C;AAEA,SAASF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}