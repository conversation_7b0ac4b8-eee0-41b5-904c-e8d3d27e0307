package com.yinma.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinma.common.result.Result;
import com.yinma.dto.RoleDTO;
import com.yinma.service.RoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 角色管理控制器
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/role")
@RequiredArgsConstructor
@Validated
@Tag(name = "角色管理", description = "角色信息的增删改查操作")
public class RoleController {

    private final RoleService roleService;

    @GetMapping("/page")
    @Operation(summary = "分页查询角色列表", description = "根据条件分页查询角色信息")
    @PreAuthorize("hasAuthority('role:query')")
    public Result<IPage<RoleDTO>> selectRolePage(@Valid RoleDTO.RoleQueryDTO queryDTO) {
        try {
            IPage<RoleDTO> page = roleService.selectRolePage(queryDTO);
            return Result.success(page);
        } catch (Exception e) {
            log.error("分页查询角色列表失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/{roleId}")
    @Operation(summary = "查询角色详情", description = "根据角色ID查询角色详细信息")
    @PreAuthorize("hasAuthority('role:query')")
    public Result<RoleDTO> selectRoleById(
            @Parameter(description = "角色ID", required = true)
            @PathVariable @NotNull Long roleId) {
        try {
            RoleDTO role = roleService.selectRoleById(roleId);
            if (role == null) {
                return Result.error("角色不存在");
            }
            return Result.success(role);
        } catch (Exception e) {
            log.error("查询角色详情失败, roleId: {}", roleId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/code/{roleCode}")
    @Operation(summary = "根据编码查询角色", description = "根据角色编码查询角色信息")
    @PreAuthorize("hasAuthority('role:query')")
    public Result<RoleDTO> selectRoleByCode(
            @Parameter(description = "角色编码", required = true)
            @PathVariable @NotEmpty String roleCode) {
        try {
            RoleDTO role = roleService.selectRoleByCode(roleCode);
            if (role == null) {
                return Result.error("角色不存在");
            }
            return Result.success(role);
        } catch (Exception e) {
            log.error("根据编码查询角色失败, roleCode: {}", roleCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @PostMapping
    @Operation(summary = "创建角色", description = "创建新的角色信息")
    @PreAuthorize("hasAuthority('role:create')")
    public Result<Long> createRole(@Valid @RequestBody RoleDTO.RoleCreateDTO createDTO) {
        try {
            Long roleId = roleService.createRole(createDTO);
            return Result.success(roleId, "角色创建成功");
        } catch (Exception e) {
            log.error("创建角色失败", e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    @PutMapping
    @Operation(summary = "更新角色", description = "更新角色信息")
    @PreAuthorize("hasAuthority('role:update')")
    public Result<Boolean> updateRole(@Valid @RequestBody RoleDTO.RoleUpdateDTO updateDTO) {
        try {
            Boolean result = roleService.updateRole(updateDTO);
            return Result.success(result, "角色更新成功");
        } catch (Exception e) {
            log.error("更新角色失败", e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/{roleId}")
    @Operation(summary = "删除角色", description = "根据角色ID删除角色")
    @PreAuthorize("hasAuthority('role:delete')")
    public Result<Boolean> deleteRole(
            @Parameter(description = "角色ID", required = true)
            @PathVariable @NotNull Long roleId) {
        try {
            Boolean result = roleService.deleteRole(roleId);
            return Result.success(result, "角色删除成功");
        } catch (Exception e) {
            log.error("删除角色失败, roleId: {}", roleId, e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/batch")
    @Operation(summary = "批量删除角色", description = "根据角色ID列表批量删除角色")
    @PreAuthorize("hasAuthority('role:delete')")
    public Result<Boolean> batchDeleteRoles(
            @Parameter(description = "角色ID列表", required = true)
            @RequestBody @NotEmpty List<Long> roleIds) {
        try {
            Boolean result = roleService.batchDeleteRoles(roleIds);
            return Result.success(result, "批量删除成功");
        } catch (Exception e) {
            log.error("批量删除角色失败, roleIds: {}", roleIds, e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }

    @PutMapping("/{roleId}/enable")
    @Operation(summary = "启用角色", description = "启用指定角色")
    @PreAuthorize("hasAuthority('role:update')")
    public Result<Boolean> enableRole(
            @Parameter(description = "角色ID", required = true)
            @PathVariable @NotNull Long roleId) {
        try {
            Boolean result = roleService.enableRole(roleId);
            return Result.success(result, "角色启用成功");
        } catch (Exception e) {
            log.error("启用角色失败, roleId: {}", roleId, e);
            return Result.error("启用失败: " + e.getMessage());
        }
    }

    @PutMapping("/{roleId}/disable")
    @Operation(summary = "禁用角色", description = "禁用指定角色")
    @PreAuthorize("hasAuthority('role:update')")
    public Result<Boolean> disableRole(
            @Parameter(description = "角色ID", required = true)
            @PathVariable @NotNull Long roleId) {
        try {
            Boolean result = roleService.disableRole(roleId);
            return Result.success(result, "角色禁用成功");
        } catch (Exception e) {
            log.error("禁用角色失败, roleId: {}", roleId, e);
            return Result.error("禁用失败: " + e.getMessage());
        }
    }

    @PutMapping("/batch/status")
    @Operation(summary = "批量更新角色状态", description = "批量更新角色状态")
    @PreAuthorize("hasAuthority('role:update')")
    public Result<Boolean> batchUpdateRoleStatus(
            @Valid @RequestBody RoleDTO.BatchStatusUpdateDTO updateDTO) {
        try {
            Boolean result = roleService.batchUpdateRoleStatus(updateDTO.getRoleIds(), updateDTO.getStatus());
            return Result.success(result, "批量更新状态成功");
        } catch (Exception e) {
            log.error("批量更新角色状态失败", e);
            return Result.error("批量更新失败: " + e.getMessage());
        }
    }

    @GetMapping("/check-code")
    @Operation(summary = "检查角色编码是否存在", description = "检查角色编码是否已被使用")
    public Result<Boolean> checkRoleCodeExists(
            @Parameter(description = "角色编码", required = true)
            @RequestParam @NotEmpty String roleCode,
            @Parameter(description = "排除的角色ID")
            @RequestParam(required = false) Long excludeRoleId) {
        try {
            Boolean exists = roleService.checkRoleCodeExists(roleCode, excludeRoleId);
            return Result.success(exists);
        } catch (Exception e) {
            log.error("检查角色编码失败, roleCode: {}", roleCode, e);
            return Result.error("检查失败: " + e.getMessage());
        }
    }

    @GetMapping("/check-name")
    @Operation(summary = "检查角色名称是否存在", description = "检查角色名称是否已被使用")
    public Result<Boolean> checkRoleNameExists(
            @Parameter(description = "角色名称", required = true)
            @RequestParam @NotEmpty String roleName,
            @Parameter(description = "排除的角色ID")
            @RequestParam(required = false) Long excludeRoleId) {
        try {
            Boolean exists = roleService.checkRoleNameExists(roleName, excludeRoleId);
            return Result.success(exists);
        } catch (Exception e) {
            log.error("检查角色名称失败, roleName: {}", roleName, e);
            return Result.error("检查失败: " + e.getMessage());
        }
    }

    @GetMapping("/{roleId}/users")
    @Operation(summary = "查询角色用户", description = "查询拥有指定角色的用户列表")
    @PreAuthorize("hasAuthority('role:query')")
    public Result<List<Long>> selectRoleUsers(
            @Parameter(description = "角色ID", required = true)
            @PathVariable @NotNull Long roleId) {
        try {
            List<Long> userIds = roleService.selectRoleUsers(roleId);
            return Result.success(userIds);
        } catch (Exception e) {
            log.error("查询角色用户失败, roleId: {}", roleId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/{roleId}/permissions")
    @Operation(summary = "查询角色权限", description = "查询角色拥有的权限列表")
    @PreAuthorize("hasAuthority('role:query')")
    public Result<List<Long>> selectRolePermissions(
            @Parameter(description = "角色ID", required = true)
            @PathVariable @NotNull Long roleId) {
        try {
            List<Long> permissionIds = roleService.selectRolePermissions(roleId);
            return Result.success(permissionIds);
        } catch (Exception e) {
            log.error("查询角色权限失败, roleId: {}", roleId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @PutMapping("/{roleId}/permissions")
    @Operation(summary = "分配角色权限", description = "为角色分配权限")
    @PreAuthorize("hasAuthority('role:assign-permission')")
    public Result<Boolean> assignRolePermissions(
            @Parameter(description = "角色ID", required = true)
            @PathVariable @NotNull Long roleId,
            @Valid @RequestBody RoleDTO.PermissionAssignDTO assignDTO) {
        try {
            Boolean result = roleService.assignRolePermissions(roleId, assignDTO.getPermissionIds());
            return Result.success(result, "权限分配成功");
        } catch (Exception e) {
            log.error("分配角色权限失败, roleId: {}, permissionIds: {}", roleId, assignDTO.getPermissionIds(), e);
            return Result.error("分配失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/{roleId}/permissions")
    @Operation(summary = "移除角色权限", description = "移除角色的权限")
    @PreAuthorize("hasAuthority('role:remove-permission')")
    public Result<Boolean> removeRolePermissions(
            @Parameter(description = "角色ID", required = true)
            @PathVariable @NotNull Long roleId,
            @Parameter(description = "权限ID列表", required = true)
            @RequestBody @NotEmpty List<Long> permissionIds) {
        try {
            Boolean result = roleService.removeRolePermissions(roleId, permissionIds);
            return Result.success(result, "权限移除成功");
        } catch (Exception e) {
            log.error("移除角色权限失败, roleId: {}, permissionIds: {}", roleId, permissionIds, e);
            return Result.error("移除失败: " + e.getMessage());
        }
    }

    @GetMapping("/enabled")
    @Operation(summary = "查询启用角色", description = "查询所有启用状态的角色")
    @PreAuthorize("hasAuthority('role:query')")
    public Result<List<RoleDTO>> selectEnabledRoles() {
        try {
            List<RoleDTO> roles = roleService.selectEnabledRoles();
            return Result.success(roles);
        } catch (Exception e) {
            log.error("查询启用角色失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/system")
    @Operation(summary = "查询系统角色", description = "查询所有系统角色")
    @PreAuthorize("hasAuthority('role:query')")
    public Result<List<RoleDTO>> selectSystemRoles() {
        try {
            List<RoleDTO> roles = roleService.selectSystemRoles();
            return Result.success(roles);
        } catch (Exception e) {
            log.error("查询系统角色失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/statistics")
    @Operation(summary = "查询角色统计", description = "查询角色统计信息")
    @PreAuthorize("hasAuthority('role:statistics')")
    public Result<RoleDTO.RoleStatisticsDTO> selectRoleStatistics() {
        try {
            RoleDTO.RoleStatisticsDTO statistics = roleService.selectRoleStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("查询角色统计失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/export")
    @Operation(summary = "导出角色数据", description = "导出角色数据")
    @PreAuthorize("hasAuthority('role:export')")
    public Result<List<RoleDTO>> exportRoles(@Valid RoleDTO.RoleQueryDTO queryDTO) {
        try {
            List<RoleDTO> roles = roleService.exportRoles(queryDTO);
            return Result.success(roles);
        } catch (Exception e) {
            log.error("导出角色数据失败", e);
            return Result.error("导出失败: " + e.getMessage());
        }
    }

    @PostMapping("/import")
    @Operation(summary = "导入角色数据", description = "导入角色数据")
    @PreAuthorize("hasAuthority('role:import')")
    public Result<RoleDTO.ImportResultDTO> importRoles(
            @Parameter(description = "角色数据列表", required = true)
            @RequestBody @NotEmpty List<RoleDTO.RoleImportDTO> roles) {
        try {
            RoleDTO.ImportResultDTO result = roleService.importRoles(roles);
            return Result.success(result, "导入完成");
        } catch (Exception e) {
            log.error("导入角色数据失败", e);
            return Result.error("导入失败: " + e.getMessage());
        }
    }

    @PostMapping("/copy")
    @Operation(summary = "复制角色", description = "复制角色及其权限")
    @PreAuthorize("hasAuthority('role:create')")
    public Result<Long> copyRole(
            @Parameter(description = "源角色ID", required = true)
            @RequestParam @NotNull Long sourceRoleId,
            @Parameter(description = "新角色名称", required = true)
            @RequestParam @NotEmpty String newRoleName,
            @Parameter(description = "新角色编码", required = true)
            @RequestParam @NotEmpty String newRoleCode) {
        try {
            Long newRoleId = roleService.copyRole(sourceRoleId, newRoleName, newRoleCode);
            return Result.success(newRoleId, "角色复制成功");
        } catch (Exception e) {
            log.error("复制角色失败, sourceRoleId: {}, newRoleName: {}, newRoleCode: {}", 
                    sourceRoleId, newRoleName, newRoleCode, e);
            return Result.error("复制失败: " + e.getMessage());
        }
    }

    @PostMapping("/sync-cache")
    @Operation(summary = "同步角色缓存", description = "同步角色权限缓存")
    @PreAuthorize("hasAuthority('role:sync-cache')")
    public Result<Boolean> syncRoleCache() {
        try {
            Boolean result = roleService.syncRoleCache();
            return Result.success(result, "缓存同步成功");
        } catch (Exception e) {
            log.error("同步角色缓存失败", e);
            return Result.error("同步失败: " + e.getMessage());
        }
    }
}