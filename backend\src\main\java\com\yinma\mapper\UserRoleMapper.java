package com.yinma.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yinma.entity.UserRoleEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用户角色关联Mapper接口
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
@Repository
@Mapper
public interface UserRoleMapper extends BaseMapper<UserRoleEntity> {

    /**
     * 根据用户ID查询角色ID列表
     * 
     * @param userId 用户ID
     * @return 角色ID列表
     */
    @Select("SELECT role_id FROM sys_user_role WHERE user_id = #{userId}")
    List<Long> selectRoleIdsByUserId(@Param("userId") Long userId);

    /**
     * 根据角色ID查询用户ID列表
     * 
     * @param roleId 角色ID
     * @return 用户ID列表
     */
    @Select("SELECT user_id FROM sys_user_role WHERE role_id = #{roleId}")
    List<Long> selectUserIdsByRoleId(@Param("roleId") Long roleId);

    /**
     * 检查用户角色关联是否存在
     * 
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 是否存在
     */
    @Select("SELECT COUNT(1) FROM sys_user_role WHERE user_id = #{userId} AND role_id = #{roleId}")
    int checkUserRoleExists(@Param("userId") Long userId, @Param("roleId") Long roleId);

    /**
     * 删除用户的所有角色关联
     * 
     * @param userId 用户ID
     * @return 删除行数
     */
    @Delete("DELETE FROM sys_user_role WHERE user_id = #{userId}")
    int deleteByUserId(@Param("userId") Long userId);

    /**
     * 删除角色的所有用户关联
     * 
     * @param roleId 角色ID
     * @return 删除行数
     */
    @Delete("DELETE FROM sys_user_role WHERE role_id = #{roleId}")
    int deleteByRoleId(@Param("roleId") Long roleId);

    /**
     * 删除指定用户角色关联
     * 
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 删除行数
     */
    @Delete("DELETE FROM sys_user_role WHERE user_id = #{userId} AND role_id = #{roleId}")
    int deleteUserRole(@Param("userId") Long userId, @Param("roleId") Long roleId);

    /**
     * 批量插入用户角色关联
     * 
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @param createUserId 创建用户ID
     * @return 插入行数
     */
    @Insert("<script>" +
            "INSERT INTO sys_user_role (user_id, role_id, create_time, create_user_id) VALUES " +
            "<foreach collection='roleIds' item='roleId' separator=','>" +
            "(#{userId}, #{roleId}, NOW(), #{createUserId})" +
            "</foreach>" +
            "</script>")
    int batchInsertUserRoles(@Param("userId") Long userId, 
                            @Param("roleIds") List<Long> roleIds,
                            @Param("createUserId") Long createUserId);

    /**
     * 批量插入角色用户关联
     * 
     * @param roleId 角色ID
     * @param userIds 用户ID列表
     * @param createUserId 创建用户ID
     * @return 插入行数
     */
    @Insert("<script>" +
            "INSERT INTO sys_user_role (user_id, role_id, create_time, create_user_id) VALUES " +
            "<foreach collection='userIds' item='userId' separator=','>" +
            "(#{userId}, #{roleId}, NOW(), #{createUserId})" +
            "</foreach>" +
            "</script>")
    int batchInsertRoleUsers(@Param("roleId") Long roleId, 
                            @Param("userIds") List<Long> userIds,
                            @Param("createUserId") Long createUserId);

    /**
     * 批量删除用户角色关联
     * 
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 删除行数
     */
    @Delete("<script>" +
            "DELETE FROM sys_user_role " +
            "WHERE user_id = #{userId} AND role_id IN " +
            "<foreach collection='roleIds' item='roleId' open='(' separator=',' close=')'>" +
            "#{roleId}" +
            "</foreach>" +
            "</script>")
    int batchDeleteUserRoles(@Param("userId") Long userId, @Param("roleIds") List<Long> roleIds);

    /**
     * 批量删除角色用户关联
     * 
     * @param roleId 角色ID
     * @param userIds 用户ID列表
     * @return 删除行数
     */
    @Delete("<script>" +
            "DELETE FROM sys_user_role " +
            "WHERE role_id = #{roleId} AND user_id IN " +
            "<foreach collection='userIds' item='userId' open='(' separator=',' close=')'>" +
            "#{userId}" +
            "</foreach>" +
            "</script>")
    int batchDeleteRoleUsers(@Param("roleId") Long roleId, @Param("userIds") List<Long> userIds);

    /**
     * 查询用户角色关联统计
     * 
     * @return 统计信息
     */
    @Select("SELECT " +
            "COUNT(DISTINCT user_id) as totalUsers, " +
            "COUNT(DISTINCT role_id) as totalRoles, " +
            "COUNT(1) as totalRelations, " +
            "AVG(role_count) as avgRolesPerUser " +
            "FROM (" +
            "SELECT user_id, COUNT(role_id) as role_count " +
            "FROM sys_user_role " +
            "GROUP BY user_id" +
            ") t")
    UserRoleStatisticsDTO selectUserRoleStatistics();

    /**
     * 查询拥有多个角色的用户
     * 
     * @param minRoleCount 最小角色数量
     * @return 用户ID列表
     */
    @Select("SELECT user_id FROM sys_user_role " +
            "GROUP BY user_id " +
            "HAVING COUNT(role_id) >= #{minRoleCount}")
    List<Long> selectUsersWithMultipleRoles(@Param("minRoleCount") Integer minRoleCount);

    /**
     * 查询拥有多个用户的角色
     * 
     * @param minUserCount 最小用户数量
     * @return 角色ID列表
     */
    @Select("SELECT role_id FROM sys_user_role " +
            "GROUP BY role_id " +
            "HAVING COUNT(user_id) >= #{minUserCount}")
    List<Long> selectRolesWithMultipleUsers(@Param("minUserCount") Integer minUserCount);

    /**
     * 查询用户角色数量
     * 
     * @param userId 用户ID
     * @return 角色数量
     */
    @Select("SELECT COUNT(1) FROM sys_user_role WHERE user_id = #{userId}")
    Long selectUserRoleCount(@Param("userId") Long userId);

    /**
     * 查询角色用户数量
     * 
     * @param roleId 角色ID
     * @return 用户数量
     */
    @Select("SELECT COUNT(1) FROM sys_user_role WHERE role_id = #{roleId}")
    Long selectRoleUserCount(@Param("roleId") Long roleId);

    /**
     * 查询没有角色的用户
     * 
     * @return 用户ID列表
     */
    @Select("SELECT u.user_id FROM sys_user u " +
            "LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id " +
            "WHERE ur.user_id IS NULL AND u.is_deleted = 0")
    List<Long> selectUsersWithoutRole();

    /**
     * 查询没有用户的角色
     * 
     * @return 角色ID列表
     */
    @Select("SELECT r.role_id FROM sys_role r " +
            "LEFT JOIN sys_user_role ur ON r.role_id = ur.role_id " +
            "WHERE ur.role_id IS NULL AND r.is_deleted = 0")
    List<Long> selectRolesWithoutUser();

    /**
     * 检查用户是否拥有指定角色
     * 
     * @param userId 用户ID
     * @param roleCode 角色编码
     * @return 是否拥有
     */
    @Select("SELECT COUNT(1) FROM sys_user_role ur " +
            "INNER JOIN sys_role r ON ur.role_id = r.role_id " +
            "WHERE ur.user_id = #{userId} AND r.role_code = #{roleCode} " +
            "AND r.status = 1 AND r.is_deleted = 0")
    int checkUserHasRole(@Param("userId") Long userId, @Param("roleCode") String roleCode);

    /**
     * 检查用户是否拥有指定角色列表中的任一角色
     * 
     * @param userId 用户ID
     * @param roleCodes 角色编码列表
     * @return 是否拥有
     */
    @Select("<script>" +
            "SELECT COUNT(1) FROM sys_user_role ur " +
            "INNER JOIN sys_role r ON ur.role_id = r.role_id " +
            "WHERE ur.user_id = #{userId} AND r.status = 1 AND r.is_deleted = 0 " +
            "AND r.role_code IN " +
            "<foreach collection='roleCodes' item='roleCode' open='(' separator=',' close=')'>" +
            "#{roleCode}" +
            "</foreach>" +
            "</script>")
    int checkUserHasAnyRole(@Param("userId") Long userId, @Param("roleCodes") List<String> roleCodes);

    /**
     * 检查用户是否拥有指定角色列表中的所有角色
     * 
     * @param userId 用户ID
     * @param roleCodes 角色编码列表
     * @return 是否拥有
     */
    @Select("<script>" +
            "SELECT COUNT(DISTINCT r.role_code) FROM sys_user_role ur " +
            "INNER JOIN sys_role r ON ur.role_id = r.role_id " +
            "WHERE ur.user_id = #{userId} AND r.status = 1 AND r.is_deleted = 0 " +
            "AND r.role_code IN " +
            "<foreach collection='roleCodes' item='roleCode' open='(' separator=',' close=')'>" +
            "#{roleCode}" +
            "</foreach>" +
            "</script>")
    int checkUserHasAllRoles(@Param("userId") Long userId, @Param("roleCodes") List<String> roleCodes);

    /**
     * 用户角色统计DTO
     */
    class UserRoleStatisticsDTO {
        private Long totalUsers;
        private Long totalRoles;
        private Long totalRelations;
        private Double avgRolesPerUser;

        // Getters and Setters
        public Long getTotalUsers() {
            return totalUsers;
        }

        public void setTotalUsers(Long totalUsers) {
            this.totalUsers = totalUsers;
        }

        public Long getTotalRoles() {
            return totalRoles;
        }

        public void setTotalRoles(Long totalRoles) {
            this.totalRoles = totalRoles;
        }

        public Long getTotalRelations() {
            return totalRelations;
        }

        public void setTotalRelations(Long totalRelations) {
            this.totalRelations = totalRelations;
        }

        public Double getAvgRolesPerUser() {
            return avgRolesPerUser;
        }

        public void setAvgRolesPerUser(Double avgRolesPerUser) {
            this.avgRolesPerUser = avgRolesPerUser;
        }
    }
}