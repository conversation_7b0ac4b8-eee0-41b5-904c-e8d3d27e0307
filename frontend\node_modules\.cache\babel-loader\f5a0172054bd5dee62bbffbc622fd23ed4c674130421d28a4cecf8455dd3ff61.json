{"ast": null, "code": "import { Color } from './color';\n/**\n * -\n */\nexport const Hyphen = (options, context) => {\n  return Color(Object.assign({\n    colorAttribute: 'stroke',\n    symbol: 'hyphen'\n  }, options), context);\n};\nHyphen.props = Object.assign({\n  defaultMarker: 'hyphen'\n}, Color.props);", "map": {"version": 3, "names": ["Color", "Hyphen", "options", "context", "Object", "assign", "colorAttribute", "symbol", "props", "defaultMarker"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\g2\\src\\shape\\point\\hyphen.ts"], "sourcesContent": ["import { ShapeComponent as SC } from '../../runtime';\nimport { Color } from './color';\n\nexport type HyphenOptions = Record<string, any>;\n\n/**\n * -\n */\nexport const Hyphen: SC<HyphenOptions> = (options, context) => {\n  return Color(\n    { colorAttribute: 'stroke', symbol: 'hyphen', ...options },\n    context,\n  );\n};\n\nHyphen.props = {\n  defaultMarker: 'hyphen',\n  ...Color.props,\n};\n"], "mappings": "AACA,SAASA,KAAK,QAAQ,SAAS;AAI/B;;;AAGA,OAAO,MAAMC,MAAM,GAAsBA,CAACC,OAAO,EAAEC,OAAO,KAAI;EAC5D,OAAOH,KAAK,CAAAI,MAAA,CAAAC,MAAA;IACRC,cAAc,EAAE,QAAQ;IAAEC,MAAM,EAAE;EAAQ,GAAKL,OAAO,GACxDC,OAAO,CACR;AACH,CAAC;AAEDF,MAAM,CAACO,KAAK,GAAAJ,MAAA,CAAAC,MAAA;EACVI,aAAa,EAAE;AAAQ,GACpBT,KAAK,CAACQ,KAAK,CACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}