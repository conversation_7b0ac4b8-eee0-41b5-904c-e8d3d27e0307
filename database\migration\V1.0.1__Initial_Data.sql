-- 西安银马实业数字化管理系统
-- 数据库迁移脚本 V1.0.1
-- 初始化基础数据
-- 创建时间: 2024-01-01
-- 描述: 插入系统初始化数据

-- ===========================================
-- 初始化用户数据
-- ===========================================

-- 插入默认管理员用户（密码：admin123）
INSERT INTO sys_user (
    id, username, password, real_name, email, dept_name, position, 
    role_ids, role_names, status, create_by, update_by
) VALUES (
    1, 'admin', '$2a$10$7JB720yubVSOfvVWbazBuOWWZHrOBXjbZcNLAcBYQVYdZSfHAJxOa', 
    '系统管理员', '<EMAIL>', '信息技术部', '系统管理员', 
    '1', '超级管理员', 'ACTIVE', 'system', 'system'
) ON CONFLICT (username) DO NOTHING;

-- 插入测试用户（密码：test123）
INSERT INTO sys_user (
    id, username, password, real_name, email, dept_name, position, 
    role_ids, role_names, status, create_by, update_by
) VALUES (
    2, 'test', '$2a$10$7JB720yubVSOfvVWbazBuOWWZHrOBXjbZcNLAcBYQVYdZSfHAJxOa', 
    '测试用户', '<EMAIL>', '技术部', '测试工程师', 
    '2', '普通用户', 'ACTIVE', 'system', 'system'
) ON CONFLICT (username) DO NOTHING;

-- 插入BOM管理员用户（密码：bom123）
INSERT INTO sys_user (
    id, username, password, real_name, email, dept_name, position, 
    role_ids, role_names, status, create_by, update_by
) VALUES (
    3, 'bom_admin', '$2a$10$7JB720yubVSOfvVWbazBuOWWZHrOBXjbZcNLAcBYQVYdZSfHAJxOa', 
    'BOM管理员', '<EMAIL>', '技术部', 'BOM工程师', 
    '3', 'BOM管理员', 'ACTIVE', 'system', 'system'
) ON CONFLICT (username) DO NOTHING;

-- 插入物料管理员用户（密码：material123）
INSERT INTO sys_user (
    id, username, password, real_name, email, dept_name, position, 
    role_ids, role_names, status, create_by, update_by
) VALUES (
    4, 'material_admin', '$2a$10$7JB720yubVSOfvVWbazBuOWWZHrOBXjbZcNLAcBYQVYdZSfHAJxOa', 
    '物料管理员', '<EMAIL>', '采购部', '物料工程师', 
    '4', '物料管理员', 'ACTIVE', 'system', 'system'
) ON CONFLICT (username) DO NOTHING;

-- ===========================================
-- 初始化物料数据
-- ===========================================

-- 原材料
INSERT INTO material (
    id, material_code, material_name, short_name, category, material_type, 
    specification, base_unit, abc_category, status, create_by, update_by
) VALUES 
-- 钢材类
(1, 'M001001', 'Q235钢板', 'Q235钢板', '钢材', 'RAW', '10mm*1000mm*2000mm', 'kg', 'A', 'ACTIVE', 'system', 'system'),
(2, 'M001002', 'Q345钢板', 'Q345钢板', '钢材', 'RAW', '12mm*1000mm*2000mm', 'kg', 'A', 'ACTIVE', 'system', 'system'),
(3, 'M001003', '不锈钢板304', '304不锈钢', '钢材', 'RAW', '8mm*1000mm*2000mm', 'kg', 'B', 'ACTIVE', 'system', 'system'),
(4, 'M001004', '角钢L50*5', '角钢50*5', '钢材', 'RAW', '50*50*5mm', 'm', 'B', 'ACTIVE', 'system', 'system'),
(5, 'M001005', '圆钢Φ20', '圆钢20', '钢材', 'RAW', 'Φ20mm', 'm', 'C', 'ACTIVE', 'system', 'system'),

-- 标准件
(6, 'M002001', '螺栓M8*20', '螺栓M8*20', '标准件', 'RAW', 'M8*20 8.8级', 'pcs', 'C', 'ACTIVE', 'system', 'system'),
(7, 'M002002', '螺栓M10*25', '螺栓M10*25', '标准件', 'RAW', 'M10*25 8.8级', 'pcs', 'C', 'ACTIVE', 'system', 'system'),
(8, 'M002003', '螺栓M12*30', '螺栓M12*30', '标准件', 'RAW', 'M12*30 8.8级', 'pcs', 'C', 'ACTIVE', 'system', 'system'),
(9, 'M002004', '螺母M8', '螺母M8', '标准件', 'RAW', 'M8 8级', 'pcs', 'C', 'ACTIVE', 'system', 'system'),
(10, 'M002005', '垫圈Φ8', '垫圈8', '标准件', 'RAW', 'Φ8 平垫圈', 'pcs', 'C', 'ACTIVE', 'system', 'system'),

-- 电机类
(11, 'M003001', '电机YE2-90L-4', '电机1.1KW', '电机', 'RAW', '1.1KW 380V 1420rpm', 'pcs', 'A', 'ACTIVE', 'system', 'system'),
(12, 'M003002', '电机YE2-112M-4', '电机4KW', '电机', 'RAW', '4KW 380V 1440rpm', 'pcs', 'A', 'ACTIVE', 'system', 'system'),
(13, 'M003003', '电机YE2-132S-4', '电机5.5KW', '电机', 'RAW', '5.5KW 380V 1460rpm', 'pcs', 'A', 'ACTIVE', 'system', 'system'),

-- 轴承类
(14, 'M004001', '轴承6208', '轴承6208', '轴承', 'RAW', '内径40mm 外径80mm', 'pcs', 'B', 'ACTIVE', 'system', 'system'),
(15, 'M004002', '轴承6210', '轴承6210', '轴承', 'RAW', '内径50mm 外径90mm', 'pcs', 'B', 'ACTIVE', 'system', 'system'),

-- 密封件
(16, 'M005001', 'O型圈40*3', 'O型圈40*3', '密封件', 'RAW', '内径40mm 线径3mm', 'pcs', 'C', 'ACTIVE', 'system', 'system'),
(17, 'M005002', '油封45*62*8', '油封45*62*8', '密封件', 'RAW', '内径45mm 外径62mm 厚8mm', 'pcs', 'C', 'ACTIVE', 'system', 'system')
ON CONFLICT (material_code) DO NOTHING;

-- 半成品
INSERT INTO material (
    id, material_code, material_name, short_name, category, material_type, 
    specification, base_unit, abc_category, status, create_by, update_by
) VALUES 
(101, 'P001001', '搅拌机主体', '搅拌机主体', '机械部件', 'SEMI', 'JS1000型', 'pcs', 'A', 'ACTIVE', 'system', 'system'),
(102, 'P001002', '搅拌臂总成', '搅拌臂', '机械部件', 'SEMI', 'JS1000配套', 'pcs', 'A', 'ACTIVE', 'system', 'system'),
(103, 'P001003', '减速机总成', '减速机', '机械部件', 'SEMI', '速比1:40', 'pcs', 'A', 'ACTIVE', 'system', 'system'),
(104, 'P001004', '卸料门总成', '卸料门', '机械部件', 'SEMI', '气动控制', 'pcs', 'B', 'ACTIVE', 'system', 'system'),
(105, 'P001005', '支撑架总成', '支撑架', '机械部件', 'SEMI', '钢结构焊接', 'pcs', 'B', 'ACTIVE', 'system', 'system')
ON CONFLICT (material_code) DO NOTHING;

-- 成品
INSERT INTO material (
    id, material_code, material_name, short_name, category, material_type, 
    specification, base_unit, abc_category, status, create_by, update_by
) VALUES 
(201, 'F001001', 'JS1000搅拌机', 'JS1000搅拌机', '搅拌设备', 'FINISHED', '1立方米强制式搅拌机', 'pcs', 'A', 'ACTIVE', 'system', 'system'),
(202, 'F001002', 'JS2000搅拌机', 'JS2000搅拌机', '搅拌设备', 'FINISHED', '2立方米强制式搅拌机', 'pcs', 'A', 'ACTIVE', 'system', 'system'),
(203, 'F001003', 'HZS120搅拌站', 'HZS120搅拌站', '搅拌设备', 'FINISHED', '120m³/h混凝土搅拌站', 'set', 'A', 'ACTIVE', 'system', 'system')
ON CONFLICT (material_code) DO NOTHING;

-- ===========================================
-- 初始化BOM数据
-- ===========================================

-- JS1000搅拌机BOM
INSERT INTO bom (
    id, bom_code, bom_name, product_code, product_name, bom_type, 
    version, status, base_quantity, unit, create_by, update_by
) VALUES 
(1, 'BOM001001', 'JS1000搅拌机制造BOM', 'F001001', 'JS1000搅拌机', 'MBOM', 'V1.0', 'ACTIVE', 1, 'pcs', 'system', 'system'),
(2, 'BOM001002', 'JS2000搅拌机制造BOM', 'F001002', 'JS2000搅拌机', 'MBOM', 'V1.0', 'ACTIVE', 1, 'pcs', 'system', 'system')
ON CONFLICT (bom_code) DO NOTHING;

-- JS1000搅拌机BOM明细
INSERT INTO bom_detail (
    id, bom_id, parent_material_code, child_material_code, child_material_name, 
    quantity, unit, level, sequence, create_by, update_by
) VALUES 
-- 第一层级（主要组件）
(1, 1, 'F001001', 'P001001', '搅拌机主体', 1, 'pcs', 1, 1, 'system', 'system'),
(2, 1, 'F001001', 'P001002', '搅拌臂总成', 2, 'pcs', 1, 2, 'system', 'system'),
(3, 1, 'F001001', 'P001003', '减速机总成', 1, 'pcs', 1, 3, 'system', 'system'),
(4, 1, 'F001001', 'M003001', '电机YE2-90L-4', 1, 'pcs', 1, 4, 'system', 'system'),
(5, 1, 'F001001', 'P001004', '卸料门总成', 2, 'pcs', 1, 5, 'system', 'system'),
(6, 1, 'F001001', 'P001005', '支撑架总成', 1, 'pcs', 1, 6, 'system', 'system'),

-- 第二层级（搅拌机主体分解）
(7, 1, 'P001001', 'M001001', 'Q235钢板', 150, 'kg', 2, 1, 'system', 'system'),
(8, 1, 'P001001', 'M001004', '角钢L50*5', 20, 'm', 2, 2, 'system', 'system'),
(9, 1, 'P001001', 'M002001', '螺栓M8*20', 50, 'pcs', 2, 3, 'system', 'system'),
(10, 1, 'P001001', 'M002004', '螺母M8', 50, 'pcs', 2, 4, 'system', 'system'),
(11, 1, 'P001001', 'M002005', '垫圈Φ8', 50, 'pcs', 2, 5, 'system', 'system'),

-- 第二层级（搅拌臂总成分解）
(12, 1, 'P001002', 'M001002', 'Q345钢板', 80, 'kg', 2, 1, 'system', 'system'),
(13, 1, 'P001002', 'M004001', '轴承6208', 4, 'pcs', 2, 2, 'system', 'system'),
(14, 1, 'P001002', 'M005001', 'O型圈40*3', 4, 'pcs', 2, 3, 'system', 'system'),
(15, 1, 'P001002', 'M002002', '螺栓M10*25', 20, 'pcs', 2, 4, 'system', 'system'),

-- 第二层级（减速机总成分解）
(16, 1, 'P001003', 'M001003', '不锈钢板304', 30, 'kg', 2, 1, 'system', 'system'),
(17, 1, 'P001003', 'M004002', '轴承6210', 2, 'pcs', 2, 2, 'system', 'system'),
(18, 1, 'P001003', 'M005002', '油封45*62*8', 2, 'pcs', 2, 3, 'system', 'system'),

-- 第二层级（卸料门总成分解）
(19, 1, 'P001004', 'M001001', 'Q235钢板', 25, 'kg', 2, 1, 'system', 'system'),
(20, 1, 'P001004', 'M002003', '螺栓M12*30', 8, 'pcs', 2, 2, 'system', 'system'),

-- 第二层级（支撑架总成分解）
(21, 1, 'P001005', 'M001004', '角钢L50*5', 50, 'm', 2, 1, 'system', 'system'),
(22, 1, 'P001005', 'M001005', '圆钢Φ20', 10, 'm', 2, 2, 'system', 'system'),
(23, 1, 'P001005', 'M002001', '螺栓M8*20', 30, 'pcs', 2, 3, 'system', 'system')
ON CONFLICT (id) DO NOTHING;

-- ===========================================
-- 初始化BOM变更日志示例数据
-- ===========================================

INSERT INTO bom_change_log (
    id, change_no, bom_code, bom_name, change_type, change_object, 
    change_field, old_value, new_value, change_reason, change_description, 
    change_status, applicant, apply_time, create_by, update_by
) VALUES 
(1, 'CHG202401001', 'BOM001001', 'JS1000搅拌机制造BOM', 'UPDATE', 'DETAIL', 
 'quantity', '40', '50', '根据实际生产需要调整螺栓用量', 
 '将M8*20螺栓数量从40个调整为50个，以满足装配要求', 
 'IMPLEMENTED', 'bom_admin', '2024-01-01 10:00:00', 'system', 'system'),
 
(2, 'CHG202401002', 'BOM001001', 'JS1000搅拌机制造BOM', 'ADD', 'DETAIL', 
 'new_component', '', 'M002005:垫圈Φ8:50pcs', '新增垫圈配件', 
 '为提高装配质量，新增Φ8垫圈50个', 
 'APPROVED', 'bom_admin', '2024-01-01 14:30:00', 'system', 'system')
ON CONFLICT (change_no) DO NOTHING;

-- 更新序列当前值
SELECT setval('seq_sys_user', (SELECT COALESCE(MAX(id), 0) + 1 FROM sys_user), false);
SELECT setval('seq_material', (SELECT COALESCE(MAX(id), 0) + 1 FROM material), false);
SELECT setval('seq_bom', (SELECT COALESCE(MAX(id), 0) + 1 FROM bom), false);
SELECT setval('seq_bom_detail', (SELECT COALESCE(MAX(id), 0) + 1 FROM bom_detail), false);
SELECT setval('seq_bom_change_log', (SELECT COALESCE(MAX(id), 0) + 1 FROM bom_change_log), false);

-- 输出完成信息
SELECT 'V1.0.1 初始化数据插入完成！' AS message;
SELECT '用户数据：' || COUNT(*) || ' 条' AS user_count FROM sys_user WHERE deleted = 0;
SELECT '物料数据：' || COUNT(*) || ' 条' AS material_count FROM material WHERE deleted = 0;
SELECT 'BOM数据：' || COUNT(*) || ' 条' AS bom_count FROM bom WHERE deleted = 0;
SELECT 'BOM明细数据：' || COUNT(*) || ' 条' AS bom_detail_count FROM bom_detail WHERE deleted = 0;
SELECT 'BOM变更记录：' || COUNT(*) || ' 条' AS change_log_count FROM bom_change_log WHERE deleted = 0;