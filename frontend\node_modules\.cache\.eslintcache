[{"D:\\customerDemo\\Link-YinMa\\frontend\\src\\index.js": "1", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\App.js": "2", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\index.js": "3", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\components\\GlobalLoading\\index.js": "4", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\components\\ErrorBoundary\\index.js": "5", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\components\\Layout\\index.js": "6", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\Dashboard\\index.js": "7", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\BomDetailManagement\\index.js": "8", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\Login\\index.js": "9", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\BomChangeLogManagement\\index.js": "10", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\MaterialManagement\\index.js": "11", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\BomManagement\\index.js": "12", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\slices\\authSlice.js": "13", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\services\\api.js": "14", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\services\\mockApi.js": "15", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\services\\mockData.js": "16", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\slices\\bomSlice.js": "17", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\slices\\manufacturingSlice.js": "18", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\slices\\projectSlice.js": "19", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\slices\\serviceSlice.js": "20", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\slices\\financialSlice.js": "21", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\slices\\decisionSlice.js": "22", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\slices\\supplierSlice.js": "23", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\slices\\userSlice.js": "24", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\slices\\materialSlice.js": "25", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\Manufacturing\\index.js": "26", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\Manufacturing\\Equipment\\index.js": "27", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\ProjectDelivery\\index.js": "28", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\ProjectDelivery\\Management\\index.js": "29", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\SmartService\\index.js": "30", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\SmartService\\Monitoring\\index.js": "31", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\SmartService\\Maintenance\\index.js": "32", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\FinancialControl\\index.js": "33", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\CollaborativeDecision\\index.js": "34", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\slices\\customerSlice.js": "35", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\slices\\productionPlanSlice.js": "36", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\slices\\supplyChainSlice.js": "37", "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\slices\\qualitySlice.js": "38"}, {"size": 2307, "mtime": 1757585647979, "results": "39", "hashOfConfig": "40"}, {"size": 3404, "mtime": 1757655713847, "results": "41", "hashOfConfig": "40"}, {"size": 1670, "mtime": 1757639861524, "results": "42", "hashOfConfig": "40"}, {"size": 3931, "mtime": 1757576405634, "results": "43", "hashOfConfig": "40"}, {"size": 3677, "mtime": 1757576383350, "results": "44", "hashOfConfig": "40"}, {"size": 7756, "mtime": 1757655761035, "results": "45", "hashOfConfig": "40"}, {"size": 15384, "mtime": 1757638573969, "results": "46", "hashOfConfig": "40"}, {"size": 31354, "mtime": 1757638573711, "results": "47", "hashOfConfig": "40"}, {"size": 7210, "mtime": 1757553596905, "results": "48", "hashOfConfig": "40"}, {"size": 28860, "mtime": 1757638573620, "results": "49", "hashOfConfig": "40"}, {"size": 34746, "mtime": 1757585898779, "results": "50", "hashOfConfig": "40"}, {"size": 23591, "mtime": 1757638573827, "results": "51", "hashOfConfig": "40"}, {"size": 5009, "mtime": 1757655732208, "results": "52", "hashOfConfig": "40"}, {"size": 22308, "mtime": 1757640909742, "results": "53", "hashOfConfig": "40"}, {"size": 15189, "mtime": 1757578412205, "results": "54", "hashOfConfig": "40"}, {"size": 19565, "mtime": 1757578356285, "results": "55", "hashOfConfig": "40"}, {"size": 5173, "mtime": 1757585924689, "results": "56", "hashOfConfig": "40"}, {"size": 6123, "mtime": 1757585948267, "results": "57", "hashOfConfig": "40"}, {"size": 6982, "mtime": 1757585974186, "results": "58", "hashOfConfig": "40"}, {"size": 7184, "mtime": 1757586000528, "results": "59", "hashOfConfig": "40"}, {"size": 8596, "mtime": 1757635874194, "results": "60", "hashOfConfig": "40"}, {"size": 10079, "mtime": 1757635872121, "results": "61", "hashOfConfig": "40"}, {"size": 9425, "mtime": 1757586034254, "results": "62", "hashOfConfig": "40"}, {"size": 10513, "mtime": 1757586070347, "results": "63", "hashOfConfig": "40"}, {"size": 8555, "mtime": 1757635874279, "results": "64", "hashOfConfig": "40"}, {"size": 461, "mtime": 1757638573999, "results": "65", "hashOfConfig": "40"}, {"size": 32264, "mtime": 1757573977963, "results": "66", "hashOfConfig": "40"}, {"size": 476, "mtime": 1757638574013, "results": "67", "hashOfConfig": "40"}, {"size": 32242, "mtime": 1757574090649, "results": "68", "hashOfConfig": "40"}, {"size": 573, "mtime": 1757638574027, "results": "69", "hashOfConfig": "40"}, {"size": 33675, "mtime": 1757574345513, "results": "70", "hashOfConfig": "40"}, {"size": 40739, "mtime": 1757574485902, "results": "71", "hashOfConfig": "40"}, {"size": 12803, "mtime": 1757641317087, "results": "72", "hashOfConfig": "40"}, {"size": 24668, "mtime": 1757642163931, "results": "73", "hashOfConfig": "40"}, {"size": 9652, "mtime": 1757639817822, "results": "74", "hashOfConfig": "40"}, {"size": 8628, "mtime": 1757639786390, "results": "75", "hashOfConfig": "40"}, {"size": 8703, "mtime": 1757639848896, "results": "76", "hashOfConfig": "40"}, {"size": 6052, "mtime": 1757639756624, "results": "77", "hashOfConfig": "40"}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "k<PERSON>waa", {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 40, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 28, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 39, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 34, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\customerDemo\\Link-YinMa\\frontend\\src\\index.js", [], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\App.js", [], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\index.js", [], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\components\\GlobalLoading\\index.js", [], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\components\\ErrorBoundary\\index.js", [], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\components\\Layout\\index.js", [], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\Dashboard\\index.js", ["192", "193", "194", "195", "196", "197", "198", "199", "200", "201", "202", "203"], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\BomDetailManagement\\index.js", ["204", "205", "206", "207", "208", "209", "210", "211", "212", "213", "214", "215", "216", "217", "218", "219", "220", "221", "222", "223", "224", "225", "226", "227", "228", "229", "230", "231", "232", "233", "234", "235", "236", "237", "238", "239", "240", "241", "242", "243"], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\Login\\index.js", ["244"], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\BomChangeLogManagement\\index.js", ["245", "246", "247", "248", "249", "250", "251", "252", "253", "254", "255", "256", "257", "258", "259", "260", "261", "262", "263", "264", "265", "266", "267", "268", "269", "270", "271", "272"], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\MaterialManagement\\index.js", ["273", "274", "275", "276", "277", "278", "279", "280", "281", "282", "283", "284", "285", "286", "287", "288", "289", "290", "291", "292", "293", "294", "295", "296", "297", "298", "299", "300", "301", "302", "303", "304", "305", "306", "307", "308", "309", "310", "311"], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\BomManagement\\index.js", ["312", "313", "314", "315", "316", "317", "318", "319", "320", "321", "322"], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\slices\\authSlice.js", [], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\services\\api.js", [], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\services\\mockApi.js", [], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\services\\mockData.js", [], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\slices\\bomSlice.js", [], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\slices\\manufacturingSlice.js", [], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\slices\\projectSlice.js", [], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\slices\\serviceSlice.js", [], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\slices\\financialSlice.js", [], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\slices\\decisionSlice.js", [], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\slices\\supplierSlice.js", [], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\slices\\userSlice.js", [], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\slices\\materialSlice.js", [], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\Manufacturing\\index.js", [], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\Manufacturing\\Equipment\\index.js", ["323", "324", "325", "326", "327", "328", "329", "330", "331", "332", "333", "334", "335", "336", "337", "338"], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\ProjectDelivery\\index.js", [], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\ProjectDelivery\\Management\\index.js", ["339", "340", "341", "342", "343", "344", "345", "346", "347", "348", "349", "350", "351"], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\SmartService\\index.js", [], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\SmartService\\Monitoring\\index.js", ["352", "353", "354", "355", "356", "357", "358", "359", "360", "361", "362", "363", "364", "365", "366", "367", "368", "369", "370", "371", "372", "373", "374", "375", "376", "377", "378"], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\SmartService\\Maintenance\\index.js", ["379", "380", "381", "382", "383", "384", "385", "386", "387", "388", "389", "390", "391", "392", "393", "394", "395", "396", "397", "398", "399", "400", "401", "402", "403", "404", "405", "406", "407", "408", "409", "410", "411", "412"], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\FinancialControl\\index.js", ["413", "414", "415", "416", "417", "418", "419", "420", "421", "422"], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\pages\\CollaborativeDecision\\index.js", ["423", "424", "425", "426", "427", "428", "429", "430", "431", "432", "433", "434", "435", "436", "437", "438"], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\slices\\customerSlice.js", [], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\slices\\productionPlanSlice.js", [], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\slices\\supplyChainSlice.js", [], [], "D:\\customerDemo\\Link-YinMa\\frontend\\src\\store\\slices\\qualitySlice.js", [], [], {"ruleId": "439", "severity": 1, "message": "440", "line": 1, "column": 27, "nodeType": "441", "messageId": "442", "endLine": 1, "endColumn": 36}, {"ruleId": "439", "severity": 1, "message": "443", "line": 17, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 17, "endColumn": 10}, {"ruleId": "439", "severity": 1, "message": "444", "line": 18, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 18, "endColumn": 8}, {"ruleId": "439", "severity": 1, "message": "445", "line": 19, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 19, "endColumn": 11}, {"ruleId": "439", "severity": 1, "message": "446", "line": 38, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 38, "endColumn": 12}, {"ruleId": "439", "severity": 1, "message": "447", "line": 39, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 39, "endColumn": 7}, {"ruleId": "439", "severity": 1, "message": "448", "line": 42, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 42, "endColumn": 11}, {"ruleId": "439", "severity": 1, "message": "449", "line": 43, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 43, "endColumn": 6}, {"ruleId": "439", "severity": 1, "message": "450", "line": 57, "column": 9, "nodeType": "441", "messageId": "442", "endLine": 57, "endColumn": 20}, {"ruleId": "439", "severity": 1, "message": "451", "line": 64, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 64, "endColumn": 17}, {"ruleId": "439", "severity": 1, "message": "452", "line": 64, "column": 19, "nodeType": "441", "messageId": "442", "endLine": 64, "endColumn": 29}, {"ruleId": "439", "severity": 1, "message": "453", "line": 69, "column": 25, "nodeType": "441", "messageId": "442", "endLine": 69, "endColumn": 41}, {"ruleId": "439", "severity": 1, "message": "454", "line": 1, "column": 38, "nodeType": "441", "messageId": "442", "endLine": 1, "endColumn": 44}, {"ruleId": "439", "severity": 1, "message": "455", "line": 16, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 16, "endColumn": 7}, {"ruleId": "439", "severity": 1, "message": "456", "line": 17, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 17, "endColumn": 9}, {"ruleId": "439", "severity": 1, "message": "457", "line": 18, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 18, "endColumn": 15}, {"ruleId": "439", "severity": 1, "message": "458", "line": 22, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 22, "endColumn": 11}, {"ruleId": "439", "severity": 1, "message": "459", "line": 24, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 24, "endColumn": 9}, {"ruleId": "439", "severity": 1, "message": "460", "line": 26, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 26, "endColumn": 8}, {"ruleId": "439", "severity": 1, "message": "461", "line": 41, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 41, "endColumn": 21}, {"ruleId": "439", "severity": 1, "message": "462", "line": 42, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 42, "endColumn": 19}, {"ruleId": "439", "severity": 1, "message": "463", "line": 44, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 44, "endColumn": 22}, {"ruleId": "439", "severity": 1, "message": "464", "line": 46, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 46, "endColumn": 21}, {"ruleId": "439", "severity": 1, "message": "465", "line": 47, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 47, "endColumn": 19}, {"ruleId": "439", "severity": 1, "message": "466", "line": 48, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 48, "endColumn": 17}, {"ruleId": "439", "severity": 1, "message": "467", "line": 51, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 51, "endColumn": 19}, {"ruleId": "439", "severity": 1, "message": "468", "line": 75, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 75, "endColumn": 22}, {"ruleId": "439", "severity": 1, "message": "469", "line": 79, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 79, "endColumn": 29}, {"ruleId": "439", "severity": 1, "message": "470", "line": 80, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 80, "endColumn": 26}, {"ruleId": "439", "severity": 1, "message": "471", "line": 81, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 81, "endColumn": 34}, {"ruleId": "439", "severity": 1, "message": "472", "line": 82, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 82, "endColumn": 33}, {"ruleId": "439", "severity": 1, "message": "473", "line": 83, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 83, "endColumn": 32}, {"ruleId": "439", "severity": 1, "message": "474", "line": 84, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 84, "endColumn": 28}, {"ruleId": "439", "severity": 1, "message": "475", "line": 85, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 85, "endColumn": 33}, {"ruleId": "439", "severity": 1, "message": "476", "line": 86, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 86, "endColumn": 26}, {"ruleId": "439", "severity": 1, "message": "477", "line": 87, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 87, "endColumn": 26}, {"ruleId": "439", "severity": 1, "message": "478", "line": 96, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 96, "endColumn": 20}, {"ruleId": "439", "severity": 1, "message": "479", "line": 100, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 100, "endColumn": 18}, {"ruleId": "439", "severity": 1, "message": "480", "line": 101, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 101, "endColumn": 22}, {"ruleId": "439", "severity": 1, "message": "481", "line": 101, "column": 24, "nodeType": "441", "messageId": "442", "endLine": 101, "endColumn": 39}, {"ruleId": "439", "severity": 1, "message": "482", "line": 102, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 102, "endColumn": 22}, {"ruleId": "439", "severity": 1, "message": "483", "line": 102, "column": 24, "nodeType": "441", "messageId": "442", "endLine": 102, "endColumn": 39}, {"ruleId": "439", "severity": 1, "message": "484", "line": 113, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 113, "endColumn": 26}, {"ruleId": "439", "severity": 1, "message": "485", "line": 114, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 114, "endColumn": 25}, {"ruleId": "439", "severity": 1, "message": "486", "line": 115, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 115, "endColumn": 24}, {"ruleId": "487", "severity": 1, "message": "488", "line": 127, "column": 6, "nodeType": "489", "endLine": 127, "endColumn": 8, "suggestions": "490"}, {"ruleId": "439", "severity": 1, "message": "491", "line": 331, "column": 9, "nodeType": "441", "messageId": "442", "endLine": 331, "endColumn": 33}, {"ruleId": "439", "severity": 1, "message": "492", "line": 351, "column": 9, "nodeType": "441", "messageId": "442", "endLine": 351, "endColumn": 27}, {"ruleId": "439", "severity": 1, "message": "493", "line": 426, "column": 9, "nodeType": "441", "messageId": "442", "endLine": 426, "endColumn": 32}, {"ruleId": "439", "severity": 1, "message": "494", "line": 454, "column": 9, "nodeType": "441", "messageId": "442", "endLine": 454, "endColumn": 25}, {"ruleId": "439", "severity": 1, "message": "495", "line": 477, "column": 9, "nodeType": "441", "messageId": "442", "endLine": 477, "endColumn": 25}, {"ruleId": "439", "severity": 1, "message": "496", "line": 522, "column": 9, "nodeType": "441", "messageId": "442", "endLine": 522, "endColumn": 21}, {"ruleId": "439", "severity": 1, "message": "497", "line": 61, "column": 13, "nodeType": "441", "messageId": "442", "endLine": 61, "endColumn": 19}, {"ruleId": "439", "severity": 1, "message": "454", "line": 1, "column": 38, "nodeType": "441", "messageId": "442", "endLine": 1, "endColumn": 44}, {"ruleId": "439", "severity": 1, "message": "456", "line": 16, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 16, "endColumn": 9}, {"ruleId": "439", "severity": 1, "message": "457", "line": 17, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 17, "endColumn": 15}, {"ruleId": "439", "severity": 1, "message": "458", "line": 21, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 21, "endColumn": 11}, {"ruleId": "439", "severity": 1, "message": "445", "line": 24, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 24, "endColumn": 11}, {"ruleId": "439", "severity": 1, "message": "460", "line": 26, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 26, "endColumn": 8}, {"ruleId": "439", "severity": 1, "message": "455", "line": 28, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 28, "endColumn": 7}, {"ruleId": "439", "severity": 1, "message": "498", "line": 33, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 33, "endColumn": 17}, {"ruleId": "439", "severity": 1, "message": "499", "line": 38, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 38, "endColumn": 16}, {"ruleId": "439", "severity": 1, "message": "500", "line": 40, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 40, "endColumn": 18}, {"ruleId": "439", "severity": 1, "message": "467", "line": 41, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 41, "endColumn": 19}, {"ruleId": "439", "severity": 1, "message": "501", "line": 42, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 42, "endColumn": 18}, {"ruleId": "439", "severity": 1, "message": "464", "line": 43, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 43, "endColumn": 21}, {"ruleId": "439", "severity": 1, "message": "502", "line": 60, "column": 9, "nodeType": "441", "messageId": "442", "endLine": 60, "endColumn": 13}, {"ruleId": "439", "severity": 1, "message": "503", "line": 61, "column": 9, "nodeType": "441", "messageId": "442", "endLine": 61, "endColumn": 16}, {"ruleId": "439", "severity": 1, "message": "468", "line": 79, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 79, "endColumn": 22}, {"ruleId": "439", "severity": 1, "message": "469", "line": 83, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 83, "endColumn": 29}, {"ruleId": "439", "severity": 1, "message": "504", "line": 84, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 84, "endColumn": 29}, {"ruleId": "439", "severity": 1, "message": "505", "line": 85, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 85, "endColumn": 36}, {"ruleId": "439", "severity": 1, "message": "506", "line": 86, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 86, "endColumn": 30}, {"ruleId": "439", "severity": 1, "message": "507", "line": 87, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 87, "endColumn": 35}, {"ruleId": "439", "severity": 1, "message": "508", "line": 88, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 88, "endColumn": 31}, {"ruleId": "439", "severity": 1, "message": "509", "line": 95, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 95, "endColumn": 21}, {"ruleId": "439", "severity": 1, "message": "510", "line": 96, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 96, "endColumn": 28}, {"ruleId": "487", "severity": 1, "message": "488", "line": 116, "column": 6, "nodeType": "489", "endLine": 116, "endColumn": 8, "suggestions": "511"}, {"ruleId": "439", "severity": 1, "message": "512", "line": 348, "column": 9, "nodeType": "441", "messageId": "442", "endLine": 348, "endColumn": 29}, {"ruleId": "439", "severity": 1, "message": "513", "line": 372, "column": 9, "nodeType": "441", "messageId": "442", "endLine": 372, "endColumn": 34}, {"ruleId": "439", "severity": 1, "message": "514", "line": 401, "column": 9, "nodeType": "441", "messageId": "442", "endLine": 401, "endColumn": 31}, {"ruleId": "439", "severity": 1, "message": "454", "line": 1, "column": 38, "nodeType": "441", "messageId": "442", "endLine": 1, "endColumn": 44}, {"ruleId": "439", "severity": 1, "message": "459", "line": 15, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 15, "endColumn": 9}, {"ruleId": "439", "severity": 1, "message": "456", "line": 16, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 16, "endColumn": 9}, {"ruleId": "439", "severity": 1, "message": "457", "line": 17, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 17, "endColumn": 15}, {"ruleId": "439", "severity": 1, "message": "458", "line": 21, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 21, "endColumn": 11}, {"ruleId": "439", "severity": 1, "message": "460", "line": 24, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 24, "endColumn": 8}, {"ruleId": "439", "severity": 1, "message": "515", "line": 30, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 30, "endColumn": 8}, {"ruleId": "439", "severity": 1, "message": "516", "line": 31, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 31, "endColumn": 7}, {"ruleId": "439", "severity": 1, "message": "517", "line": 32, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 32, "endColumn": 9}, {"ruleId": "439", "severity": 1, "message": "466", "line": 43, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 43, "endColumn": 17}, {"ruleId": "439", "severity": 1, "message": "465", "line": 44, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 44, "endColumn": 19}, {"ruleId": "439", "severity": 1, "message": "464", "line": 48, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 48, "endColumn": 21}, {"ruleId": "439", "severity": 1, "message": "518", "line": 49, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 49, "endColumn": 20}, {"ruleId": "439", "severity": 1, "message": "519", "line": 50, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 50, "endColumn": 18}, {"ruleId": "439", "severity": 1, "message": "520", "line": 52, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 52, "endColumn": 20}, {"ruleId": "439", "severity": 1, "message": "521", "line": 56, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 56, "endColumn": 15}, {"ruleId": "439", "severity": 1, "message": "522", "line": 58, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 58, "endColumn": 15}, {"ruleId": "439", "severity": 1, "message": "523", "line": 61, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 61, "endColumn": 18}, {"ruleId": "439", "severity": 1, "message": "503", "line": 69, "column": 9, "nodeType": "441", "messageId": "442", "endLine": 69, "endColumn": 16}, {"ruleId": "439", "severity": 1, "message": "524", "line": 70, "column": 9, "nodeType": "441", "messageId": "442", "endLine": 70, "endColumn": 17}, {"ruleId": "439", "severity": 1, "message": "468", "line": 88, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 88, "endColumn": 22}, {"ruleId": "439", "severity": 1, "message": "469", "line": 92, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 92, "endColumn": 29}, {"ruleId": "439", "severity": 1, "message": "474", "line": 93, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 93, "endColumn": 28}, {"ruleId": "439", "severity": 1, "message": "475", "line": 94, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 94, "endColumn": 33}, {"ruleId": "439", "severity": 1, "message": "525", "line": 95, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 95, "endColumn": 34}, {"ruleId": "439", "severity": 1, "message": "526", "line": 96, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 96, "endColumn": 30}, {"ruleId": "439", "severity": 1, "message": "527", "line": 97, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 97, "endColumn": 27}, {"ruleId": "439", "severity": 1, "message": "528", "line": 98, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 98, "endColumn": 27}, {"ruleId": "439", "severity": 1, "message": "529", "line": 99, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 99, "endColumn": 33}, {"ruleId": "439", "severity": 1, "message": "530", "line": 107, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 107, "endColumn": 26}, {"ruleId": "439", "severity": 1, "message": "531", "line": 108, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 108, "endColumn": 22}, {"ruleId": "439", "severity": 1, "message": "532", "line": 109, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 109, "endColumn": 19}, {"ruleId": "439", "severity": 1, "message": "533", "line": 110, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 110, "endColumn": 19}, {"ruleId": "439", "severity": 1, "message": "534", "line": 111, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 111, "endColumn": 25}, {"ruleId": "439", "severity": 1, "message": "535", "line": 126, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 126, "endColumn": 26}, {"ruleId": "439", "severity": 1, "message": "536", "line": 130, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 130, "endColumn": 19}, {"ruleId": "487", "severity": 1, "message": "488", "line": 137, "column": 6, "nodeType": "489", "endLine": 137, "endColumn": 8, "suggestions": "537"}, {"ruleId": "439", "severity": 1, "message": "493", "line": 455, "column": 9, "nodeType": "441", "messageId": "442", "endLine": 455, "endColumn": 32}, {"ruleId": "439", "severity": 1, "message": "538", "line": 507, "column": 9, "nodeType": "441", "messageId": "442", "endLine": 507, "endColumn": 21}, {"ruleId": "439", "severity": 1, "message": "454", "line": 1, "column": 38, "nodeType": "441", "messageId": "442", "endLine": 1, "endColumn": 44}, {"ruleId": "439", "severity": 1, "message": "455", "line": 17, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 17, "endColumn": 7}, {"ruleId": "439", "severity": 1, "message": "458", "line": 22, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 22, "endColumn": 11}, {"ruleId": "439", "severity": 1, "message": "443", "line": 24, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 24, "endColumn": 10}, {"ruleId": "439", "severity": 1, "message": "523", "line": 35, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 35, "endColumn": 18}, {"ruleId": "439", "severity": 1, "message": "539", "line": 42, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 42, "endColumn": 15}, {"ruleId": "439", "severity": 1, "message": "465", "line": 43, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 43, "endColumn": 19}, {"ruleId": "439", "severity": 1, "message": "466", "line": 44, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 44, "endColumn": 17}, {"ruleId": "439", "severity": 1, "message": "540", "line": 49, "column": 9, "nodeType": "441", "messageId": "442", "endLine": 49, "endColumn": 15}, {"ruleId": "439", "severity": 1, "message": "468", "line": 90, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 90, "endColumn": 22}, {"ruleId": "487", "severity": 1, "message": "541", "line": 119, "column": 6, "nodeType": "489", "endLine": 119, "endColumn": 47, "suggestions": "542"}, {"ruleId": "439", "severity": 1, "message": "445", "line": 21, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 21, "endColumn": 11}, {"ruleId": "439", "severity": 1, "message": "460", "line": 22, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 22, "endColumn": 8}, {"ruleId": "439", "severity": 1, "message": "543", "line": 23, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 23, "endColumn": 10}, {"ruleId": "439", "severity": 1, "message": "444", "line": 24, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 24, "endColumn": 8}, {"ruleId": "439", "severity": 1, "message": "544", "line": 27, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 27, "endColumn": 8}, {"ruleId": "439", "severity": 1, "message": "545", "line": 34, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 34, "endColumn": 21}, {"ruleId": "439", "severity": 1, "message": "546", "line": 36, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 36, "endColumn": 15}, {"ruleId": "439", "severity": 1, "message": "547", "line": 37, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 37, "endColumn": 15}, {"ruleId": "439", "severity": 1, "message": "523", "line": 38, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 38, "endColumn": 18}, {"ruleId": "439", "severity": 1, "message": "548", "line": 39, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 39, "endColumn": 28}, {"ruleId": "439", "severity": 1, "message": "463", "line": 40, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 40, "endColumn": 22}, {"ruleId": "439", "severity": 1, "message": "549", "line": 41, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 41, "endColumn": 22}, {"ruleId": "439", "severity": 1, "message": "501", "line": 42, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 42, "endColumn": 18}, {"ruleId": "439", "severity": 1, "message": "539", "line": 43, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 43, "endColumn": 15}, {"ruleId": "439", "severity": 1, "message": "550", "line": 46, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 46, "endColumn": 13}, {"ruleId": "487", "severity": 1, "message": "551", "line": 222, "column": 6, "nodeType": "489", "endLine": 222, "endColumn": 8, "suggestions": "552"}, {"ruleId": "439", "severity": 1, "message": "459", "line": 13, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 13, "endColumn": 9}, {"ruleId": "439", "severity": 1, "message": "460", "line": 21, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 21, "endColumn": 8}, {"ruleId": "439", "severity": 1, "message": "466", "line": 36, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 36, "endColumn": 17}, {"ruleId": "439", "severity": 1, "message": "553", "line": 38, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 38, "endColumn": 16}, {"ruleId": "439", "severity": 1, "message": "547", "line": 39, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 39, "endColumn": 15}, {"ruleId": "439", "severity": 1, "message": "463", "line": 40, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 40, "endColumn": 22}, {"ruleId": "439", "severity": 1, "message": "549", "line": 41, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 41, "endColumn": 22}, {"ruleId": "439", "severity": 1, "message": "548", "line": 42, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 42, "endColumn": 28}, {"ruleId": "439", "severity": 1, "message": "554", "line": 47, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 47, "endColumn": 17}, {"ruleId": "439", "severity": 1, "message": "555", "line": 48, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 48, "endColumn": 15}, {"ruleId": "439", "severity": 1, "message": "550", "line": 50, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 50, "endColumn": 13}, {"ruleId": "487", "severity": 1, "message": "551", "line": 212, "column": 6, "nodeType": "489", "endLine": 212, "endColumn": 8, "suggestions": "556"}, {"ruleId": "439", "severity": 1, "message": "557", "line": 268, "column": 13, "nodeType": "441", "messageId": "442", "endLine": 268, "endColumn": 23}, {"ruleId": "439", "severity": 1, "message": "558", "line": 13, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 13, "endColumn": 8}, {"ruleId": "439", "severity": 1, "message": "445", "line": 18, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 18, "endColumn": 11}, {"ruleId": "439", "severity": 1, "message": "543", "line": 21, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 21, "endColumn": 10}, {"ruleId": "439", "severity": 1, "message": "559", "line": 33, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 33, "endColumn": 15}, {"ruleId": "439", "severity": 1, "message": "501", "line": 40, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 40, "endColumn": 18}, {"ruleId": "439", "severity": 1, "message": "560", "line": 41, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 41, "endColumn": 22}, {"ruleId": "439", "severity": 1, "message": "539", "line": 42, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 42, "endColumn": 15}, {"ruleId": "439", "severity": 1, "message": "549", "line": 49, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 49, "endColumn": 22}, {"ruleId": "439", "severity": 1, "message": "561", "line": 50, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 50, "endColumn": 15}, {"ruleId": "439", "severity": 1, "message": "520", "line": 51, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 51, "endColumn": 20}, {"ruleId": "439", "severity": 1, "message": "462", "line": 52, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 52, "endColumn": 19}, {"ruleId": "439", "severity": 1, "message": "562", "line": 53, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 53, "endColumn": 19}, {"ruleId": "439", "severity": 1, "message": "563", "line": 58, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 58, "endColumn": 21}, {"ruleId": "439", "severity": 1, "message": "564", "line": 59, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 59, "endColumn": 20}, {"ruleId": "439", "severity": 1, "message": "565", "line": 64, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 64, "endColumn": 16}, {"ruleId": "439", "severity": 1, "message": "566", "line": 65, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 65, "endColumn": 19}, {"ruleId": "439", "severity": 1, "message": "567", "line": 66, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 66, "endColumn": 28}, {"ruleId": "439", "severity": 1, "message": "568", "line": 67, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 67, "endColumn": 14}, {"ruleId": "439", "severity": 1, "message": "569", "line": 69, "column": 16, "nodeType": "441", "messageId": "442", "endLine": 69, "endColumn": 22}, {"ruleId": "439", "severity": 1, "message": "570", "line": 69, "column": 24, "nodeType": "441", "messageId": "442", "endLine": 69, "endColumn": 27}, {"ruleId": "439", "severity": 1, "message": "571", "line": 72, "column": 9, "nodeType": "441", "messageId": "442", "endLine": 72, "endColumn": 15}, {"ruleId": "439", "severity": 1, "message": "503", "line": 73, "column": 9, "nodeType": "441", "messageId": "442", "endLine": 73, "endColumn": 16}, {"ruleId": "439", "severity": 1, "message": "450", "line": 74, "column": 9, "nodeType": "441", "messageId": "442", "endLine": 74, "endColumn": 20}, {"ruleId": "439", "severity": 1, "message": "572", "line": 87, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 87, "endColumn": 14}, {"ruleId": "487", "severity": 1, "message": "573", "line": 280, "column": 6, "nodeType": "489", "endLine": 280, "endColumn": 56, "suggestions": "574"}, {"ruleId": "439", "severity": 1, "message": "575", "line": 400, "column": 9, "nodeType": "441", "messageId": "442", "endLine": 400, "endColumn": 34}, {"ruleId": "439", "severity": 1, "message": "576", "line": 410, "column": 9, "nodeType": "441", "messageId": "442", "endLine": 410, "endColumn": 33}, {"ruleId": "439", "severity": 1, "message": "459", "line": 15, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 15, "endColumn": 9}, {"ruleId": "439", "severity": 1, "message": "445", "line": 19, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 19, "endColumn": 11}, {"ruleId": "439", "severity": 1, "message": "458", "line": 20, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 20, "endColumn": 11}, {"ruleId": "439", "severity": 1, "message": "543", "line": 23, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 23, "endColumn": 10}, {"ruleId": "439", "severity": 1, "message": "443", "line": 29, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 29, "endColumn": 10}, {"ruleId": "439", "severity": 1, "message": "577", "line": 33, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 33, "endColumn": 14}, {"ruleId": "439", "severity": 1, "message": "578", "line": 34, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 34, "endColumn": 11}, {"ruleId": "439", "severity": 1, "message": "544", "line": 35, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 35, "endColumn": 8}, {"ruleId": "439", "severity": 1, "message": "579", "line": 36, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 36, "endColumn": 9}, {"ruleId": "439", "severity": 1, "message": "548", "line": 43, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 43, "endColumn": 28}, {"ruleId": "439", "severity": 1, "message": "549", "line": 44, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 44, "endColumn": 22}, {"ruleId": "439", "severity": 1, "message": "466", "line": 51, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 51, "endColumn": 17}, {"ruleId": "439", "severity": 1, "message": "580", "line": 53, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 53, "endColumn": 18}, {"ruleId": "439", "severity": 1, "message": "523", "line": 54, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 54, "endColumn": 18}, {"ruleId": "439", "severity": 1, "message": "561", "line": 55, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 55, "endColumn": 15}, {"ruleId": "439", "severity": 1, "message": "567", "line": 57, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 57, "endColumn": 28}, {"ruleId": "439", "severity": 1, "message": "581", "line": 58, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 58, "endColumn": 14}, {"ruleId": "439", "severity": 1, "message": "582", "line": 59, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 59, "endColumn": 17}, {"ruleId": "439", "severity": 1, "message": "583", "line": 60, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 60, "endColumn": 22}, {"ruleId": "439", "severity": 1, "message": "584", "line": 61, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 61, "endColumn": 16}, {"ruleId": "439", "severity": 1, "message": "585", "line": 62, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 62, "endColumn": 15}, {"ruleId": "439", "severity": 1, "message": "566", "line": 63, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 63, "endColumn": 19}, {"ruleId": "439", "severity": 1, "message": "521", "line": 67, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 67, "endColumn": 15}, {"ruleId": "439", "severity": 1, "message": "555", "line": 68, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 68, "endColumn": 15}, {"ruleId": "439", "severity": 1, "message": "586", "line": 69, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 69, "endColumn": 17}, {"ruleId": "439", "severity": 1, "message": "587", "line": 71, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 71, "endColumn": 17}, {"ruleId": "439", "severity": 1, "message": "588", "line": 73, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 73, "endColumn": 17}, {"ruleId": "439", "severity": 1, "message": "500", "line": 74, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 74, "endColumn": 18}, {"ruleId": "439", "severity": 1, "message": "589", "line": 75, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 75, "endColumn": 15}, {"ruleId": "439", "severity": 1, "message": "568", "line": 76, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 76, "endColumn": 14}, {"ruleId": "439", "severity": 1, "message": "590", "line": 78, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 78, "endColumn": 22}, {"ruleId": "439", "severity": 1, "message": "502", "line": 89, "column": 9, "nodeType": "441", "messageId": "442", "endLine": 89, "endColumn": 13}, {"ruleId": "439", "severity": 1, "message": "524", "line": 91, "column": 9, "nodeType": "441", "messageId": "442", "endLine": 91, "endColumn": 17}, {"ruleId": "487", "severity": 1, "message": "591", "line": 302, "column": 6, "nodeType": "489", "endLine": 302, "endColumn": 8, "suggestions": "592"}, {"ruleId": "439", "severity": 1, "message": "543", "line": 16, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 16, "endColumn": 10}, {"ruleId": "439", "severity": 1, "message": "443", "line": 24, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 24, "endColumn": 10}, {"ruleId": "439", "severity": 1, "message": "462", "line": 30, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 30, "endColumn": 19}, {"ruleId": "439", "severity": 1, "message": "593", "line": 37, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 37, "endColumn": 14}, {"ruleId": "439", "severity": 1, "message": "594", "line": 39, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 39, "endColumn": 15}, {"ruleId": "439", "severity": 1, "message": "595", "line": 40, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 40, "endColumn": 21}, {"ruleId": "439", "severity": 1, "message": "596", "line": 41, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 41, "endColumn": 17}, {"ruleId": "439", "severity": 1, "message": "597", "line": 43, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 43, "endColumn": 22}, {"ruleId": "439", "severity": 1, "message": "598", "line": 49, "column": 9, "nodeType": "441", "messageId": "442", "endLine": 49, "endColumn": 17}, {"ruleId": "487", "severity": 1, "message": "599", "line": 118, "column": 6, "nodeType": "489", "endLine": 118, "endColumn": 33, "suggestions": "600"}, {"ruleId": "439", "severity": 1, "message": "445", "line": 8, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 8, "endColumn": 11}, {"ruleId": "439", "severity": 1, "message": "460", "line": 24, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 24, "endColumn": 8}, {"ruleId": "439", "severity": 1, "message": "601", "line": 27, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 27, "endColumn": 7}, {"ruleId": "439", "severity": 1, "message": "602", "line": 30, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 30, "endColumn": 8}, {"ruleId": "439", "severity": 1, "message": "603", "line": 31, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 31, "endColumn": 13}, {"ruleId": "439", "severity": 1, "message": "498", "line": 42, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 42, "endColumn": 17}, {"ruleId": "439", "severity": 1, "message": "604", "line": 47, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 47, "endColumn": 19}, {"ruleId": "439", "severity": 1, "message": "605", "line": 48, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 48, "endColumn": 19}, {"ruleId": "439", "severity": 1, "message": "606", "line": 49, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 49, "endColumn": 15}, {"ruleId": "439", "severity": 1, "message": "561", "line": 51, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 51, "endColumn": 15}, {"ruleId": "439", "severity": 1, "message": "465", "line": 53, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 53, "endColumn": 19}, {"ruleId": "439", "severity": 1, "message": "586", "line": 55, "column": 3, "nodeType": "441", "messageId": "442", "endLine": 55, "endColumn": 17}, {"ruleId": "439", "severity": 1, "message": "607", "line": 57, "column": 10, "nodeType": "441", "messageId": "442", "endLine": 57, "endColumn": 26}, {"ruleId": "439", "severity": 1, "message": "450", "line": 63, "column": 9, "nodeType": "441", "messageId": "442", "endLine": 63, "endColumn": 20}, {"ruleId": "439", "severity": 1, "message": "608", "line": 71, "column": 22, "nodeType": "441", "messageId": "442", "endLine": 71, "endColumn": 35}, {"ruleId": "487", "severity": 1, "message": "609", "line": 196, "column": 6, "nodeType": "489", "endLine": 196, "endColumn": 31, "suggestions": "610"}, "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'Divider' is defined but never used.", "'Alert' is defined but never used.", "'Timeline' is defined but never used.", "'LineChart' is defined but never used.", "'Line' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'RangePicker' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "'setDashboardData' is assigned a value but never used.", "'useRef' is defined but never used.", "'Tree' is defined but never used.", "'Drawer' is defined but never used.", "'Descriptions' is defined but never used.", "'Progress' is defined but never used.", "'Upload' is defined but never used.", "'Badge' is defined but never used.", "'CalculatorOutlined' is defined but never used.", "'BarChartOutlined' is defined but never used.", "'CheckCircleOutlined' is defined but never used.", "'InfoCircleOutlined' is defined but never used.", "'DownloadOutlined' is defined but never used.", "'UploadOutlined' is defined but never used.", "'BranchesOutlined' is defined but never used.", "'selectedRows' is assigned a value but never used.", "'detailDrawerVisible' is assigned a value but never used.", "'treeModalVisible' is assigned a value but never used.", "'costAnalysisModalVisible' is assigned a value but never used.", "'requirementModalVisible' is assigned a value but never used.", "'substituteModalVisible' is assigned a value but never used.", "'importModalVisible' is assigned a value but never used.", "'batchUpdateModalVisible' is assigned a value but never used.", "'moveModalVisible' is assigned a value but never used.", "'sortModalVisible' is assigned a value but never used.", "'currentBom' is assigned a value but never used.", "'treeData' is assigned a value but never used.", "'expandedKeys' is assigned a value but never used.", "'setExpandedKeys' is assigned a value but never used.", "'selectedKeys' is assigned a value but never used.", "'setSelectedKeys' is assigned a value but never used.", "'costAnalysisData' is assigned a value but never used.", "'requirementData' is assigned a value but never used.", "'substituteData' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array.", "ArrayExpression", ["611"], "'handleRequirementSummary' is assigned a value but never used.", "'handleCostAnalysis' is assigned a value but never used.", "'handleBatchUpdateSubmit' is assigned a value but never used.", "'handleMoveSubmit' is assigned a value but never used.", "'handleSortSubmit' is assigned a value but never used.", "'handleImport' is assigned a value but never used.", "'result' is assigned a value but never used.", "'DeleteOutlined' is defined but never used.", "'CloseOutlined' is defined but never used.", "'HistoryOutlined' is defined but never used.", "'WarningOutlined' is defined but never used.", "'Step' is assigned a value but never used.", "'TabPane' is assigned a value but never used.", "'historyModalVisible' is assigned a value but never used.", "'impactAnalysisModalVisible' is assigned a value but never used.", "'approvalModalVisible' is assigned a value but never used.", "'batchApprovalModalVisible' is assigned a value but never used.", "'executionModalVisible' is assigned a value but never used.", "'historyData' is assigned a value but never used.", "'impactAnalysisData' is assigned a value but never used.", ["612"], "'handleApprovalSubmit' is assigned a value but never used.", "'handleBatchApprovalSubmit' is assigned a value but never used.", "'handleExecutionConfirm' is assigned a value but never used.", "'Image' is defined but never used.", "'List' is defined but never used.", "'Avatar' is defined but never used.", "'FileExcelOutlined' is defined but never used.", "'PictureOutlined' is defined but never used.", "'LineChartOutlined' is defined but never used.", "'TeamOutlined' is defined but never used.", "'TagsOutlined' is defined but never used.", "'SettingOutlined' is defined but never used.", "'TreeNode' is assigned a value but never used.", "'priceHistoryModalVisible' is assigned a value but never used.", "'supplierModalVisible' is assigned a value but never used.", "'stockModalVisible' is assigned a value but never used.", "'usageModalVisible' is assigned a value but never used.", "'abcAnalysisModalVisible' is assigned a value but never used.", "'priceHistoryData' is assigned a value but never used.", "'supplierData' is assigned a value but never used.", "'stockData' is assigned a value but never used.", "'usageData' is assigned a value but never used.", "'abcAnalysisData' is assigned a value but never used.", "'warehouseOptions' is assigned a value but never used.", "'uploading' is assigned a value but never used.", ["613"], "'handleUpload' is assigned a value but never used.", "'SyncOutlined' is defined but never used.", "'Search' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["614"], "'Tooltip' is defined but never used.", "'Radio' is defined but never used.", "'PlayCircleOutlined' is defined but never used.", "'StopOutlined' is defined but never used.", "'ToolOutlined' is defined but never used.", "'ExclamationCircleOutlined' is defined but never used.", "'ClockCircleOutlined' is defined but never used.", "'api' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["615"], "'TruckOutlined' is defined but never used.", "'CameraOutlined' is defined but never used.", "'StarOutlined' is defined but never used.", ["616"], "'submitData' is assigned a value but never used.", "'Input' is defined but never used.", "'notification' is defined but never used.", "'CloseCircleOutlined' is defined but never used.", "'BellOutlined' is defined but never used.", "'PieChartOutlined' is defined but never used.", "'CaretRightOutlined' is defined but never used.", "'CaretDownOutlined' is defined but never used.", "'CloudOutlined' is defined but never used.", "'DatabaseOutlined' is defined but never used.", "'SafetyCertificateOutlined' is defined but never used.", "'BugOutlined' is defined but never used.", "'Column' is defined but never used.", "'Pie' is defined but never used.", "'Option' is assigned a value but never used.", "'form' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchEquipmentData' and 'updateMonitoringData'. Either include them or remove the dependency array.", ["617"], "'getMaintenanceStatusColor' is assigned a value but never used.", "'getMaintenanceStatusText' is assigned a value but never used.", "'InputNumber' is defined but never used.", "'Checkbox' is defined but never used.", "'Switch' is defined but never used.", "'PrinterOutlined' is defined but never used.", "'CarOutlined' is defined but never used.", "'WrenchOutlined' is defined but never used.", "'ThunderboltOutlined' is defined but never used.", "'HeartOutlined' is defined but never used.", "'FireOutlined' is defined but never used.", "'FilterOutlined' is defined but never used.", "'ReloadOutlined' is defined but never used.", "'ImportOutlined' is defined but never used.", "'BookOutlined' is defined but never used.", "'CloudUploadOutlined' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchEquipmentData', 'fetchMaintenanceData', and 'fetchTechnicianData'. Either include them or remove the dependency array.", ["618"], "'EyeOutlined' is defined but never used.", "'BankOutlined' is defined but never used.", "'CreditCardOutlined' is defined but never used.", "'WalletOutlined' is defined but never used.", "'financialAPI' is defined but never used.", "'TextArea' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadFinancialData'. Either include it or remove the dependency array.", ["619"], "'Rate' is defined but never used.", "'Empty' is defined but never used.", "'Popconfirm' is defined but never used.", "'ShareAltOutlined' is defined but never used.", "'FileTextOutlined' is defined but never used.", "'UserOutlined' is defined but never used.", "'collaborativeAPI' is defined but never used.", "'setStatistics' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadDecisions'. Either include it or remove the dependency array.", ["620"], {"desc": "621", "fix": "622"}, {"desc": "621", "fix": "623"}, {"desc": "621", "fix": "624"}, {"desc": "625", "fix": "626"}, {"desc": "627", "fix": "628"}, {"desc": "627", "fix": "629"}, {"desc": "630", "fix": "631"}, {"desc": "632", "fix": "633"}, {"desc": "634", "fix": "635"}, {"desc": "636", "fix": "637"}, "Update the dependencies array to be: [loadData]", {"range": "638", "text": "639"}, {"range": "640", "text": "639"}, {"range": "641", "text": "639"}, "Update the dependencies array to be: [loadData, pagination.pageSize]", {"range": "642", "text": "643"}, "Update the dependencies array to be: [fetchData]", {"range": "644", "text": "645"}, {"range": "646", "text": "645"}, "Update the dependencies array to be: [fetchEquipmentData, realTimeMode, refreshInterval, selectedEquipment, updateMonitoringData]", {"range": "647", "text": "648"}, "Update the dependencies array to be: [fetchEquipmentData, fetchMaintenanceData, fetchTechnicianData]", {"range": "649", "text": "650"}, "Update the dependencies array to be: [selectedPeriod, dateRange, loadFinancialData]", {"range": "651", "text": "652"}, "Update the dependencies array to be: [activeTab, filterStatus, loadDecisions]", {"range": "653", "text": "654"}, [3409, 3411], "[loadData]", [2862, 2864], [3608, 3610], [2610, 2651], "[loadData, pagination.pageSize]", [7185, 7187], "[fetchData]", [7743, 7745], [6891, 6941], "[fetchEquipmentData, realTimeMode, refreshInterval, selectedEquipment, updateMonitoringData]", [7744, 7746], "[fetchEquipmentData, fetchMaintenanceData, fetchTechnicianData]", [2886, 2913], "[selected<PERSON><PERSON><PERSON>, dateRange, loadFinancialData]", [4337, 4362], "[activeTab, filterStatus, loadDecisions]"]