-- 西安银马实业数字化管理系统
-- 数据库迁移脚本 V1.0.0
-- 初始化数据库架构
-- 创建时间: 2024-01-01
-- 描述: 创建系统基础表结构

-- ===========================================
-- 创建序列
-- ===========================================

-- 用户表序列
CREATE SEQUENCE IF NOT EXISTS seq_sys_user 
    START WITH 1000 
    INCREMENT BY 1 
    NO MINVALUE 
    NO MAXVALUE 
    CACHE 1;

-- BOM表序列
CREATE SEQUENCE IF NOT EXISTS seq_bom 
    START WITH 1000 
    INCREMENT BY 1 
    NO MINVALUE 
    NO MAXVALUE 
    CACHE 1;

-- BOM明细表序列
CREATE SEQUENCE IF NOT EXISTS seq_bom_detail 
    START WITH 1000 
    INCREMENT BY 1 
    NO MINVALUE 
    NO MAXVALUE 
    CACHE 1;

-- 物料表序列
CREATE SEQUENCE IF NOT EXISTS seq_material 
    START WITH 1000 
    INCREMENT BY 1 
    NO MINVALUE 
    NO MAXVALUE 
    CACHE 1;

-- BOM变更日志表序列
CREATE SEQUENCE IF NOT EXISTS seq_bom_change_log 
    START WITH 1000 
    INCREMENT BY 1 
    NO MINVALUE 
    NO MAXVALUE 
    CACHE 1;

-- ===========================================
-- 系统管理表
-- ===========================================

-- 用户表
CREATE TABLE sys_user (
    id BIGINT PRIMARY KEY DEFAULT nextval('seq_sys_user'),
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    real_name VARCHAR(50) NOT NULL,
    nickname VARCHAR(50),
    email VARCHAR(100),
    phone VARCHAR(20),
    gender CHAR(1) DEFAULT 'U',
    avatar VARCHAR(500),
    dept_id BIGINT,
    dept_name VARCHAR(100),
    position VARCHAR(100),
    role_ids VARCHAR(500),
    role_names VARCHAR(500),
    status VARCHAR(20) DEFAULT 'ACTIVE',
    last_login_time TIMESTAMP,
    last_login_ip VARCHAR(50),
    login_fail_count INTEGER DEFAULT 0,
    lock_time TIMESTAMP,
    password_expire_time TIMESTAMP,
    remark TEXT,
    create_by VARCHAR(50),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(50),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted INTEGER DEFAULT 0
);

-- 用户表注释
COMMENT ON TABLE sys_user IS '系统用户表';
COMMENT ON COLUMN sys_user.id IS '主键ID';
COMMENT ON COLUMN sys_user.username IS '用户名';
COMMENT ON COLUMN sys_user.password IS '密码';
COMMENT ON COLUMN sys_user.real_name IS '真实姓名';
COMMENT ON COLUMN sys_user.nickname IS '昵称';
COMMENT ON COLUMN sys_user.email IS '邮箱';
COMMENT ON COLUMN sys_user.phone IS '手机号';
COMMENT ON COLUMN sys_user.gender IS '性别：M-男，F-女，U-未知';
COMMENT ON COLUMN sys_user.avatar IS '头像';
COMMENT ON COLUMN sys_user.dept_id IS '部门ID';
COMMENT ON COLUMN sys_user.dept_name IS '部门名称';
COMMENT ON COLUMN sys_user.position IS '职位';
COMMENT ON COLUMN sys_user.role_ids IS '角色ID列表（逗号分隔）';
COMMENT ON COLUMN sys_user.role_names IS '角色名称列表（逗号分隔）';
COMMENT ON COLUMN sys_user.status IS '状态：ACTIVE-启用，INACTIVE-停用，LOCKED-锁定';
COMMENT ON COLUMN sys_user.last_login_time IS '最后登录时间';
COMMENT ON COLUMN sys_user.last_login_ip IS '最后登录IP';
COMMENT ON COLUMN sys_user.login_fail_count IS '登录失败次数';
COMMENT ON COLUMN sys_user.lock_time IS '账户锁定时间';
COMMENT ON COLUMN sys_user.password_expire_time IS '密码过期时间';
COMMENT ON COLUMN sys_user.remark IS '备注';
COMMENT ON COLUMN sys_user.create_by IS '创建人';
COMMENT ON COLUMN sys_user.create_time IS '创建时间';
COMMENT ON COLUMN sys_user.update_by IS '更新人';
COMMENT ON COLUMN sys_user.update_time IS '更新时间';
COMMENT ON COLUMN sys_user.deleted IS '删除标志（0代表存在 1代表删除）';

-- ===========================================
-- BOM管理表
-- ===========================================

-- BOM主表
CREATE TABLE bom (
    id BIGINT PRIMARY KEY DEFAULT nextval('seq_bom'),
    bom_code VARCHAR(50) NOT NULL UNIQUE,
    bom_name VARCHAR(200) NOT NULL,
    product_code VARCHAR(50) NOT NULL,
    product_name VARCHAR(200) NOT NULL,
    bom_type VARCHAR(20) NOT NULL,
    version VARCHAR(20) NOT NULL,
    status VARCHAR(20) DEFAULT 'DRAFT',
    effective_date TIMESTAMP,
    expiry_date TIMESTAMP,
    base_quantity DECIMAL(18,6) NOT NULL DEFAULT 1,
    unit VARCHAR(20) NOT NULL,
    remark TEXT,
    create_by VARCHAR(50),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(50),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted INTEGER DEFAULT 0
);

-- BOM主表注释
COMMENT ON TABLE bom IS 'BOM主表';
COMMENT ON COLUMN bom.id IS '主键ID';
COMMENT ON COLUMN bom.bom_code IS 'BOM编码';
COMMENT ON COLUMN bom.bom_name IS 'BOM名称';
COMMENT ON COLUMN bom.product_code IS '产品编码';
COMMENT ON COLUMN bom.product_name IS '产品名称';
COMMENT ON COLUMN bom.bom_type IS 'BOM类型：EBOM-工程BOM，PBOM-工艺BOM，MBOM-制造BOM';
COMMENT ON COLUMN bom.version IS 'BOM版本';
COMMENT ON COLUMN bom.status IS '状态：DRAFT-草稿，ACTIVE-生效，INACTIVE-失效';
COMMENT ON COLUMN bom.effective_date IS '生效日期';
COMMENT ON COLUMN bom.expiry_date IS '失效日期';
COMMENT ON COLUMN bom.base_quantity IS '基本数量';
COMMENT ON COLUMN bom.unit IS '计量单位';
COMMENT ON COLUMN bom.remark IS '备注';
COMMENT ON COLUMN bom.create_by IS '创建人';
COMMENT ON COLUMN bom.create_time IS '创建时间';
COMMENT ON COLUMN bom.update_by IS '更新人';
COMMENT ON COLUMN bom.update_time IS '更新时间';
COMMENT ON COLUMN bom.deleted IS '删除标志（0代表存在 1代表删除）';

-- BOM明细表
CREATE TABLE bom_detail (
    id BIGINT PRIMARY KEY DEFAULT nextval('seq_bom_detail'),
    bom_id BIGINT NOT NULL,
    parent_material_code VARCHAR(50) NOT NULL,
    child_material_code VARCHAR(50) NOT NULL,
    child_material_name VARCHAR(200) NOT NULL,
    specification VARCHAR(200),
    model VARCHAR(100),
    quantity DECIMAL(18,6) NOT NULL,
    unit VARCHAR(20) NOT NULL,
    loss_rate DECIMAL(5,2) DEFAULT 0,
    substitute_flag INTEGER DEFAULT 0,
    substitute_group VARCHAR(50),
    priority INTEGER DEFAULT 1,
    level INTEGER DEFAULT 1,
    sequence INTEGER DEFAULT 1,
    effective_date TIMESTAMP,
    expiry_date TIMESTAMP,
    remark TEXT,
    create_by VARCHAR(50),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(50),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted INTEGER DEFAULT 0,
    FOREIGN KEY (bom_id) REFERENCES bom(id)
);

-- BOM明细表注释
COMMENT ON TABLE bom_detail IS 'BOM明细表';
COMMENT ON COLUMN bom_detail.id IS '主键ID';
COMMENT ON COLUMN bom_detail.bom_id IS 'BOM主表ID';
COMMENT ON COLUMN bom_detail.parent_material_code IS '父级物料编码';
COMMENT ON COLUMN bom_detail.child_material_code IS '子级物料编码';
COMMENT ON COLUMN bom_detail.child_material_name IS '子级物料名称';
COMMENT ON COLUMN bom_detail.specification IS '物料规格';
COMMENT ON COLUMN bom_detail.model IS '物料型号';
COMMENT ON COLUMN bom_detail.quantity IS '需求数量';
COMMENT ON COLUMN bom_detail.unit IS '计量单位';
COMMENT ON COLUMN bom_detail.loss_rate IS '损耗率(%)';
COMMENT ON COLUMN bom_detail.substitute_flag IS '替代料标识：0-主料，1-替代料';
COMMENT ON COLUMN bom_detail.substitute_group IS '替代料组';
COMMENT ON COLUMN bom_detail.priority IS '优先级';
COMMENT ON COLUMN bom_detail.level IS '层级';
COMMENT ON COLUMN bom_detail.sequence IS '序号';
COMMENT ON COLUMN bom_detail.effective_date IS '生效日期';
COMMENT ON COLUMN bom_detail.expiry_date IS '失效日期';
COMMENT ON COLUMN bom_detail.remark IS '备注';
COMMENT ON COLUMN bom_detail.create_by IS '创建人';
COMMENT ON COLUMN bom_detail.create_time IS '创建时间';
COMMENT ON COLUMN bom_detail.update_by IS '更新人';
COMMENT ON COLUMN bom_detail.update_time IS '更新时间';
COMMENT ON COLUMN bom_detail.deleted IS '删除标志（0代表存在 1代表删除）';

-- ===========================================
-- 物料管理表
-- ===========================================

-- 物料主数据表
CREATE TABLE material (
    id BIGINT PRIMARY KEY DEFAULT nextval('seq_material'),
    material_code VARCHAR(50) NOT NULL UNIQUE,
    material_name VARCHAR(200) NOT NULL,
    short_name VARCHAR(100),
    category VARCHAR(100) NOT NULL,
    material_type VARCHAR(20) NOT NULL,
    specification VARCHAR(200),
    model VARCHAR(100),
    brand VARCHAR(100),
    base_unit VARCHAR(20) NOT NULL,
    purchase_unit VARCHAR(20),
    stock_unit VARCHAR(20),
    sales_unit VARCHAR(20),
    standard_cost DECIMAL(18,4),
    latest_purchase_price DECIMAL(18,4),
    average_purchase_price DECIMAL(18,4),
    sales_price DECIMAL(18,4),
    safety_stock DECIMAL(18,6),
    min_stock DECIMAL(18,6),
    max_stock DECIMAL(18,6),
    lead_time INTEGER,
    abc_category CHAR(1),
    main_supplier_code VARCHAR(50),
    main_supplier_name VARCHAR(200),
    quality_grade VARCHAR(20),
    shelf_life INTEGER,
    storage_condition VARCHAR(200),
    batch_management INTEGER DEFAULT 0,
    serial_management INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    remark TEXT,
    create_by VARCHAR(50),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(50),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted INTEGER DEFAULT 0
);

-- 物料主数据表注释
COMMENT ON TABLE material IS '物料主数据表';
COMMENT ON COLUMN material.id IS '主键ID';
COMMENT ON COLUMN material.material_code IS '物料编码';
COMMENT ON COLUMN material.material_name IS '物料名称';
COMMENT ON COLUMN material.short_name IS '物料简称';
COMMENT ON COLUMN material.category IS '物料分类';
COMMENT ON COLUMN material.material_type IS '物料类型：RAW-原材料，SEMI-半成品，FINISHED-成品，SPARE-备件';
COMMENT ON COLUMN material.specification IS '规格型号';
COMMENT ON COLUMN material.model IS '型号';
COMMENT ON COLUMN material.brand IS '品牌';
COMMENT ON COLUMN material.base_unit IS '基本计量单位';
COMMENT ON COLUMN material.purchase_unit IS '采购计量单位';
COMMENT ON COLUMN material.stock_unit IS '库存计量单位';
COMMENT ON COLUMN material.sales_unit IS '销售计量单位';
COMMENT ON COLUMN material.standard_cost IS '标准成本';
COMMENT ON COLUMN material.latest_purchase_price IS '最新采购价';
COMMENT ON COLUMN material.average_purchase_price IS '平均采购价';
COMMENT ON COLUMN material.sales_price IS '销售价格';
COMMENT ON COLUMN material.safety_stock IS '安全库存';
COMMENT ON COLUMN material.min_stock IS '最小库存';
COMMENT ON COLUMN material.max_stock IS '最大库存';
COMMENT ON COLUMN material.lead_time IS '采购提前期(天)';
COMMENT ON COLUMN material.abc_category IS 'ABC分类：A-重要，B-一般，C-次要';
COMMENT ON COLUMN material.main_supplier_code IS '主供应商编码';
COMMENT ON COLUMN material.main_supplier_name IS '主供应商名称';
COMMENT ON COLUMN material.quality_grade IS '质量等级';
COMMENT ON COLUMN material.shelf_life IS '保质期(天)';
COMMENT ON COLUMN material.storage_condition IS '存储条件';
COMMENT ON COLUMN material.batch_management IS '是否批次管理：0-否，1-是';
COMMENT ON COLUMN material.serial_management IS '是否序列号管理：0-否，1-是';
COMMENT ON COLUMN material.status IS '状态：ACTIVE-启用，INACTIVE-停用';
COMMENT ON COLUMN material.remark IS '备注';
COMMENT ON COLUMN material.create_by IS '创建人';
COMMENT ON COLUMN material.create_time IS '创建时间';
COMMENT ON COLUMN material.update_by IS '更新人';
COMMENT ON COLUMN material.update_time IS '更新时间';
COMMENT ON COLUMN material.deleted IS '删除标志（0代表存在 1代表删除）';

-- ===========================================
-- BOM变更管理表
-- ===========================================

-- BOM变更日志表
CREATE TABLE bom_change_log (
    id BIGINT PRIMARY KEY DEFAULT nextval('seq_bom_change_log'),
    change_no VARCHAR(50) NOT NULL UNIQUE,
    bom_code VARCHAR(50) NOT NULL,
    bom_name VARCHAR(200) NOT NULL,
    change_type VARCHAR(20) NOT NULL,
    change_object VARCHAR(20) NOT NULL,
    change_field VARCHAR(100),
    old_value TEXT,
    new_value TEXT,
    change_reason VARCHAR(500) NOT NULL,
    change_description TEXT,
    impact_analysis TEXT,
    change_status VARCHAR(20) DEFAULT 'PENDING',
    applicant VARCHAR(50) NOT NULL,
    apply_time TIMESTAMP NOT NULL,
    reviewer VARCHAR(50),
    review_time TIMESTAMP,
    review_comment TEXT,
    implementer VARCHAR(50),
    implement_time TIMESTAMP,
    implement_result TEXT,
    remark TEXT,
    create_by VARCHAR(50),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(50),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted INTEGER DEFAULT 0
);

-- BOM变更日志表注释
COMMENT ON TABLE bom_change_log IS 'BOM变更日志表';
COMMENT ON COLUMN bom_change_log.id IS '主键ID';
COMMENT ON COLUMN bom_change_log.change_no IS '变更单号';
COMMENT ON COLUMN bom_change_log.bom_code IS 'BOM编码';
COMMENT ON COLUMN bom_change_log.bom_name IS 'BOM名称';
COMMENT ON COLUMN bom_change_log.change_type IS '变更类型：ADD-新增，UPDATE-修改，DELETE-删除';
COMMENT ON COLUMN bom_change_log.change_object IS '变更对象：BOM-BOM主表，DETAIL-BOM明细';
COMMENT ON COLUMN bom_change_log.change_field IS '变更字段';
COMMENT ON COLUMN bom_change_log.old_value IS '变更前值';
COMMENT ON COLUMN bom_change_log.new_value IS '变更后值';
COMMENT ON COLUMN bom_change_log.change_reason IS '变更原因';
COMMENT ON COLUMN bom_change_log.change_description IS '变更描述';
COMMENT ON COLUMN bom_change_log.impact_analysis IS '影响分析';
COMMENT ON COLUMN bom_change_log.change_status IS '变更状态：PENDING-待审核，APPROVED-已批准，REJECTED-已拒绝，IMPLEMENTED-已实施';
COMMENT ON COLUMN bom_change_log.applicant IS '申请人';
COMMENT ON COLUMN bom_change_log.apply_time IS '申请时间';
COMMENT ON COLUMN bom_change_log.reviewer IS '审核人';
COMMENT ON COLUMN bom_change_log.review_time IS '审核时间';
COMMENT ON COLUMN bom_change_log.review_comment IS '审核意见';
COMMENT ON COLUMN bom_change_log.implementer IS '实施人';
COMMENT ON COLUMN bom_change_log.implement_time IS '实施时间';
COMMENT ON COLUMN bom_change_log.implement_result IS '实施结果';
COMMENT ON COLUMN bom_change_log.remark IS '备注';
COMMENT ON COLUMN bom_change_log.create_by IS '创建人';
COMMENT ON COLUMN bom_change_log.create_time IS '创建时间';
COMMENT ON COLUMN bom_change_log.update_by IS '更新人';
COMMENT ON COLUMN bom_change_log.update_time IS '更新时间';
COMMENT ON COLUMN bom_change_log.deleted IS '删除标志（0代表存在 1代表删除）';

-- ===========================================
-- 创建索引
-- ===========================================

-- 用户表索引
CREATE INDEX idx_sys_user_username ON sys_user(username);
CREATE INDEX idx_sys_user_email ON sys_user(email);
CREATE INDEX idx_sys_user_phone ON sys_user(phone);
CREATE INDEX idx_sys_user_dept_id ON sys_user(dept_id);
CREATE INDEX idx_sys_user_status ON sys_user(status);
CREATE INDEX idx_sys_user_deleted ON sys_user(deleted);

-- BOM主表索引
CREATE INDEX idx_bom_code ON bom(bom_code);
CREATE INDEX idx_bom_product_code ON bom(product_code);
CREATE INDEX idx_bom_type ON bom(bom_type);
CREATE INDEX idx_bom_status ON bom(status);
CREATE INDEX idx_bom_version ON bom(version);
CREATE INDEX idx_bom_deleted ON bom(deleted);

-- BOM明细表索引
CREATE INDEX idx_bom_detail_bom_id ON bom_detail(bom_id);
CREATE INDEX idx_bom_detail_parent_code ON bom_detail(parent_material_code);
CREATE INDEX idx_bom_detail_child_code ON bom_detail(child_material_code);
CREATE INDEX idx_bom_detail_level ON bom_detail(level);
CREATE INDEX idx_bom_detail_deleted ON bom_detail(deleted);

-- 物料主数据表索引
CREATE INDEX idx_material_code ON material(material_code);
CREATE INDEX idx_material_name ON material(material_name);
CREATE INDEX idx_material_category ON material(category);
CREATE INDEX idx_material_type ON material(material_type);
CREATE INDEX idx_material_abc_category ON material(abc_category);
CREATE INDEX idx_material_status ON material(status);
CREATE INDEX idx_material_deleted ON material(deleted);

-- BOM变更日志表索引
CREATE INDEX idx_bom_change_log_change_no ON bom_change_log(change_no);
CREATE INDEX idx_bom_change_log_bom_code ON bom_change_log(bom_code);
CREATE INDEX idx_bom_change_log_change_type ON bom_change_log(change_type);
CREATE INDEX idx_bom_change_log_change_status ON bom_change_log(change_status);
CREATE INDEX idx_bom_change_log_applicant ON bom_change_log(applicant);
CREATE INDEX idx_bom_change_log_apply_time ON bom_change_log(apply_time);
CREATE INDEX idx_bom_change_log_deleted ON bom_change_log(deleted);

-- 输出完成信息
SELECT 'V1.0.0 数据库架构创建完成！' AS message;