#!/bin/bash

# 西安银马实业数字化管理系统
# 数据库部署脚本
# 支持PostgreSQL数据库的创建、初始化、迁移和备份

# 设置脚本参数
set -e  # 遇到错误立即退出
set -u  # 使用未定义变量时报错

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 默认配置
DEFAULT_DB_HOST="localhost"
DEFAULT_DB_PORT="5432"
DEFAULT_DB_NAME="yinma_management"
DEFAULT_DB_USER="postgres"
DEFAULT_ADMIN_DB="postgres"

# 从环境变量或使用默认值
DB_HOST=${DB_HOST:-$DEFAULT_DB_HOST}
DB_PORT=${DB_PORT:-$DEFAULT_DB_PORT}
DB_NAME=${DB_NAME:-$DEFAULT_DB_NAME}
DB_USER=${DB_USER:-$DEFAULT_DB_USER}
ADMIN_DB=${ADMIN_DB:-$DEFAULT_ADMIN_DB}

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MIGRATION_DIR="$SCRIPT_DIR/migration"
BACKUP_DIR="$SCRIPT_DIR/backup"

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 显示帮助信息
show_help() {
    cat << EOF
西安银马实业数字化管理系统 - 数据库部署脚本

用法: $0 [选项] <命令>

命令:
  init        初始化数据库（创建数据库和用户）
  migrate     执行数据库迁移
  seed        插入初始化数据
  backup      备份数据库
  restore     恢复数据库
  reset       重置数据库（删除并重新创建）
  status      检查数据库状态
  help        显示此帮助信息

选项:
  -h, --host      数据库主机 (默认: $DEFAULT_DB_HOST)
  -p, --port      数据库端口 (默认: $DEFAULT_DB_PORT)
  -d, --database  数据库名称 (默认: $DEFAULT_DB_NAME)
  -u, --user      数据库用户 (默认: $DEFAULT_DB_USER)
  -w, --password  数据库密码 (提示输入)
  --admin-db      管理员数据库 (默认: $DEFAULT_ADMIN_DB)
  --force         强制执行（跳过确认）
  --dry-run       模拟执行（不实际操作）
  --verbose       详细输出
  --help          显示帮助信息

环境变量:
  DB_HOST         数据库主机
  DB_PORT         数据库端口
  DB_NAME         数据库名称
  DB_USER         数据库用户
  DB_PASSWORD     数据库密码
  ADMIN_DB        管理员数据库

示例:
  $0 init                           # 初始化数据库
  $0 migrate                        # 执行迁移
  $0 backup                         # 备份数据库
  $0 -h ************* -d yinma init # 指定主机和数据库名初始化
  $0 --force reset                  # 强制重置数据库

EOF
}

# 检查PostgreSQL客户端工具
check_postgresql_tools() {
    log_info "检查PostgreSQL客户端工具..."
    
    if ! command -v psql &> /dev/null; then
        log_error "psql 命令未找到，请安装PostgreSQL客户端工具"
        exit 1
    fi
    
    if ! command -v pg_dump &> /dev/null; then
        log_error "pg_dump 命令未找到，请安装PostgreSQL客户端工具"
        exit 1
    fi
    
    log_success "PostgreSQL客户端工具检查通过"
}

# 检查数据库连接
check_database_connection() {
    log_info "检查数据库连接..."
    
    if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$ADMIN_DB" -c "SELECT 1;" &> /dev/null; then
        log_success "数据库连接成功"
        return 0
    else
        log_error "无法连接到数据库服务器"
        return 1
    fi
}

# 检查数据库是否存在
check_database_exists() {
    local db_name="$1"
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$ADMIN_DB" -tAc "SELECT 1 FROM pg_database WHERE datname='$db_name';" | grep -q 1
}

# 创建数据库
create_database() {
    log_info "创建数据库 '$DB_NAME'..."
    
    if check_database_exists "$DB_NAME"; then
        log_warning "数据库 '$DB_NAME' 已存在"
        return 0
    fi
    
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$ADMIN_DB" -c "CREATE DATABASE \"$DB_NAME\" WITH ENCODING='UTF8' LC_COLLATE='zh_CN.UTF-8' LC_CTYPE='zh_CN.UTF-8';"
    
    if [ $? -eq 0 ]; then
        log_success "数据库 '$DB_NAME' 创建成功"
    else
        log_error "数据库 '$DB_NAME' 创建失败"
        exit 1
    fi
}

# 删除数据库
drop_database() {
    log_info "删除数据库 '$DB_NAME'..."
    
    if ! check_database_exists "$DB_NAME"; then
        log_warning "数据库 '$DB_NAME' 不存在"
        return 0
    fi
    
    # 终止所有连接
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$ADMIN_DB" -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = '$DB_NAME';"
    
    # 删除数据库
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$ADMIN_DB" -c "DROP DATABASE IF EXISTS \"$DB_NAME\";"
    
    if [ $? -eq 0 ]; then
        log_success "数据库 '$DB_NAME' 删除成功"
    else
        log_error "数据库 '$DB_NAME' 删除失败"
        exit 1
    fi
}

# 执行SQL文件
execute_sql_file() {
    local sql_file="$1"
    local description="$2"
    
    if [ ! -f "$sql_file" ]; then
        log_error "SQL文件不存在: $sql_file"
        return 1
    fi
    
    log_info "执行 $description: $(basename "$sql_file")"
    
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "$sql_file"
    
    if [ $? -eq 0 ]; then
        log_success "$description 执行成功"
    else
        log_error "$description 执行失败"
        return 1
    fi
}

# 初始化数据库
init_database() {
    log_info "开始初始化数据库..."
    
    check_postgresql_tools
    check_database_connection
    create_database
    
    log_success "数据库初始化完成"
}

# 执行数据库迁移
migrate_database() {
    log_info "开始执行数据库迁移..."
    
    if [ ! -d "$MIGRATION_DIR" ]; then
        log_error "迁移目录不存在: $MIGRATION_DIR"
        exit 1
    fi
    
    # 按版本号排序执行迁移文件
    for migration_file in $(ls "$MIGRATION_DIR"/V*.sql 2>/dev/null | sort -V); do
        execute_sql_file "$migration_file" "数据库迁移"
    done
    
    log_success "数据库迁移完成"
}

# 插入初始化数据
seed_database() {
    log_info "开始插入初始化数据..."
    
    if [ -f "$SCRIPT_DIR/init.sql" ]; then
        execute_sql_file "$SCRIPT_DIR/init.sql" "初始化数据"
    else
        log_warning "初始化数据文件不存在: $SCRIPT_DIR/init.sql"
    fi
    
    log_success "初始化数据插入完成"
}

# 备份数据库
backup_database() {
    local backup_file="$BACKUP_DIR/yinma_backup_$(date +%Y%m%d_%H%M%S).sql"
    
    log_info "开始备份数据库到: $backup_file"
    
    PGPASSWORD="$DB_PASSWORD" pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" --verbose --clean --no-owner --no-privileges > "$backup_file"
    
    if [ $? -eq 0 ]; then
        log_success "数据库备份完成: $backup_file"
        
        # 压缩备份文件
        gzip "$backup_file"
        log_success "备份文件已压缩: ${backup_file}.gz"
        
        # 清理旧备份（保留最近30个）
        ls -t "$BACKUP_DIR"/yinma_backup_*.sql.gz 2>/dev/null | tail -n +31 | xargs -r rm
        log_info "已清理旧备份文件"
    else
        log_error "数据库备份失败"
        exit 1
    fi
}

# 恢复数据库
restore_database() {
    local backup_file="$1"
    
    if [ -z "$backup_file" ]; then
        log_error "请指定备份文件路径"
        exit 1
    fi
    
    if [ ! -f "$backup_file" ]; then
        log_error "备份文件不存在: $backup_file"
        exit 1
    fi
    
    log_info "开始从备份文件恢复数据库: $backup_file"
    
    # 如果是压缩文件，先解压
    if [[ "$backup_file" == *.gz ]]; then
        local temp_file="${backup_file%.gz}"
        gunzip -c "$backup_file" > "$temp_file"
        backup_file="$temp_file"
    fi
    
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "$backup_file"
    
    if [ $? -eq 0 ]; then
        log_success "数据库恢复完成"
    else
        log_error "数据库恢复失败"
        exit 1
    fi
}

# 重置数据库
reset_database() {
    log_warning "即将重置数据库 '$DB_NAME'，所有数据将被删除！"
    
    if [ "$FORCE" != "true" ]; then
        read -p "确认继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "操作已取消"
            exit 0
        fi
    fi
    
    drop_database
    init_database
    migrate_database
    seed_database
    
    log_success "数据库重置完成"
}

# 检查数据库状态
check_database_status() {
    log_info "检查数据库状态..."
    
    check_postgresql_tools
    
    if check_database_connection; then
        log_success "数据库服务器连接正常"
    else
        log_error "无法连接到数据库服务器"
        exit 1
    fi
    
    if check_database_exists "$DB_NAME"; then
        log_success "数据库 '$DB_NAME' 存在"
        
        # 检查表是否存在
        local table_count=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -tAc "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';")
        log_info "数据库中共有 $table_count 个表"
        
        # 检查数据量
        if [ "$table_count" -gt 0 ]; then
            log_info "主要表数据量:"
            for table in sys_user material bom bom_detail bom_change_log; do
                local count=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -tAc "SELECT COUNT(*) FROM $table WHERE deleted = 0;" 2>/dev/null || echo "0")
                log_info "  $table: $count 条记录"
            done
        fi
    else
        log_warning "数据库 '$DB_NAME' 不存在"
    fi
}

# 解析命令行参数
FORCE="false"
DRY_RUN="false"
VERBOSE="false"

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--host)
            DB_HOST="$2"
            shift 2
            ;;
        -p|--port)
            DB_PORT="$2"
            shift 2
            ;;
        -d|--database)
            DB_NAME="$2"
            shift 2
            ;;
        -u|--user)
            DB_USER="$2"
            shift 2
            ;;
        -w|--password)
            read -s -p "请输入数据库密码: " DB_PASSWORD
            echo
            shift
            ;;
        --admin-db)
            ADMIN_DB="$2"
            shift 2
            ;;
        --force)
            FORCE="true"
            shift
            ;;
        --dry-run)
            DRY_RUN="true"
            shift
            ;;
        --verbose)
            VERBOSE="true"
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        -*)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
        *)
            break
            ;;
    esac
done

# 如果没有提供密码，尝试从环境变量获取
if [ -z "${DB_PASSWORD:-}" ]; then
    if [ -n "${PGPASSWORD:-}" ]; then
        DB_PASSWORD="$PGPASSWORD"
    else
        read -s -p "请输入数据库密码: " DB_PASSWORD
        echo
    fi
fi

# 获取命令
COMMAND="${1:-help}"

# 显示配置信息
if [ "$VERBOSE" = "true" ]; then
    log_info "配置信息:"
    log_info "  数据库主机: $DB_HOST"
    log_info "  数据库端口: $DB_PORT"
    log_info "  数据库名称: $DB_NAME"
    log_info "  数据库用户: $DB_USER"
    log_info "  管理员数据库: $ADMIN_DB"
    log_info "  脚本目录: $SCRIPT_DIR"
    log_info "  迁移目录: $MIGRATION_DIR"
    log_info "  备份目录: $BACKUP_DIR"
fi

# 执行命令
case $COMMAND in
    init)
        init_database
        ;;
    migrate)
        migrate_database
        ;;
    seed)
        seed_database
        ;;
    backup)
        backup_database
        ;;
    restore)
        restore_database "$2"
        ;;
    reset)
        reset_database
        ;;
    status)
        check_database_status
        ;;
    help)
        show_help
        ;;
    *)
        log_error "未知命令: $COMMAND"
        show_help
        exit 1
        ;;
esac

log_success "操作完成！"