package com.yinma.service;

import com.yinma.dto.LoginRequest;
import com.yinma.dto.LoginResponse;

/**
 * 认证服务接口
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
public interface AuthService {

    /**
     * 用户登录
     * 
     * @param request 登录请求
     * @return 登录响应
     */
    LoginResponse login(LoginRequest request);

    /**
     * 刷新访问令牌
     * 
     * @param refreshToken 刷新令牌
     * @return 新的令牌信息
     */
    LoginResponse refreshToken(String refreshToken);

    /**
     * 验证用户凭证
     * 
     * @param username 用户名
     * @param password 密码
     * @return 是否验证成功
     */
    boolean validateCredentials(String username, String password);

    /**
     * 生成令牌
     * 
     * @param username 用户名
     * @return 令牌信息
     */
    LoginResponse generateTokens(String username);

    /**
     * 记录登录日志
     * 
     * @param username 用户名
     * @param success 是否成功
     * @param ip IP地址
     * @param userAgent 用户代理
     */
    void recordLoginLog(String username, boolean success, String ip, String userAgent);
}