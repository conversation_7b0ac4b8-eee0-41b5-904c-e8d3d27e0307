{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\g2\\src\\spec\\dataTransform.ts"], "sourcesContent": ["import { DataComponent } from '../runtime';\n\nexport type DataTransform =\n  | SortByTransform\n  | SortTransform\n  | PickTransform\n  | RenameTransform\n  | FoldTransform\n  | JoinTransform\n  | FilterDataTransform\n  | MapTransform\n  | SliceTransform\n  | KDEDataTransform\n  | VennDataTransform\n  | LogDataTransform\n  | CustomTransform\n  | EMADataTransform;\n\nexport type DataTransformTypes =\n  | 'sortBy'\n  | 'sort'\n  | 'pick'\n  | 'rename'\n  | 'fold'\n  | 'join'\n  | 'filter'\n  | 'map'\n  | 'slice'\n  | 'kde'\n  | 'venn'\n  | 'log'\n  | 'custom'\n  | 'ema'\n  | DataComponent;\n\nexport type SortByTransform = {\n  type?: 'sortBy';\n  /** type: [field, order]; order: true => ascend, false => descend */\n  fields?: (string | [string, boolean?])[];\n};\n\nexport type PickTransform = {\n  type?: 'pick';\n  fields?: string[];\n};\n\nexport type RenameTransform = {\n  type?: 'rename';\n  [key: string]: string;\n};\n\nexport type FilterDataTransform = {\n  type?: 'filter';\n  /**\n   * The filter condition, same with [Array.prototype.filter](https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Array/filter).\n   */\n  callback?: (v: any, idx: number, arr: any[]) => boolean;\n};\n\nexport type SliceTransform = {\n  type?: 'slice';\n  /**\n   * The start index for slice. Default is 0.\n   */\n  start?: number;\n  /**\n   * The end index for slice. Default is arr.length - 1\n   */\n  end?: number;\n};\n\nexport type SortTransform = {\n  type?: 'sort';\n  /**\n   * The sort comparator, same with [Array.prototype.sort](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort).\n   */\n  callback?: (a: any, b: any) => number;\n};\n\nexport type FoldTransform = {\n  type?: 'fold';\n  /**\n   * Set fields will be folded.\n   */\n  fields?: string[];\n  /**\n   * Fold key field, default is `key`.\n   */\n  key?: string;\n  /**\n   * Fold value field, default is `value`.\n   */\n  value?: string;\n};\n\nexport type JoinTransform = {\n  type?: 'join';\n  /**\n   * The dataset to be joined.\n   */\n  join: Record<string, any>[];\n  /**\n   * Join keys of 2 dataset, [k1, k2] means join on ds1.k1 === ds2.k2.\n   */\n  on: [string | ((d: any) => string), string | ((d: any) => string)];\n  /**\n   * Select fields from joined dataset.\n   */\n  select: string[];\n  /**\n   * Rename the select fields, default: keep the original name.\n   */\n  as?: string[];\n  /**\n   * When not matched, use `unknown` instead.\n   */\n  unknown?: any;\n};\n\nexport type MapTransform = {\n  type?: 'map';\n  callback?: (d: any) => any;\n};\n\nexport type CustomDataTransform = {\n  type?: 'custom';\n  callback?: (d: any) => any;\n};\n\nexport type KDEDataTransform = {\n  type?: 'kde';\n  /**\n   * Kernel Density Estimation field.\n   */\n  field: string;\n  /**\n   * Group data by fields.\n   */\n  groupBy: string[];\n  /**\n   * Generate new fieds, default: ['y', 'size']\n   */\n  as?: ['y', 'size'];\n  /**\n   * Defaults to smallest value in the array minus some threshold.\n   */\n  min?: number;\n  /**\n   * Defaults to largest value in the array plus some threshold.\n   */\n  max?: number;\n  /**\n   * Number of points to represent the pdf. Defaults to 10.\n   */\n  size?: number;\n  /**\n   * Determine how many points to the left and right does an element affect,\n   * similar to bandwidth in kernel density estimation. Defaults to 2.\n   */\n  width?: number;\n};\n\nexport type VennDataTransform = {\n  type?: 'venn';\n  /**\n   * Canvas padding for 4 direction.\n   * Default is `0`.\n   */\n  padding?: number;\n  /**\n   * Set the sets field.\n   * Default is `sets`.\n   */\n  sets?: string;\n  /**\n   * Set the size field for each set.\n   * Default is `size`.\n   */\n  size?: string;\n  /**\n   * Set the generated fields, includes: [key, x, y, path]\n   */\n  as?: [string, string];\n};\n\n// eslint-disable-next-line\nexport type LogDataTransform = {};\n\nexport type CustomTransform = {\n  type?: DataComponent;\n  [key: string]: any;\n};\n\nexport type EMADataTransform = {\n  type?: 'ema';\n  field?: string; // The field to be smoothed, default: 'y'\n  alpha?: number; // smooth factor, default: 0.6\n  as?: string; // Set the generated field, default: 'y'\n};\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}