package com.yinma.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinma.common.Result;
import com.yinma.dto.BomDetailDTO;
import com.yinma.entity.BomDetailEntity;
import com.yinma.service.BomDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * BOM明细管理Controller
 * 
 * <AUTHOR>
 * @since 2024-01-20
 */
@Api(tags = "BOM明细管理")
@RestController
@RequestMapping("/api/bom-detail")
public class BomDetailController {

    @Autowired
    private BomDetailService bomDetailService;

    @ApiOperation("分页查询BOM明细列表")
    @GetMapping("/page")
    public Result<IPage<BomDetailDTO>> queryPage(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("BOM ID") @RequestParam(required = false) Long bomId,
            @ApiParam("物料编码") @RequestParam(required = false) String materialCode,
            @ApiParam("物料名称") @RequestParam(required = false) String materialName,
            @ApiParam("物料分类") @RequestParam(required = false) String materialCategory,
            @ApiParam("是否关键物料") @RequestParam(required = false) Boolean isKeyMaterial) {
        
        Page<BomDetailEntity> page = new Page<>(current, size);
        BomDetailDTO.BomDetailQueryDTO queryDTO = new BomDetailDTO.BomDetailQueryDTO();
        queryDTO.setBomId(bomId);
        queryDTO.setMaterialCode(materialCode);
        queryDTO.setMaterialName(materialName);
        queryDTO.setMaterialCategory(materialCategory);
        queryDTO.setIsKeyMaterial(isKeyMaterial);
        
        IPage<BomDetailDTO> result = bomDetailService.queryPage(page, queryDTO);
        return Result.success(result);
    }

    @ApiOperation("获取BOM明细树形结构")
    @GetMapping("/tree/{bomId}")
    public Result<List<BomDetailDTO.BomDetailTreeDTO>> getDetailTree(
            @ApiParam("BOM ID") @PathVariable Long bomId,
            @ApiParam("展开层级") @RequestParam(defaultValue = "3") Integer expandLevel) {
        
        List<BomDetailDTO.BomDetailTreeDTO> tree = bomDetailService.getDetailTree(bomId, expandLevel);
        return Result.success(tree);
    }

    @ApiOperation("获取BOM明细详情")
    @GetMapping("/{detailId}")
    public Result<BomDetailDTO> getDetailInfo(
            @ApiParam("明细ID") @PathVariable Long detailId) {
        
        BomDetailDTO detail = bomDetailService.getDetailInfo(detailId);
        if (detail == null) {
            return Result.error("BOM明细不存在");
        }
        return Result.success(detail);
    }

    @ApiOperation("创建BOM明细")
    @PostMapping
    public Result<BomDetailDTO> createDetail(@Valid @RequestBody BomDetailDTO bomDetailDTO) {
        BomDetailDTO result = bomDetailService.createDetail(bomDetailDTO);
        return Result.success(result);
    }

    @ApiOperation("更新BOM明细")
    @PutMapping("/{detailId}")
    public Result<BomDetailDTO> updateDetail(
            @ApiParam("明细ID") @PathVariable Long detailId,
            @Valid @RequestBody BomDetailDTO bomDetailDTO) {
        
        bomDetailDTO.setDetailId(detailId);
        BomDetailDTO result = bomDetailService.updateDetail(bomDetailDTO);
        return Result.success(result);
    }

    @ApiOperation("删除BOM明细")
    @DeleteMapping("/{detailId}")
    public Result<Boolean> deleteDetail(
            @ApiParam("明细ID") @PathVariable Long detailId) {
        
        boolean success = bomDetailService.deleteDetail(detailId);
        return Result.success(success);
    }

    @ApiOperation("批量删除BOM明细")
    @DeleteMapping("/batch")
    public Result<Boolean> batchDeleteDetails(
            @ApiParam("明细ID列表") @RequestBody List<Long> detailIds) {
        
        boolean success = bomDetailService.batchDeleteDetails(detailIds);
        return Result.success(success);
    }

    @ApiOperation("复制BOM明细")
    @PostMapping("/copy/{detailId}")
    public Result<BomDetailDTO> copyDetail(
            @ApiParam("源明细ID") @PathVariable Long detailId,
            @ApiParam("目标BOM ID") @RequestParam Long targetBomId,
            @ApiParam("目标父明细ID") @RequestParam(required = false) Long targetParentId) {
        
        BomDetailDTO result = bomDetailService.copyDetail(detailId, targetBomId, targetParentId);
        return Result.success(result);
    }

    @ApiOperation("移动BOM明细")
    @PutMapping("/move/{detailId}")
    public Result<Boolean> moveDetail(
            @ApiParam("明细ID") @PathVariable Long detailId,
            @ApiParam("新父明细ID") @RequestParam(required = false) Long newParentId,
            @ApiParam("新排序号") @RequestParam(required = false) Integer newSortOrder) {
        
        boolean success = bomDetailService.moveDetail(detailId, newParentId, newSortOrder);
        return Result.success(success);
    }

    @ApiOperation("调整BOM明细排序")
    @PutMapping("/sort")
    public Result<Boolean> sortDetails(
            @ApiParam("排序信息列表") @RequestBody List<BomDetailDTO.DetailSortDTO> sortList) {
        
        boolean success = bomDetailService.sortDetails(sortList);
        return Result.success(success);
    }

    @ApiOperation("计算物料需求汇总")
    @GetMapping("/material-requirement/{bomId}")
    public Result<List<BomDetailDTO.MaterialRequirementDTO>> calculateMaterialRequirement(
            @ApiParam("BOM ID") @PathVariable Long bomId,
            @ApiParam("生产数量") @RequestParam BigDecimal productionQuantity,
            @ApiParam("是否包含损耗") @RequestParam(defaultValue = "true") Boolean includeLoss) {
        
        List<BomDetailDTO.MaterialRequirementDTO> requirements = 
            bomDetailService.calculateMaterialRequirement(bomId, productionQuantity, includeLoss);
        return Result.success(requirements);
    }

    @ApiOperation("分析成本构成")
    @GetMapping("/cost-analysis/{bomId}")
    public Result<BomDetailDTO.CostAnalysisDTO> analyzeCost(
            @ApiParam("BOM ID") @PathVariable Long bomId,
            @ApiParam("生产数量") @RequestParam(defaultValue = "1") BigDecimal productionQuantity) {
        
        BomDetailDTO.CostAnalysisDTO analysis = bomDetailService.analyzeCost(bomId, productionQuantity);
        return Result.success(analysis);
    }

    @ApiOperation("查询关键物料")
    @GetMapping("/key-materials/{bomId}")
    public Result<List<BomDetailDTO.KeyMaterialDTO>> getKeyMaterials(
            @ApiParam("BOM ID") @PathVariable Long bomId) {
        
        List<BomDetailDTO.KeyMaterialDTO> keyMaterials = bomDetailService.getKeyMaterials(bomId);
        return Result.success(keyMaterials);
    }

    @ApiOperation("查询替代物料")
    @GetMapping("/substitutes/{detailId}")
    public Result<List<BomDetailDTO.SubstituteMaterialDTO>> getSubstituteMaterials(
            @ApiParam("明细ID") @PathVariable Long detailId) {
        
        List<BomDetailDTO.SubstituteMaterialDTO> substitutes = bomDetailService.getSubstituteMaterials(detailId);
        return Result.success(substitutes);
    }

    @ApiOperation("设置替代物料")
    @PostMapping("/substitutes/{detailId}")
    public Result<Boolean> setSubstituteMaterials(
            @ApiParam("明细ID") @PathVariable Long detailId,
            @ApiParam("替代物料列表") @RequestBody List<BomDetailDTO.SubstituteMaterialDTO> substitutes) {
        
        boolean success = bomDetailService.setSubstituteMaterials(detailId, substitutes);
        return Result.success(success);
    }

    @ApiOperation("批量更新用量")
    @PutMapping("/batch-update-quantity")
    public Result<Boolean> batchUpdateQuantity(
            @ApiParam("更新列表") @RequestBody List<BomDetailDTO.QuantityUpdateDTO> updateList) {
        
        boolean success = bomDetailService.batchUpdateQuantity(updateList);
        return Result.success(success);
    }

    @ApiOperation("批量更新损耗率")
    @PutMapping("/batch-update-loss-rate")
    public Result<Boolean> batchUpdateLossRate(
            @ApiParam("更新列表") @RequestBody List<BomDetailDTO.LossRateUpdateDTO> updateList) {
        
        boolean success = bomDetailService.batchUpdateLossRate(updateList);
        return Result.success(success);
    }

    @ApiOperation("检查循环引用")
    @GetMapping("/check-circular/{bomId}")
    public Result<BomDetailDTO.CircularReferenceCheckDTO> checkCircularReference(
            @ApiParam("BOM ID") @PathVariable Long bomId,
            @ApiParam("要添加的物料ID") @RequestParam Long materialId) {
        
        BomDetailDTO.CircularReferenceCheckDTO checkResult = 
            bomDetailService.checkCircularReference(bomId, materialId);
        return Result.success(checkResult);
    }

    @ApiOperation("获取BOM层级结构")
    @GetMapping("/level-structure/{bomId}")
    public Result<List<BomDetailDTO.BomLevelDTO>> getBomLevelStructure(
            @ApiParam("BOM ID") @PathVariable Long bomId) {
        
        List<BomDetailDTO.BomLevelDTO> levelStructure = bomDetailService.getBomLevelStructure(bomId);
        return Result.success(levelStructure);
    }

    @ApiOperation("导出BOM明细")
    @GetMapping("/export/{bomId}")
    public Result<String> exportBomDetails(
            @ApiParam("BOM ID") @PathVariable Long bomId,
            @ApiParam("导出格式") @RequestParam(defaultValue = "EXCEL") String format) {
        
        try {
            byte[] data = bomDetailService.exportBomDetails(bomId, format);
            // TODO: 返回文件下载链接或直接返回文件流
            return Result.success("导出成功");
        } catch (Exception e) {
            return Result.error("导出失败: " + e.getMessage());
        }
    }

    @ApiOperation("导入BOM明细")
    @PostMapping("/import/{bomId}")
    public Result<BomDetailDTO.ImportResultDTO> importBomDetails(
            @ApiParam("BOM ID") @PathVariable Long bomId,
            @ApiParam("导入数据") @RequestBody List<BomDetailDTO> detailList) {
        
        try {
            BomDetailDTO.ImportResultDTO result = bomDetailService.importBomDetails(bomId, detailList);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("导入失败: " + e.getMessage());
        }
    }

    @ApiOperation("校验BOM明细数据")
    @PostMapping("/validate")
    public Result<BomDetailDTO.ValidationResultDTO> validateBomDetails(
            @ApiParam("明细数据列表") @RequestBody List<BomDetailDTO> detailList) {
        
        BomDetailDTO.ValidationResultDTO result = bomDetailService.validateBomDetails(detailList);
        return Result.success(result);
    }

    @ApiOperation("获取BOM明细统计信息")
    @GetMapping("/statistics/{bomId}")
    public Result<BomDetailDTO.BomDetailStatisticsDTO> getBomDetailStatistics(
            @ApiParam("BOM ID") @PathVariable Long bomId) {
        
        BomDetailDTO.BomDetailStatisticsDTO statistics = bomDetailService.getBomDetailStatistics(bomId);
        return Result.success(statistics);
    }

    @ApiOperation("对比两个BOM的明细差异")
    @GetMapping("/compare")
    public Result<BomDetailDTO.BomComparisonDTO> compareBomDetails(
            @ApiParam("源BOM ID") @RequestParam Long sourceBomId,
            @ApiParam("目标BOM ID") @RequestParam Long targetBomId) {
        
        BomDetailDTO.BomComparisonDTO comparison = bomDetailService.compareBomDetails(sourceBomId, targetBomId);
        return Result.success(comparison);
    }

    @ApiOperation("获取物料在BOM中的使用情况")
    @GetMapping("/material-usage/{materialId}")
    public Result<List<BomDetailDTO.MaterialUsageInBomDTO>> getMaterialUsageInBom(
            @ApiParam("物料ID") @PathVariable Long materialId) {
        
        List<BomDetailDTO.MaterialUsageInBomDTO> usage = bomDetailService.getMaterialUsageInBom(materialId);
        return Result.success(usage);
    }

    @ApiOperation("查找可替代的BOM明细")
    @GetMapping("/find-alternatives/{detailId}")
    public Result<List<BomDetailDTO.AlternativeDetailDTO>> findAlternativeDetails(
            @ApiParam("明细ID") @PathVariable Long detailId) {
        
        List<BomDetailDTO.AlternativeDetailDTO> alternatives = bomDetailService.findAlternativeDetails(detailId);
        return Result.success(alternatives);
    }

    @ApiOperation("优化BOM结构")
    @PostMapping("/optimize/{bomId}")
    public Result<BomDetailDTO.OptimizationResultDTO> optimizeBomStructure(
            @ApiParam("BOM ID") @PathVariable Long bomId,
            @ApiParam("优化选项") @RequestBody BomDetailDTO.OptimizationOptionsDTO options) {
        
        BomDetailDTO.OptimizationResultDTO result = bomDetailService.optimizeBomStructure(bomId, options);
        return Result.success(result);
    }
}