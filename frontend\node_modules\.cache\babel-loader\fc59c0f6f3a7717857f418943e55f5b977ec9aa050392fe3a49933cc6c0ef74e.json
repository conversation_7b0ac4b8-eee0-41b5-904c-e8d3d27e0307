{"ast": null, "code": "import { deepMix } from '@antv/util';\nimport { groupSort, max, min, sum, mean, median, sort } from '@antv/vendor/d3-array';\nimport { columnOf } from './utils/helper';\nfunction createReducer(channel, options, encode) {\n  const {\n    by = channel,\n    reducer = 'max'\n  } = options;\n  const [V] = columnOf(encode, by);\n  if (typeof reducer === 'function') return GI => reducer(GI, V);\n  if (reducer === 'max') return GI => max(GI, i => +V[i]);\n  if (reducer === 'min') return GI => min(GI, i => +V[i]);\n  if (reducer === 'sum') return GI => sum(GI, i => +V[i]);\n  if (reducer === 'median') return GI => median(GI, i => +V[i]);\n  if (reducer === 'mean') return GI => mean(GI, i => +V[i]);\n  if (reducer === 'first') return GI => V[GI[0]];\n  if (reducer === 'last') return GI => V[GI[GI.length - 1]];\n  throw new Error(`Unknown reducer: ${reducer}`);\n}\n// If domain is specified, only sort data in the domain.\nfunction filterIndex(I, values, specifiedDomain) {\n  if (!Array.isArray(specifiedDomain)) return I;\n  const domain = new Set(specifiedDomain);\n  return I.filter(i => domain.has(values[i]));\n}\n/**\n * Sort marks groups by groups.\n */\nexport const Sort = (options = {}) => {\n  return (I, mark) => {\n    const {\n      reverse,\n      slice,\n      channel,\n      by,\n      ordinal = true,\n      reducer\n    } = options;\n    const {\n      encode,\n      scale = {}\n    } = mark;\n    const domain = scale[channel].domain;\n    const [V] = columnOf(encode, by !== null && by !== void 0 ? by : channel);\n    const [T] = columnOf(encode, channel);\n    const normalizeReducer = createReducer(channel, {\n      by,\n      reducer\n    }, encode);\n    const SI = filterIndex(I, T, domain);\n    const sortedDomain = groupSort(SI, normalizeReducer, i => T[i]);\n    // when ordinal is true, do not change the index of the data.\n    const sortedI = !ordinal ? sort(I, i => V[i]) : I;\n    if (reverse) {\n      !ordinal && sortedI.reverse();\n      sortedDomain.reverse();\n    }\n    const s = typeof slice === 'number' ? [0, slice] : slice;\n    const slicedDomain = slice ? sortedDomain.slice(...s) : sortedDomain;\n    return [sortedI, deepMix(mark, {\n      scale: {\n        [channel]: {\n          domain: slicedDomain\n        }\n      }\n    })];\n  };\n};\nSort.props = {};", "map": {"version": 3, "names": ["deepMix", "groupSort", "max", "min", "sum", "mean", "median", "sort", "columnOf", "createReducer", "channel", "options", "encode", "by", "reducer", "V", "GI", "i", "length", "Error", "filterIndex", "I", "values", "specifiedDomain", "Array", "isArray", "domain", "Set", "filter", "has", "Sort", "mark", "reverse", "slice", "ordinal", "scale", "T", "normalizeReducer", "SI", "sortedDomain", "sortedI", "s", "slicedDomain", "props"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\g2\\src\\transform\\sort.ts"], "sourcesContent": ["import { deepMix } from '@antv/util';\nimport {\n  Primitive,\n  groupSort,\n  max,\n  min,\n  sum,\n  mean,\n  median,\n  sort,\n} from '@antv/vendor/d3-array';\nimport { G2Mark, TransformComponent as TC } from '../runtime';\nimport { columnOf } from './utils/helper';\n\nfunction createReducer(channel, options, encode): (I: number[]) => any {\n  const { by = channel, reducer = 'max' } = options;\n  const [V] = columnOf(encode, by);\n  if (typeof reducer === 'function') return (GI: number[]) => reducer(GI, V);\n  if (reducer === 'max') return (GI: number[]) => max(GI, (i) => +V[i]);\n  if (reducer === 'min') return (GI: number[]) => min(GI, (i) => +V[i]);\n  if (reducer === 'sum') return (GI: number[]) => sum(GI, (i) => +V[i]);\n  if (reducer === 'median') return (GI: number[]) => median(GI, (i) => +V[i]);\n  if (reducer === 'mean') return (GI: number[]) => mean(GI, (i) => +V[i]);\n  if (reducer === 'first') return (GI: number[]) => V[GI[0]];\n  if (reducer === 'last') return (GI: number[]) => V[GI[GI.length - 1]];\n  throw new Error(`Unknown reducer: ${reducer}`);\n}\n\nexport type SortOptions = {\n  by?: string;\n  reverse?: boolean;\n  channel?: string;\n  slice?: number | [number, number];\n  ordinal?: boolean;\n  reducer?:\n    | 'max'\n    | 'min'\n    | 'sum'\n    | 'first'\n    | 'last'\n    | 'mean'\n    | 'median'\n    | ((I: number[], V: Primitive[]) => Primitive);\n};\n\n// If domain is specified, only sort data in the domain.\nfunction filterIndex(I, values, specifiedDomain): number[] {\n  if (!Array.isArray(specifiedDomain)) return I;\n  const domain = new Set(specifiedDomain);\n  return I.filter((i) => domain.has(values[i]));\n}\n\n/**\n * Sort marks groups by groups.\n */\nexport const Sort: TC<SortOptions> = (options = {}) => {\n  return (I, mark) => {\n    const { reverse, slice, channel, by, ordinal = true, reducer } = options;\n    const { encode, scale = {} } = mark;\n    const domain = scale[channel].domain;\n    const [V] = columnOf(encode, by ?? channel);\n    const [T] = columnOf(encode, channel);\n    const normalizeReducer = createReducer(channel, { by, reducer }, encode);\n    const SI = filterIndex(I, T, domain);\n    const sortedDomain = groupSort(SI, normalizeReducer, (i: number) => T[i]);\n\n    // when ordinal is true, do not change the index of the data.\n    const sortedI = !ordinal ? sort(I, (i: number) => V[i]) : I;\n    if (reverse) {\n      !ordinal && sortedI.reverse();\n      sortedDomain.reverse();\n    }\n    const s = typeof slice === 'number' ? [0, slice] : slice;\n    const slicedDomain = slice ? sortedDomain.slice(...s) : sortedDomain;\n    return [\n      sortedI,\n      deepMix(mark, { scale: { [channel]: { domain: slicedDomain } } }),\n    ];\n  };\n};\n\nSort.props = {};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAEEC,SAAS,EACTC,GAAG,EACHC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,MAAM,EACNC,IAAI,QACC,uBAAuB;AAE9B,SAASC,QAAQ,QAAQ,gBAAgB;AAEzC,SAASC,aAAaA,CAACC,OAAO,EAAEC,OAAO,EAAEC,MAAM;EAC7C,MAAM;IAAEC,EAAE,GAAGH,OAAO;IAAEI,OAAO,GAAG;EAAK,CAAE,GAAGH,OAAO;EACjD,MAAM,CAACI,CAAC,CAAC,GAAGP,QAAQ,CAACI,MAAM,EAAEC,EAAE,CAAC;EAChC,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE,OAAQE,EAAY,IAAKF,OAAO,CAACE,EAAE,EAAED,CAAC,CAAC;EAC1E,IAAID,OAAO,KAAK,KAAK,EAAE,OAAQE,EAAY,IAAKd,GAAG,CAACc,EAAE,EAAGC,CAAC,IAAK,CAACF,CAAC,CAACE,CAAC,CAAC,CAAC;EACrE,IAAIH,OAAO,KAAK,KAAK,EAAE,OAAQE,EAAY,IAAKb,GAAG,CAACa,EAAE,EAAGC,CAAC,IAAK,CAACF,CAAC,CAACE,CAAC,CAAC,CAAC;EACrE,IAAIH,OAAO,KAAK,KAAK,EAAE,OAAQE,EAAY,IAAKZ,GAAG,CAACY,EAAE,EAAGC,CAAC,IAAK,CAACF,CAAC,CAACE,CAAC,CAAC,CAAC;EACrE,IAAIH,OAAO,KAAK,QAAQ,EAAE,OAAQE,EAAY,IAAKV,MAAM,CAACU,EAAE,EAAGC,CAAC,IAAK,CAACF,CAAC,CAACE,CAAC,CAAC,CAAC;EAC3E,IAAIH,OAAO,KAAK,MAAM,EAAE,OAAQE,EAAY,IAAKX,IAAI,CAACW,EAAE,EAAGC,CAAC,IAAK,CAACF,CAAC,CAACE,CAAC,CAAC,CAAC;EACvE,IAAIH,OAAO,KAAK,OAAO,EAAE,OAAQE,EAAY,IAAKD,CAAC,CAACC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC1D,IAAIF,OAAO,KAAK,MAAM,EAAE,OAAQE,EAAY,IAAKD,CAAC,CAACC,EAAE,CAACA,EAAE,CAACE,MAAM,GAAG,CAAC,CAAC,CAAC;EACrE,MAAM,IAAIC,KAAK,CAAC,oBAAoBL,OAAO,EAAE,CAAC;AAChD;AAmBA;AACA,SAASM,WAAWA,CAACC,CAAC,EAAEC,MAAM,EAAEC,eAAe;EAC7C,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,eAAe,CAAC,EAAE,OAAOF,CAAC;EAC7C,MAAMK,MAAM,GAAG,IAAIC,GAAG,CAACJ,eAAe,CAAC;EACvC,OAAOF,CAAC,CAACO,MAAM,CAAEX,CAAC,IAAKS,MAAM,CAACG,GAAG,CAACP,MAAM,CAACL,CAAC,CAAC,CAAC,CAAC;AAC/C;AAEA;;;AAGA,OAAO,MAAMa,IAAI,GAAoBA,CAACnB,OAAO,GAAG,EAAE,KAAI;EACpD,OAAO,CAACU,CAAC,EAAEU,IAAI,KAAI;IACjB,MAAM;MAAEC,OAAO;MAAEC,KAAK;MAAEvB,OAAO;MAAEG,EAAE;MAAEqB,OAAO,GAAG,IAAI;MAAEpB;IAAO,CAAE,GAAGH,OAAO;IACxE,MAAM;MAAEC,MAAM;MAAEuB,KAAK,GAAG;IAAE,CAAE,GAAGJ,IAAI;IACnC,MAAML,MAAM,GAAGS,KAAK,CAACzB,OAAO,CAAC,CAACgB,MAAM;IACpC,MAAM,CAACX,CAAC,CAAC,GAAGP,QAAQ,CAACI,MAAM,EAAEC,EAAE,aAAFA,EAAE,cAAFA,EAAE,GAAIH,OAAO,CAAC;IAC3C,MAAM,CAAC0B,CAAC,CAAC,GAAG5B,QAAQ,CAACI,MAAM,EAAEF,OAAO,CAAC;IACrC,MAAM2B,gBAAgB,GAAG5B,aAAa,CAACC,OAAO,EAAE;MAAEG,EAAE;MAAEC;IAAO,CAAE,EAAEF,MAAM,CAAC;IACxE,MAAM0B,EAAE,GAAGlB,WAAW,CAACC,CAAC,EAAEe,CAAC,EAAEV,MAAM,CAAC;IACpC,MAAMa,YAAY,GAAGtC,SAAS,CAACqC,EAAE,EAAED,gBAAgB,EAAGpB,CAAS,IAAKmB,CAAC,CAACnB,CAAC,CAAC,CAAC;IAEzE;IACA,MAAMuB,OAAO,GAAG,CAACN,OAAO,GAAG3B,IAAI,CAACc,CAAC,EAAGJ,CAAS,IAAKF,CAAC,CAACE,CAAC,CAAC,CAAC,GAAGI,CAAC;IAC3D,IAAIW,OAAO,EAAE;MACX,CAACE,OAAO,IAAIM,OAAO,CAACR,OAAO,EAAE;MAC7BO,YAAY,CAACP,OAAO,EAAE;;IAExB,MAAMS,CAAC,GAAG,OAAOR,KAAK,KAAK,QAAQ,GAAG,CAAC,CAAC,EAAEA,KAAK,CAAC,GAAGA,KAAK;IACxD,MAAMS,YAAY,GAAGT,KAAK,GAAGM,YAAY,CAACN,KAAK,CAAC,GAAGQ,CAAC,CAAC,GAAGF,YAAY;IACpE,OAAO,CACLC,OAAO,EACPxC,OAAO,CAAC+B,IAAI,EAAE;MAAEI,KAAK,EAAE;QAAE,CAACzB,OAAO,GAAG;UAAEgB,MAAM,EAAEgB;QAAY;MAAE;IAAE,CAAE,CAAC,CAClE;EACH,CAAC;AACH,CAAC;AAEDZ,IAAI,CAACa,KAAK,GAAG,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}