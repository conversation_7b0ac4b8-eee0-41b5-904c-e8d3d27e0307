package com.yinma.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * BOM明细实体类
 * 记录BOM中的具体物料信息和用量关系
 * 
 * <AUTHOR> System
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("bom_detail")
public class BomDetailEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 明细主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * BOM主表ID
     */
    @TableField("bom_id")
    private Long bomId;

    /**
     * 物料编码
     */
    @TableField("material_code")
    private String materialCode;

    /**
     * 物料名称
     */
    @TableField("material_name")
    private String materialName;

    /**
     * 物料规格
     */
    @TableField("material_spec")
    private String materialSpec;

    /**
     * 物料类型：RAW-原材料，SEMI-半成品，FINISHED-成品，PURCHASE-外购件
     */
    @TableField("material_type")
    private String materialType;

    /**
     * 物料分类
     */
    @TableField("material_category")
    private String materialCategory;

    /**
     * 序号（在BOM中的排序）
     */
    @TableField("sequence_no")
    private Integer sequenceNo;

    /**
     * 父级明细ID（用于多层BOM结构）
     */
    @TableField("parent_detail_id")
    private Long parentDetailId;

    /**
     * BOM层级
     */
    @TableField("level")
    private Integer level;

    /**
     * 需求数量
     */
    @TableField("required_quantity")
    private BigDecimal requiredQuantity;

    /**
     * 基本单位
     */
    @TableField("base_unit")
    private String baseUnit;

    /**
     * 损耗率（%）
     */
    @TableField("scrap_rate")
    private BigDecimal scrapRate;

    /**
     * 实际需求数量（含损耗）
     */
    @TableField("actual_quantity")
    private BigDecimal actualQuantity;

    /**
     * 单位成本
     */
    @TableField("unit_cost")
    private BigDecimal unitCost;

    /**
     * 总成本
     */
    @TableField("total_cost")
    private BigDecimal totalCost;

    /**
     * 供应商编码
     */
    @TableField("supplier_code")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @TableField("supplier_name")
    private String supplierName;

    /**
     * 采购提前期（天）
     */
    @TableField("lead_time")
    private Integer leadTime;

    /**
     * 最小采购量
     */
    @TableField("min_order_qty")
    private BigDecimal minOrderQty;

    /**
     * 安全库存
     */
    @TableField("safety_stock")
    private BigDecimal safetyStock;

    /**
     * 工艺工序号
     */
    @TableField("operation_no")
    private String operationNo;

    /**
     * 工作中心
     */
    @TableField("work_center")
    private String workCenter;

    /**
     * 投料点：START-开工投料，PROCESS-过程投料，END-完工投料
     */
    @TableField("issue_point")
    private String issuePoint;

    /**
     * 是否关键物料
     */
    @TableField("is_critical")
    private Boolean isCritical;

    /**
     * 是否可替代
     */
    @TableField("is_substitutable")
    private Boolean isSubstitutable;

    /**
     * 替代物料组
     */
    @TableField("substitute_group")
    private String substituteGroup;

    /**
     * 生效日期
     */
    @TableField("effective_date")
    private LocalDateTime effectiveDate;

    /**
     * 失效日期
     */
    @TableField("expiry_date")
    private LocalDateTime expiryDate;

    /**
     * 变更原因
     */
    @TableField("change_reason")
    private String changeReason;

    /**
     * 备注说明
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 物料主数据（非数据库字段）
     */
    @TableField(exist = false)
    private MaterialEntity material;

    /**
     * 子级明细列表（非数据库字段）
     */
    @TableField(exist = false)
    private java.util.List<BomDetailEntity> children;

    /**
     * 是否为叶子节点（非数据库字段）
     */
    @TableField(exist = false)
    private Boolean isLeaf;

    /**
     * 完整路径（非数据库字段）
     */
    @TableField(exist = false)
    private String fullPath;

    /**
     * 累计需求数量（非数据库字段）
     */
    @TableField(exist = false)
    private BigDecimal cumulativeQuantity;

    /**
     * 物料类型枚举
     */
    public enum MaterialType {
        RAW("RAW", "原材料"),
        SEMI("SEMI", "半成品"),
        FINISHED("FINISHED", "成品"),
        PURCHASE("PURCHASE", "外购件");

        private final String code;
        private final String name;

        MaterialType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * 投料点枚举
     */
    public enum IssuePoint {
        START("START", "开工投料"),
        PROCESS("PROCESS", "过程投料"),
        END("END", "完工投料");

        private final String code;
        private final String name;

        IssuePoint(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }
}