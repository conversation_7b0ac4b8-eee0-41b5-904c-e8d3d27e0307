package com.yinma.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinma.entity.MaterialEntity;
import com.yinma.dto.MaterialDTO;
import java.util.List;
import java.util.Map;

/**
 * 物料主数据管理Service接口
 * 提供物料信息管理、库存管理、价格管理等业务逻辑处理
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
public interface MaterialService extends IService<MaterialEntity> {

    /**
     * 分页查询物料信息
     * 
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Page<MaterialDTO> queryPage(MaterialDTO.MaterialQueryDTO queryDTO);

    /**
     * 根据ID查询物料详情
     * 
     * @param materialId 物料ID
     * @return 物料详情
     */
    MaterialDTO getMaterialDetail(Long materialId);

    /**
     * 根据编码查询物料信息
     * 
     * @param materialCode 物料编码
     * @return 物料信息
     */
    MaterialDTO getMaterialByCode(String materialCode);

    /**
     * 创建物料
     * 
     * @param materialDTO 物料信息
     * @return 物料ID
     */
    Long createMaterial(MaterialDTO materialDTO);

    /**
     * 更新物料信息
     * 
     * @param materialDTO 物料信息
     * @return 是否成功
     */
    Boolean updateMaterial(MaterialDTO materialDTO);

    /**
     * 删除物料
     * 
     * @param materialId 物料ID
     * @param deleteUserId 删除用户ID
     * @return 是否成功
     */
    Boolean deleteMaterial(Long materialId, Long deleteUserId);

    /**
     * 批量删除物料
     * 
     * @param materialIds 物料ID列表
     * @param deleteUserId 删除用户ID
     * @return 是否成功
     */
    Boolean batchDeleteMaterial(List<Long> materialIds, Long deleteUserId);

    /**
     * 启用/禁用物料
     * 
     * @param materialId 物料ID
     * @param enabled 是否启用
     * @param updateUserId 更新用户ID
     * @return 是否成功
     */
    Boolean enableMaterial(Long materialId, Boolean enabled, Long updateUserId);

    /**
     * 批量启用/禁用物料
     * 
     * @param materialIds 物料ID列表
     * @param enabled 是否启用
     * @param updateUserId 更新用户ID
     * @return 是否成功
     */
    Boolean batchEnableMaterial(List<Long> materialIds, Boolean enabled, Long updateUserId);

    /**
     * 查询物料库存信息
     * 
     * @param materialId 物料ID
     * @return 库存信息
     */
    MaterialDTO.InventoryDTO getMaterialInventory(Long materialId);

    /**
     * 查询物料供应商列表
     * 
     * @param materialId 物料ID
     * @return 供应商列表
     */
    List<MaterialDTO.SupplierDTO> getMaterialSuppliers(Long materialId);

    /**
     * 查询物料价格历史
     * 
     * @param materialId 物料ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 价格历史
     */
    List<MaterialDTO.PriceHistoryDTO> getMaterialPriceHistory(Long materialId, String startDate, String endDate);

    /**
     * 查询物料库存明细
     * 
     * @param materialId 物料ID
     * @return 库存明细
     */
    List<MaterialDTO.InventoryDetailDTO> getMaterialInventoryDetail(Long materialId);

    /**
     * 查询物料替代关系
     * 
     * @param materialId 物料ID
     * @return 替代关系
     */
    List<MaterialDTO.SubstituteDTO> getMaterialSubstitutes(Long materialId);

    /**
     * 查询低库存物料
     * 
     * @param warehouseId 仓库ID（可选）
     * @return 低库存物料列表
     */
    List<MaterialDTO> getLowStockMaterials(Long warehouseId);

    /**
     * 查询超期物料
     * 
     * @param days 超期天数
     * @return 超期物料列表
     */
    List<MaterialDTO> getExpiredMaterials(Integer days);

    /**
     * 物料分类统计
     * 
     * @return 分类统计数据
     */
    List<Map<String, Object>> getMaterialCategoryStats();

    /**
     * ABC分析数据
     * 
     * @param analysisType 分析类型（value/quantity/frequency）
     * @return ABC分析结果
     */
    List<Map<String, Object>> getAbcAnalysisData(String analysisType);

    /**
     * 批量更新ABC分类
     * 
     * @param materialIds 物料ID列表
     * @param abcCategory ABC分类
     * @param updateUserId 更新用户ID
     * @return 是否成功
     */
    Boolean batchUpdateAbcCategory(List<Long> materialIds, String abcCategory, Long updateUserId);

    /**
     * 批量更新物料价格
     * 
     * @param priceUpdateList 价格更新列表
     * @param updateUserId 更新用户ID
     * @return 是否成功
     */
    Boolean batchUpdatePrice(List<MaterialDTO.PriceUpdateDTO> priceUpdateList, Long updateUserId);

    /**
     * 查询物料使用情况
     * 
     * @param materialId 物料ID
     * @return 使用情况
     */
    MaterialDTO.UsageDTO getMaterialUsage(Long materialId);

    /**
     * 检查物料编码是否存在
     * 
     * @param materialCode 物料编码
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    Boolean checkMaterialCodeExists(String materialCode, Long excludeId);

    /**
     * 生成物料编码建议
     * 
     * @param categoryCode 分类编码
     * @param materialType 物料类型
     * @return 编码建议
     */
    String generateMaterialCodeSuggestion(String categoryCode, String materialType);

    /**
     * 查询物料完整信息（含库存、供应商、价格等）
     * 
     * @param materialId 物料ID
     * @return 完整信息
     */
    MaterialDTO getMaterialFullInfo(Long materialId);

    /**
     * 导出物料数据
     * 
     * @param queryDTO 查询条件
     * @return 导出文件路径
     */
    String exportMaterials(MaterialDTO.MaterialQueryDTO queryDTO);

    /**
     * 导入物料数据
     * 
     * @param filePath 文件路径
     * @param importUserId 导入用户ID
     * @return 导入结果
     */
    Map<String, Object> importMaterials(String filePath, Long importUserId);

    /**
     * 获取物料导入模板
     * 
     * @return 模板文件路径
     */
    String getMaterialImportTemplate();

    /**
     * 物料数据校验
     * 
     * @param materialDTO 物料数据
     * @return 校验结果
     */
    Map<String, Object> validateMaterialData(MaterialDTO materialDTO);

    /**
     * 批量物料数据校验
     * 
     * @param materialList 物料数据列表
     * @return 校验结果
     */
    Map<String, Object> batchValidateMaterialData(List<MaterialDTO> materialList);

    /**
     * 获取物料统计看板
     * 
     * @return 看板数据
     */
    Map<String, Object> getMaterialDashboard();

    /**
     * 获取物料趋势数据
     * 
     * @param period 统计周期（day/week/month）
     * @param count 统计数量
     * @return 趋势数据
     */
    List<Map<String, Object>> getMaterialTrend(String period, Integer count);

    /**
     * 获取热门物料
     * 
     * @param limit 数量限制
     * @return 热门物料列表
     */
    List<MaterialDTO> getPopularMaterials(Integer limit);

    /**
     * 获取新增物料
     * 
     * @param days 天数
     * @return 新增物料列表
     */
    List<MaterialDTO> getNewMaterials(Integer days);

    /**
     * 物料搜索建议
     * 
     * @param keyword 关键词
     * @param limit 数量限制
     * @return 搜索建议
     */
    List<String> getMaterialSearchSuggestions(String keyword, Integer limit);

    /**
     * 获取物料标签
     * 
     * @param materialId 物料ID
     * @return 标签列表
     */
    List<String> getMaterialTags(Long materialId);

    /**
     * 更新物料标签
     * 
     * @param materialId 物料ID
     * @param tags 标签列表
     * @param updateUserId 更新用户ID
     * @return 是否成功
     */
    Boolean updateMaterialTags(Long materialId, List<String> tags, Long updateUserId);

    /**
     * 根据标签查询物料
     * 
     * @param tags 标签列表
     * @return 物料列表
     */
    List<MaterialDTO> getMaterialsByTags(List<String> tags);
}