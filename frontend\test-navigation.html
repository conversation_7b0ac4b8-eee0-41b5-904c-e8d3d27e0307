<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1890ff;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
        }
        .test-section h2 {
            color: #262626;
            margin-bottom: 15px;
        }
        .test-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px;
            background: #fafafa;
            border-radius: 4px;
        }
        .test-item a {
            color: #1890ff;
            text-decoration: none;
            margin-right: 15px;
            min-width: 200px;
        }
        .test-item a:hover {
            text-decoration: underline;
        }
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        .status.pending {
            background: #fff7e6;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }
        .description {
            color: #666;
            font-size: 14px;
            margin-left: 10px;
        }
        .instructions {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .instructions h3 {
            margin-top: 0;
            color: #0050b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>西安银马实业数字化管理系统 - 导航测试</h1>
        
        <div class="instructions">
            <h3>测试说明</h3>
            <p>请按照以下步骤测试每个菜单项的导航功能：</p>
            <ol>
                <li>点击下方的链接，验证页面是否能正确加载</li>
                <li>检查页面内容是否完整显示</li>
                <li>验证数据是否正常加载（如果有数据表格或列表）</li>
                <li>测试页面的基本功能是否正常工作</li>
            </ol>
        </div>

        <div class="test-section">
            <h2>主要功能模块</h2>
            
            <div class="test-item">
                <a href="http://localhost:3001/dashboard" target="_blank">数据看板</a>
                <span class="status pending">待测试</span>
                <span class="description">系统主页，显示关键业务指标和统计信息</span>
            </div>
            
            <div class="test-item">
                <a href="http://localhost:3001/bom" target="_blank">BOM管理中心</a>
                <span class="status pending">待测试</span>
                <span class="description">设备BOM管理，包括创建、编辑、版本控制等功能</span>
            </div>
            
            <div class="test-item">
                <a href="http://localhost:3001/bom-detail" target="_blank">BOM明细管理</a>
                <span class="status pending">待测试</span>
                <span class="description">BOM详细信息管理，物料清单明细</span>
            </div>
            
            <div class="test-item">
                <a href="http://localhost:3001/bom-change-log" target="_blank">BOM变更日志</a>
                <span class="status pending">待测试</span>
                <span class="description">BOM变更历史记录和审批流程</span>
            </div>
            
            <div class="test-item">
                <a href="http://localhost:3001/material" target="_blank">物料主数据管理</a>
                <span class="status pending">待测试</span>
                <span class="description">物料基础信息管理，包括分类、规格、供应商等</span>
            </div>
            
            <div class="test-item">
                <a href="http://localhost:3001/financial" target="_blank">财务管控中心</a>
                <span class="status pending">待测试</span>
                <span class="description">财务数据分析和成本控制</span>
            </div>
            
            <div class="test-item">
                <a href="http://localhost:3001/decision" target="_blank">协同决策平台</a>
                <span class="status pending">待测试</span>
                <span class="description">业务决策支持和协同工作平台</span>
            </div>
        </div>

        <div class="test-section">
            <h2>子模块功能</h2>
            
            <div class="test-item">
                <a href="http://localhost:3001/manufacturing/equipment" target="_blank">设备管理</a>
                <span class="status pending">待测试</span>
                <span class="description">生产设备管理，设备状态监控和维护</span>
            </div>
            
            <div class="test-item">
                <a href="http://localhost:3001/project/management" target="_blank">项目管理</a>
                <span class="status pending">待测试</span>
                <span class="description">项目交付管理，进度跟踪和资源分配</span>
            </div>
            
            <div class="test-item">
                <a href="http://localhost:3001/service/maintenance" target="_blank">维护管理</a>
                <span class="status pending">待测试</span>
                <span class="description">设备维护计划和维修记录管理</span>
            </div>
            
            <div class="test-item">
                <a href="http://localhost:3001/service/monitoring" target="_blank">远程监控</a>
                <span class="status pending">待测试</span>
                <span class="description">设备远程监控和实时状态查看</span>
            </div>
        </div>

        <div class="test-section">
            <h2>测试结果记录</h2>
            <p>请在测试完成后，记录以下信息：</p>
            <ul>
                <li>✅ 页面能正常加载</li>
                <li>✅ 页面布局完整</li>
                <li>✅ 数据正常显示</li>
                <li>✅ 基本功能可用</li>
                <li>❌ 发现的问题和错误</li>
            </ul>
        </div>
    </div>

    <script>
        // 简单的测试状态管理
        document.addEventListener('DOMContentLoaded', function() {
            const links = document.querySelectorAll('.test-item a');
            links.forEach(link => {
                link.addEventListener('click', function() {
                    const statusElement = this.nextElementSibling;
                    statusElement.textContent = '测试中...';
                    statusElement.className = 'status pending';
                    
                    // 模拟测试完成后的状态更新
                    setTimeout(() => {
                        statusElement.textContent = '已测试';
                        statusElement.className = 'status success';
                    }, 2000);
                });
            });
        });
    </script>
</body>
</html>
