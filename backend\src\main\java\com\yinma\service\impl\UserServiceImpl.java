package com.yinma.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinma.dto.UserDTO;
import com.yinma.entity.UserEntity;
import com.yinma.mapper.UserMapper;
import com.yinma.mapper.UserRoleMapper;
import com.yinma.mapper.RolePermissionMapper;
import com.yinma.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户信息Service实现类
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, UserEntity> implements UserService {

    private final UserMapper userMapper;
    private final UserRoleMapper userRoleMapper;
    private final RolePermissionMapper rolePermissionMapper;
    private final PasswordEncoder passwordEncoder;

    @Override
    public IPage<UserDTO> selectUserPage(UserDTO.UserQueryDTO queryDTO) {
        Page<UserEntity> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        
        LambdaQueryWrapper<UserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.hasText(queryDTO.getUsername()), UserEntity::getUsername, queryDTO.getUsername())
               .like(StringUtils.hasText(queryDTO.getNickname()), UserEntity::getNickname, queryDTO.getNickname())
               .like(StringUtils.hasText(queryDTO.getEmail()), UserEntity::getEmail, queryDTO.getEmail())
               .like(StringUtils.hasText(queryDTO.getMobile()), UserEntity::getMobile, queryDTO.getMobile())
               .eq(queryDTO.getStatus() != null, UserEntity::getStatus, queryDTO.getStatus())
               .eq(queryDTO.getDeptId() != null, UserEntity::getDeptId, queryDTO.getDeptId())
               .ge(queryDTO.getCreateTimeStart() != null, UserEntity::getCreateTime, queryDTO.getCreateTimeStart())
               .le(queryDTO.getCreateTimeEnd() != null, UserEntity::getCreateTime, queryDTO.getCreateTimeEnd())
               .eq(UserEntity::getIsDeleted, 0)
               .orderByDesc(UserEntity::getCreateTime);
        
        IPage<UserEntity> entityPage = userMapper.selectPage(page, wrapper);
        
        // 转换为DTO
        IPage<UserDTO> dtoPage = new Page<>();
        BeanUtils.copyProperties(entityPage, dtoPage);
        
        List<UserDTO> dtoList = entityPage.getRecords().stream().map(entity -> {
            UserDTO dto = new UserDTO();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        }).collect(Collectors.toList());
        
        dtoPage.setRecords(dtoList);
        return dtoPage;
    }

    @Override
    public UserDTO selectUserById(Long userId) {
        UserEntity entity = userMapper.selectById(userId);
        if (entity == null) {
            return null;
        }
        
        UserDTO dto = new UserDTO();
        BeanUtils.copyProperties(entity, dto);
        
        // 查询用户角色
        List<String> roles = selectUserRoles(userId);
        dto.setRoles(roles);
        
        // 查询用户权限
        List<String> permissions = selectUserPermissions(userId);
        dto.setPermissions(permissions);
        
        return dto;
    }

    @Override
    public UserEntity selectUserByUsername(String username) {
        LambdaQueryWrapper<UserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserEntity::getUsername, username)
               .eq(UserEntity::getIsDeleted, 0);
        return userMapper.selectOne(wrapper);
    }

    @Override
    public UserEntity selectUserByEmail(String email) {
        LambdaQueryWrapper<UserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserEntity::getEmail, email)
               .eq(UserEntity::getIsDeleted, 0);
        return userMapper.selectOne(wrapper);
    }

    @Override
    public UserEntity selectUserByMobile(String mobile) {
        LambdaQueryWrapper<UserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserEntity::getMobile, mobile)
               .eq(UserEntity::getIsDeleted, 0);
        return userMapper.selectOne(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createUser(UserDTO.UserCreateDTO createDTO) {
        // 检查用户名是否存在
        if (checkUsernameExists(createDTO.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }
        
        // 检查邮箱是否存在
        if (StringUtils.hasText(createDTO.getEmail()) && checkEmailExists(createDTO.getEmail())) {
            throw new RuntimeException("邮箱已存在");
        }
        
        // 检查手机号是否存在
        if (StringUtils.hasText(createDTO.getMobile()) && checkMobileExists(createDTO.getMobile())) {
            throw new RuntimeException("手机号已存在");
        }
        
        UserEntity entity = new UserEntity();
        BeanUtils.copyProperties(createDTO, entity);
        
        // 加密密码
        entity.setPassword(passwordEncoder.encode(createDTO.getPassword()));
        entity.setCreateTime(LocalDateTime.now());
        entity.setIsDeleted(0);
        
        userMapper.insert(entity);
        
        // 分配角色
        if (createDTO.getRoleIds() != null && !createDTO.getRoleIds().isEmpty()) {
            assignUserRoles(entity.getUserId(), createDTO.getRoleIds());
        }
        
        return entity.getUserId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateUser(UserDTO.UserUpdateDTO updateDTO) {
        UserEntity entity = userMapper.selectById(updateDTO.getUserId());
        if (entity == null) {
            throw new RuntimeException("用户不存在");
        }
        
        // 检查用户名是否存在（排除自己）
        if (StringUtils.hasText(updateDTO.getUsername()) && 
            !updateDTO.getUsername().equals(entity.getUsername()) && 
            checkUsernameExists(updateDTO.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }
        
        // 检查邮箱是否存在（排除自己）
        if (StringUtils.hasText(updateDTO.getEmail()) && 
            !updateDTO.getEmail().equals(entity.getEmail()) && 
            checkEmailExists(updateDTO.getEmail())) {
            throw new RuntimeException("邮箱已存在");
        }
        
        // 检查手机号是否存在（排除自己）
        if (StringUtils.hasText(updateDTO.getMobile()) && 
            !updateDTO.getMobile().equals(entity.getMobile()) && 
            checkMobileExists(updateDTO.getMobile())) {
            throw new RuntimeException("手机号已存在");
        }
        
        BeanUtils.copyProperties(updateDTO, entity);
        entity.setUpdateTime(LocalDateTime.now());
        
        int result = userMapper.updateById(entity);
        
        // 更新角色
        if (updateDTO.getRoleIds() != null) {
            userRoleMapper.deleteByUserId(updateDTO.getUserId());
            if (!updateDTO.getRoleIds().isEmpty()) {
                assignUserRoles(updateDTO.getUserId(), updateDTO.getRoleIds());
            }
        }
        
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteUser(Long userId) {
        UserEntity entity = new UserEntity();
        entity.setUserId(userId);
        entity.setIsDeleted(1);
        entity.setUpdateTime(LocalDateTime.now());
        
        int result = userMapper.updateById(entity);
        
        // 删除用户角色关联
        userRoleMapper.deleteByUserId(userId);
        
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDeleteUsers(List<Long> userIds) {
        for (Long userId : userIds) {
            deleteUser(userId);
        }
        return true;
    }

    @Override
    public Boolean enableUser(Long userId) {
        UserEntity entity = new UserEntity();
        entity.setUserId(userId);
        entity.setStatus(1);
        entity.setUpdateTime(LocalDateTime.now());
        
        int result = userMapper.updateById(entity);
        return result > 0;
    }

    @Override
    public Boolean disableUser(Long userId) {
        UserEntity entity = new UserEntity();
        entity.setUserId(userId);
        entity.setStatus(0);
        entity.setUpdateTime(LocalDateTime.now());
        
        int result = userMapper.updateById(entity);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchUpdateUserStatus(List<Long> userIds, Integer status) {
        for (Long userId : userIds) {
            UserEntity entity = new UserEntity();
            entity.setUserId(userId);
            entity.setStatus(status);
            entity.setUpdateTime(LocalDateTime.now());
            userMapper.updateById(entity);
        }
        return true;
    }

    @Override
    public Boolean resetPassword(Long userId, String newPassword) {
        UserEntity entity = new UserEntity();
        entity.setUserId(userId);
        entity.setPassword(passwordEncoder.encode(newPassword));
        entity.setUpdateTime(LocalDateTime.now());
        
        int result = userMapper.updateById(entity);
        return result > 0;
    }

    @Override
    public Boolean changePassword(Long userId, String oldPassword, String newPassword) {
        UserEntity entity = userMapper.selectById(userId);
        if (entity == null) {
            throw new RuntimeException("用户不存在");
        }
        
        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, entity.getPassword())) {
            throw new RuntimeException("旧密码错误");
        }
        
        return resetPassword(userId, newPassword);
    }

    @Override
    public UserDTO.LoginResultDTO login(UserDTO.LoginDTO loginDTO) {
        // TODO: 实现登录逻辑，包括JWT Token生成
        throw new RuntimeException("登录功能待实现");
    }

    @Override
    public Boolean logout(Long userId) {
        // TODO: 实现登出逻辑，包括Token失效
        throw new RuntimeException("登出功能待实现");
    }

    @Override
    public UserDTO.LoginResultDTO refreshToken(String refreshToken) {
        // TODO: 实现Token刷新逻辑
        throw new RuntimeException("Token刷新功能待实现");
    }

    @Override
    public Boolean checkUsernameExists(String username) {
        return userMapper.checkUsernameExists(username) > 0;
    }

    @Override
    public Boolean checkEmailExists(String email) {
        return userMapper.checkEmailExists(email) > 0;
    }

    @Override
    public Boolean checkMobileExists(String mobile) {
        return userMapper.checkMobileExists(mobile) > 0;
    }

    @Override
    public List<UserDTO> selectUsersByDeptId(Long deptId) {
        List<UserEntity> entities = userMapper.selectUsersByDeptId(deptId);
        return entities.stream().map(entity -> {
            UserDTO dto = new UserDTO();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<UserDTO> selectUsersByRoleId(Long roleId) {
        List<UserEntity> entities = userMapper.selectUsersByRoleId(roleId);
        return entities.stream().map(entity -> {
            UserDTO dto = new UserDTO();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<String> selectUserRoles(Long userId) {
        return userMapper.selectUserRoles(userId);
    }

    @Override
    public List<String> selectUserPermissions(Long userId) {
        return rolePermissionMapper.selectPermissionCodesByUserId(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean assignUserRoles(Long userId, List<Long> roleIds) {
        // 删除现有角色
        userRoleMapper.deleteByUserId(userId);
        
        // 分配新角色
        if (!roleIds.isEmpty()) {
            userRoleMapper.batchInsertUserRoles(userId, roleIds, 1L); // TODO: 获取当前用户ID
        }
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeUserRoles(Long userId, List<Long> roleIds) {
        userRoleMapper.batchDeleteUserRoles(userId, roleIds);
        return true;
    }

    @Override
    public Boolean updateLastLoginTime(Long userId) {
        return userMapper.updateLastLoginTime(userId) > 0;
    }

    @Override
    public UserDTO.UserStatisticsDTO selectUserStatistics() {
        return userMapper.selectUserStatistics();
    }

    @Override
    public Long selectOnlineUserCount() {
        return userMapper.selectOnlineUserCount();
    }

    @Override
    public List<UserDTO> selectPasswordExpiredUsers(Integer days) {
        List<UserEntity> entities = userMapper.selectPasswordExpiredUsers(days);
        return entities.stream().map(entity -> {
            UserDTO dto = new UserDTO();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<UserDTO> selectLongTimeNoLoginUsers(Integer days) {
        List<UserEntity> entities = userMapper.selectLongTimeNoLoginUsers(days);
        return entities.stream().map(entity -> {
            UserDTO dto = new UserDTO();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<UserDTO.DeptUserStatisticsDTO> selectDeptUserStatistics() {
        return userMapper.selectDeptUserStatistics();
    }

    @Override
    public List<UserDTO.RoleUserStatisticsDTO> selectRoleUserStatistics() {
        return userMapper.selectRoleUserStatistics();
    }

    @Override
    public List<UserDTO> exportUsers(UserDTO.UserQueryDTO queryDTO) {
        // TODO: 实现用户数据导出
        throw new RuntimeException("用户数据导出功能待实现");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserDTO.ImportResultDTO importUsers(List<UserDTO.UserImportDTO> users) {
        // TODO: 实现用户数据导入
        throw new RuntimeException("用户数据导入功能待实现");
    }

    @Override
    public Boolean checkUserHasRole(Long userId, String roleCode) {
        return userRoleMapper.checkUserHasRole(userId, roleCode) > 0;
    }

    @Override
    public Boolean checkUserHasPermission(Long userId, String permissionCode) {
        return rolePermissionMapper.checkUserHasPermission(userId, permissionCode) > 0;
    }

    @Override
    public Boolean checkUserHasAnyRole(Long userId, List<String> roleCodes) {
        return userRoleMapper.checkUserHasAnyRole(userId, roleCodes) > 0;
    }

    @Override
    public Boolean checkUserHasAnyPermission(Long userId, List<String> permissionCodes) {
        return rolePermissionMapper.checkUserHasAnyPermission(userId, permissionCodes) > 0;
    }

    @Override
    public Boolean checkUserHasAllRoles(Long userId, List<String> roleCodes) {
        int count = userRoleMapper.checkUserHasAllRoles(userId, roleCodes);
        return count == roleCodes.size();
    }

    @Override
    public Boolean checkUserHasAllPermissions(Long userId, List<String> permissionCodes) {
        int count = rolePermissionMapper.checkUserHasAllPermissions(userId, permissionCodes);
        return count == permissionCodes.size();
    }

    @Override
    public List<UserDTO.MenuPermissionDTO> selectUserMenuPermissions(Long userId) {
        // TODO: 实现用户菜单权限查询
        throw new RuntimeException("用户菜单权限查询功能待实现");
    }

    @Override
    public List<String> selectUserButtonPermissions(Long userId) {
        // TODO: 实现用户按钮权限查询
        throw new RuntimeException("用户按钮权限查询功能待实现");
    }

    @Override
    public UserDTO.DataPermissionDTO selectUserDataPermissions(Long userId) {
        // TODO: 实现用户数据权限查询
        throw new RuntimeException("用户数据权限查询功能待实现");
    }
}