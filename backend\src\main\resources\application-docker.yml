# 西安银马实业数字化管理系统 - Docker环境配置
# 用于Docker容器中的Spring Boot应用配置

spring:
  # 应用配置
  application:
    name: yinma-management-system
  
  # 数据源配置
  datasource:
    url: jdbc:postgresql://${POSTGRES_HOST:postgres}:${POSTGRES_PORT:5432}/${POSTGRES_DB:yinma_management}
    username: ${POSTGRES_USER:yinma_user}
    password: ${POSTGRES_PASSWORD:yinma_password}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      max-lifetime: 1200000
      leak-detection-threshold: 60000
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: false
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
        batch_versioned_data: true
  
  # Redis配置
  redis:
    host: ${REDIS_HOST:redis}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: 0
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 2000ms
  
  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 3600000
      cache-null-values: false
  
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB
      file-size-threshold: 2KB
  
  # Jackson配置
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  
  # 国际化配置
  messages:
    basename: i18n/messages
    encoding: UTF-8
    cache-duration: 3600

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024
  tomcat:
    uri-encoding: UTF-8
    max-threads: 200
    min-spare-threads: 10
    max-connections: 8192
    accept-count: 100
    connection-timeout: 20000

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
      show-components: always
  health:
    redis:
      enabled: true
    db:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

# 日志配置
logging:
  level:
    root: INFO
    com.yinma: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /app/logs/application.log
    max-size: 100MB
    max-history: 30
    total-size-cap: 1GB

# 应用自定义配置
yinma:
  # JWT配置
  jwt:
    secret: ${JWT_SECRET:yinma-management-system-jwt-secret-key-2024}
    expiration: 86400000  # 24小时
    refresh-expiration: 604800000  # 7天
  
  # 文件存储配置
  file:
    upload-path: /app/uploads
    max-size: 10485760  # 10MB
    allowed-types: jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,ppt,pptx,txt
  
  # 系统配置
  system:
    name: 西安银马实业数字化管理系统
    version: 1.0.0
    description: 设备制造企业数字化管理平台
    contact:
      name: 银马实业技术团队
      email: <EMAIL>
      url: https://www.yinma.com
  
  # 安全配置
  security:
    # 跨域配置
    cors:
      allowed-origins: "*"
      allowed-methods: GET,POST,PUT,DELETE,OPTIONS
      allowed-headers: "*"
      allow-credentials: true
      max-age: 3600
    
    # 密码策略
    password:
      min-length: 8
      require-uppercase: true
      require-lowercase: true
      require-digit: true
      require-special: false
  
  # 业务配置
  business:
    # 分页配置
    page:
      default-size: 20
      max-size: 100
    
    # 缓存配置
    cache:
      default-ttl: 3600  # 1小时
      user-ttl: 1800     # 30分钟
      dict-ttl: 7200     # 2小时
    
    # 任务配置
    task:
      core-pool-size: 5
      max-pool-size: 20
      queue-capacity: 100
      keep-alive-seconds: 60

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    multiple-result-sets-enabled: true
    use-column-label: true
    use-generated-keys: true
    auto-mapping-behavior: partial
    auto-mapping-unknown-column-behavior: warning
    default-executor-type: reuse
    default-statement-timeout: 25000
    default-fetch-size: 100
    safe-row-bounds-enabled: false
    map-underscore-to-camel-case: true
    local-cache-scope: session
    jdbc-type-for-null: other
    lazy-load-trigger-methods: equals,clone,hashCode,toString
  global-config:
    db-config:
      id-type: auto
      table-underline: true
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.yinma.entity