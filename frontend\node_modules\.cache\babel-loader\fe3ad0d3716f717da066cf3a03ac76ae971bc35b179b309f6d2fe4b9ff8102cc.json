{"ast": null, "code": "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\nexport var scheme = new Array(3).concat(\"fc8d59ffffbf91cf60\", \"d7191cfdae61a6d96a1a9641\", \"d7191cfdae61ffffbfa6d96a1a9641\", \"d73027fc8d59fee08bd9ef8b91cf601a9850\", \"d73027fc8d59fee08bffffbfd9ef8b91cf601a9850\", \"d73027f46d43fdae61fee08bd9ef8ba6d96a66bd631a9850\", \"d73027f46d43fdae61fee08bffffbfd9ef8ba6d96a66bd631a9850\", \"a50026d73027f46d43fdae61fee08bd9ef8ba6d96a66bd631a9850006837\", \"a50026d73027f46d43fdae61fee08bffffbfd9ef8ba6d96a66bd631a9850006837\").map(colors);\nexport default ramp(scheme);", "map": {"version": 3, "names": ["colors", "ramp", "scheme", "Array", "concat", "map"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/d3-scale-chromatic/src/diverging/RdYlGn.js"], "sourcesContent": ["import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"fc8d59ffffbf91cf60\",\n  \"d7191cfdae61a6d96a1a9641\",\n  \"d7191cfdae61ffffbfa6d96a1a9641\",\n  \"d73027fc8d59fee08bd9ef8b91cf601a9850\",\n  \"d73027fc8d59fee08bffffbfd9ef8b91cf601a9850\",\n  \"d73027f46d43fdae61fee08bd9ef8ba6d96a66bd631a9850\",\n  \"d73027f46d43fdae61fee08bffffbfd9ef8ba6d96a66bd631a9850\",\n  \"a50026d73027f46d43fdae61fee08bd9ef8ba6d96a66bd631a9850006837\",\n  \"a50026d73027f46d43fdae61fee08bffffbfd9ef8ba6d96a66bd631a9850006837\"\n).map(colors);\n\nexport default ramp(scheme);\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAE7B,OAAO,IAAIC,MAAM,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CACrC,oBAAoB,EACpB,0BAA0B,EAC1B,gCAAgC,EAChC,sCAAsC,EACtC,4CAA4C,EAC5C,kDAAkD,EAClD,wDAAwD,EACxD,8DAA8D,EAC9D,oEACF,CAAC,CAACC,GAAG,CAACL,MAAM,CAAC;AAEb,eAAeC,IAAI,CAACC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}