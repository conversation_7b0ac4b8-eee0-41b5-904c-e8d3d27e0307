/**
 * 系统监控配置
 * 用于配置系统性能监控、健康检查、告警等功能
 */

const config = {
  // 监控配置
  monitoring: {
    // 是否启用监控
    enabled: process.env.MONITORING_ENABLED === 'true' || true,
    
    // 监控数据收集间隔（秒）
    collectInterval: parseInt(process.env.MONITORING_INTERVAL) || 30,
    
    // 数据保留时间（天）
    retentionDays: parseInt(process.env.MONITORING_RETENTION_DAYS) || 30,
    
    // 监控指标配置
    metrics: {
      // 系统性能指标
      system: {
        cpu: true,           // CPU使用率
        memory: true,        // 内存使用率
        disk: true,          // 磁盘使用率
        network: true        // 网络IO
      },
      
      // 应用性能指标
      application: {
        responseTime: true,   // 响应时间
        throughput: true,     // 吞吐量
        errorRate: true,      // 错误率
        activeUsers: true     // 活跃用户数
      },
      
      // 数据库性能指标
      database: {
        connections: true,    // 连接数
        queryTime: true,      // 查询时间
        slowQueries: true,    // 慢查询
        lockWaits: true       // 锁等待
      }
    }
  },
  
  // 健康检查配置
  healthCheck: {
    // 检查间隔（秒）
    interval: parseInt(process.env.HEALTH_CHECK_INTERVAL) || 60,
    
    // 超时时间（毫秒）
    timeout: parseInt(process.env.HEALTH_CHECK_TIMEOUT) || 5000,
    
    // 检查项目
    checks: {
      database: {
        enabled: true,
        query: 'SELECT 1',
        timeout: 3000
      },
      redis: {
        enabled: true,
        command: 'ping',
        timeout: 2000
      },
      fileSystem: {
        enabled: true,
        path: './temp',
        timeout: 1000
      },
      externalApi: {
        enabled: false,
        url: process.env.EXTERNAL_API_URL,
        timeout: 5000
      }
    }
  },
  
  // 告警配置
  alerting: {
    // 是否启用告警
    enabled: process.env.ALERTING_ENABLED === 'true' || true,
    
    // 告警规则
    rules: {
      // CPU使用率告警
      cpuUsage: {
        threshold: 80,        // 阈值（%）
        duration: 300,        // 持续时间（秒）
        severity: 'warning'   // 告警级别
      },
      
      // 内存使用率告警
      memoryUsage: {
        threshold: 85,
        duration: 300,
        severity: 'warning'
      },
      
      // 磁盘使用率告警
      diskUsage: {
        threshold: 90,
        duration: 60,
        severity: 'critical'
      },
      
      // 响应时间告警
      responseTime: {
        threshold: 2000,      // 毫秒
        duration: 180,
        severity: 'warning'
      },
      
      // 错误率告警
      errorRate: {
        threshold: 5,         // %
        duration: 300,
        severity: 'critical'
      },
      
      // 数据库连接告警
      dbConnections: {
        threshold: 80,        // %
        duration: 180,
        severity: 'warning'
      }
    },
    
    // 通知配置
    notifications: {
      email: {
        enabled: process.env.EMAIL_ALERTS_ENABLED === 'true' || false,
        recipients: (process.env.ALERT_EMAIL_RECIPIENTS || '').split(',').filter(Boolean),
        smtp: {
          host: process.env.SMTP_HOST,
          port: parseInt(process.env.SMTP_PORT) || 587,
          secure: process.env.SMTP_SECURE === 'true',
          auth: {
            user: process.env.SMTP_USER,
            pass: process.env.SMTP_PASS
          }
        }
      },
      
      webhook: {
        enabled: process.env.WEBHOOK_ALERTS_ENABLED === 'true' || false,
        url: process.env.ALERT_WEBHOOK_URL,
        timeout: 5000
      },
      
      sms: {
        enabled: process.env.SMS_ALERTS_ENABLED === 'true' || false,
        provider: process.env.SMS_PROVIDER || 'aliyun',
        config: {
          accessKeyId: process.env.SMS_ACCESS_KEY_ID,
          accessKeySecret: process.env.SMS_ACCESS_KEY_SECRET,
          signName: process.env.SMS_SIGN_NAME,
          templateCode: process.env.SMS_TEMPLATE_CODE
        },
        recipients: (process.env.ALERT_SMS_RECIPIENTS || '').split(',').filter(Boolean)
      }
    }
  },
  
  // 性能分析配置
  profiling: {
    // 是否启用性能分析
    enabled: process.env.PROFILING_ENABLED === 'true' || false,
    
    // 采样率（%）
    sampleRate: parseInt(process.env.PROFILING_SAMPLE_RATE) || 1,
    
    // 分析类型
    types: {
      cpu: true,           // CPU分析
      memory: true,        // 内存分析
      heap: false,         // 堆分析
      gc: false            // 垃圾回收分析
    }
  },
  
  // 链路追踪配置
  tracing: {
    // 是否启用链路追踪
    enabled: process.env.TRACING_ENABLED === 'true' || false,
    
    // 采样率（%）
    sampleRate: parseInt(process.env.TRACING_SAMPLE_RATE) || 10,
    
    // 服务名称
    serviceName: process.env.SERVICE_NAME || 'yinma-backend',
    
    // Jaeger配置
    jaeger: {
      endpoint: process.env.JAEGER_ENDPOINT || 'http://localhost:14268/api/traces',
      agentHost: process.env.JAEGER_AGENT_HOST || 'localhost',
      agentPort: parseInt(process.env.JAEGER_AGENT_PORT) || 6832
    }
  },
  
  // 指标导出配置
  export: {
    // Prometheus配置
    prometheus: {
      enabled: process.env.PROMETHEUS_ENABLED === 'true' || false,
      port: parseInt(process.env.PROMETHEUS_PORT) || 9090,
      path: process.env.PROMETHEUS_PATH || '/metrics'
    },
    
    // InfluxDB配置
    influxdb: {
      enabled: process.env.INFLUXDB_ENABLED === 'true' || false,
      url: process.env.INFLUXDB_URL || 'http://localhost:8086',
      database: process.env.INFLUXDB_DATABASE || 'yinma_monitoring',
      username: process.env.INFLUXDB_USERNAME,
      password: process.env.INFLUXDB_PASSWORD
    }
  }
};

// 环境特定配置
if (process.env.NODE_ENV === 'development') {
  // 开发环境配置
  config.monitoring.collectInterval = 10;
  config.healthCheck.interval = 30;
  config.alerting.enabled = false;
  config.profiling.enabled = true;
  config.tracing.enabled = true;
  config.tracing.sampleRate = 100;
} else if (process.env.NODE_ENV === 'production') {
  // 生产环境配置
  config.monitoring.collectInterval = 60;
  config.healthCheck.interval = 120;
  config.alerting.enabled = true;
  config.profiling.enabled = false;
  config.tracing.sampleRate = 1;
}

module.exports = config;