{"ast": null, "code": "import { presetPrimaryColors } from '@ant-design/colors';\nexport function validProgress(progress) {\n  if (!progress || progress < 0) {\n    return 0;\n  }\n  if (progress > 100) {\n    return 100;\n  }\n  return progress;\n}\nexport function getSuccessPercent({\n  success,\n  successPercent\n}) {\n  let percent = successPercent;\n  /** @deprecated Use `percent` instead */\n  if (success && 'progress' in success) {\n    percent = success.progress;\n  }\n  if (success && 'percent' in success) {\n    percent = success.percent;\n  }\n  return percent;\n}\nexport const getPercentage = ({\n  percent,\n  success,\n  successPercent\n}) => {\n  const realSuccessPercent = validProgress(getSuccessPercent({\n    success,\n    successPercent\n  }));\n  return [realSuccessPercent, validProgress(validProgress(percent) - realSuccessPercent)];\n};\nexport const getStrokeColor = ({\n  success = {},\n  strokeColor\n}) => {\n  const {\n    strokeColor: successColor\n  } = success;\n  return [successColor || presetPrimaryColors.green, strokeColor || null];\n};\nexport const getSize = (size, type, extra) => {\n  var _a, _b, _c, _d;\n  let width = -1;\n  let height = -1;\n  if (type === 'step') {\n    const steps = extra.steps;\n    const strokeWidth = extra.strokeWidth;\n    if (typeof size === 'string' || typeof size === 'undefined') {\n      width = size === 'small' ? 2 : 14;\n      height = strokeWidth !== null && strokeWidth !== void 0 ? strokeWidth : 8;\n    } else if (typeof size === 'number') {\n      [width, height] = [size, size];\n    } else {\n      [width = 14, height = 8] = Array.isArray(size) ? size : [size.width, size.height];\n    }\n    width *= steps;\n  } else if (type === 'line') {\n    const strokeWidth = extra === null || extra === void 0 ? void 0 : extra.strokeWidth;\n    if (typeof size === 'string' || typeof size === 'undefined') {\n      height = strokeWidth || (size === 'small' ? 6 : 8);\n    } else if (typeof size === 'number') {\n      [width, height] = [size, size];\n    } else {\n      [width = -1, height = 8] = Array.isArray(size) ? size : [size.width, size.height];\n    }\n  } else if (type === 'circle' || type === 'dashboard') {\n    if (typeof size === 'string' || typeof size === 'undefined') {\n      [width, height] = size === 'small' ? [60, 60] : [120, 120];\n    } else if (typeof size === 'number') {\n      [width, height] = [size, size];\n    } else if (Array.isArray(size)) {\n      width = (_b = (_a = size[0]) !== null && _a !== void 0 ? _a : size[1]) !== null && _b !== void 0 ? _b : 120;\n      height = (_d = (_c = size[0]) !== null && _c !== void 0 ? _c : size[1]) !== null && _d !== void 0 ? _d : 120;\n    }\n  }\n  return [width, height];\n};", "map": {"version": 3, "names": ["presetPrimaryColors", "validProgress", "progress", "getSuccessPercent", "success", "successPercent", "percent", "getPercentage", "realSuccessPercent", "getStrokeColor", "strokeColor", "successColor", "green", "getSize", "size", "type", "extra", "_a", "_b", "_c", "_d", "width", "height", "steps", "strokeWidth", "Array", "isArray"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/antd/es/progress/utils.js"], "sourcesContent": ["import { presetPrimaryColors } from '@ant-design/colors';\nexport function validProgress(progress) {\n  if (!progress || progress < 0) {\n    return 0;\n  }\n  if (progress > 100) {\n    return 100;\n  }\n  return progress;\n}\nexport function getSuccessPercent({\n  success,\n  successPercent\n}) {\n  let percent = successPercent;\n  /** @deprecated Use `percent` instead */\n  if (success && 'progress' in success) {\n    percent = success.progress;\n  }\n  if (success && 'percent' in success) {\n    percent = success.percent;\n  }\n  return percent;\n}\nexport const getPercentage = ({\n  percent,\n  success,\n  successPercent\n}) => {\n  const realSuccessPercent = validProgress(getSuccessPercent({\n    success,\n    successPercent\n  }));\n  return [realSuccessPercent, validProgress(validProgress(percent) - realSuccessPercent)];\n};\nexport const getStrokeColor = ({\n  success = {},\n  strokeColor\n}) => {\n  const {\n    strokeColor: successColor\n  } = success;\n  return [successColor || presetPrimaryColors.green, strokeColor || null];\n};\nexport const getSize = (size, type, extra) => {\n  var _a, _b, _c, _d;\n  let width = -1;\n  let height = -1;\n  if (type === 'step') {\n    const steps = extra.steps;\n    const strokeWidth = extra.strokeWidth;\n    if (typeof size === 'string' || typeof size === 'undefined') {\n      width = size === 'small' ? 2 : 14;\n      height = strokeWidth !== null && strokeWidth !== void 0 ? strokeWidth : 8;\n    } else if (typeof size === 'number') {\n      [width, height] = [size, size];\n    } else {\n      [width = 14, height = 8] = Array.isArray(size) ? size : [size.width, size.height];\n    }\n    width *= steps;\n  } else if (type === 'line') {\n    const strokeWidth = extra === null || extra === void 0 ? void 0 : extra.strokeWidth;\n    if (typeof size === 'string' || typeof size === 'undefined') {\n      height = strokeWidth || (size === 'small' ? 6 : 8);\n    } else if (typeof size === 'number') {\n      [width, height] = [size, size];\n    } else {\n      [width = -1, height = 8] = Array.isArray(size) ? size : [size.width, size.height];\n    }\n  } else if (type === 'circle' || type === 'dashboard') {\n    if (typeof size === 'string' || typeof size === 'undefined') {\n      [width, height] = size === 'small' ? [60, 60] : [120, 120];\n    } else if (typeof size === 'number') {\n      [width, height] = [size, size];\n    } else if (Array.isArray(size)) {\n      width = (_b = (_a = size[0]) !== null && _a !== void 0 ? _a : size[1]) !== null && _b !== void 0 ? _b : 120;\n      height = (_d = (_c = size[0]) !== null && _c !== void 0 ? _c : size[1]) !== null && _d !== void 0 ? _d : 120;\n    }\n  }\n  return [width, height];\n};"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,oBAAoB;AACxD,OAAO,SAASC,aAAaA,CAACC,QAAQ,EAAE;EACtC,IAAI,CAACA,QAAQ,IAAIA,QAAQ,GAAG,CAAC,EAAE;IAC7B,OAAO,CAAC;EACV;EACA,IAAIA,QAAQ,GAAG,GAAG,EAAE;IAClB,OAAO,GAAG;EACZ;EACA,OAAOA,QAAQ;AACjB;AACA,OAAO,SAASC,iBAAiBA,CAAC;EAChCC,OAAO;EACPC;AACF,CAAC,EAAE;EACD,IAAIC,OAAO,GAAGD,cAAc;EAC5B;EACA,IAAID,OAAO,IAAI,UAAU,IAAIA,OAAO,EAAE;IACpCE,OAAO,GAAGF,OAAO,CAACF,QAAQ;EAC5B;EACA,IAAIE,OAAO,IAAI,SAAS,IAAIA,OAAO,EAAE;IACnCE,OAAO,GAAGF,OAAO,CAACE,OAAO;EAC3B;EACA,OAAOA,OAAO;AAChB;AACA,OAAO,MAAMC,aAAa,GAAGA,CAAC;EAC5BD,OAAO;EACPF,OAAO;EACPC;AACF,CAAC,KAAK;EACJ,MAAMG,kBAAkB,GAAGP,aAAa,CAACE,iBAAiB,CAAC;IACzDC,OAAO;IACPC;EACF,CAAC,CAAC,CAAC;EACH,OAAO,CAACG,kBAAkB,EAAEP,aAAa,CAACA,aAAa,CAACK,OAAO,CAAC,GAAGE,kBAAkB,CAAC,CAAC;AACzF,CAAC;AACD,OAAO,MAAMC,cAAc,GAAGA,CAAC;EAC7BL,OAAO,GAAG,CAAC,CAAC;EACZM;AACF,CAAC,KAAK;EACJ,MAAM;IACJA,WAAW,EAAEC;EACf,CAAC,GAAGP,OAAO;EACX,OAAO,CAACO,YAAY,IAAIX,mBAAmB,CAACY,KAAK,EAAEF,WAAW,IAAI,IAAI,CAAC;AACzE,CAAC;AACD,OAAO,MAAMG,OAAO,GAAGA,CAACC,IAAI,EAAEC,IAAI,EAAEC,KAAK,KAAK;EAC5C,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EAClB,IAAIC,KAAK,GAAG,CAAC,CAAC;EACd,IAAIC,MAAM,GAAG,CAAC,CAAC;EACf,IAAIP,IAAI,KAAK,MAAM,EAAE;IACnB,MAAMQ,KAAK,GAAGP,KAAK,CAACO,KAAK;IACzB,MAAMC,WAAW,GAAGR,KAAK,CAACQ,WAAW;IACrC,IAAI,OAAOV,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,WAAW,EAAE;MAC3DO,KAAK,GAAGP,IAAI,KAAK,OAAO,GAAG,CAAC,GAAG,EAAE;MACjCQ,MAAM,GAAGE,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAG,CAAC;IAC3E,CAAC,MAAM,IAAI,OAAOV,IAAI,KAAK,QAAQ,EAAE;MACnC,CAACO,KAAK,EAAEC,MAAM,CAAC,GAAG,CAACR,IAAI,EAAEA,IAAI,CAAC;IAChC,CAAC,MAAM;MACL,CAACO,KAAK,GAAG,EAAE,EAAEC,MAAM,GAAG,CAAC,CAAC,GAAGG,KAAK,CAACC,OAAO,CAACZ,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAACO,KAAK,EAAEP,IAAI,CAACQ,MAAM,CAAC;IACnF;IACAD,KAAK,IAAIE,KAAK;EAChB,CAAC,MAAM,IAAIR,IAAI,KAAK,MAAM,EAAE;IAC1B,MAAMS,WAAW,GAAGR,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACQ,WAAW;IACnF,IAAI,OAAOV,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,WAAW,EAAE;MAC3DQ,MAAM,GAAGE,WAAW,KAAKV,IAAI,KAAK,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;IACpD,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MACnC,CAACO,KAAK,EAAEC,MAAM,CAAC,GAAG,CAACR,IAAI,EAAEA,IAAI,CAAC;IAChC,CAAC,MAAM;MACL,CAACO,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,GAAG,CAAC,CAAC,GAAGG,KAAK,CAACC,OAAO,CAACZ,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAACO,KAAK,EAAEP,IAAI,CAACQ,MAAM,CAAC;IACnF;EACF,CAAC,MAAM,IAAIP,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,WAAW,EAAE;IACpD,IAAI,OAAOD,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,WAAW,EAAE;MAC3D,CAACO,KAAK,EAAEC,MAAM,CAAC,GAAGR,IAAI,KAAK,OAAO,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;IAC5D,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MACnC,CAACO,KAAK,EAAEC,MAAM,CAAC,GAAG,CAACR,IAAI,EAAEA,IAAI,CAAC;IAChC,CAAC,MAAM,IAAIW,KAAK,CAACC,OAAO,CAACZ,IAAI,CAAC,EAAE;MAC9BO,KAAK,GAAG,CAACH,EAAE,GAAG,CAACD,EAAE,GAAGH,IAAI,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIG,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGH,IAAI,CAAC,CAAC,CAAC,MAAM,IAAI,IAAII,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,GAAG;MAC3GI,MAAM,GAAG,CAACF,EAAE,GAAG,CAACD,EAAE,GAAGL,IAAI,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIK,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGL,IAAI,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIM,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,GAAG;IAC9G;EACF;EACA,OAAO,CAACC,KAAK,EAAEC,MAAM,CAAC;AACxB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}