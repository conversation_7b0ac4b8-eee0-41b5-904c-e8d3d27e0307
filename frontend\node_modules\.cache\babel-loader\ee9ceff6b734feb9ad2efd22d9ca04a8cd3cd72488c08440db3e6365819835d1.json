{"ast": null, "code": "import { cubehelix } from \"d3-color\";\nimport { interpolateCubehelixLong } from \"d3-interpolate\";\nexport var warm = interpolateCubehelixLong(cubehelix(-100, 0.75, 0.35), cubehelix(80, 1.50, 0.8));\nexport var cool = interpolateCubehelixLong(cubehelix(260, 0.75, 0.35), cubehelix(80, 1.50, 0.8));\nvar c = cubehelix();\nexport default function (t) {\n  if (t < 0 || t > 1) t -= Math.floor(t);\n  var ts = Math.abs(t - 0.5);\n  c.h = 360 * t - 100;\n  c.s = 1.5 - 1.5 * ts;\n  c.l = 0.8 - 0.9 * ts;\n  return c + \"\";\n}", "map": {"version": 3, "names": ["cubehelix", "interpolateCubehelixLong", "warm", "cool", "c", "t", "Math", "floor", "ts", "abs", "h", "s", "l"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/d3-scale-chromatic/src/sequential-multi/rainbow.js"], "sourcesContent": ["import {cubehelix} from \"d3-color\";\nimport {interpolateCubehelixLong} from \"d3-interpolate\";\n\nexport var warm = interpolateCubehelixLong(cubehelix(-100, 0.75, 0.35), cubehelix(80, 1.50, 0.8));\n\nexport var cool = interpolateCubehelixLong(cubehelix(260, 0.75, 0.35), cubehelix(80, 1.50, 0.8));\n\nvar c = cubehelix();\n\nexport default function(t) {\n  if (t < 0 || t > 1) t -= Math.floor(t);\n  var ts = Math.abs(t - 0.5);\n  c.h = 360 * t - 100;\n  c.s = 1.5 - 1.5 * ts;\n  c.l = 0.8 - 0.9 * ts;\n  return c + \"\";\n}\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,UAAU;AAClC,SAAQC,wBAAwB,QAAO,gBAAgB;AAEvD,OAAO,IAAIC,IAAI,GAAGD,wBAAwB,CAACD,SAAS,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,EAAEA,SAAS,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;AAEjG,OAAO,IAAIG,IAAI,GAAGF,wBAAwB,CAACD,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,EAAEA,SAAS,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;AAEhG,IAAII,CAAC,GAAGJ,SAAS,CAAC,CAAC;AAEnB,eAAe,UAASK,CAAC,EAAE;EACzB,IAAIA,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIC,IAAI,CAACC,KAAK,CAACF,CAAC,CAAC;EACtC,IAAIG,EAAE,GAAGF,IAAI,CAACG,GAAG,CAACJ,CAAC,GAAG,GAAG,CAAC;EAC1BD,CAAC,CAACM,CAAC,GAAG,GAAG,GAAGL,CAAC,GAAG,GAAG;EACnBD,CAAC,CAACO,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGH,EAAE;EACpBJ,CAAC,CAACQ,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGJ,EAAE;EACpB,OAAOJ,CAAC,GAAG,EAAE;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}