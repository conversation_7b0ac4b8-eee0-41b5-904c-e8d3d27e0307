package com.yinma.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinma.dto.BomDTO;
import com.yinma.service.BomService;
import com.yinma.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * BOM管理控制器
 * 提供BOM的CRUD操作、版本管理、导入导出等功能
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
@Tag(name = "BOM管理", description = "BOM管理相关接口")
@RestController
@RequestMapping("/api/bom")
public class BomController {

    @Autowired
    private BomService bomService;

    @Operation(summary = "分页查询BOM列表")
    @PostMapping("/page")
    public Result<Page<BomDTO>> queryPage(@RequestBody BomDTO.BomQueryDTO queryDTO) {
        Page<BomDTO> result = bomService.queryPage(queryDTO);
        return Result.success(result);
    }

    @Operation(summary = "查询BOM详情")
    @GetMapping("/{bomId}")
    public Result<BomDTO> getBomDetail(
            @Parameter(description = "BOM ID") @PathVariable Long bomId) {
        BomDTO result = bomService.getBomDetail(bomId);
        if (result == null) {
            return Result.error("BOM不存在");
        }
        return Result.success(result);
    }

    @Operation(summary = "查询BOM树形结构")
    @GetMapping("/tree/{rootBomId}")
    public Result<List<BomDTO>> getBomTree(
            @Parameter(description = "根BOM ID") @PathVariable Long rootBomId) {
        List<BomDTO> result = bomService.getBomTree(rootBomId);
        return Result.success(result);
    }

    @Operation(summary = "查询主BOM列表")
    @PostMapping("/main")
    public Result<List<BomDTO>> getMainBomList(@RequestBody BomDTO.BomQueryDTO queryDTO) {
        List<BomDTO> result = bomService.getMainBomList(queryDTO);
        return Result.success(result);
    }

    @Operation(summary = "创建BOM")
    @PostMapping
    public Result<Long> createBom(@Valid @RequestBody BomDTO bomDTO) {
        try {
            Long bomId = bomService.createBom(bomDTO);
            return Result.success(bomId, "BOM创建成功");
        } catch (Exception e) {
            return Result.error("BOM创建失败: " + e.getMessage());
        }
    }

    @Operation(summary = "更新BOM")
    @PutMapping
    public Result<Boolean> updateBom(@Valid @RequestBody BomDTO bomDTO) {
        try {
            Boolean result = bomService.updateBom(bomDTO);
            return Result.success(result, "BOM更新成功");
        } catch (Exception e) {
            return Result.error("BOM更新失败: " + e.getMessage());
        }
    }

    @Operation(summary = "删除BOM")
    @DeleteMapping("/{bomId}")
    public Result<Boolean> deleteBom(
            @Parameter(description = "BOM ID") @PathVariable Long bomId,
            @Parameter(description = "删除用户ID") @RequestParam Long deleteUserId) {
        try {
            Boolean result = bomService.deleteBom(bomId, deleteUserId);
            return Result.success(result, "BOM删除成功");
        } catch (Exception e) {
            return Result.error("BOM删除失败: " + e.getMessage());
        }
    }

    @Operation(summary = "批量删除BOM")
    @DeleteMapping("/batch")
    public Result<Boolean> batchDeleteBom(
            @Parameter(description = "BOM ID列表") @RequestBody List<Long> bomIds,
            @Parameter(description = "删除用户ID") @RequestParam Long deleteUserId) {
        try {
            Boolean result = bomService.batchDeleteBom(bomIds, deleteUserId);
            return Result.success(result, "BOM批量删除成功");
        } catch (Exception e) {
            return Result.error("BOM批量删除失败: " + e.getMessage());
        }
    }

    @Operation(summary = "复制BOM")
    @PostMapping("/copy")
    public Result<Long> copyBom(
            @Parameter(description = "源BOM ID") @RequestParam Long sourceBomId,
            @Parameter(description = "新BOM编码") @RequestParam String newBomCode,
            @Parameter(description = "新BOM名称") @RequestParam String newBomName,
            @Parameter(description = "创建用户ID") @RequestParam Long createUserId) {
        try {
            Long bomId = bomService.copyBom(sourceBomId, newBomCode, newBomName, createUserId);
            return Result.success(bomId, "BOM复制成功");
        } catch (Exception e) {
            return Result.error("BOM复制失败: " + e.getMessage());
        }
    }

    @Operation(summary = "查询BOM版本历史")
    @GetMapping("/version/history/{bomId}")
    public Result<List<BomDTO>> getBomVersionHistory(
            @Parameter(description = "BOM ID") @PathVariable Long bomId) {
        List<BomDTO> result = bomService.getBomVersionHistory(bomId);
        return Result.success(result);
    }

    @Operation(summary = "创建新版本")
    @PostMapping("/version/create")
    public Result<Boolean> createNewVersion(
            @Parameter(description = "BOM ID") @RequestParam Long bomId,
            @Parameter(description = "新版本号") @RequestParam String newVersion,
            @Parameter(description = "版本描述") @RequestParam(required = false) String versionDescription,
            @Parameter(description = "创建用户ID") @RequestParam Long createUserId) {
        try {
            Boolean result = bomService.createNewVersion(bomId, newVersion, versionDescription, createUserId);
            return Result.success(result, "新版本创建成功");
        } catch (Exception e) {
            return Result.error("新版本创建失败: " + e.getMessage());
        }
    }

    @Operation(summary = "激活版本")
    @PostMapping("/version/activate")
    public Result<Boolean> activateVersion(
            @Parameter(description = "BOM ID") @RequestParam Long bomId,
            @Parameter(description = "激活用户ID") @RequestParam Long activateUserId) {
        try {
            Boolean result = bomService.activateVersion(bomId, activateUserId);
            return Result.success(result, "版本激活成功");
        } catch (Exception e) {
            return Result.error("版本激活失败: " + e.getMessage());
        }
    }

    @Operation(summary = "检查BOM编码是否存在")
    @GetMapping("/check-code")
    public Result<Boolean> checkBomCodeExists(
            @Parameter(description = "BOM编码") @RequestParam String bomCode,
            @Parameter(description = "排除的BOM ID") @RequestParam(required = false) Long excludeId) {
        Boolean exists = bomService.checkBomCodeExists(bomCode, excludeId);
        return Result.success(exists);
    }

    @Operation(summary = "生成BOM编码建议")
    @GetMapping("/generate-code")
    public Result<String> generateBomCodeSuggestion(
            @Parameter(description = "BOM类型") @RequestParam(required = false) String bomType,
            @Parameter(description = "产品编码") @RequestParam(required = false) String productCode) {
        String suggestion = bomService.generateBomCodeSuggestion(bomType, productCode);
        return Result.success(suggestion);
    }

    @Operation(summary = "计算物料需求")
    @GetMapping("/material-requirement")
    public Result<List<BomDTO.MaterialRequirementDTO>> calculateMaterialRequirement(
            @Parameter(description = "BOM ID") @RequestParam Long bomId,
            @Parameter(description = "需求数量") @RequestParam Double quantity) {
        List<BomDTO.MaterialRequirementDTO> result = bomService.calculateMaterialRequirement(bomId, quantity);
        return Result.success(result);
    }

    @Operation(summary = "BOM成本分析")
    @GetMapping("/cost-analysis/{bomId}")
    public Result<List<BomDTO.CostAnalysisDTO>> analyzeBomCost(
            @Parameter(description = "BOM ID") @PathVariable Long bomId) {
        List<BomDTO.CostAnalysisDTO> result = bomService.analyzeBomCost(bomId);
        return Result.success(result);
    }

    @Operation(summary = "导出BOM")
    @PostMapping("/export")
    public Result<String> exportBom(@RequestBody BomDTO.BomQueryDTO queryDTO) {
        try {
            String filePath = bomService.exportBom(queryDTO);
            return Result.success(filePath, "BOM导出成功");
        } catch (Exception e) {
            return Result.error("BOM导出失败: " + e.getMessage());
        }
    }

    @Operation(summary = "导入BOM")
    @PostMapping("/import")
    public Result<Map<String, Object>> importBom(
            @Parameter(description = "导入文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "导入用户ID") @RequestParam Long importUserId) {
        try {
            // TODO: 处理文件上传
            String filePath = "/uploads/" + file.getOriginalFilename();
            Map<String, Object> result = bomService.importBom(filePath, importUserId);
            return Result.success(result, "BOM导入完成");
        } catch (Exception e) {
            return Result.error("BOM导入失败: " + e.getMessage());
        }
    }

    @Operation(summary = "校验BOM数据")
    @PostMapping("/validate")
    public Result<Map<String, Object>> validateBomData(@RequestBody BomDTO bomDTO) {
        Map<String, Object> result = bomService.validateBomData(bomDTO);
        return Result.success(result);
    }

    @Operation(summary = "获取BOM统计看板")
    @GetMapping("/dashboard")
    public Result<Map<String, Object>> getBomDashboard() {
        Map<String, Object> result = bomService.getBomDashboard();
        return Result.success(result);
    }

    @Operation(summary = "BOM对比")
    @PostMapping("/compare")
    public Result<Map<String, Object>> compareBom(
            @Parameter(description = "源BOM ID") @RequestParam Long sourceBomId,
            @Parameter(description = "目标BOM ID") @RequestParam Long targetBomId) {
        // TODO: 实现BOM对比功能
        Map<String, Object> result = Map.of(
            "differences", List.of(),
            "summary", Map.of(
                "totalDifferences", 0,
                "addedItems", 0,
                "removedItems", 0,
                "modifiedItems", 0
            )
        );
        return Result.success(result);
    }

    @Operation(summary = "BOM转换")
    @PostMapping("/convert")
    public Result<Long> convertBom(
            @Parameter(description = "源BOM ID") @RequestParam Long sourceBomId,
            @Parameter(description = "目标BOM类型") @RequestParam String targetBomType,
            @Parameter(description = "转换用户ID") @RequestParam Long convertUserId) {
        try {
            // TODO: 实现BOM转换功能
            // 这里应该根据业务规则进行EBOM->PBOM->MBOM的转换
            Long newBomId = bomService.copyBom(sourceBomId, 
                bomService.generateBomCodeSuggestion(targetBomType, null),
                "转换后的" + targetBomType,
                convertUserId);
            return Result.success(newBomId, "BOM转换成功");
        } catch (Exception e) {
            return Result.error("BOM转换失败: " + e.getMessage());
        }
    }

    @Operation(summary = "BOM审批")
    @PostMapping("/approve")
    public Result<Boolean> approveBom(
            @Parameter(description = "BOM ID") @RequestParam Long bomId,
            @Parameter(description = "审批结果") @RequestParam Boolean approved,
            @Parameter(description = "审批意见") @RequestParam(required = false) String approvalComment,
            @Parameter(description = "审批用户ID") @RequestParam Long approvalUserId) {
        try {
            // TODO: 实现BOM审批功能
            // 这里应该更新BOM状态并记录审批历史
            return Result.success(true, approved ? "BOM审批通过" : "BOM审批拒绝");
        } catch (Exception e) {
            return Result.error("BOM审批失败: " + e.getMessage());
        }
    }

    @Operation(summary = "BOM发布")
    @PostMapping("/release")
    public Result<Boolean> releaseBom(
            @Parameter(description = "BOM ID") @RequestParam Long bomId,
            @Parameter(description = "发布用户ID") @RequestParam Long releaseUserId) {
        try {
            // TODO: 实现BOM发布功能
            // 这里应该将BOM状态更新为已发布，并触发相关业务流程
            return Result.success(true, "BOM发布成功");
        } catch (Exception e) {
            return Result.error("BOM发布失败: " + e.getMessage());
        }
    }

    @Operation(summary = "BOM冻结")
    @PostMapping("/freeze")
    public Result<Boolean> freezeBom(
            @Parameter(description = "BOM ID") @RequestParam Long bomId,
            @Parameter(description = "冻结原因") @RequestParam(required = false) String freezeReason,
            @Parameter(description = "冻结用户ID") @RequestParam Long freezeUserId) {
        try {
            // TODO: 实现BOM冻结功能
            // 这里应该将BOM状态更新为冻结，禁止修改
            return Result.success(true, "BOM冻结成功");
        } catch (Exception e) {
            return Result.error("BOM冻结失败: " + e.getMessage());
        }
    }

    @Operation(summary = "BOM解冻")
    @PostMapping("/unfreeze")
    public Result<Boolean> unfreezeBom(
            @Parameter(description = "BOM ID") @RequestParam Long bomId,
            @Parameter(description = "解冻用户ID") @RequestParam Long unfreezeUserId) {
        try {
            // TODO: 实现BOM解冻功能
            // 这里应该将BOM状态恢复为可编辑
            return Result.success(true, "BOM解冻成功");
        } catch (Exception e) {
            return Result.error("BOM解冻失败: " + e.getMessage());
        }
    }
}