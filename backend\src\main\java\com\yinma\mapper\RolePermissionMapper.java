package com.yinma.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yinma.entity.RolePermissionEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 角色权限关联Mapper接口
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
@Repository
@Mapper
public interface RolePermissionMapper extends BaseMapper<RolePermissionEntity> {

    /**
     * 根据角色ID查询权限ID列表
     * 
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    @Select("SELECT permission_id FROM sys_role_permission WHERE role_id = #{roleId}")
    List<Long> selectPermissionIdsByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据权限ID查询角色ID列表
     * 
     * @param permissionId 权限ID
     * @return 角色ID列表
     */
    @Select("SELECT role_id FROM sys_role_permission WHERE permission_id = #{permissionId}")
    List<Long> selectRoleIdsByPermissionId(@Param("permissionId") Long permissionId);

    /**
     * 检查角色权限关联是否存在
     * 
     * @param roleId 角色ID
     * @param permissionId 权限ID
     * @return 是否存在
     */
    @Select("SELECT COUNT(1) FROM sys_role_permission WHERE role_id = #{roleId} AND permission_id = #{permissionId}")
    int checkRolePermissionExists(@Param("roleId") Long roleId, @Param("permissionId") Long permissionId);

    /**
     * 删除角色的所有权限关联
     * 
     * @param roleId 角色ID
     * @return 删除行数
     */
    @Delete("DELETE FROM sys_role_permission WHERE role_id = #{roleId}")
    int deleteByRoleId(@Param("roleId") Long roleId);

    /**
     * 删除权限的所有角色关联
     * 
     * @param permissionId 权限ID
     * @return 删除行数
     */
    @Delete("DELETE FROM sys_role_permission WHERE permission_id = #{permissionId}")
    int deleteByPermissionId(@Param("permissionId") Long permissionId);

    /**
     * 删除指定角色权限关联
     * 
     * @param roleId 角色ID
     * @param permissionId 权限ID
     * @return 删除行数
     */
    @Delete("DELETE FROM sys_role_permission WHERE role_id = #{roleId} AND permission_id = #{permissionId}")
    int deleteRolePermission(@Param("roleId") Long roleId, @Param("permissionId") Long permissionId);

    /**
     * 批量插入角色权限关联
     * 
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @param createUserId 创建用户ID
     * @return 插入行数
     */
    @Insert("<script>" +
            "INSERT INTO sys_role_permission (role_id, permission_id, create_time, create_user_id) VALUES " +
            "<foreach collection='permissionIds' item='permissionId' separator=','>" +
            "(#{roleId}, #{permissionId}, NOW(), #{createUserId})" +
            "</foreach>" +
            "</script>")
    int batchInsertRolePermissions(@Param("roleId") Long roleId, 
                                  @Param("permissionIds") List<Long> permissionIds,
                                  @Param("createUserId") Long createUserId);

    /**
     * 批量插入权限角色关联
     * 
     * @param permissionId 权限ID
     * @param roleIds 角色ID列表
     * @param createUserId 创建用户ID
     * @return 插入行数
     */
    @Insert("<script>" +
            "INSERT INTO sys_role_permission (role_id, permission_id, create_time, create_user_id) VALUES " +
            "<foreach collection='roleIds' item='roleId' separator=','>" +
            "(#{roleId}, #{permissionId}, NOW(), #{createUserId})" +
            "</foreach>" +
            "</script>")
    int batchInsertPermissionRoles(@Param("permissionId") Long permissionId, 
                                  @Param("roleIds") List<Long> roleIds,
                                  @Param("createUserId") Long createUserId);

    /**
     * 批量删除角色权限关联
     * 
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @return 删除行数
     */
    @Delete("<script>" +
            "DELETE FROM sys_role_permission " +
            "WHERE role_id = #{roleId} AND permission_id IN " +
            "<foreach collection='permissionIds' item='permissionId' open='(' separator=',' close=')'>" +
            "#{permissionId}" +
            "</foreach>" +
            "</script>")
    int batchDeleteRolePermissions(@Param("roleId") Long roleId, @Param("permissionIds") List<Long> permissionIds);

    /**
     * 批量删除权限角色关联
     * 
     * @param permissionId 权限ID
     * @param roleIds 角色ID列表
     * @return 删除行数
     */
    @Delete("<script>" +
            "DELETE FROM sys_role_permission " +
            "WHERE permission_id = #{permissionId} AND role_id IN " +
            "<foreach collection='roleIds' item='roleId' open='(' separator=',' close=')'>" +
            "#{roleId}" +
            "</foreach>" +
            "</script>")
    int batchDeletePermissionRoles(@Param("permissionId") Long permissionId, @Param("roleIds") List<Long> roleIds);

    /**
     * 查询用户的所有权限ID（通过角色关联）
     * 
     * @param userId 用户ID
     * @return 权限ID列表
     */
    @Select("SELECT DISTINCT rp.permission_id " +
            "FROM sys_role_permission rp " +
            "INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id " +
            "INNER JOIN sys_role r ON ur.role_id = r.role_id " +
            "WHERE ur.user_id = #{userId} AND r.status = 1 AND r.is_deleted = 0")
    List<Long> selectPermissionIdsByUserId(@Param("userId") Long userId);

    /**
     * 查询用户的所有权限编码（通过角色关联）
     * 
     * @param userId 用户ID
     * @return 权限编码列表
     */
    @Select("SELECT DISTINCT p.permission_code " +
            "FROM sys_role_permission rp " +
            "INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id " +
            "INNER JOIN sys_role r ON ur.role_id = r.role_id " +
            "INNER JOIN sys_permission p ON rp.permission_id = p.permission_id " +
            "WHERE ur.user_id = #{userId} AND r.status = 1 AND r.is_deleted = 0 " +
            "AND p.status = 1 AND p.is_deleted = 0")
    List<String> selectPermissionCodesByUserId(@Param("userId") Long userId);

    /**
     * 查询角色权限关联统计
     * 
     * @return 统计信息
     */
    @Select("SELECT " +
            "COUNT(DISTINCT role_id) as totalRoles, " +
            "COUNT(DISTINCT permission_id) as totalPermissions, " +
            "COUNT(1) as totalRelations, " +
            "AVG(permission_count) as avgPermissionsPerRole " +
            "FROM (" +
            "SELECT role_id, COUNT(permission_id) as permission_count " +
            "FROM sys_role_permission " +
            "GROUP BY role_id" +
            ") t")
    RolePermissionStatisticsDTO selectRolePermissionStatistics();

    /**
     * 查询拥有多个权限的角色
     * 
     * @param minPermissionCount 最小权限数量
     * @return 角色ID列表
     */
    @Select("SELECT role_id FROM sys_role_permission " +
            "GROUP BY role_id " +
            "HAVING COUNT(permission_id) >= #{minPermissionCount}")
    List<Long> selectRolesWithMultiplePermissions(@Param("minPermissionCount") Integer minPermissionCount);

    /**
     * 查询拥有多个角色的权限
     * 
     * @param minRoleCount 最小角色数量
     * @return 权限ID列表
     */
    @Select("SELECT permission_id FROM sys_role_permission " +
            "GROUP BY permission_id " +
            "HAVING COUNT(role_id) >= #{minRoleCount}")
    List<Long> selectPermissionsWithMultipleRoles(@Param("minRoleCount") Integer minRoleCount);

    /**
     * 查询角色权限数量
     * 
     * @param roleId 角色ID
     * @return 权限数量
     */
    @Select("SELECT COUNT(1) FROM sys_role_permission WHERE role_id = #{roleId}")
    Long selectRolePermissionCount(@Param("roleId") Long roleId);

    /**
     * 查询权限角色数量
     * 
     * @param permissionId 权限ID
     * @return 角色数量
     */
    @Select("SELECT COUNT(1) FROM sys_role_permission WHERE permission_id = #{permissionId}")
    Long selectPermissionRoleCount(@Param("permissionId") Long permissionId);

    /**
     * 查询没有权限的角色
     * 
     * @return 角色ID列表
     */
    @Select("SELECT r.role_id FROM sys_role r " +
            "LEFT JOIN sys_role_permission rp ON r.role_id = rp.role_id " +
            "WHERE rp.role_id IS NULL AND r.is_deleted = 0")
    List<Long> selectRolesWithoutPermission();

    /**
     * 查询没有角色的权限
     * 
     * @return 权限ID列表
     */
    @Select("SELECT p.permission_id FROM sys_permission p " +
            "LEFT JOIN sys_role_permission rp ON p.permission_id = rp.permission_id " +
            "WHERE rp.permission_id IS NULL AND p.is_deleted = 0")
    List<Long> selectPermissionsWithoutRole();

    /**
     * 检查角色是否拥有指定权限
     * 
     * @param roleId 角色ID
     * @param permissionCode 权限编码
     * @return 是否拥有
     */
    @Select("SELECT COUNT(1) FROM sys_role_permission rp " +
            "INNER JOIN sys_permission p ON rp.permission_id = p.permission_id " +
            "WHERE rp.role_id = #{roleId} AND p.permission_code = #{permissionCode} " +
            "AND p.status = 1 AND p.is_deleted = 0")
    int checkRoleHasPermission(@Param("roleId") Long roleId, @Param("permissionCode") String permissionCode);

    /**
     * 检查角色是否拥有指定权限列表中的任一权限
     * 
     * @param roleId 角色ID
     * @param permissionCodes 权限编码列表
     * @return 是否拥有
     */
    @Select("<script>" +
            "SELECT COUNT(1) FROM sys_role_permission rp " +
            "INNER JOIN sys_permission p ON rp.permission_id = p.permission_id " +
            "WHERE rp.role_id = #{roleId} AND p.status = 1 AND p.is_deleted = 0 " +
            "AND p.permission_code IN " +
            "<foreach collection='permissionCodes' item='permissionCode' open='(' separator=',' close=')'>" +
            "#{permissionCode}" +
            "</foreach>" +
            "</script>")
    int checkRoleHasAnyPermission(@Param("roleId") Long roleId, @Param("permissionCodes") List<String> permissionCodes);

    /**
     * 检查角色是否拥有指定权限列表中的所有权限
     * 
     * @param roleId 角色ID
     * @param permissionCodes 权限编码列表
     * @return 是否拥有
     */
    @Select("<script>" +
            "SELECT COUNT(DISTINCT p.permission_code) FROM sys_role_permission rp " +
            "INNER JOIN sys_permission p ON rp.permission_id = p.permission_id " +
            "WHERE rp.role_id = #{roleId} AND p.status = 1 AND p.is_deleted = 0 " +
            "AND p.permission_code IN " +
            "<foreach collection='permissionCodes' item='permissionCode' open='(' separator=',' close=')'>" +
            "#{permissionCode}" +
            "</foreach>" +
            "</script>")
    int checkRoleHasAllPermissions(@Param("roleId") Long roleId, @Param("permissionCodes") List<String> permissionCodes);

    /**
     * 检查用户是否拥有指定权限（通过角色关联）
     * 
     * @param userId 用户ID
     * @param permissionCode 权限编码
     * @return 是否拥有
     */
    @Select("SELECT COUNT(1) FROM sys_role_permission rp " +
            "INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id " +
            "INNER JOIN sys_role r ON ur.role_id = r.role_id " +
            "INNER JOIN sys_permission p ON rp.permission_id = p.permission_id " +
            "WHERE ur.user_id = #{userId} AND p.permission_code = #{permissionCode} " +
            "AND r.status = 1 AND r.is_deleted = 0 " +
            "AND p.status = 1 AND p.is_deleted = 0")
    int checkUserHasPermission(@Param("userId") Long userId, @Param("permissionCode") String permissionCode);

    /**
     * 检查用户是否拥有指定权限列表中的任一权限（通过角色关联）
     * 
     * @param userId 用户ID
     * @param permissionCodes 权限编码列表
     * @return 是否拥有
     */
    @Select("<script>" +
            "SELECT COUNT(1) FROM sys_role_permission rp " +
            "INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id " +
            "INNER JOIN sys_role r ON ur.role_id = r.role_id " +
            "INNER JOIN sys_permission p ON rp.permission_id = p.permission_id " +
            "WHERE ur.user_id = #{userId} AND r.status = 1 AND r.is_deleted = 0 " +
            "AND p.status = 1 AND p.is_deleted = 0 " +
            "AND p.permission_code IN " +
            "<foreach collection='permissionCodes' item='permissionCode' open='(' separator=',' close=')'>" +
            "#{permissionCode}" +
            "</foreach>" +
            "</script>")
    int checkUserHasAnyPermission(@Param("userId") Long userId, @Param("permissionCodes") List<String> permissionCodes);

    /**
     * 检查用户是否拥有指定权限列表中的所有权限（通过角色关联）
     * 
     * @param userId 用户ID
     * @param permissionCodes 权限编码列表
     * @return 是否拥有
     */
    @Select("<script>" +
            "SELECT COUNT(DISTINCT p.permission_code) FROM sys_role_permission rp " +
            "INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id " +
            "INNER JOIN sys_role r ON ur.role_id = r.role_id " +
            "INNER JOIN sys_permission p ON rp.permission_id = p.permission_id " +
            "WHERE ur.user_id = #{userId} AND r.status = 1 AND r.is_deleted = 0 " +
            "AND p.status = 1 AND p.is_deleted = 0 " +
            "AND p.permission_code IN " +
            "<foreach collection='permissionCodes' item='permissionCode' open='(' separator=',' close=')'>" +
            "#{permissionCode}" +
            "</foreach>" +
            "</script>")
    int checkUserHasAllPermissions(@Param("userId") Long userId, @Param("permissionCodes") List<String> permissionCodes);

    /**
     * 角色权限统计DTO
     */
    class RolePermissionStatisticsDTO {
        private Long totalRoles;
        private Long totalPermissions;
        private Long totalRelations;
        private Double avgPermissionsPerRole;

        // Getters and Setters
        public Long getTotalRoles() {
            return totalRoles;
        }

        public void setTotalRoles(Long totalRoles) {
            this.totalRoles = totalRoles;
        }

        public Long getTotalPermissions() {
            return totalPermissions;
        }

        public void setTotalPermissions(Long totalPermissions) {
            this.totalPermissions = totalPermissions;
        }

        public Long getTotalRelations() {
            return totalRelations;
        }

        public void setTotalRelations(Long totalRelations) {
            this.totalRelations = totalRelations;
        }

        public Double getAvgPermissionsPerRole() {
            return avgPermissionsPerRole;
        }

        public void setAvgPermissionsPerRole(Double avgPermissionsPerRole) {
            this.avgPermissionsPerRole = avgPermissionsPerRole;
        }
    }
}