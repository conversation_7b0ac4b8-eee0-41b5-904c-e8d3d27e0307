package com.yinma.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * BOM明细表实体类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("bom_detail")
public class BomDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * BOM主表ID
     */
    @NotNull(message = "BOM主表ID不能为空")
    @TableField("bom_id")
    private Long bomId;

    /**
     * 父级物料编码
     */
    @NotBlank(message = "父级物料编码不能为空")
    @TableField("parent_material_code")
    private String parentMaterialCode;

    /**
     * 子级物料编码
     */
    @NotBlank(message = "子级物料编码不能为空")
    @TableField("child_material_code")
    private String childMaterialCode;

    /**
     * 子级物料名称
     */
    @NotBlank(message = "子级物料名称不能为空")
    @TableField("child_material_name")
    private String childMaterialName;

    /**
     * 物料规格
     */
    @TableField("specification")
    private String specification;

    /**
     * 物料型号
     */
    @TableField("model")
    private String model;

    /**
     * 需求数量
     */
    @NotNull(message = "需求数量不能为空")
    @TableField("quantity")
    private BigDecimal quantity;

    /**
     * 计量单位
     */
    @NotBlank(message = "计量单位不能为空")
    @TableField("unit")
    private String unit;

    /**
     * 损耗率(%)
     */
    @TableField("loss_rate")
    private BigDecimal lossRate;

    /**
     * 替代料标识：0-主料，1-替代料
     */
    @TableField("substitute_flag")
    private Integer substituteFlag;

    /**
     * 替代料组
     */
    @TableField("substitute_group")
    private String substituteGroup;

    /**
     * 优先级
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 层级
     */
    @TableField("level")
    private Integer level;

    /**
     * 序号
     */
    @TableField("sequence")
    private Integer sequence;

    /**
     * 生效日期
     */
    @TableField("effective_date")
    private LocalDateTime effectiveDate;

    /**
     * 失效日期
     */
    @TableField("expiry_date")
    private LocalDateTime expiryDate;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}