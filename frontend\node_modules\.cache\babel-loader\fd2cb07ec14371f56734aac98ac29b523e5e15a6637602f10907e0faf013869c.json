{"ast": null, "code": "import number from \"./number.js\";\nvar reA = /[-+]?(?:\\d+\\.?\\d*|\\.?\\d+)(?:[eE][-+]?\\d+)?/g,\n  reB = new RegExp(reA.source, \"g\");\nfunction zero(b) {\n  return function () {\n    return b;\n  };\n}\nfunction one(b) {\n  return function (t) {\n    return b(t) + \"\";\n  };\n}\nexport default function (a, b) {\n  var bi = reA.lastIndex = reB.lastIndex = 0,\n    // scan index for next number in b\n    am,\n    // current match in a\n    bm,\n    // current match in b\n    bs,\n    // string preceding current number in b, if any\n    i = -1,\n    // index in s\n    s = [],\n    // string constants and placeholders\n    q = []; // number interpolators\n\n  // Coerce inputs to strings.\n  a = a + \"\", b = b + \"\";\n\n  // Interpolate pairs of numbers in a & b.\n  while ((am = reA.exec(a)) && (bm = reB.exec(b))) {\n    if ((bs = bm.index) > bi) {\n      // a string precedes the next number in b\n      bs = b.slice(bi, bs);\n      if (s[i]) s[i] += bs; // coalesce with previous string\n      else s[++i] = bs;\n    }\n    if ((am = am[0]) === (bm = bm[0])) {\n      // numbers in a & b match\n      if (s[i]) s[i] += bm; // coalesce with previous string\n      else s[++i] = bm;\n    } else {\n      // interpolate non-matching numbers\n      s[++i] = null;\n      q.push({\n        i: i,\n        x: number(am, bm)\n      });\n    }\n    bi = reB.lastIndex;\n  }\n\n  // Add remains of b.\n  if (bi < b.length) {\n    bs = b.slice(bi);\n    if (s[i]) s[i] += bs; // coalesce with previous string\n    else s[++i] = bs;\n  }\n\n  // Special optimization for only a single match.\n  // Otherwise, interpolate each of the numbers and rejoin the string.\n  return s.length < 2 ? q[0] ? one(q[0].x) : zero(b) : (b = q.length, function (t) {\n    for (var i = 0, o; i < b; ++i) s[(o = q[i]).i] = o.x(t);\n    return s.join(\"\");\n  });\n}", "map": {"version": 3, "names": ["number", "reA", "reB", "RegExp", "source", "zero", "b", "one", "t", "a", "bi", "lastIndex", "am", "bm", "bs", "i", "s", "q", "exec", "index", "slice", "push", "x", "length", "o", "join"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/d3-interpolate/src/string.js"], "sourcesContent": ["import number from \"./number.js\";\n\nvar reA = /[-+]?(?:\\d+\\.?\\d*|\\.?\\d+)(?:[eE][-+]?\\d+)?/g,\n    reB = new RegExp(reA.source, \"g\");\n\nfunction zero(b) {\n  return function() {\n    return b;\n  };\n}\n\nfunction one(b) {\n  return function(t) {\n    return b(t) + \"\";\n  };\n}\n\nexport default function(a, b) {\n  var bi = reA.lastIndex = reB.lastIndex = 0, // scan index for next number in b\n      am, // current match in a\n      bm, // current match in b\n      bs, // string preceding current number in b, if any\n      i = -1, // index in s\n      s = [], // string constants and placeholders\n      q = []; // number interpolators\n\n  // Coerce inputs to strings.\n  a = a + \"\", b = b + \"\";\n\n  // Interpolate pairs of numbers in a & b.\n  while ((am = reA.exec(a))\n      && (bm = reB.exec(b))) {\n    if ((bs = bm.index) > bi) { // a string precedes the next number in b\n      bs = b.slice(bi, bs);\n      if (s[i]) s[i] += bs; // coalesce with previous string\n      else s[++i] = bs;\n    }\n    if ((am = am[0]) === (bm = bm[0])) { // numbers in a & b match\n      if (s[i]) s[i] += bm; // coalesce with previous string\n      else s[++i] = bm;\n    } else { // interpolate non-matching numbers\n      s[++i] = null;\n      q.push({i: i, x: number(am, bm)});\n    }\n    bi = reB.lastIndex;\n  }\n\n  // Add remains of b.\n  if (bi < b.length) {\n    bs = b.slice(bi);\n    if (s[i]) s[i] += bs; // coalesce with previous string\n    else s[++i] = bs;\n  }\n\n  // Special optimization for only a single match.\n  // Otherwise, interpolate each of the numbers and rejoin the string.\n  return s.length < 2 ? (q[0]\n      ? one(q[0].x)\n      : zero(b))\n      : (b = q.length, function(t) {\n          for (var i = 0, o; i < b; ++i) s[(o = q[i]).i] = o.x(t);\n          return s.join(\"\");\n        });\n}\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,aAAa;AAEhC,IAAIC,GAAG,GAAG,6CAA6C;EACnDC,GAAG,GAAG,IAAIC,MAAM,CAACF,GAAG,CAACG,MAAM,EAAE,GAAG,CAAC;AAErC,SAASC,IAAIA,CAACC,CAAC,EAAE;EACf,OAAO,YAAW;IAChB,OAAOA,CAAC;EACV,CAAC;AACH;AAEA,SAASC,GAAGA,CAACD,CAAC,EAAE;EACd,OAAO,UAASE,CAAC,EAAE;IACjB,OAAOF,CAAC,CAACE,CAAC,CAAC,GAAG,EAAE;EAClB,CAAC;AACH;AAEA,eAAe,UAASC,CAAC,EAAEH,CAAC,EAAE;EAC5B,IAAII,EAAE,GAAGT,GAAG,CAACU,SAAS,GAAGT,GAAG,CAACS,SAAS,GAAG,CAAC;IAAE;IACxCC,EAAE;IAAE;IACJC,EAAE;IAAE;IACJC,EAAE;IAAE;IACJC,CAAC,GAAG,CAAC,CAAC;IAAE;IACRC,CAAC,GAAG,EAAE;IAAE;IACRC,CAAC,GAAG,EAAE,CAAC,CAAC;;EAEZ;EACAR,CAAC,GAAGA,CAAC,GAAG,EAAE,EAAEH,CAAC,GAAGA,CAAC,GAAG,EAAE;;EAEtB;EACA,OAAO,CAACM,EAAE,GAAGX,GAAG,CAACiB,IAAI,CAACT,CAAC,CAAC,MAChBI,EAAE,GAAGX,GAAG,CAACgB,IAAI,CAACZ,CAAC,CAAC,CAAC,EAAE;IACzB,IAAI,CAACQ,EAAE,GAAGD,EAAE,CAACM,KAAK,IAAIT,EAAE,EAAE;MAAE;MAC1BI,EAAE,GAAGR,CAAC,CAACc,KAAK,CAACV,EAAE,EAAEI,EAAE,CAAC;MACpB,IAAIE,CAAC,CAACD,CAAC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,IAAID,EAAE,CAAC,CAAC;MAAA,KACjBE,CAAC,CAAC,EAAED,CAAC,CAAC,GAAGD,EAAE;IAClB;IACA,IAAI,CAACF,EAAE,GAAGA,EAAE,CAAC,CAAC,CAAC,OAAOC,EAAE,GAAGA,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;MAAE;MACnC,IAAIG,CAAC,CAACD,CAAC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,IAAIF,EAAE,CAAC,CAAC;MAAA,KACjBG,CAAC,CAAC,EAAED,CAAC,CAAC,GAAGF,EAAE;IAClB,CAAC,MAAM;MAAE;MACPG,CAAC,CAAC,EAAED,CAAC,CAAC,GAAG,IAAI;MACbE,CAAC,CAACI,IAAI,CAAC;QAACN,CAAC,EAAEA,CAAC;QAAEO,CAAC,EAAEtB,MAAM,CAACY,EAAE,EAAEC,EAAE;MAAC,CAAC,CAAC;IACnC;IACAH,EAAE,GAAGR,GAAG,CAACS,SAAS;EACpB;;EAEA;EACA,IAAID,EAAE,GAAGJ,CAAC,CAACiB,MAAM,EAAE;IACjBT,EAAE,GAAGR,CAAC,CAACc,KAAK,CAACV,EAAE,CAAC;IAChB,IAAIM,CAAC,CAACD,CAAC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,IAAID,EAAE,CAAC,CAAC;IAAA,KACjBE,CAAC,CAAC,EAAED,CAAC,CAAC,GAAGD,EAAE;EAClB;;EAEA;EACA;EACA,OAAOE,CAAC,CAACO,MAAM,GAAG,CAAC,GAAIN,CAAC,CAAC,CAAC,CAAC,GACrBV,GAAG,CAACU,CAAC,CAAC,CAAC,CAAC,CAACK,CAAC,CAAC,GACXjB,IAAI,CAACC,CAAC,CAAC,IACNA,CAAC,GAAGW,CAAC,CAACM,MAAM,EAAE,UAASf,CAAC,EAAE;IACzB,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAES,CAAC,EAAET,CAAC,GAAGT,CAAC,EAAE,EAAES,CAAC,EAAEC,CAAC,CAAC,CAACQ,CAAC,GAAGP,CAAC,CAACF,CAAC,CAAC,EAAEA,CAAC,CAAC,GAAGS,CAAC,CAACF,CAAC,CAACd,CAAC,CAAC;IACvD,OAAOQ,CAAC,CAACS,IAAI,CAAC,EAAE,CAAC;EACnB,CAAC,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}