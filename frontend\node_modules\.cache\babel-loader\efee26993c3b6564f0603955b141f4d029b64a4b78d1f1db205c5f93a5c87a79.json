{"ast": null, "code": "import projection from \"./index.js\";\nimport { abs, epsilon } from \"../math.js\";\nexport function naturalEarth1Raw(lambda, phi) {\n  var phi2 = phi * phi,\n    phi4 = phi2 * phi2;\n  return [lambda * (0.8707 - 0.131979 * phi2 + phi4 * (-0.013791 + phi4 * (0.003971 * phi2 - 0.001529 * phi4))), phi * (1.007226 + phi2 * (0.015085 + phi4 * (-0.044475 + 0.028874 * phi2 - 0.005916 * phi4)))];\n}\nnaturalEarth1Raw.invert = function (x, y) {\n  var phi = y,\n    i = 25,\n    delta;\n  do {\n    var phi2 = phi * phi,\n      phi4 = phi2 * phi2;\n    phi -= delta = (phi * (1.007226 + phi2 * (0.015085 + phi4 * (-0.044475 + 0.028874 * phi2 - 0.005916 * phi4))) - y) / (1.007226 + phi2 * (0.015085 * 3 + phi4 * (-0.044475 * 7 + 0.028874 * 9 * phi2 - 0.005916 * 11 * phi4)));\n  } while (abs(delta) > epsilon && --i > 0);\n  return [x / (0.8707 + (phi2 = phi * phi) * (-0.131979 + phi2 * (-0.013791 + phi2 * phi2 * phi2 * (0.003971 - 0.001529 * phi2)))), phi];\n};\nexport default function () {\n  return projection(naturalEarth1Raw).scale(175.295);\n}", "map": {"version": 3, "names": ["projection", "abs", "epsilon", "naturalEarth1Raw", "lambda", "phi", "phi2", "phi4", "invert", "x", "y", "i", "delta", "scale"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/d3-geo/src/projection/naturalEarth1.js"], "sourcesContent": ["import projection from \"./index.js\";\nimport {abs, epsilon} from \"../math.js\";\n\nexport function naturalEarth1Raw(lambda, phi) {\n  var phi2 = phi * phi, phi4 = phi2 * phi2;\n  return [\n    lambda * (0.8707 - 0.131979 * phi2 + phi4 * (-0.013791 + phi4 * (0.003971 * phi2 - 0.001529 * phi4))),\n    phi * (1.007226 + phi2 * (0.015085 + phi4 * (-0.044475 + 0.028874 * phi2 - 0.005916 * phi4)))\n  ];\n}\n\nnaturalEarth1Raw.invert = function(x, y) {\n  var phi = y, i = 25, delta;\n  do {\n    var phi2 = phi * phi, phi4 = phi2 * phi2;\n    phi -= delta = (phi * (1.007226 + phi2 * (0.015085 + phi4 * (-0.044475 + 0.028874 * phi2 - 0.005916 * phi4))) - y) /\n        (1.007226 + phi2 * (0.015085 * 3 + phi4 * (-0.044475 * 7 + 0.028874 * 9 * phi2 - 0.005916 * 11 * phi4)));\n  } while (abs(delta) > epsilon && --i > 0);\n  return [\n    x / (0.8707 + (phi2 = phi * phi) * (-0.131979 + phi2 * (-0.013791 + phi2 * phi2 * phi2 * (0.003971 - 0.001529 * phi2)))),\n    phi\n  ];\n};\n\nexport default function() {\n  return projection(naturalEarth1Raw)\n      .scale(175.295);\n}\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,SAAQC,GAAG,EAAEC,OAAO,QAAO,YAAY;AAEvC,OAAO,SAASC,gBAAgBA,CAACC,MAAM,EAAEC,GAAG,EAAE;EAC5C,IAAIC,IAAI,GAAGD,GAAG,GAAGA,GAAG;IAAEE,IAAI,GAAGD,IAAI,GAAGA,IAAI;EACxC,OAAO,CACLF,MAAM,IAAI,MAAM,GAAG,QAAQ,GAAGE,IAAI,GAAGC,IAAI,IAAI,CAAC,QAAQ,GAAGA,IAAI,IAAI,QAAQ,GAAGD,IAAI,GAAG,QAAQ,GAAGC,IAAI,CAAC,CAAC,CAAC,EACrGF,GAAG,IAAI,QAAQ,GAAGC,IAAI,IAAI,QAAQ,GAAGC,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,GAAGD,IAAI,GAAG,QAAQ,GAAGC,IAAI,CAAC,CAAC,CAAC,CAC9F;AACH;AAEAJ,gBAAgB,CAACK,MAAM,GAAG,UAASC,CAAC,EAAEC,CAAC,EAAE;EACvC,IAAIL,GAAG,GAAGK,CAAC;IAAEC,CAAC,GAAG,EAAE;IAAEC,KAAK;EAC1B,GAAG;IACD,IAAIN,IAAI,GAAGD,GAAG,GAAGA,GAAG;MAAEE,IAAI,GAAGD,IAAI,GAAGA,IAAI;IACxCD,GAAG,IAAIO,KAAK,GAAG,CAACP,GAAG,IAAI,QAAQ,GAAGC,IAAI,IAAI,QAAQ,GAAGC,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,GAAGD,IAAI,GAAG,QAAQ,GAAGC,IAAI,CAAC,CAAC,CAAC,GAAGG,CAAC,KAC5G,QAAQ,GAAGJ,IAAI,IAAI,QAAQ,GAAG,CAAC,GAAGC,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAGD,IAAI,GAAG,QAAQ,GAAG,EAAE,GAAGC,IAAI,CAAC,CAAC,CAAC;EAC9G,CAAC,QAAQN,GAAG,CAACW,KAAK,CAAC,GAAGV,OAAO,IAAI,EAAES,CAAC,GAAG,CAAC;EACxC,OAAO,CACLF,CAAC,IAAI,MAAM,GAAG,CAACH,IAAI,GAAGD,GAAG,GAAGA,GAAG,KAAK,CAAC,QAAQ,GAAGC,IAAI,IAAI,CAAC,QAAQ,GAAGA,IAAI,GAAGA,IAAI,GAAGA,IAAI,IAAI,QAAQ,GAAG,QAAQ,GAAGA,IAAI,CAAC,CAAC,CAAC,CAAC,EACxHD,GAAG,CACJ;AACH,CAAC;AAED,eAAe,YAAW;EACxB,OAAOL,UAAU,CAACG,gBAAgB,CAAC,CAC9BU,KAAK,CAAC,OAAO,CAAC;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}