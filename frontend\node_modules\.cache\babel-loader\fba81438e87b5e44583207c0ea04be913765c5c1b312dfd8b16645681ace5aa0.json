{"ast": null, "code": "import { abs, atan, atan2, cos, epsilon, halfPi, log, pi, pow, sign, sin, sqrt, tan } from \"../math.js\";\nimport { conicProjection } from \"./conic.js\";\nimport { mercatorRaw } from \"./mercator.js\";\nfunction tany(y) {\n  return tan((halfPi + y) / 2);\n}\nexport function conicConformalRaw(y0, y1) {\n  var cy0 = cos(y0),\n    n = y0 === y1 ? sin(y0) : log(cy0 / cos(y1)) / log(tany(y1) / tany(y0)),\n    f = cy0 * pow(tany(y0), n) / n;\n  if (!n) return mercatorRaw;\n  function project(x, y) {\n    if (f > 0) {\n      if (y < -halfPi + epsilon) y = -halfPi + epsilon;\n    } else {\n      if (y > halfPi - epsilon) y = halfPi - epsilon;\n    }\n    var r = f / pow(tany(y), n);\n    return [r * sin(n * x), f - r * cos(n * x)];\n  }\n  project.invert = function (x, y) {\n    var fy = f - y,\n      r = sign(n) * sqrt(x * x + fy * fy),\n      l = atan2(x, abs(fy)) * sign(fy);\n    if (fy * n < 0) l -= pi * sign(x) * sign(fy);\n    return [l / n, 2 * atan(pow(f / r, 1 / n)) - halfPi];\n  };\n  return project;\n}\nexport default function () {\n  return conicProjection(conicConformalRaw).scale(109.5).parallels([30, 30]);\n}", "map": {"version": 3, "names": ["abs", "atan", "atan2", "cos", "epsilon", "halfPi", "log", "pi", "pow", "sign", "sin", "sqrt", "tan", "conicProjection", "mercatorRaw", "tany", "y", "conicConformalRaw", "y0", "y1", "cy0", "n", "f", "project", "x", "r", "invert", "fy", "l", "scale", "parallels"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/d3-geo/src/projection/conicConformal.js"], "sourcesContent": ["import {abs, atan, atan2, cos, epsilon, halfPi, log, pi, pow, sign, sin, sqrt, tan} from \"../math.js\";\nimport {conicProjection} from \"./conic.js\";\nimport {mercatorRaw} from \"./mercator.js\";\n\nfunction tany(y) {\n  return tan((halfPi + y) / 2);\n}\n\nexport function conicConformalRaw(y0, y1) {\n  var cy0 = cos(y0),\n      n = y0 === y1 ? sin(y0) : log(cy0 / cos(y1)) / log(tany(y1) / tany(y0)),\n      f = cy0 * pow(tany(y0), n) / n;\n\n  if (!n) return mercatorRaw;\n\n  function project(x, y) {\n    if (f > 0) { if (y < -halfPi + epsilon) y = -halfPi + epsilon; }\n    else { if (y > halfPi - epsilon) y = halfPi - epsilon; }\n    var r = f / pow(tany(y), n);\n    return [r * sin(n * x), f - r * cos(n * x)];\n  }\n\n  project.invert = function(x, y) {\n    var fy = f - y, r = sign(n) * sqrt(x * x + fy * fy),\n      l = atan2(x, abs(fy)) * sign(fy);\n    if (fy * n < 0)\n      l -= pi * sign(x) * sign(fy);\n    return [l / n, 2 * atan(pow(f / r, 1 / n)) - halfPi];\n  };\n\n  return project;\n}\n\nexport default function() {\n  return conicProjection(conicConformalRaw)\n      .scale(109.5)\n      .parallels([30, 30]);\n}\n"], "mappings": "AAAA,SAAQA,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,OAAO,EAAEC,MAAM,EAAEC,GAAG,EAAEC,EAAE,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,QAAO,YAAY;AACrG,SAAQC,eAAe,QAAO,YAAY;AAC1C,SAAQC,WAAW,QAAO,eAAe;AAEzC,SAASC,IAAIA,CAACC,CAAC,EAAE;EACf,OAAOJ,GAAG,CAAC,CAACP,MAAM,GAAGW,CAAC,IAAI,CAAC,CAAC;AAC9B;AAEA,OAAO,SAASC,iBAAiBA,CAACC,EAAE,EAAEC,EAAE,EAAE;EACxC,IAAIC,GAAG,GAAGjB,GAAG,CAACe,EAAE,CAAC;IACbG,CAAC,GAAGH,EAAE,KAAKC,EAAE,GAAGT,GAAG,CAACQ,EAAE,CAAC,GAAGZ,GAAG,CAACc,GAAG,GAAGjB,GAAG,CAACgB,EAAE,CAAC,CAAC,GAAGb,GAAG,CAACS,IAAI,CAACI,EAAE,CAAC,GAAGJ,IAAI,CAACG,EAAE,CAAC,CAAC;IACvEI,CAAC,GAAGF,GAAG,GAAGZ,GAAG,CAACO,IAAI,CAACG,EAAE,CAAC,EAAEG,CAAC,CAAC,GAAGA,CAAC;EAElC,IAAI,CAACA,CAAC,EAAE,OAAOP,WAAW;EAE1B,SAASS,OAAOA,CAACC,CAAC,EAAER,CAAC,EAAE;IACrB,IAAIM,CAAC,GAAG,CAAC,EAAE;MAAE,IAAIN,CAAC,GAAG,CAACX,MAAM,GAAGD,OAAO,EAAEY,CAAC,GAAG,CAACX,MAAM,GAAGD,OAAO;IAAE,CAAC,MAC3D;MAAE,IAAIY,CAAC,GAAGX,MAAM,GAAGD,OAAO,EAAEY,CAAC,GAAGX,MAAM,GAAGD,OAAO;IAAE;IACvD,IAAIqB,CAAC,GAAGH,CAAC,GAAGd,GAAG,CAACO,IAAI,CAACC,CAAC,CAAC,EAAEK,CAAC,CAAC;IAC3B,OAAO,CAACI,CAAC,GAAGf,GAAG,CAACW,CAAC,GAAGG,CAAC,CAAC,EAAEF,CAAC,GAAGG,CAAC,GAAGtB,GAAG,CAACkB,CAAC,GAAGG,CAAC,CAAC,CAAC;EAC7C;EAEAD,OAAO,CAACG,MAAM,GAAG,UAASF,CAAC,EAAER,CAAC,EAAE;IAC9B,IAAIW,EAAE,GAAGL,CAAC,GAAGN,CAAC;MAAES,CAAC,GAAGhB,IAAI,CAACY,CAAC,CAAC,GAAGV,IAAI,CAACa,CAAC,GAAGA,CAAC,GAAGG,EAAE,GAAGA,EAAE,CAAC;MACjDC,CAAC,GAAG1B,KAAK,CAACsB,CAAC,EAAExB,GAAG,CAAC2B,EAAE,CAAC,CAAC,GAAGlB,IAAI,CAACkB,EAAE,CAAC;IAClC,IAAIA,EAAE,GAAGN,CAAC,GAAG,CAAC,EACZO,CAAC,IAAIrB,EAAE,GAAGE,IAAI,CAACe,CAAC,CAAC,GAAGf,IAAI,CAACkB,EAAE,CAAC;IAC9B,OAAO,CAACC,CAAC,GAAGP,CAAC,EAAE,CAAC,GAAGpB,IAAI,CAACO,GAAG,CAACc,CAAC,GAAGG,CAAC,EAAE,CAAC,GAAGJ,CAAC,CAAC,CAAC,GAAGhB,MAAM,CAAC;EACtD,CAAC;EAED,OAAOkB,OAAO;AAChB;AAEA,eAAe,YAAW;EACxB,OAAOV,eAAe,CAACI,iBAAiB,CAAC,CACpCY,KAAK,CAAC,KAAK,CAAC,CACZC,SAAS,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}