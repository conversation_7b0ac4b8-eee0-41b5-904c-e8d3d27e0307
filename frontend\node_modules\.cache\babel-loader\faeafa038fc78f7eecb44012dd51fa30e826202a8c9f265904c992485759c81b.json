{"ast": null, "code": "const genSpaceCompactStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [componentCls]: {\n      '&-block': {\n        display: 'flex',\n        width: '100%'\n      },\n      '&-vertical': {\n        flexDirection: 'column'\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport default genSpaceCompactStyle;", "map": {"version": 3, "names": ["genSpaceCompactStyle", "token", "componentCls", "display", "width", "flexDirection"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/antd/es/space/style/compact.js"], "sourcesContent": ["const genSpaceCompactStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [componentCls]: {\n      '&-block': {\n        display: 'flex',\n        width: '100%'\n      },\n      '&-vertical': {\n        flexDirection: 'column'\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport default genSpaceCompactStyle;"], "mappings": "AAAA,MAAMA,oBAAoB,GAAGC,KAAK,IAAI;EACpC,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,OAAO;IACL,CAACC,YAAY,GAAG;MACd,SAAS,EAAE;QACTC,OAAO,EAAE,MAAM;QACfC,KAAK,EAAE;MACT,CAAC;MACD,YAAY,EAAE;QACZC,aAAa,EAAE;MACjB;IACF;EACF,CAAC;AACH,CAAC;AACD;AACA,eAAeL,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}