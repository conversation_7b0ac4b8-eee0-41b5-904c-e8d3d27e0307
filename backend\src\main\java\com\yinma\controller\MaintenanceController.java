package com.yinma.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinma.common.Result;
import com.yinma.entity.Maintenance;
import com.yinma.entity.MaintenancePlan;
import com.yinma.entity.MaintenanceWorkItem;
import com.yinma.entity.MaintenancePart;
import com.yinma.service.MaintenanceService;
import com.yinma.dto.MaintenanceDTO;
import com.yinma.dto.MaintenanceStatisticsDTO;
import com.yinma.vo.MaintenanceVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 维护管理控制器
 * 银马实业设备维护保养管理系统
 * 
 * <AUTHOR>
 * @since 2024-05-20
 */
@Api(tags = "维护管理")
@RestController
@RequestMapping("/api/maintenance")
@Slf4j
public class MaintenanceController {

    @Autowired
    private MaintenanceService maintenanceService;

    /**
     * 分页查询维护记录
     */
    @ApiOperation("分页查询维护记录")
    @GetMapping("/page")
    public Result<IPage<MaintenanceVO>> getMaintenancePage(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("工单号") @RequestParam(required = false) String workOrderNo,
            @ApiParam("设备名称") @RequestParam(required = false) String equipmentName,
            @ApiParam("设备ID") @RequestParam(required = false) Long equipmentId,
            @ApiParam("客户名称") @RequestParam(required = false) String customer,
            @ApiParam("维护类型") @RequestParam(required = false) String maintenanceType,
            @ApiParam("状态") @RequestParam(required = false) String status,
            @ApiParam("优先级") @RequestParam(required = false) String priority,
            @ApiParam("技师ID") @RequestParam(required = false) Long technicianId,
            @ApiParam("开始日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @ApiParam("结束日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        
        try {
            Page<Maintenance> page = new Page<>(current, size);
            IPage<MaintenanceVO> result = maintenanceService.getMaintenancePage(
                page, workOrderNo, equipmentName, equipmentId, customer, 
                maintenanceType, status, priority, technicianId, startDate, endDate);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询维护记录失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询维护记录详情
     */
    @ApiOperation("查询维护记录详情")
    @GetMapping("/{id}")
    public Result<MaintenanceVO> getMaintenanceById(
            @ApiParam("维护记录ID") @PathVariable Long id) {
        try {
            MaintenanceVO maintenance = maintenanceService.getMaintenanceDetailById(id);
            if (maintenance != null) {
                return Result.success(maintenance);
            } else {
                return Result.error("维护记录不存在");
            }
        } catch (Exception e) {
            log.error("查询维护记录详情失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 创建维护工单
     */
    @ApiOperation("创建维护工单")
    @PostMapping
    public Result<Maintenance> createMaintenance(
            @ApiParam("维护工单信息") @Valid @RequestBody MaintenanceDTO maintenanceDTO) {
        try {
            Maintenance maintenance = maintenanceService.createMaintenance(maintenanceDTO);
            return Result.success(maintenance);
        } catch (Exception e) {
            log.error("创建维护工单失败", e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 更新维护工单
     */
    @ApiOperation("更新维护工单")
    @PutMapping("/{id}")
    public Result<Maintenance> updateMaintenance(
            @ApiParam("维护记录ID") @PathVariable Long id,
            @ApiParam("维护工单信息") @Valid @RequestBody MaintenanceDTO maintenanceDTO) {
        try {
            Maintenance maintenance = maintenanceService.updateMaintenance(id, maintenanceDTO);
            return Result.success(maintenance);
        } catch (Exception e) {
            log.error("更新维护工单失败", e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除维护记录
     */
    @ApiOperation("删除维护记录")
    @DeleteMapping("/{id}")
    public Result<Void> deleteMaintenance(
            @ApiParam("维护记录ID") @PathVariable Long id) {
        try {
            boolean success = maintenanceService.removeById(id);
            if (success) {
                return Result.success();
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除维护记录失败", e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除维护记录
     */
    @ApiOperation("批量删除维护记录")
    @DeleteMapping("/batch")
    public Result<Void> batchDeleteMaintenance(
            @ApiParam("维护记录ID列表") @RequestBody List<Long> ids) {
        try {
            boolean success = maintenanceService.removeByIds(ids);
            if (success) {
                return Result.success();
            } else {
                return Result.error("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除维护记录失败", e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 更新维护状态
     */
    @ApiOperation("更新维护状态")
    @PutMapping("/{id}/status")
    public Result<Maintenance> updateMaintenanceStatus(
            @ApiParam("维护记录ID") @PathVariable Long id,
            @ApiParam("新状态") @RequestParam String status,
            @ApiParam("备注") @RequestParam(required = false) String remark) {
        try {
            Maintenance maintenance = maintenanceService.updateMaintenanceStatus(id, status, remark);
            return Result.success(maintenance);
        } catch (Exception e) {
            log.error("更新维护状态失败", e);
            return Result.error("更新状态失败: " + e.getMessage());
        }
    }

    /**
     * 开始维护工作
     */
    @ApiOperation("开始维护工作")
    @PutMapping("/{id}/start")
    public Result<Maintenance> startMaintenance(
            @ApiParam("维护记录ID") @PathVariable Long id) {
        try {
            Maintenance maintenance = maintenanceService.startMaintenance(id);
            return Result.success(maintenance);
        } catch (Exception e) {
            log.error("开始维护工作失败", e);
            return Result.error("开始维护失败: " + e.getMessage());
        }
    }

    /**
     * 完成维护工作
     */
    @ApiOperation("完成维护工作")
    @PutMapping("/{id}/complete")
    public Result<Maintenance> completeMaintenance(
            @ApiParam("维护记录ID") @PathVariable Long id,
            @ApiParam("完成信息") @RequestBody Map<String, Object> completeInfo) {
        try {
            Maintenance maintenance = maintenanceService.completeMaintenance(id, completeInfo);
            return Result.success(maintenance);
        } catch (Exception e) {
            log.error("完成维护工作失败", e);
            return Result.error("完成维护失败: " + e.getMessage());
        }
    }

    /**
     * 查询维护工作项
     */
    @ApiOperation("查询维护工作项")
    @GetMapping("/{id}/work-items")
    public Result<List<MaintenanceWorkItem>> getMaintenanceWorkItems(
            @ApiParam("维护记录ID") @PathVariable Long id) {
        try {
            List<MaintenanceWorkItem> workItems = maintenanceService.getMaintenanceWorkItems(id);
            return Result.success(workItems);
        } catch (Exception e) {
            log.error("查询维护工作项失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 更新工作项状态
     */
    @ApiOperation("更新工作项状态")
    @PutMapping("/work-items/{itemId}/status")
    public Result<MaintenanceWorkItem> updateWorkItemStatus(
            @ApiParam("工作项ID") @PathVariable Long itemId,
            @ApiParam("状态") @RequestParam String status,
            @ApiParam("耗时(小时)") @RequestParam(required = false) Double duration,
            @ApiParam("备注") @RequestParam(required = false) String remark) {
        try {
            MaintenanceWorkItem workItem = maintenanceService.updateWorkItemStatus(
                itemId, status, duration, remark);
            return Result.success(workItem);
        } catch (Exception e) {
            log.error("更新工作项状态失败", e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 查询使用配件
     */
    @ApiOperation("查询使用配件")
    @GetMapping("/{id}/parts")
    public Result<List<MaintenancePart>> getMaintenanceParts(
            @ApiParam("维护记录ID") @PathVariable Long id) {
        try {
            List<MaintenancePart> parts = maintenanceService.getMaintenanceParts(id);
            return Result.success(parts);
        } catch (Exception e) {
            log.error("查询使用配件失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 添加使用配件
     */
    @ApiOperation("添加使用配件")
    @PostMapping("/{id}/parts")
    public Result<MaintenancePart> addMaintenancePart(
            @ApiParam("维护记录ID") @PathVariable Long id,
            @ApiParam("配件信息") @RequestBody MaintenancePart part) {
        try {
            part.setMaintenanceId(id);
            MaintenancePart savedPart = maintenanceService.addMaintenancePart(part);
            return Result.success(savedPart);
        } catch (Exception e) {
            log.error("添加使用配件失败", e);
            return Result.error("添加失败: " + e.getMessage());
        }
    }

    /**
     * 查询维护计划
     */
    @ApiOperation("查询维护计划")
    @GetMapping("/plans")
    public Result<List<MaintenancePlan>> getMaintenancePlans(
            @ApiParam("设备ID") @RequestParam(required = false) Long equipmentId,
            @ApiParam("客户名称") @RequestParam(required = false) String customer,
            @ApiParam("是否逾期") @RequestParam(required = false) Boolean overdue,
            @ApiParam("即将到期天数") @RequestParam(defaultValue = "7") Integer dueDays) {
        try {
            List<MaintenancePlan> plans = maintenanceService.getMaintenancePlans(
                equipmentId, customer, overdue, dueDays);
            return Result.success(plans);
        } catch (Exception e) {
            log.error("查询维护计划失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 创建维护计划
     */
    @ApiOperation("创建维护计划")
    @PostMapping("/plans")
    public Result<MaintenancePlan> createMaintenancePlan(
            @ApiParam("维护计划信息") @Valid @RequestBody MaintenancePlan plan) {
        try {
            MaintenancePlan savedPlan = maintenanceService.createMaintenancePlan(plan);
            return Result.success(savedPlan);
        } catch (Exception e) {
            log.error("创建维护计划失败", e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 根据计划创建维护工单
     */
    @ApiOperation("根据计划创建维护工单")
    @PostMapping("/plans/{planId}/create-order")
    public Result<Maintenance> createMaintenanceFromPlan(
            @ApiParam("计划ID") @PathVariable Long planId,
            @ApiParam("技师ID") @RequestParam Long technicianId,
            @ApiParam("计划日期") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate plannedDate) {
        try {
            Maintenance maintenance = maintenanceService.createMaintenanceFromPlan(
                planId, technicianId, plannedDate);
            return Result.success(maintenance);
        } catch (Exception e) {
            log.error("根据计划创建维护工单失败", e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 延期维护计划
     */
    @ApiOperation("延期维护计划")
    @PutMapping("/plans/{planId}/postpone")
    public Result<MaintenancePlan> postponeMaintenancePlan(
            @ApiParam("计划ID") @PathVariable Long planId,
            @ApiParam("新的计划日期") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate newDate,
            @ApiParam("延期原因") @RequestParam String reason) {
        try {
            MaintenancePlan plan = maintenanceService.postponeMaintenancePlan(planId, newDate, reason);
            return Result.success(plan);
        } catch (Exception e) {
            log.error("延期维护计划失败", e);
            return Result.error("延期失败: " + e.getMessage());
        }
    }

    /**
     * 获取维护统计数据
     */
    @ApiOperation("获取维护统计数据")
    @GetMapping("/statistics")
    public Result<MaintenanceStatisticsDTO> getMaintenanceStatistics(
            @ApiParam("开始日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @ApiParam("结束日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
            @ApiParam("设备ID") @RequestParam(required = false) Long equipmentId,
            @ApiParam("客户名称") @RequestParam(required = false) String customer) {
        try {
            MaintenanceStatisticsDTO statistics = maintenanceService.getMaintenanceStatistics(
                startDate, endDate, equipmentId, customer);
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取维护统计数据失败", e);
            return Result.error("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取维护趋势数据
     */
    @ApiOperation("获取维护趋势数据")
    @GetMapping("/trends")
    public Result<Map<String, Object>> getMaintenanceTrends(
            @ApiParam("统计类型") @RequestParam(defaultValue = "monthly") String type,
            @ApiParam("开始日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @ApiParam("结束日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        try {
            Map<String, Object> trends = maintenanceService.getMaintenanceTrends(type, startDate, endDate);
            return Result.success(trends);
        } catch (Exception e) {
            log.error("获取维护趋势数据失败", e);
            return Result.error("获取趋势数据失败: " + e.getMessage());
        }
    }

    /**
     * 上传维护附件
     */
    @ApiOperation("上传维护附件")
    @PostMapping("/{id}/attachments")
    public Result<String> uploadMaintenanceAttachment(
            @ApiParam("维护记录ID") @PathVariable Long id,
            @ApiParam("附件文件") @RequestParam("file") MultipartFile file) {
        try {
            String fileUrl = maintenanceService.uploadMaintenanceAttachment(id, file);
            return Result.success(fileUrl);
        } catch (Exception e) {
            log.error("上传维护附件失败", e);
            return Result.error("上传失败: " + e.getMessage());
        }
    }

    /**
     * 导出维护报告
     */
    @ApiOperation("导出维护报告")
    @GetMapping("/export")
    public Result<String> exportMaintenanceReport(
            @ApiParam("开始日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @ApiParam("结束日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
            @ApiParam("设备ID") @RequestParam(required = false) Long equipmentId,
            @ApiParam("客户名称") @RequestParam(required = false) String customer,
            @ApiParam("报告格式") @RequestParam(defaultValue = "excel") String format) {
        try {
            String reportUrl = maintenanceService.exportMaintenanceReport(
                startDate, endDate, equipmentId, customer, format);
            return Result.success(reportUrl);
        } catch (Exception e) {
            log.error("导出维护报告失败", e);
            return Result.error("导出失败: " + e.getMessage());
        }
    }

    /**
     * 获取技师工作负载
     */
    @ApiOperation("获取技师工作负载")
    @GetMapping("/technician-workload")
    public Result<List<Map<String, Object>>> getTechnicianWorkload(
            @ApiParam("开始日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @ApiParam("结束日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        try {
            List<Map<String, Object>> workload = maintenanceService.getTechnicianWorkload(startDate, endDate);
            return Result.success(workload);
        } catch (Exception e) {
            log.error("获取技师工作负载失败", e);
            return Result.error("获取工作负载失败: " + e.getMessage());
        }
    }

    /**
     * 获取设备维护历史
     */
    @ApiOperation("获取设备维护历史")
    @GetMapping("/equipment/{equipmentId}/history")
    public Result<List<MaintenanceVO>> getEquipmentMaintenanceHistory(
            @ApiParam("设备ID") @PathVariable Long equipmentId,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer size) {
        try {
            Page<Maintenance> page = new Page<>(current, size);
            List<MaintenanceVO> history = maintenanceService.getEquipmentMaintenanceHistory(equipmentId, page);
            return Result.success(history);
        } catch (Exception e) {
            log.error("获取设备维护历史失败", e);
            return Result.error("获取维护历史失败: " + e.getMessage());
        }
    }

    /**
     * 客户评价维护服务
     */
    @ApiOperation("客户评价维护服务")
    @PutMapping("/{id}/rating")
    public Result<Maintenance> rateMaintenance(
            @ApiParam("维护记录ID") @PathVariable Long id,
            @ApiParam("评分(1-5)") @RequestParam Integer rating,
            @ApiParam("反馈意见") @RequestParam(required = false) String feedback) {
        try {
            Maintenance maintenance = maintenanceService.rateMaintenance(id, rating, feedback);
            return Result.success(maintenance);
        } catch (Exception e) {
            log.error("客户评价维护服务失败", e);
            return Result.error("评价失败: " + e.getMessage());
        }
    }
}