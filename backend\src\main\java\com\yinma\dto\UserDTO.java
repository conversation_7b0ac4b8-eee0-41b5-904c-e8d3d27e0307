package com.yinma.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户信息DTO
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
@Data
@Accessors(chain = true)
@Schema(description = "用户信息DTO")
public class UserDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户名")
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 20, message = "用户名长度必须在3-20个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "用户名只能包含字母、数字和下划线")
    private String username;

    @Schema(description = "密码")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    private String password;

    @Schema(description = "真实姓名")
    @NotBlank(message = "真实姓名不能为空")
    @Size(max = 50, message = "真实姓名长度不能超过50个字符")
    private String realName;

    @Schema(description = "邮箱")
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    private String email;

    @Schema(description = "手机号")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    @Schema(description = "头像URL")
    private String avatar;

    @Schema(description = "性别：0-未知，1-男，2-女")
    @Min(value = 0, message = "性别值不正确")
    @Max(value = 2, message = "性别值不正确")
    private Integer gender;

    @Schema(description = "生日")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime birthday;

    @Schema(description = "部门ID")
    private Long deptId;

    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "职位")
    @Size(max = 100, message = "职位长度不能超过100个字符")
    private String position;

    @Schema(description = "员工编号")
    @Size(max = 50, message = "员工编号长度不能超过50个字符")
    private String employeeNo;

    @Schema(description = "入职日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime hireDate;

    @Schema(description = "用户状态：0-禁用，1-启用")
    private Integer status;

    @Schema(description = "是否超级管理员：0-否，1-是")
    private Integer isAdmin;

    @Schema(description = "最后登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastLoginTime;

    @Schema(description = "最后登录IP")
    private String lastLoginIp;

    @Schema(description = "登录次数")
    private Integer loginCount;

    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    @Schema(description = "角色列表")
    private List<RoleDTO> roles;

    @Schema(description = "权限列表")
    private List<String> permissions;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 用户查询DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "用户查询DTO")
    public static class UserQueryDTO implements Serializable {

        @Schema(description = "页码")
        @Min(value = 1, message = "页码必须大于0")
        private Integer current = 1;

        @Schema(description = "每页大小")
        @Min(value = 1, message = "每页大小必须大于0")
        @Max(value = 100, message = "每页大小不能超过100")
        private Integer size = 10;

        @Schema(description = "用户名")
        private String username;

        @Schema(description = "真实姓名")
        private String realName;

        @Schema(description = "邮箱")
        private String email;

        @Schema(description = "手机号")
        private String phone;

        @Schema(description = "部门ID")
        private Long deptId;

        @Schema(description = "职位")
        private String position;

        @Schema(description = "员工编号")
        private String employeeNo;

        @Schema(description = "用户状态：0-禁用，1-启用")
        private Integer status;

        @Schema(description = "是否超级管理员：0-否，1-是")
        private Integer isAdmin;

        @Schema(description = "创建开始时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createTimeStart;

        @Schema(description = "创建结束时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createTimeEnd;
    }

    /**
     * 登录DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "登录DTO")
    public static class LoginDTO implements Serializable {

        @Schema(description = "用户名")
        @NotBlank(message = "用户名不能为空")
        private String username;

        @Schema(description = "密码")
        @NotBlank(message = "密码不能为空")
        private String password;

        @Schema(description = "验证码")
        private String captcha;

        @Schema(description = "验证码key")
        private String captchaKey;

        @Schema(description = "记住我")
        private Boolean rememberMe = false;
    }

    /**
     * 登录结果DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "登录结果DTO")
    public static class LoginResultDTO implements Serializable {

        @Schema(description = "访问令牌")
        private String accessToken;

        @Schema(description = "刷新令牌")
        private String refreshToken;

        @Schema(description = "令牌类型")
        private String tokenType = "Bearer";

        @Schema(description = "过期时间（秒）")
        private Long expiresIn;

        @Schema(description = "用户信息")
        private UserDTO userInfo;
    }

    /**
     * 修改密码DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "修改密码DTO")
    public static class ChangePasswordDTO implements Serializable {

        @Schema(description = "原密码")
        @NotBlank(message = "原密码不能为空")
        private String oldPassword;

        @Schema(description = "新密码")
        @NotBlank(message = "新密码不能为空")
        @Size(min = 6, max = 20, message = "新密码长度必须在6-20个字符之间")
        private String newPassword;

        @Schema(description = "确认密码")
        @NotBlank(message = "确认密码不能为空")
        private String confirmPassword;
    }

    /**
     * 重置密码DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "重置密码DTO")
    public static class ResetPasswordDTO implements Serializable {

        @Schema(description = "用户ID")
        @NotNull(message = "用户ID不能为空")
        private Long userId;

        @Schema(description = "新密码")
        @NotBlank(message = "新密码不能为空")
        @Size(min = 6, max = 20, message = "新密码长度必须在6-20个字符之间")
        private String newPassword;
    }

    /**
     * 用户角色分配DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "用户角色分配DTO")
    public static class UserRoleAssignDTO implements Serializable {

        @Schema(description = "用户ID")
        @NotNull(message = "用户ID不能为空")
        private Long userId;

        @Schema(description = "角色ID列表")
        @NotEmpty(message = "角色ID列表不能为空")
        private List<Long> roleIds;
    }

    /**
     * 用户状态更新DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "用户状态更新DTO")
    public static class UserStatusUpdateDTO implements Serializable {

        @Schema(description = "用户ID")
        @NotNull(message = "用户ID不能为空")
        private Long userId;

        @Schema(description = "状态：0-禁用，1-启用")
        @NotNull(message = "状态不能为空")
        @Min(value = 0, message = "状态值不正确")
        @Max(value = 1, message = "状态值不正确")
        private Integer status;
    }

    /**
     * 用户个人信息更新DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "用户个人信息更新DTO")
    public static class UserProfileUpdateDTO implements Serializable {

        @Schema(description = "真实姓名")
        @NotBlank(message = "真实姓名不能为空")
        @Size(max = 50, message = "真实姓名长度不能超过50个字符")
        private String realName;

        @Schema(description = "邮箱")
        @Email(message = "邮箱格式不正确")
        @Size(max = 100, message = "邮箱长度不能超过100个字符")
        private String email;

        @Schema(description = "手机号")
        @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
        private String phone;

        @Schema(description = "头像URL")
        private String avatar;

        @Schema(description = "性别：0-未知，1-男，2-女")
        @Min(value = 0, message = "性别值不正确")
        @Max(value = 2, message = "性别值不正确")
        private Integer gender;

        @Schema(description = "生日")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime birthday;
    }

    /**
     * 用户统计DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "用户统计DTO")
    public static class UserStatisticsDTO implements Serializable {

        @Schema(description = "总用户数")
        private Long totalUsers;

        @Schema(description = "启用用户数")
        private Long activeUsers;

        @Schema(description = "禁用用户数")
        private Long inactiveUsers;

        @Schema(description = "今日新增用户数")
        private Long todayNewUsers;

        @Schema(description = "本月新增用户数")
        private Long monthNewUsers;

        @Schema(description = "在线用户数")
        private Long onlineUsers;
    }
}