# 西安银马实业数字化管理系统 - 开发指南

## 目录
- [开发环境搭建](#开发环境搭建)
- [代码规范](#代码规范)
- [项目结构说明](#项目结构说明)
- [开发流程](#开发流程)
- [测试指南](#测试指南)
- [部署指南](#部署指南)
- [常见问题](#常见问题)

## 开发环境搭建

### 必需软件
- **Java**: OpenJDK 11 或更高版本
- **Node.js**: 16.x 或更高版本
- **PostgreSQL**: 13.x 或更高版本
- **Redis**: 6.x 或更高版本
- **Git**: 版本控制
- **Docker**: 容器化部署（可选）

### IDE推荐
- **后端开发**: IntelliJ IDEA / Eclipse
- **前端开发**: VS Code / WebStorm
- **数据库管理**: DBeaver / pgAdmin

### 环境配置

1. **克隆项目**
```bash
git clone [项目地址]
cd Link-YinMa
```

2. **数据库初始化**
```bash
# 创建数据库
psql -U postgres -c "CREATE DATABASE yinma_db;"

# 执行初始化脚本
psql -U postgres -d yinma_db -f database/init.sql
```

3. **Redis配置**
```bash
# 使用Docker启动Redis
docker run -d --name redis -p 6379:6379 redis:6-alpine
```

4. **后端配置**
```bash
cd backend
cp src/main/resources/application-dev.yml.example src/main/resources/application-dev.yml
# 修改数据库连接配置
```

5. **前端配置**
```bash
cd frontend
npm install
cp .env.example .env.local
# 修改API地址配置
```

## 代码规范

### 后端代码规范

#### Java代码风格
- 使用Google Java Style Guide
- 类名使用PascalCase
- 方法名和变量名使用camelCase
- 常量使用UPPER_SNAKE_CASE
- 包名使用小写字母

#### 注释规范
```java
/**
 * BOM管理服务类
 * 提供BOM的增删改查和业务逻辑处理
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class BomService {
    
    /**
     * 根据ID查询BOM信息
     * 
     * @param id BOM ID
     * @return BOM详细信息
     * @throws BomNotFoundException 当BOM不存在时抛出
     */
    public BomDTO findById(Long id) {
        // 实现逻辑
    }
}
```

#### 异常处理
```java
// 自定义业务异常
public class BomNotFoundException extends BusinessException {
    public BomNotFoundException(String message) {
        super(ErrorCode.BOM_NOT_FOUND, message);
    }
}

// 全局异常处理
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(BomNotFoundException.class)
    public ResponseEntity<ErrorResponse> handleBomNotFound(BomNotFoundException e) {
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
            .body(new ErrorResponse(e.getErrorCode(), e.getMessage()));
    }
}
```

### 前端代码规范

#### React组件规范
```jsx
// 函数组件使用箭头函数
const BomList = ({ onEdit, onDelete }) => {
  // Hook在组件顶部
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  
  // 事件处理函数
  const handleEdit = useCallback((record) => {
    onEdit?.(record);
  }, [onEdit]);
  
  // 渲染函数
  return (
    <div className="bom-list">
      {/* JSX内容 */}
    </div>
  );
};

// PropTypes定义
BomList.propTypes = {
  onEdit: PropTypes.func,
  onDelete: PropTypes.func,
};

export default BomList;
```

#### CSS规范
```css
/* 使用BEM命名规范 */
.bom-list {
  padding: 16px;
}

.bom-list__header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.bom-list__search {
  flex: 1;
  margin-right: 16px;
}

.bom-list__actions {
  display: flex;
  gap: 8px;
}
```

#### API调用规范
```javascript
// 使用自定义Hook
const BomManagement = () => {
  const {
    data: bomList,
    loading,
    error,
    refresh
  } = usePaginationApi('/api/bom', {
    pageSize: 20,
    defaultParams: { status: 'active' }
  });
  
  const { submit: deleteBom } = useDelete('/api/bom');
  
  const handleDelete = async (id) => {
    try {
      await deleteBom(id);
      message.success('删除成功');
      refresh();
    } catch (error) {
      // 错误已由Hook处理
    }
  };
};
```

## 项目结构说明

### 后端结构
```
backend/src/main/java/com/yinma/
├── YinmaApplication.java          # 启动类
├── config/                        # 配置类
│   ├── DatabaseConfig.java        # 数据库配置
│   ├── RedisConfig.java           # Redis配置
│   ├── SecurityConfig.java        # 安全配置
│   └── SwaggerConfig.java         # API文档配置
├── controller/                    # 控制器层
│   ├── BomController.java         # BOM管理控制器
│   ├── MaterialController.java    # 物料管理控制器
│   └── BaseController.java        # 基础控制器
├── service/                       # 服务层
│   ├── BomService.java            # BOM业务逻辑
│   ├── MaterialService.java       # 物料业务逻辑
│   └── impl/                      # 服务实现
├── repository/                    # 数据访问层
│   ├── BomRepository.java         # BOM数据访问
│   └── MaterialRepository.java    # 物料数据访问
├── entity/                        # 实体类
│   ├── Bom.java                   # BOM实体
│   ├── Material.java              # 物料实体
│   └── BaseEntity.java            # 基础实体
├── dto/                           # 数据传输对象
│   ├── BomDTO.java                # BOM传输对象
│   ├── MaterialDTO.java           # 物料传输对象
│   └── PageDTO.java               # 分页传输对象
├── exception/                     # 异常处理
│   ├── BusinessException.java     # 业务异常
│   ├── ErrorCode.java             # 错误码定义
│   └── GlobalExceptionHandler.java # 全局异常处理
└── util/                          # 工具类
    ├── DateUtil.java              # 日期工具
    ├── StringUtil.java            # 字符串工具
    └── ValidationUtil.java        # 验证工具
```

### 前端结构
```
frontend/src/
├── App.js                         # 主应用组件
├── components/                     # 通用组件
│   ├── Common/                     # 基础组件
│   │   ├── ErrorBoundary.js        # 错误边界
│   │   ├── LoadingProvider.js      # 全局Loading
│   │   └── ConfirmModal.js         # 确认对话框
│   ├── Layout/                     # 布局组件
│   │   ├── Header.js               # 页头
│   │   ├── Sidebar.js              # 侧边栏
│   │   └── Footer.js               # 页脚
│   └── Examples/                   # 示例组件
│       └── ApiHookExample.js       # API Hook示例
├── pages/                          # 页面组件
│   ├── BomManagement/              # BOM管理
│   │   ├── index.js                # 主页面
│   │   ├── BomForm.js              # BOM表单
│   │   └── BomDetail.js            # BOM详情
│   ├── MaterialManagement/         # 物料管理
│   └── Dashboard/                  # 仪表板
├── hooks/                          # 自定义Hook
│   ├── useApi.js                   # API请求Hook
│   ├── useForm.js                  # 表单Hook
│   └── usePermission.js            # 权限Hook
├── services/                       # API服务
│   ├── api.js                      # API基础配置
│   ├── bomApi.js                   # BOM API
│   └── materialApi.js              # 物料API
├── store/                          # Redux状态管理
│   ├── index.js                    # Store配置
│   ├── slices/                     # 状态切片
│   └── middleware/                 # 中间件
├── utils/                          # 工具函数
│   ├── validation.js               # 数据验证
│   ├── format.js                   # 格式化工具
│   └── constants.js                # 常量定义
└── styles/                         # 样式文件
    ├── global.css                  # 全局样式
    └── variables.css               # CSS变量
```

## 开发流程

### 功能开发流程

1. **需求分析**
   - 理解业务需求
   - 设计数据模型
   - 定义API接口

2. **后端开发**
   - 创建实体类
   - 实现Repository层
   - 实现Service层
   - 实现Controller层
   - 编写单元测试

3. **前端开发**
   - 创建页面组件
   - 实现API调用
   - 添加表单验证
   - 处理错误情况
   - 编写组件测试

4. **集成测试**
   - 前后端联调
   - 功能测试
   - 性能测试

5. **代码审查**
   - 代码规范检查
   - 逻辑审查
   - 安全审查

### Git工作流

```bash
# 创建功能分支
git checkout -b feature/bom-management

# 开发过程中提交
git add .
git commit -m "feat: 添加BOM列表查询功能"

# 推送到远程
git push origin feature/bom-management

# 创建Pull Request
# 代码审查通过后合并到主分支
```

### 提交信息规范

使用Conventional Commits规范：

```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动

示例：
feat(bom): 添加BOM批量导入功能
fix(material): 修复物料查询分页问题
docs: 更新API文档
```

## 测试指南

### 后端测试

#### 单元测试
```java
@ExtendWith(MockitoExtension.class)
class BomServiceTest {
    
    @Mock
    private BomRepository bomRepository;
    
    @InjectMocks
    private BomService bomService;
    
    @Test
    void shouldReturnBomWhenIdExists() {
        // Given
        Long bomId = 1L;
        Bom bom = new Bom();
        bom.setId(bomId);
        when(bomRepository.findById(bomId)).thenReturn(Optional.of(bom));
        
        // When
        BomDTO result = bomService.findById(bomId);
        
        // Then
        assertThat(result.getId()).isEqualTo(bomId);
    }
}
```

#### 集成测试
```java
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@Testcontainers
class BomControllerIntegrationTest {
    
    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:13")
            .withDatabaseName("test")
            .withUsername("test")
            .withPassword("test");
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void shouldCreateBom() {
        // 测试BOM创建接口
    }
}
```

### 前端测试

#### 组件测试
```javascript
import { render, screen, fireEvent } from '@testing-library/react';
import { BomList } from './BomList';

describe('BomList', () => {
  test('should render bom list', () => {
    const mockData = [
      { id: 1, name: 'BOM-001', status: 'active' }
    ];
    
    render(<BomList data={mockData} />);
    
    expect(screen.getByText('BOM-001')).toBeInTheDocument();
  });
  
  test('should call onEdit when edit button clicked', () => {
    const mockOnEdit = jest.fn();
    const mockData = [{ id: 1, name: 'BOM-001' }];
    
    render(<BomList data={mockData} onEdit={mockOnEdit} />);
    
    fireEvent.click(screen.getByText('编辑'));
    
    expect(mockOnEdit).toHaveBeenCalledWith(mockData[0]);
  });
});
```

#### E2E测试
```javascript
// cypress/integration/bom-management.spec.js
describe('BOM Management', () => {
  beforeEach(() => {
    cy.login('admin', 'password');
    cy.visit('/bom-management');
  });
  
  it('should create new bom', () => {
    cy.get('[data-testid="add-bom-btn"]').click();
    cy.get('[data-testid="bom-name-input"]').type('新BOM');
    cy.get('[data-testid="save-btn"]').click();
    
    cy.contains('创建成功').should('be.visible');
    cy.contains('新BOM').should('be.visible');
  });
});
```

## 部署指南

### 开发环境部署

```bash
# 使用Docker Compose
docker-compose -f docker-compose.dev.yml up -d
```

### 生产环境部署

1. **构建镜像**
```bash
# 构建后端镜像
cd backend
docker build -t yinma-backend:latest .

# 构建前端镜像
cd frontend
docker build -t yinma-frontend:latest .
```

2. **部署到服务器**
```bash
# 生产环境部署
docker-compose -f docker-compose.prod.yml up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

3. **健康检查**
```bash
# 检查后端服务
curl http://localhost:8080/health

# 检查前端服务
curl http://localhost:3000
```

### 监控配置

1. **系统监控**
   - CPU、内存、磁盘使用率监控
   - 应用性能监控
   - 数据库连接池监控

2. **日志管理**
   - 应用日志收集
   - 错误日志告警
   - 访问日志分析

3. **告警配置**
   - 系统资源告警
   - 应用错误告警
   - 业务指标告警

## 常见问题

### 开发环境问题

**Q: 后端启动失败，提示数据库连接错误**
A: 检查PostgreSQL是否启动，配置文件中的数据库连接信息是否正确。

**Q: 前端启动后API请求失败**
A: 检查后端服务是否启动，前端配置的API地址是否正确。

**Q: Redis连接失败**
A: 检查Redis服务是否启动，端口是否被占用。

### 部署问题

**Q: Docker容器启动失败**
A: 检查Docker镜像是否构建成功，端口是否被占用，环境变量是否配置正确。

**Q: 数据库迁移失败**
A: 检查数据库权限，迁移脚本语法是否正确。

### 性能问题

**Q: 页面加载缓慢**
A: 检查网络请求，优化SQL查询，启用缓存机制。

**Q: 内存使用过高**
A: 检查是否有内存泄漏，优化数据查询，调整JVM参数。

## 联系方式

- **技术负责人**: [待填写]
- **开发团队**: [待填写]
- **技术支持**: [待填写]
- **项目地址**: [待填写]

---

*本文档会随着项目发展持续更新，请关注最新版本。*