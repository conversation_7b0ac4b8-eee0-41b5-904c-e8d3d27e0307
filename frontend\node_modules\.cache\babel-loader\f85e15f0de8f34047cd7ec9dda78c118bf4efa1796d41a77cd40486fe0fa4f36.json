{"ast": null, "code": "/**\n * Cut input selection into 2 part and return text before selection start\n */\nexport function getBeforeSelectionText(input) {\n  var selectionStart = input.selectionStart;\n  return input.value.slice(0, selectionStart);\n}\n/**\n * Find the last match prefix index\n */\nexport function getLastMeasureIndex(text, prefix) {\n  return prefix.reduce(function (lastMatch, prefixStr) {\n    var lastIndex = text.lastIndexOf(prefixStr);\n    if (lastIndex > lastMatch.location) {\n      return {\n        location: lastIndex,\n        prefix: prefixStr\n      };\n    }\n    return lastMatch;\n  }, {\n    location: -1,\n    prefix: ''\n  });\n}\nfunction lower(char) {\n  return (char || '').toLowerCase();\n}\nfunction reduceText(text, targetText, split) {\n  var firstChar = text[0];\n  if (!firstChar || firstChar === split) {\n    return text;\n  }\n\n  // Reuse rest text as it can\n  var restText = text;\n  var targetTextLen = targetText.length;\n  for (var i = 0; i < targetTextLen; i += 1) {\n    if (lower(restText[i]) !== lower(targetText[i])) {\n      restText = restText.slice(i);\n      break;\n    } else if (i === targetTextLen - 1) {\n      restText = restText.slice(targetTextLen);\n    }\n  }\n  return restText;\n}\n\n/**\n * Paint targetText into current text:\n *  text: little@litest\n *  targetText: light\n *  => little @light test\n */\nexport function replaceWithMeasure(text, measureConfig) {\n  var measureLocation = measureConfig.measureLocation,\n    prefix = measureConfig.prefix,\n    targetText = measureConfig.targetText,\n    selectionStart = measureConfig.selectionStart,\n    split = measureConfig.split;\n\n  // Before text will append one space if have other text\n  var beforeMeasureText = text.slice(0, measureLocation);\n  if (beforeMeasureText[beforeMeasureText.length - split.length] === split) {\n    beforeMeasureText = beforeMeasureText.slice(0, beforeMeasureText.length - split.length);\n  }\n  if (beforeMeasureText) {\n    beforeMeasureText = \"\".concat(beforeMeasureText).concat(split);\n  }\n\n  // Cut duplicate string with current targetText\n  var restText = reduceText(text.slice(selectionStart), targetText.slice(selectionStart - measureLocation - prefix.length), split);\n  if (restText.slice(0, split.length) === split) {\n    restText = restText.slice(split.length);\n  }\n  var connectedStartText = \"\".concat(beforeMeasureText).concat(prefix).concat(targetText).concat(split);\n  return {\n    text: \"\".concat(connectedStartText).concat(restText),\n    selectionLocation: connectedStartText.length\n  };\n}\nexport function setInputSelection(input, location) {\n  input.setSelectionRange(location, location);\n\n  /**\n   * Reset caret into view.\n   * Since this function always called by user control, it's safe to focus element.\n   */\n  input.blur();\n  input.focus();\n}\nexport function validateSearch(text, split) {\n  return !split || text.indexOf(split) === -1;\n}\nexport function filterOption(input, _ref) {\n  var _ref$value = _ref.value,\n    value = _ref$value === void 0 ? '' : _ref$value;\n  var lowerCase = input.toLowerCase();\n  return value.toLowerCase().indexOf(lowerCase) !== -1;\n}", "map": {"version": 3, "names": ["getBeforeSelectionText", "input", "selectionStart", "value", "slice", "getLastMeasureIndex", "text", "prefix", "reduce", "lastMatch", "prefixStr", "lastIndex", "lastIndexOf", "location", "lower", "char", "toLowerCase", "reduceText", "targetText", "split", "firstChar", "restText", "targetTextLen", "length", "i", "replaceWithMeasure", "measureConfig", "measureLocation", "beforeMeasureText", "concat", "connectedStartText", "selectionLocation", "setInputSelection", "setSelectionRange", "blur", "focus", "validateSearch", "indexOf", "filterOption", "_ref", "_ref$value", "lowerCase"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/rc-mentions/es/util.js"], "sourcesContent": ["/**\n * Cut input selection into 2 part and return text before selection start\n */\nexport function getBeforeSelectionText(input) {\n  var selectionStart = input.selectionStart;\n  return input.value.slice(0, selectionStart);\n}\n/**\n * Find the last match prefix index\n */\nexport function getLastMeasureIndex(text, prefix) {\n  return prefix.reduce(function (lastMatch, prefixStr) {\n    var lastIndex = text.lastIndexOf(prefixStr);\n    if (lastIndex > lastMatch.location) {\n      return {\n        location: lastIndex,\n        prefix: prefixStr\n      };\n    }\n    return lastMatch;\n  }, {\n    location: -1,\n    prefix: ''\n  });\n}\nfunction lower(char) {\n  return (char || '').toLowerCase();\n}\nfunction reduceText(text, targetText, split) {\n  var firstChar = text[0];\n  if (!firstChar || firstChar === split) {\n    return text;\n  }\n\n  // Reuse rest text as it can\n  var restText = text;\n  var targetTextLen = targetText.length;\n  for (var i = 0; i < targetTextLen; i += 1) {\n    if (lower(restText[i]) !== lower(targetText[i])) {\n      restText = restText.slice(i);\n      break;\n    } else if (i === targetTextLen - 1) {\n      restText = restText.slice(targetTextLen);\n    }\n  }\n  return restText;\n}\n\n/**\n * Paint targetText into current text:\n *  text: little@litest\n *  targetText: light\n *  => little @light test\n */\nexport function replaceWithMeasure(text, measureConfig) {\n  var measureLocation = measureConfig.measureLocation,\n    prefix = measureConfig.prefix,\n    targetText = measureConfig.targetText,\n    selectionStart = measureConfig.selectionStart,\n    split = measureConfig.split;\n\n  // Before text will append one space if have other text\n  var beforeMeasureText = text.slice(0, measureLocation);\n  if (beforeMeasureText[beforeMeasureText.length - split.length] === split) {\n    beforeMeasureText = beforeMeasureText.slice(0, beforeMeasureText.length - split.length);\n  }\n  if (beforeMeasureText) {\n    beforeMeasureText = \"\".concat(beforeMeasureText).concat(split);\n  }\n\n  // Cut duplicate string with current targetText\n  var restText = reduceText(text.slice(selectionStart), targetText.slice(selectionStart - measureLocation - prefix.length), split);\n  if (restText.slice(0, split.length) === split) {\n    restText = restText.slice(split.length);\n  }\n  var connectedStartText = \"\".concat(beforeMeasureText).concat(prefix).concat(targetText).concat(split);\n  return {\n    text: \"\".concat(connectedStartText).concat(restText),\n    selectionLocation: connectedStartText.length\n  };\n}\nexport function setInputSelection(input, location) {\n  input.setSelectionRange(location, location);\n\n  /**\n   * Reset caret into view.\n   * Since this function always called by user control, it's safe to focus element.\n   */\n  input.blur();\n  input.focus();\n}\nexport function validateSearch(text, split) {\n  return !split || text.indexOf(split) === -1;\n}\nexport function filterOption(input, _ref) {\n  var _ref$value = _ref.value,\n    value = _ref$value === void 0 ? '' : _ref$value;\n  var lowerCase = input.toLowerCase();\n  return value.toLowerCase().indexOf(lowerCase) !== -1;\n}"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,SAASA,sBAAsBA,CAACC,KAAK,EAAE;EAC5C,IAAIC,cAAc,GAAGD,KAAK,CAACC,cAAc;EACzC,OAAOD,KAAK,CAACE,KAAK,CAACC,KAAK,CAAC,CAAC,EAAEF,cAAc,CAAC;AAC7C;AACA;AACA;AACA;AACA,OAAO,SAASG,mBAAmBA,CAACC,IAAI,EAAEC,MAAM,EAAE;EAChD,OAAOA,MAAM,CAACC,MAAM,CAAC,UAAUC,SAAS,EAAEC,SAAS,EAAE;IACnD,IAAIC,SAAS,GAAGL,IAAI,CAACM,WAAW,CAACF,SAAS,CAAC;IAC3C,IAAIC,SAAS,GAAGF,SAAS,CAACI,QAAQ,EAAE;MAClC,OAAO;QACLA,QAAQ,EAAEF,SAAS;QACnBJ,MAAM,EAAEG;MACV,CAAC;IACH;IACA,OAAOD,SAAS;EAClB,CAAC,EAAE;IACDI,QAAQ,EAAE,CAAC,CAAC;IACZN,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AACA,SAASO,KAAKA,CAACC,IAAI,EAAE;EACnB,OAAO,CAACA,IAAI,IAAI,EAAE,EAAEC,WAAW,CAAC,CAAC;AACnC;AACA,SAASC,UAAUA,CAACX,IAAI,EAAEY,UAAU,EAAEC,KAAK,EAAE;EAC3C,IAAIC,SAAS,GAAGd,IAAI,CAAC,CAAC,CAAC;EACvB,IAAI,CAACc,SAAS,IAAIA,SAAS,KAAKD,KAAK,EAAE;IACrC,OAAOb,IAAI;EACb;;EAEA;EACA,IAAIe,QAAQ,GAAGf,IAAI;EACnB,IAAIgB,aAAa,GAAGJ,UAAU,CAACK,MAAM;EACrC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,aAAa,EAAEE,CAAC,IAAI,CAAC,EAAE;IACzC,IAAIV,KAAK,CAACO,QAAQ,CAACG,CAAC,CAAC,CAAC,KAAKV,KAAK,CAACI,UAAU,CAACM,CAAC,CAAC,CAAC,EAAE;MAC/CH,QAAQ,GAAGA,QAAQ,CAACjB,KAAK,CAACoB,CAAC,CAAC;MAC5B;IACF,CAAC,MAAM,IAAIA,CAAC,KAAKF,aAAa,GAAG,CAAC,EAAE;MAClCD,QAAQ,GAAGA,QAAQ,CAACjB,KAAK,CAACkB,aAAa,CAAC;IAC1C;EACF;EACA,OAAOD,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,kBAAkBA,CAACnB,IAAI,EAAEoB,aAAa,EAAE;EACtD,IAAIC,eAAe,GAAGD,aAAa,CAACC,eAAe;IACjDpB,MAAM,GAAGmB,aAAa,CAACnB,MAAM;IAC7BW,UAAU,GAAGQ,aAAa,CAACR,UAAU;IACrChB,cAAc,GAAGwB,aAAa,CAACxB,cAAc;IAC7CiB,KAAK,GAAGO,aAAa,CAACP,KAAK;;EAE7B;EACA,IAAIS,iBAAiB,GAAGtB,IAAI,CAACF,KAAK,CAAC,CAAC,EAAEuB,eAAe,CAAC;EACtD,IAAIC,iBAAiB,CAACA,iBAAiB,CAACL,MAAM,GAAGJ,KAAK,CAACI,MAAM,CAAC,KAAKJ,KAAK,EAAE;IACxES,iBAAiB,GAAGA,iBAAiB,CAACxB,KAAK,CAAC,CAAC,EAAEwB,iBAAiB,CAACL,MAAM,GAAGJ,KAAK,CAACI,MAAM,CAAC;EACzF;EACA,IAAIK,iBAAiB,EAAE;IACrBA,iBAAiB,GAAG,EAAE,CAACC,MAAM,CAACD,iBAAiB,CAAC,CAACC,MAAM,CAACV,KAAK,CAAC;EAChE;;EAEA;EACA,IAAIE,QAAQ,GAAGJ,UAAU,CAACX,IAAI,CAACF,KAAK,CAACF,cAAc,CAAC,EAAEgB,UAAU,CAACd,KAAK,CAACF,cAAc,GAAGyB,eAAe,GAAGpB,MAAM,CAACgB,MAAM,CAAC,EAAEJ,KAAK,CAAC;EAChI,IAAIE,QAAQ,CAACjB,KAAK,CAAC,CAAC,EAAEe,KAAK,CAACI,MAAM,CAAC,KAAKJ,KAAK,EAAE;IAC7CE,QAAQ,GAAGA,QAAQ,CAACjB,KAAK,CAACe,KAAK,CAACI,MAAM,CAAC;EACzC;EACA,IAAIO,kBAAkB,GAAG,EAAE,CAACD,MAAM,CAACD,iBAAiB,CAAC,CAACC,MAAM,CAACtB,MAAM,CAAC,CAACsB,MAAM,CAACX,UAAU,CAAC,CAACW,MAAM,CAACV,KAAK,CAAC;EACrG,OAAO;IACLb,IAAI,EAAE,EAAE,CAACuB,MAAM,CAACC,kBAAkB,CAAC,CAACD,MAAM,CAACR,QAAQ,CAAC;IACpDU,iBAAiB,EAAED,kBAAkB,CAACP;EACxC,CAAC;AACH;AACA,OAAO,SAASS,iBAAiBA,CAAC/B,KAAK,EAAEY,QAAQ,EAAE;EACjDZ,KAAK,CAACgC,iBAAiB,CAACpB,QAAQ,EAAEA,QAAQ,CAAC;;EAE3C;AACF;AACA;AACA;EACEZ,KAAK,CAACiC,IAAI,CAAC,CAAC;EACZjC,KAAK,CAACkC,KAAK,CAAC,CAAC;AACf;AACA,OAAO,SAASC,cAAcA,CAAC9B,IAAI,EAAEa,KAAK,EAAE;EAC1C,OAAO,CAACA,KAAK,IAAIb,IAAI,CAAC+B,OAAO,CAAClB,KAAK,CAAC,KAAK,CAAC,CAAC;AAC7C;AACA,OAAO,SAASmB,YAAYA,CAACrC,KAAK,EAAEsC,IAAI,EAAE;EACxC,IAAIC,UAAU,GAAGD,IAAI,CAACpC,KAAK;IACzBA,KAAK,GAAGqC,UAAU,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,UAAU;EACjD,IAAIC,SAAS,GAAGxC,KAAK,CAACe,WAAW,CAAC,CAAC;EACnC,OAAOb,KAAK,CAACa,WAAW,CAAC,CAAC,CAACqB,OAAO,CAACI,SAAS,CAAC,KAAK,CAAC,CAAC;AACtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}