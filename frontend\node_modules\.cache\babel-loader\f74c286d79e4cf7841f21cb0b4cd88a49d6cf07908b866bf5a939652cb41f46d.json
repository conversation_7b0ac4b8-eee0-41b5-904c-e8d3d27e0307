{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nfunction uniqBy(arr, mapper) {\n  const map = new Map();\n  for (let i = 0; i < arr.length; i++) {\n    const item = arr[i];\n    const key = mapper(item);\n    if (!map.has(key)) {\n      map.set(key, item);\n    }\n  }\n  return Array.from(map.values());\n}\nexports.uniqBy = uniqBy;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "uniqBy", "arr", "mapper", "map", "Map", "i", "length", "item", "key", "has", "set", "Array", "from", "values"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/es-toolkit/dist/array/uniqBy.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nfunction uniqBy(arr, mapper) {\n    const map = new Map();\n    for (let i = 0; i < arr.length; i++) {\n        const item = arr[i];\n        const key = mapper(item);\n        if (!map.has(key)) {\n            map.set(key, item);\n        }\n    }\n    return Array.from(map.values());\n}\n\nexports.uniqBy = uniqBy;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,SAASC,MAAMA,CAACC,GAAG,EAAEC,MAAM,EAAE;EACzB,MAAMC,GAAG,GAAG,IAAIC,GAAG,CAAC,CAAC;EACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;IACjC,MAAME,IAAI,GAAGN,GAAG,CAACI,CAAC,CAAC;IACnB,MAAMG,GAAG,GAAGN,MAAM,CAACK,IAAI,CAAC;IACxB,IAAI,CAACJ,GAAG,CAACM,GAAG,CAACD,GAAG,CAAC,EAAE;MACfL,GAAG,CAACO,GAAG,CAACF,<PERSON>G,EAAED,IAAI,CAAC;IACtB;EACJ;EACA,OAAOI,KAAK,CAACC,IAAI,CAACT,GAAG,CAACU,MAAM,CAAC,CAAC,CAAC;AACnC;AAEAjB,OAAO,CAACI,MAAM,GAAGA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}