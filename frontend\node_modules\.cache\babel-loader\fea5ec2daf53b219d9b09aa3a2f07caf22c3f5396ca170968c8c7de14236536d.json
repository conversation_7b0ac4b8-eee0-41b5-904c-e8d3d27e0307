{"ast": null, "code": "function fill(array, value, start = 0, end = array.length) {\n  const length = array.length;\n  const finalStart = Math.max(start >= 0 ? start : length + start, 0);\n  const finalEnd = Math.min(end >= 0 ? end : length + end, length);\n  for (let i = finalStart; i < finalEnd; i++) {\n    array[i] = value;\n  }\n  return array;\n}\nexport { fill };", "map": {"version": 3, "names": ["fill", "array", "value", "start", "end", "length", "finalStart", "Math", "max", "finalEnd", "min", "i"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/es-toolkit/dist/array/fill.mjs"], "sourcesContent": ["function fill(array, value, start = 0, end = array.length) {\n    const length = array.length;\n    const finalStart = Math.max(start >= 0 ? start : length + start, 0);\n    const finalEnd = Math.min(end >= 0 ? end : length + end, length);\n    for (let i = finalStart; i < finalEnd; i++) {\n        array[i] = value;\n    }\n    return array;\n}\n\nexport { fill };\n"], "mappings": "AAAA,SAASA,IAAIA,CAACC,KAAK,EAAEC,KAAK,EAAEC,KAAK,GAAG,CAAC,EAAEC,GAAG,GAAGH,KAAK,CAACI,MAAM,EAAE;EACvD,MAAMA,MAAM,GAAGJ,KAAK,CAACI,MAAM;EAC3B,MAAMC,UAAU,GAAGC,IAAI,CAACC,GAAG,CAACL,KAAK,IAAI,CAAC,GAAGA,KAAK,GAAGE,MAAM,GAAGF,KAAK,EAAE,CAAC,CAAC;EACnE,MAAMM,QAAQ,GAAGF,IAAI,CAACG,GAAG,CAACN,GAAG,IAAI,CAAC,GAAGA,GAAG,GAAGC,MAAM,GAAGD,GAAG,EAAEC,MAAM,CAAC;EAChE,KAAK,IAAIM,CAAC,GAAGL,UAAU,EAAEK,CAAC,GAAGF,QAAQ,EAAEE,CAAC,EAAE,EAAE;IACxCV,KAAK,CAACU,CAAC,CAAC,GAAGT,KAAK;EACpB;EACA,OAAOD,KAAK;AAChB;AAEA,SAASD,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}