{"ast": null, "code": "/**\n * We trade Map as deps which may change with same value but different ref object.\n * We should make it as hash for deps\n * */\nexport function stringify(obj) {\n  var tgt;\n  if (obj instanceof Map) {\n    tgt = {};\n    obj.forEach(function (v, k) {\n      tgt[k] = v;\n    });\n  } else {\n    tgt = obj;\n  }\n  return JSON.stringify(tgt);\n}\nvar RC_TABS_DOUBLE_QUOTE = 'TABS_DQ';\nexport function genDataNodeKey(key) {\n  return String(key).replace(/\"/g, RC_TABS_DOUBLE_QUOTE);\n}\nexport function getRemovable(closable, closeIcon, editable, disabled) {\n  if (\n  // Only editable tabs can be removed\n  !editable ||\n  // Tabs cannot be removed when disabled\n  disabled ||\n  // closable is false\n  closable === false ||\n  // If closable is undefined, the remove button should be hidden when closeIcon is null or false\n  closable === undefined && (closeIcon === false || closeIcon === null)) {\n    return false;\n  }\n  return true;\n}", "map": {"version": 3, "names": ["stringify", "obj", "tgt", "Map", "for<PERSON>ach", "v", "k", "JSON", "RC_TABS_DOUBLE_QUOTE", "genDataNodeKey", "key", "String", "replace", "getRemovable", "closable", "closeIcon", "editable", "disabled", "undefined"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/rc-tabs/es/util.js"], "sourcesContent": ["/**\n * We trade Map as deps which may change with same value but different ref object.\n * We should make it as hash for deps\n * */\nexport function stringify(obj) {\n  var tgt;\n  if (obj instanceof Map) {\n    tgt = {};\n    obj.forEach(function (v, k) {\n      tgt[k] = v;\n    });\n  } else {\n    tgt = obj;\n  }\n  return JSON.stringify(tgt);\n}\nvar RC_TABS_DOUBLE_QUOTE = 'TABS_DQ';\nexport function genDataNodeKey(key) {\n  return String(key).replace(/\"/g, RC_TABS_DOUBLE_QUOTE);\n}\nexport function getRemovable(closable, closeIcon, editable, disabled) {\n  if (\n  // Only editable tabs can be removed\n  !editable ||\n  // Tabs cannot be removed when disabled\n  disabled ||\n  // closable is false\n  closable === false ||\n  // If closable is undefined, the remove button should be hidden when closeIcon is null or false\n  closable === undefined && (closeIcon === false || closeIcon === null)) {\n    return false;\n  }\n  return true;\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,SAASA,CAACC,GAAG,EAAE;EAC7B,IAAIC,GAAG;EACP,IAAID,GAAG,YAAYE,GAAG,EAAE;IACtBD,GAAG,GAAG,CAAC,CAAC;IACRD,GAAG,CAACG,OAAO,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MAC1BJ,GAAG,CAACI,CAAC,CAAC,GAAGD,CAAC;IACZ,CAAC,CAAC;EACJ,CAAC,MAAM;IACLH,GAAG,GAAGD,GAAG;EACX;EACA,OAAOM,IAAI,CAACP,SAAS,CAACE,GAAG,CAAC;AAC5B;AACA,IAAIM,oBAAoB,GAAG,SAAS;AACpC,OAAO,SAASC,cAAcA,CAACC,GAAG,EAAE;EAClC,OAAOC,MAAM,CAACD,GAAG,CAAC,CAACE,OAAO,CAAC,IAAI,EAAEJ,oBAAoB,CAAC;AACxD;AACA,OAAO,SAASK,YAAYA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;EACpE;EACA;EACA,CAACD,QAAQ;EACT;EACAC,QAAQ;EACR;EACAH,QAAQ,KAAK,KAAK;EAClB;EACAA,QAAQ,KAAKI,SAAS,KAAKH,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,IAAI,CAAC,EAAE;IACrE,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}