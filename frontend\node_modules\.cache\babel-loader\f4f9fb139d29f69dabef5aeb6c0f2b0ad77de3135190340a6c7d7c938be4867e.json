{"ast": null, "code": "import { tickIncrement } from '../utils';\n/**\n *  Insert breaks into ticks and delete the ticks covered by breaks.\n */\nexport const insertBreaksToTicks = (ticks, breaks) => {\n  if (!(breaks === null || breaks === void 0 ? void 0 : breaks.length)) return ticks;\n  const edgePoints = [...ticks, ...breaks.flatMap(b => [b.start, b.end])];\n  const uniqueSortedTicks = Array.from(new Set(edgePoints)).sort((a, b) => a - b);\n  const filteredTicks = uniqueSortedTicks.filter(tick => !breaks.some(({\n    start,\n    end\n  }) => tick > start && tick < end));\n  return filteredTicks.length ? filteredTicks : ticks;\n};\nexport const d3Ticks = (begin, end, count, breaks) => {\n  let n;\n  let ticks;\n  let start = begin;\n  let stop = end;\n  if (start === stop && count > 0) {\n    return [start];\n  }\n  let step = tickIncrement(start, stop, count);\n  if (step === 0 || !Number.isFinite(step)) {\n    return [];\n  }\n  if (step > 0) {\n    start = Math.ceil(start / step);\n    stop = Math.floor(stop / step);\n    ticks = new Array(n = Math.ceil(stop - start + 1));\n    for (let i = 0; i < n; i += 1) {\n      ticks[i] = (start + i) * step;\n    }\n  } else {\n    step = -step;\n    start = Math.ceil(start * step);\n    stop = Math.floor(stop * step);\n    ticks = new Array(n = Math.ceil(stop - start + 1));\n    for (let i = 0; i < n; i += 1) {\n      ticks[i] = (start + i) / step;\n    }\n  }\n  return insertBreaksToTicks(ticks, breaks);\n};", "map": {"version": 3, "names": ["tickIncrement", "insertBreaksToTicks", "ticks", "breaks", "length", "edgePoints", "flatMap", "b", "start", "end", "uniqueSortedTicks", "Array", "from", "Set", "sort", "a", "filteredTicks", "filter", "tick", "some", "d3Ticks", "begin", "count", "n", "stop", "step", "Number", "isFinite", "Math", "ceil", "floor", "i"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\scale\\src\\tick-methods\\d3-ticks.ts"], "sourcesContent": ["import { TickMethod } from '../types';\nimport { tickIncrement } from '../utils';\nimport type { BreakOptions } from '../types';\n\n/**\n *  Insert breaks into ticks and delete the ticks covered by breaks.\n */\nexport const insertBreaksToTicks = (ticks: number[], breaks?: BreakOptions[]): number[] => {\n  if (!breaks?.length) return ticks;\n  const edgePoints = [...ticks, ...breaks.flatMap((b) => [b.start, b.end])];\n  const uniqueSortedTicks = Array.from(new Set(edgePoints)).sort((a, b) => a - b);\n  const filteredTicks = uniqueSortedTicks.filter(\n    (tick) => !breaks.some(({ start, end }) => tick > start && tick < end)\n  );\n  return filteredTicks.length ? filteredTicks : ticks;\n};\n\nexport const d3Ticks: TickMethod = (begin: number, end: number, count: number, breaks?: BreakOptions[]) => {\n  let n;\n  let ticks;\n\n  let start = begin;\n  let stop = end;\n\n  if (start === stop && count > 0) {\n    return [start];\n  }\n\n  let step = tickIncrement(start, stop, count);\n\n  if (step === 0 || !Number.isFinite(step)) {\n    return [];\n  }\n\n  if (step > 0) {\n    start = Math.ceil(start / step);\n    stop = Math.floor(stop / step);\n    ticks = new Array((n = Math.ceil(stop - start + 1)));\n    for (let i = 0; i < n; i += 1) {\n      ticks[i] = (start + i) * step;\n    }\n  } else {\n    step = -step;\n    start = Math.ceil(start * step);\n    stop = Math.floor(stop * step);\n    ticks = new Array((n = Math.ceil(stop - start + 1)));\n    for (let i = 0; i < n; i += 1) {\n      ticks[i] = (start + i) / step;\n    }\n  }\n\n  return insertBreaksToTicks(ticks, breaks);\n};\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,UAAU;AAGxC;;;AAGA,OAAO,MAAMC,mBAAmB,GAAGA,CAACC,KAAe,EAAEC,MAAuB,KAAc;EACxF,IAAI,EAACA,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEC,MAAM,GAAE,OAAOF,KAAK;EACjC,MAAMG,UAAU,GAAG,CAAC,GAAGH,KAAK,EAAE,GAAGC,MAAM,CAACG,OAAO,CAAEC,CAAC,IAAK,CAACA,CAAC,CAACC,KAAK,EAAED,CAAC,CAACE,GAAG,CAAC,CAAC,CAAC;EACzE,MAAMC,iBAAiB,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACR,UAAU,CAAC,CAAC,CAACS,IAAI,CAAC,CAACC,CAAC,EAAER,CAAC,KAAKQ,CAAC,GAAGR,CAAC,CAAC;EAC/E,MAAMS,aAAa,GAAGN,iBAAiB,CAACO,MAAM,CAC3CC,IAAI,IAAK,CAACf,MAAM,CAACgB,IAAI,CAAC,CAAC;IAAEX,KAAK;IAAEC;EAAG,CAAE,KAAKS,IAAI,GAAGV,KAAK,IAAIU,IAAI,GAAGT,GAAG,CAAC,CACvE;EACD,OAAOO,aAAa,CAACZ,MAAM,GAAGY,aAAa,GAAGd,KAAK;AACrD,CAAC;AAED,OAAO,MAAMkB,OAAO,GAAeA,CAACC,KAAa,EAAEZ,GAAW,EAAEa,KAAa,EAAEnB,MAAuB,KAAI;EACxG,IAAIoB,CAAC;EACL,IAAIrB,KAAK;EAET,IAAIM,KAAK,GAAGa,KAAK;EACjB,IAAIG,IAAI,GAAGf,GAAG;EAEd,IAAID,KAAK,KAAKgB,IAAI,IAAIF,KAAK,GAAG,CAAC,EAAE;IAC/B,OAAO,CAACd,KAAK,CAAC;;EAGhB,IAAIiB,IAAI,GAAGzB,aAAa,CAACQ,KAAK,EAAEgB,IAAI,EAAEF,KAAK,CAAC;EAE5C,IAAIG,IAAI,KAAK,CAAC,IAAI,CAACC,MAAM,CAACC,QAAQ,CAACF,IAAI,CAAC,EAAE;IACxC,OAAO,EAAE;;EAGX,IAAIA,IAAI,GAAG,CAAC,EAAE;IACZjB,KAAK,GAAGoB,IAAI,CAACC,IAAI,CAACrB,KAAK,GAAGiB,IAAI,CAAC;IAC/BD,IAAI,GAAGI,IAAI,CAACE,KAAK,CAACN,IAAI,GAAGC,IAAI,CAAC;IAC9BvB,KAAK,GAAG,IAAIS,KAAK,CAAEY,CAAC,GAAGK,IAAI,CAACC,IAAI,CAACL,IAAI,GAAGhB,KAAK,GAAG,CAAC,CAAE,CAAC;IACpD,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,CAAC,EAAEQ,CAAC,IAAI,CAAC,EAAE;MAC7B7B,KAAK,CAAC6B,CAAC,CAAC,GAAG,CAACvB,KAAK,GAAGuB,CAAC,IAAIN,IAAI;;GAEhC,MAAM;IACLA,IAAI,GAAG,CAACA,IAAI;IACZjB,KAAK,GAAGoB,IAAI,CAACC,IAAI,CAACrB,KAAK,GAAGiB,IAAI,CAAC;IAC/BD,IAAI,GAAGI,IAAI,CAACE,KAAK,CAACN,IAAI,GAAGC,IAAI,CAAC;IAC9BvB,KAAK,GAAG,IAAIS,KAAK,CAAEY,CAAC,GAAGK,IAAI,CAACC,IAAI,CAACL,IAAI,GAAGhB,KAAK,GAAG,CAAC,CAAE,CAAC;IACpD,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,CAAC,EAAEQ,CAAC,IAAI,CAAC,EAAE;MAC7B7B,KAAK,CAAC6B,CAAC,CAAC,GAAG,CAACvB,KAAK,GAAGuB,CAAC,IAAIN,IAAI;;;EAIjC,OAAOxB,mBAAmB,CAACC,KAAK,EAAEC,MAAM,CAAC;AAC3C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}