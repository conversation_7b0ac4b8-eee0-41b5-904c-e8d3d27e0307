{"ast": null, "code": "export function clonePath(path) {\n  return path.map(function (x) {\n    return Array.isArray(x) ? [].concat(x) : x;\n  });\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "path", "map", "x", "Array", "isArray", "concat"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\util\\src\\path\\process\\clone-path.ts"], "sourcesContent": ["import type { PathArray, PathSegment } from '../types';\n\nexport function clonePath(path: PathArray | PathSegment): PathArray {\n  return path.map((x) => (Array.isArray(x) ? [].concat(x) : x)) as PathArray;\n}\n"], "mappings": "AAEA,OAAM,SAAUA,SAASA,CAACC,IAA6B;EACrD,OAAOA,IAAI,CAACC,GAAG,CAAC,UAACC,CAAC;IAAK,OAACC,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC,GAAG,EAAE,CAACG,MAAM,CAACH,CAAC,CAAC,GAAGA,CAAC;EAApC,CAAqC,CAAc;AAC5E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}