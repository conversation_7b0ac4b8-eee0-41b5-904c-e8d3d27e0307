package com.yinma.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * BOM主表实体类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("bom")
public class Bom implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * BOM编码
     */
    @NotBlank(message = "BOM编码不能为空")
    @TableField("bom_code")
    private String bomCode;

    /**
     * BOM名称
     */
    @NotBlank(message = "BOM名称不能为空")
    @TableField("bom_name")
    private String bomName;

    /**
     * 产品编码
     */
    @NotBlank(message = "产品编码不能为空")
    @TableField("product_code")
    private String productCode;

    /**
     * 产品名称
     */
    @NotBlank(message = "产品名称不能为空")
    @TableField("product_name")
    private String productName;

    /**
     * BOM类型：EBOM-工程BOM，PBOM-工艺BOM，MBOM-制造BOM
     */
    @NotBlank(message = "BOM类型不能为空")
    @TableField("bom_type")
    private String bomType;

    /**
     * BOM版本
     */
    @NotBlank(message = "BOM版本不能为空")
    @TableField("version")
    private String version;

    /**
     * 状态：DRAFT-草稿，ACTIVE-生效，INACTIVE-失效
     */
    @TableField("status")
    private String status;

    /**
     * 生效日期
     */
    @TableField("effective_date")
    private LocalDateTime effectiveDate;

    /**
     * 失效日期
     */
    @TableField("expiry_date")
    private LocalDateTime expiryDate;

    /**
     * 基本数量
     */
    @NotNull(message = "基本数量不能为空")
    @TableField("base_quantity")
    private BigDecimal baseQuantity;

    /**
     * 计量单位
     */
    @NotBlank(message = "计量单位不能为空")
    @TableField("unit")
    private String unit;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}