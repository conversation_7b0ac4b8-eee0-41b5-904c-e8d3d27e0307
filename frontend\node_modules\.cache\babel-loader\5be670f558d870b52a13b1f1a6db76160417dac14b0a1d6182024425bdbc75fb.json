{"ast": null, "code": "import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport { authAPI } from '../../services/api';\n\n/**\n * 登录异步操作\n */\nexport const login = createAsyncThunk('auth/login', async (credentials, {\n  rejectWithValue\n}) => {\n  try {\n    const response = await authAPI.login(credentials);\n    // 保存token到localStorage\n    localStorage.setItem('token', response.data.token);\n    localStorage.setItem('userInfo', JSON.stringify(response.data.userInfo));\n    return response.data;\n  } catch (error) {\n    var _error$response, _error$response$data;\n    return rejectWithValue(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || '登录失败');\n  }\n});\n\n/**\n * 登出异步操作\n */\nexport const logout = createAsyncThunk('auth/logout', async (_, {\n  rejectWithValue\n}) => {\n  try {\n    await authAPI.logout();\n    // 清除本地存储\n    localStorage.removeItem('token');\n    localStorage.removeItem('userInfo');\n    return null;\n  } catch (error) {\n    var _error$response2, _error$response2$data;\n    // 即使登出失败也要清除本地存储\n    localStorage.removeItem('token');\n    localStorage.removeItem('userInfo');\n    return rejectWithValue(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || '登出失败');\n  }\n});\n\n/**\n * 获取用户信息\n */\nexport const getUserInfo = createAsyncThunk('auth/getUserInfo', async (_, {\n  rejectWithValue\n}) => {\n  try {\n    const response = await authAPI.getUserInfo();\n    return response.data;\n  } catch (error) {\n    var _error$response3, _error$response3$data;\n    return rejectWithValue(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || '获取用户信息失败');\n  }\n});\n\n/**\n * 认证状态slice\n */\nconst authSlice = createSlice({\n  name: 'auth',\n  initialState: {\n    isAuthenticated: !!localStorage.getItem('token') || true,\n    // 临时设置为true用于测试\n    token: localStorage.getItem('token') || 'test-token',\n    userInfo: localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')) : {\n      name: '测试用户',\n      role: 'admin',\n      avatar: null\n    },\n    loading: false,\n    error: null\n  },\n  reducers: {\n    clearError: state => {\n      state.error = null;\n    },\n    setLoading: (state, action) => {\n      state.loading = action.payload;\n    },\n    // 初始化认证状态（用于页面刷新时恢复状态）\n    initializeAuth: state => {\n      const token = localStorage.getItem('token');\n      const userInfo = localStorage.getItem('userInfo');\n      if (token && userInfo) {\n        state.isAuthenticated = true;\n        state.token = token;\n        state.userInfo = JSON.parse(userInfo);\n      } else {\n        state.isAuthenticated = false;\n        state.token = null;\n        state.userInfo = null;\n      }\n    }\n  },\n  extraReducers: builder => {\n    builder\n    // 登录\n    .addCase(login.pending, state => {\n      state.loading = true;\n      state.error = null;\n    }).addCase(login.fulfilled, (state, action) => {\n      state.loading = false;\n      state.isAuthenticated = true;\n      state.token = action.payload.token;\n      state.userInfo = action.payload.userInfo;\n      state.error = null;\n    }).addCase(login.rejected, (state, action) => {\n      state.loading = false;\n      state.isAuthenticated = false;\n      state.token = null;\n      state.userInfo = null;\n      state.error = action.payload;\n    })\n    // 登出\n    .addCase(logout.pending, state => {\n      state.loading = true;\n    }).addCase(logout.fulfilled, state => {\n      state.loading = false;\n      state.isAuthenticated = false;\n      state.token = null;\n      state.userInfo = null;\n      state.error = null;\n    }).addCase(logout.rejected, (state, action) => {\n      state.loading = false;\n      state.isAuthenticated = false;\n      state.token = null;\n      state.userInfo = null;\n      state.error = action.payload;\n    })\n    // 获取用户信息\n    .addCase(getUserInfo.pending, state => {\n      state.loading = true;\n    }).addCase(getUserInfo.fulfilled, (state, action) => {\n      state.loading = false;\n      state.userInfo = action.payload;\n      localStorage.setItem('userInfo', JSON.stringify(action.payload));\n    }).addCase(getUserInfo.rejected, (state, action) => {\n      state.loading = false;\n      state.error = action.payload;\n      // 如果获取用户信息失败，可能token已过期，清除认证状态\n      state.isAuthenticated = false;\n      state.token = null;\n      state.userInfo = null;\n      localStorage.removeItem('token');\n      localStorage.removeItem('userInfo');\n    });\n  }\n});\nexport const {\n  clearError,\n  setLoading,\n  initializeAuth\n} = authSlice.actions;\n\n// 选择器\nexport const selectAuth = state => state.auth;\nexport const selectIsAuthenticated = state => state.auth.isAuthenticated;\nexport const selectUserInfo = state => state.auth.userInfo;\nexport const selectAuthLoading = state => state.auth.loading;\nexport const selectAuthError = state => state.auth.error;\nexport default authSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "authAPI", "login", "credentials", "rejectWithValue", "response", "localStorage", "setItem", "data", "token", "JSON", "stringify", "userInfo", "error", "_error$response", "_error$response$data", "message", "logout", "_", "removeItem", "_error$response2", "_error$response2$data", "getUserInfo", "_error$response3", "_error$response3$data", "authSlice", "name", "initialState", "isAuthenticated", "getItem", "parse", "role", "avatar", "loading", "reducers", "clearError", "state", "setLoading", "action", "payload", "initializeAuth", "extraReducers", "builder", "addCase", "pending", "fulfilled", "rejected", "actions", "selectAuth", "auth", "selectIsAuthenticated", "selectUserInfo", "selectAuthLoading", "selectAuthError", "reducer"], "sources": ["D:/customerDemo/Link-YinMa/frontend/src/store/slices/authSlice.js"], "sourcesContent": ["import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport { authAPI } from '../../services/api';\n\n/**\n * 登录异步操作\n */\nexport const login = createAsyncThunk(\n  'auth/login',\n  async (credentials, { rejectWithValue }) => {\n    try {\n      const response = await authAPI.login(credentials);\n      // 保存token到localStorage\n      localStorage.setItem('token', response.data.token);\n      localStorage.setItem('userInfo', JSON.stringify(response.data.userInfo));\n      return response.data;\n    } catch (error) {\n      return rejectWithValue(error.response?.data?.message || '登录失败');\n    }\n  }\n);\n\n/**\n * 登出异步操作\n */\nexport const logout = createAsyncThunk(\n  'auth/logout',\n  async (_, { rejectWithValue }) => {\n    try {\n      await authAPI.logout();\n      // 清除本地存储\n      localStorage.removeItem('token');\n      localStorage.removeItem('userInfo');\n      return null;\n    } catch (error) {\n      // 即使登出失败也要清除本地存储\n      localStorage.removeItem('token');\n      localStorage.removeItem('userInfo');\n      return rejectWithValue(error.response?.data?.message || '登出失败');\n    }\n  }\n);\n\n/**\n * 获取用户信息\n */\nexport const getUserInfo = createAsyncThunk(\n  'auth/getUserInfo',\n  async (_, { rejectWithValue }) => {\n    try {\n      const response = await authAPI.getUserInfo();\n      return response.data;\n    } catch (error) {\n      return rejectWithValue(error.response?.data?.message || '获取用户信息失败');\n    }\n  }\n);\n\n/**\n * 认证状态slice\n */\nconst authSlice = createSlice({\n  name: 'auth',\n  initialState: {\n    isAuthenticated: !!localStorage.getItem('token') || true, // 临时设置为true用于测试\n    token: localStorage.getItem('token') || 'test-token',\n    userInfo: localStorage.getItem('userInfo')\n      ? JSON.parse(localStorage.getItem('userInfo'))\n      : { name: '测试用户', role: 'admin', avatar: null },\n    loading: false,\n    error: null,\n  },\n  reducers: {\n    clearError: (state) => {\n      state.error = null;\n    },\n    setLoading: (state, action) => {\n      state.loading = action.payload;\n    },\n    // 初始化认证状态（用于页面刷新时恢复状态）\n    initializeAuth: (state) => {\n      const token = localStorage.getItem('token');\n      const userInfo = localStorage.getItem('userInfo');\n      \n      if (token && userInfo) {\n        state.isAuthenticated = true;\n        state.token = token;\n        state.userInfo = JSON.parse(userInfo);\n      } else {\n        state.isAuthenticated = false;\n        state.token = null;\n        state.userInfo = null;\n      }\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      // 登录\n      .addCase(login.pending, (state) => {\n        state.loading = true;\n        state.error = null;\n      })\n      .addCase(login.fulfilled, (state, action) => {\n        state.loading = false;\n        state.isAuthenticated = true;\n        state.token = action.payload.token;\n        state.userInfo = action.payload.userInfo;\n        state.error = null;\n      })\n      .addCase(login.rejected, (state, action) => {\n        state.loading = false;\n        state.isAuthenticated = false;\n        state.token = null;\n        state.userInfo = null;\n        state.error = action.payload;\n      })\n      // 登出\n      .addCase(logout.pending, (state) => {\n        state.loading = true;\n      })\n      .addCase(logout.fulfilled, (state) => {\n        state.loading = false;\n        state.isAuthenticated = false;\n        state.token = null;\n        state.userInfo = null;\n        state.error = null;\n      })\n      .addCase(logout.rejected, (state, action) => {\n        state.loading = false;\n        state.isAuthenticated = false;\n        state.token = null;\n        state.userInfo = null;\n        state.error = action.payload;\n      })\n      // 获取用户信息\n      .addCase(getUserInfo.pending, (state) => {\n        state.loading = true;\n      })\n      .addCase(getUserInfo.fulfilled, (state, action) => {\n        state.loading = false;\n        state.userInfo = action.payload;\n        localStorage.setItem('userInfo', JSON.stringify(action.payload));\n      })\n      .addCase(getUserInfo.rejected, (state, action) => {\n        state.loading = false;\n        state.error = action.payload;\n        // 如果获取用户信息失败，可能token已过期，清除认证状态\n        state.isAuthenticated = false;\n        state.token = null;\n        state.userInfo = null;\n        localStorage.removeItem('token');\n        localStorage.removeItem('userInfo');\n      });\n  },\n});\n\nexport const { clearError, setLoading, initializeAuth } = authSlice.actions;\n\n// 选择器\nexport const selectAuth = (state) => state.auth;\nexport const selectIsAuthenticated = (state) => state.auth.isAuthenticated;\nexport const selectUserInfo = (state) => state.auth.userInfo;\nexport const selectAuthLoading = (state) => state.auth.loading;\nexport const selectAuthError = (state) => state.auth.error;\n\nexport default authSlice.reducer;"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAQ,kBAAkB;AAChE,SAASC,OAAO,QAAQ,oBAAoB;;AAE5C;AACA;AACA;AACA,OAAO,MAAMC,KAAK,GAAGF,gBAAgB,CACnC,YAAY,EACZ,OAAOG,WAAW,EAAE;EAAEC;AAAgB,CAAC,KAAK;EAC1C,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMJ,OAAO,CAACC,KAAK,CAACC,WAAW,CAAC;IACjD;IACAG,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEF,QAAQ,CAACG,IAAI,CAACC,KAAK,CAAC;IAClDH,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEG,IAAI,CAACC,SAAS,CAACN,QAAQ,CAACG,IAAI,CAACI,QAAQ,CAAC,CAAC;IACxE,OAAOP,QAAQ,CAACG,IAAI;EACtB,CAAC,CAAC,OAAOK,KAAK,EAAE;IAAA,IAAAC,eAAA,EAAAC,oBAAA;IACd,OAAOX,eAAe,CAAC,EAAAU,eAAA,GAAAD,KAAK,CAACR,QAAQ,cAAAS,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBN,IAAI,cAAAO,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,MAAM,CAAC;EACjE;AACF,CACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,MAAM,GAAGjB,gBAAgB,CACpC,aAAa,EACb,OAAOkB,CAAC,EAAE;EAAEd;AAAgB,CAAC,KAAK;EAChC,IAAI;IACF,MAAMH,OAAO,CAACgB,MAAM,CAAC,CAAC;IACtB;IACAX,YAAY,CAACa,UAAU,CAAC,OAAO,CAAC;IAChCb,YAAY,CAACa,UAAU,CAAC,UAAU,CAAC;IACnC,OAAO,IAAI;EACb,CAAC,CAAC,OAAON,KAAK,EAAE;IAAA,IAAAO,gBAAA,EAAAC,qBAAA;IACd;IACAf,YAAY,CAACa,UAAU,CAAC,OAAO,CAAC;IAChCb,YAAY,CAACa,UAAU,CAAC,UAAU,CAAC;IACnC,OAAOf,eAAe,CAAC,EAAAgB,gBAAA,GAAAP,KAAK,CAACR,QAAQ,cAAAe,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBZ,IAAI,cAAAa,qBAAA,uBAApBA,qBAAA,CAAsBL,OAAO,KAAI,MAAM,CAAC;EACjE;AACF,CACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMM,WAAW,GAAGtB,gBAAgB,CACzC,kBAAkB,EAClB,OAAOkB,CAAC,EAAE;EAAEd;AAAgB,CAAC,KAAK;EAChC,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMJ,OAAO,CAACqB,WAAW,CAAC,CAAC;IAC5C,OAAOjB,QAAQ,CAACG,IAAI;EACtB,CAAC,CAAC,OAAOK,KAAK,EAAE;IAAA,IAAAU,gBAAA,EAAAC,qBAAA;IACd,OAAOpB,eAAe,CAAC,EAAAmB,gBAAA,GAAAV,KAAK,CAACR,QAAQ,cAAAkB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBf,IAAI,cAAAgB,qBAAA,uBAApBA,qBAAA,CAAsBR,OAAO,KAAI,UAAU,CAAC;EACrE;AACF,CACF,CAAC;;AAED;AACA;AACA;AACA,MAAMS,SAAS,GAAG1B,WAAW,CAAC;EAC5B2B,IAAI,EAAE,MAAM;EACZC,YAAY,EAAE;IACZC,eAAe,EAAE,CAAC,CAACtB,YAAY,CAACuB,OAAO,CAAC,OAAO,CAAC,IAAI,IAAI;IAAE;IAC1DpB,KAAK,EAAEH,YAAY,CAACuB,OAAO,CAAC,OAAO,CAAC,IAAI,YAAY;IACpDjB,QAAQ,EAAEN,YAAY,CAACuB,OAAO,CAAC,UAAU,CAAC,GACtCnB,IAAI,CAACoB,KAAK,CAACxB,YAAY,CAACuB,OAAO,CAAC,UAAU,CAAC,CAAC,GAC5C;MAAEH,IAAI,EAAE,MAAM;MAAEK,IAAI,EAAE,OAAO;MAAEC,MAAM,EAAE;IAAK,CAAC;IACjDC,OAAO,EAAE,KAAK;IACdpB,KAAK,EAAE;EACT,CAAC;EACDqB,QAAQ,EAAE;IACRC,UAAU,EAAGC,KAAK,IAAK;MACrBA,KAAK,CAACvB,KAAK,GAAG,IAAI;IACpB,CAAC;IACDwB,UAAU,EAAEA,CAACD,KAAK,EAAEE,MAAM,KAAK;MAC7BF,KAAK,CAACH,OAAO,GAAGK,MAAM,CAACC,OAAO;IAChC,CAAC;IACD;IACAC,cAAc,EAAGJ,KAAK,IAAK;MACzB,MAAM3B,KAAK,GAAGH,YAAY,CAACuB,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMjB,QAAQ,GAAGN,YAAY,CAACuB,OAAO,CAAC,UAAU,CAAC;MAEjD,IAAIpB,KAAK,IAAIG,QAAQ,EAAE;QACrBwB,KAAK,CAACR,eAAe,GAAG,IAAI;QAC5BQ,KAAK,CAAC3B,KAAK,GAAGA,KAAK;QACnB2B,KAAK,CAACxB,QAAQ,GAAGF,IAAI,CAACoB,KAAK,CAAClB,QAAQ,CAAC;MACvC,CAAC,MAAM;QACLwB,KAAK,CAACR,eAAe,GAAG,KAAK;QAC7BQ,KAAK,CAAC3B,KAAK,GAAG,IAAI;QAClB2B,KAAK,CAACxB,QAAQ,GAAG,IAAI;MACvB;IACF;EACF,CAAC;EACD6B,aAAa,EAAGC,OAAO,IAAK;IAC1BA;IACE;IAAA,CACCC,OAAO,CAACzC,KAAK,CAAC0C,OAAO,EAAGR,KAAK,IAAK;MACjCA,KAAK,CAACH,OAAO,GAAG,IAAI;MACpBG,KAAK,CAACvB,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACD8B,OAAO,CAACzC,KAAK,CAAC2C,SAAS,EAAE,CAACT,KAAK,EAAEE,MAAM,KAAK;MAC3CF,KAAK,CAACH,OAAO,GAAG,KAAK;MACrBG,KAAK,CAACR,eAAe,GAAG,IAAI;MAC5BQ,KAAK,CAAC3B,KAAK,GAAG6B,MAAM,CAACC,OAAO,CAAC9B,KAAK;MAClC2B,KAAK,CAACxB,QAAQ,GAAG0B,MAAM,CAACC,OAAO,CAAC3B,QAAQ;MACxCwB,KAAK,CAACvB,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACD8B,OAAO,CAACzC,KAAK,CAAC4C,QAAQ,EAAE,CAACV,KAAK,EAAEE,MAAM,KAAK;MAC1CF,KAAK,CAACH,OAAO,GAAG,KAAK;MACrBG,KAAK,CAACR,eAAe,GAAG,KAAK;MAC7BQ,KAAK,CAAC3B,KAAK,GAAG,IAAI;MAClB2B,KAAK,CAACxB,QAAQ,GAAG,IAAI;MACrBwB,KAAK,CAACvB,KAAK,GAAGyB,MAAM,CAACC,OAAO;IAC9B,CAAC;IACD;IAAA,CACCI,OAAO,CAAC1B,MAAM,CAAC2B,OAAO,EAAGR,KAAK,IAAK;MAClCA,KAAK,CAACH,OAAO,GAAG,IAAI;IACtB,CAAC,CAAC,CACDU,OAAO,CAAC1B,MAAM,CAAC4B,SAAS,EAAGT,KAAK,IAAK;MACpCA,KAAK,CAACH,OAAO,GAAG,KAAK;MACrBG,KAAK,CAACR,eAAe,GAAG,KAAK;MAC7BQ,KAAK,CAAC3B,KAAK,GAAG,IAAI;MAClB2B,KAAK,CAACxB,QAAQ,GAAG,IAAI;MACrBwB,KAAK,CAACvB,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACD8B,OAAO,CAAC1B,MAAM,CAAC6B,QAAQ,EAAE,CAACV,KAAK,EAAEE,MAAM,KAAK;MAC3CF,KAAK,CAACH,OAAO,GAAG,KAAK;MACrBG,KAAK,CAACR,eAAe,GAAG,KAAK;MAC7BQ,KAAK,CAAC3B,KAAK,GAAG,IAAI;MAClB2B,KAAK,CAACxB,QAAQ,GAAG,IAAI;MACrBwB,KAAK,CAACvB,KAAK,GAAGyB,MAAM,CAACC,OAAO;IAC9B,CAAC;IACD;IAAA,CACCI,OAAO,CAACrB,WAAW,CAACsB,OAAO,EAAGR,KAAK,IAAK;MACvCA,KAAK,CAACH,OAAO,GAAG,IAAI;IACtB,CAAC,CAAC,CACDU,OAAO,CAACrB,WAAW,CAACuB,SAAS,EAAE,CAACT,KAAK,EAAEE,MAAM,KAAK;MACjDF,KAAK,CAACH,OAAO,GAAG,KAAK;MACrBG,KAAK,CAACxB,QAAQ,GAAG0B,MAAM,CAACC,OAAO;MAC/BjC,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEG,IAAI,CAACC,SAAS,CAAC2B,MAAM,CAACC,OAAO,CAAC,CAAC;IAClE,CAAC,CAAC,CACDI,OAAO,CAACrB,WAAW,CAACwB,QAAQ,EAAE,CAACV,KAAK,EAAEE,MAAM,KAAK;MAChDF,KAAK,CAACH,OAAO,GAAG,KAAK;MACrBG,KAAK,CAACvB,KAAK,GAAGyB,MAAM,CAACC,OAAO;MAC5B;MACAH,KAAK,CAACR,eAAe,GAAG,KAAK;MAC7BQ,KAAK,CAAC3B,KAAK,GAAG,IAAI;MAClB2B,KAAK,CAACxB,QAAQ,GAAG,IAAI;MACrBN,YAAY,CAACa,UAAU,CAAC,OAAO,CAAC;MAChCb,YAAY,CAACa,UAAU,CAAC,UAAU,CAAC;IACrC,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEgB,UAAU;EAAEE,UAAU;EAAEG;AAAe,CAAC,GAAGf,SAAS,CAACsB,OAAO;;AAE3E;AACA,OAAO,MAAMC,UAAU,GAAIZ,KAAK,IAAKA,KAAK,CAACa,IAAI;AAC/C,OAAO,MAAMC,qBAAqB,GAAId,KAAK,IAAKA,KAAK,CAACa,IAAI,CAACrB,eAAe;AAC1E,OAAO,MAAMuB,cAAc,GAAIf,KAAK,IAAKA,KAAK,CAACa,IAAI,CAACrC,QAAQ;AAC5D,OAAO,MAAMwC,iBAAiB,GAAIhB,KAAK,IAAKA,KAAK,CAACa,IAAI,CAAChB,OAAO;AAC9D,OAAO,MAAMoB,eAAe,GAAIjB,KAAK,IAAKA,KAAK,CAACa,IAAI,CAACpC,KAAK;AAE1D,eAAeY,SAAS,CAAC6B,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}