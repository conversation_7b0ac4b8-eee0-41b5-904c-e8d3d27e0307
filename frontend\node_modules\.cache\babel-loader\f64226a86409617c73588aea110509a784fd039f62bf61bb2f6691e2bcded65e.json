{"ast": null, "code": "import { distanceSquareRoot } from './distance-square-root';\n/**\n * Returns a {x,y} point at a given length, the total length and\n * the minimum and maximum {x,y} coordinates of a C (cubic-bezier) segment.\n */\nfunction getPointAtCubicSegmentLength(x1, y1, c1x, c1y, c2x, c2y, x2, y2, t) {\n  var t1 = 1 - t;\n  return {\n    x: Math.pow(t1, 3) * x1 + 3 * Math.pow(t1, 2) * t * c1x + 3 * t1 * Math.pow(t, 2) * c2x + Math.pow(t, 3) * x2,\n    y: Math.pow(t1, 3) * y1 + 3 * Math.pow(t1, 2) * t * c1y + 3 * t1 * Math.pow(t, 2) * c2y + Math.pow(t, 3) * y2\n  };\n}\n/**\n * Returns the length of a C (cubic-bezier) segment\n * or an {x,y} point at a given length.\n */\nexport function segmentCubicFactory(x1, y1, c1x, c1y, c2x, c2y, x2, y2, distance, options) {\n  var _a;\n  var _b = options.bbox,\n    bbox = _b === void 0 ? true : _b,\n    _c = options.length,\n    length = _c === void 0 ? true : _c,\n    _d = options.sampleSize,\n    sampleSize = _d === void 0 ? 10 : _d;\n  var distanceIsNumber = typeof distance === 'number';\n  var x = x1;\n  var y = y1;\n  var LENGTH = 0;\n  var prev = [x, y, LENGTH];\n  var cur = [x, y];\n  var t = 0;\n  var POINT = {\n    x: 0,\n    y: 0\n  };\n  var POINTS = [{\n    x: x,\n    y: y\n  }];\n  if (distanceIsNumber && distance <= 0) {\n    POINT = {\n      x: x,\n      y: y\n    };\n  }\n  // bad perf when size = 300\n  for (var j = 0; j <= sampleSize; j += 1) {\n    t = j / sampleSize;\n    _a = getPointAtCubicSegmentLength(x1, y1, c1x, c1y, c2x, c2y, x2, y2, t), x = _a.x, y = _a.y;\n    if (bbox) {\n      POINTS.push({\n        x: x,\n        y: y\n      });\n    }\n    if (length) {\n      LENGTH += distanceSquareRoot(cur, [x, y]);\n    }\n    cur = [x, y];\n    if (distanceIsNumber && LENGTH >= distance && distance > prev[2]) {\n      var dv = (LENGTH - distance) / (LENGTH - prev[2]);\n      POINT = {\n        x: cur[0] * (1 - dv) + prev[0] * dv,\n        y: cur[1] * (1 - dv) + prev[1] * dv\n      };\n    }\n    prev = [x, y, LENGTH];\n  }\n  if (distanceIsNumber && distance >= LENGTH) {\n    POINT = {\n      x: x2,\n      y: y2\n    };\n  }\n  return {\n    length: LENGTH,\n    point: POINT,\n    min: {\n      x: Math.min.apply(null, POINTS.map(function (n) {\n        return n.x;\n      })),\n      y: Math.min.apply(null, POINTS.map(function (n) {\n        return n.y;\n      }))\n    },\n    max: {\n      x: Math.max.apply(null, POINTS.map(function (n) {\n        return n.x;\n      })),\n      y: Math.max.apply(null, POINTS.map(function (n) {\n        return n.y;\n      }))\n    }\n  };\n}", "map": {"version": 3, "names": ["distanceSquareRoot", "getPointAtCubicSegmentLength", "x1", "y1", "c1x", "c1y", "c2x", "c2y", "x2", "y2", "t", "t1", "x", "Math", "pow", "y", "segmentCubicFactory", "distance", "options", "_b", "bbox", "_c", "length", "_d", "sampleSize", "distanceIsNumber", "LENGTH", "prev", "cur", "POINT", "POINTS", "j", "_a", "push", "dv", "point", "min", "apply", "map", "n", "max"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\util\\src\\path\\util\\segment-cubic-factory.ts"], "sourcesContent": ["import type { LengthFactory, PathLengthFactoryOptions } from '../types';\nimport { distanceSquareRoot } from './distance-square-root';\n\n/**\n * Returns a {x,y} point at a given length, the total length and\n * the minimum and maximum {x,y} coordinates of a C (cubic-bezier) segment.\n */\nfunction getPointAtCubicSegmentLength(\n  x1: number,\n  y1: number,\n  c1x: number,\n  c1y: number,\n  c2x: number,\n  c2y: number,\n  x2: number,\n  y2: number,\n  t: number,\n) {\n  const t1 = 1 - t;\n  return {\n    x: t1 ** 3 * x1 + 3 * t1 ** 2 * t * c1x + 3 * t1 * t ** 2 * c2x + t ** 3 * x2,\n    y: t1 ** 3 * y1 + 3 * t1 ** 2 * t * c1y + 3 * t1 * t ** 2 * c2y + t ** 3 * y2,\n  };\n}\n\n/**\n * Returns the length of a C (cubic-bezier) segment\n * or an {x,y} point at a given length.\n */\nexport function segmentCubicFactory(\n  x1: number,\n  y1: number,\n  c1x: number,\n  c1y: number,\n  c2x: number,\n  c2y: number,\n  x2: number,\n  y2: number,\n  distance: number,\n  options: Partial<PathLengthFactoryOptions>,\n): LengthFactory {\n  const { bbox = true, length = true, sampleSize = 10 } = options;\n  const distanceIsNumber = typeof distance === 'number';\n  let x = x1;\n  let y = y1;\n  let LENGTH = 0;\n  let prev = [x, y, LENGTH];\n  let cur: [number, number] = [x, y];\n  let t = 0;\n  let POINT = { x: 0, y: 0 };\n  const POINTS = [{ x, y }];\n\n  if (distanceIsNumber && distance <= 0) {\n    POINT = { x, y };\n  }\n\n  // bad perf when size = 300\n  for (let j = 0; j <= sampleSize; j += 1) {\n    t = j / sampleSize;\n\n    ({ x, y } = getPointAtCubicSegmentLength(x1, y1, c1x, c1y, c2x, c2y, x2, y2, t));\n\n    if (bbox) {\n      POINTS.push({ x, y });\n    }\n\n    if (length) {\n      LENGTH += distanceSquareRoot(cur, [x, y]);\n    }\n    cur = [x, y];\n\n    if (distanceIsNumber && LENGTH >= distance && distance > prev[2]) {\n      const dv = (LENGTH - distance) / (LENGTH - prev[2]);\n\n      POINT = {\n        x: cur[0] * (1 - dv) + prev[0] * dv,\n        y: cur[1] * (1 - dv) + prev[1] * dv,\n      };\n    }\n    prev = [x, y, LENGTH];\n  }\n\n  if (distanceIsNumber && distance >= LENGTH) {\n    POINT = { x: x2, y: y2 };\n  }\n\n  return {\n    length: LENGTH,\n    point: POINT,\n    min: {\n      x: Math.min.apply(\n        null,\n        POINTS.map((n) => n.x),\n      ),\n      y: Math.min.apply(\n        null,\n        POINTS.map((n) => n.y),\n      ),\n    },\n    max: {\n      x: Math.max.apply(\n        null,\n        POINTS.map((n) => n.x),\n      ),\n      y: Math.max.apply(\n        null,\n        POINTS.map((n) => n.y),\n      ),\n    },\n  };\n}\n"], "mappings": "AACA,SAASA,kBAAkB,QAAQ,wBAAwB;AAE3D;;;;AAIA,SAASC,4BAA4BA,CACnCC,EAAU,EACVC,EAAU,EACVC,GAAW,EACXC,GAAW,EACXC,GAAW,EACXC,GAAW,EACXC,EAAU,EACVC,EAAU,EACVC,CAAS;EAET,IAAMC,EAAE,GAAG,CAAC,GAAGD,CAAC;EAChB,OAAO;IACLE,CAAC,EAAEC,IAAA,CAAAC,GAAA,CAAAH,EAAE,EAAI,CAAC,IAAGT,EAAE,GAAG,CAAC,GAAGW,IAAA,CAAAC,GAAA,CAAAH,EAAE,EAAI,CAAC,IAAGD,CAAC,GAAGN,GAAG,GAAG,CAAC,GAAGO,EAAE,GAAGE,IAAA,CAAAC,GAAA,CAAAJ,CAAC,EAAI,CAAC,IAAGJ,GAAG,GAAGO,IAAA,CAAAC,GAAA,CAAAJ,CAAC,EAAI,CAAC,IAAGF,EAAE;IAC7EO,CAAC,EAAEF,IAAA,CAAAC,GAAA,CAAAH,EAAE,EAAI,CAAC,IAAGR,EAAE,GAAG,CAAC,GAAGU,IAAA,CAAAC,GAAA,CAAAH,EAAE,EAAI,CAAC,IAAGD,CAAC,GAAGL,GAAG,GAAG,CAAC,GAAGM,EAAE,GAAGE,IAAA,CAAAC,GAAA,CAAAJ,CAAC,EAAI,CAAC,IAAGH,GAAG,GAAGM,IAAA,CAAAC,GAAA,CAAAJ,CAAC,EAAI,CAAC,IAAGD;GAC5E;AACH;AAEA;;;;AAIA,OAAM,SAAUO,mBAAmBA,CACjCd,EAAU,EACVC,EAAU,EACVC,GAAW,EACXC,GAAW,EACXC,GAAW,EACXC,GAAW,EACXC,EAAU,EACVC,EAAU,EACVQ,QAAgB,EAChBC,OAA0C;;EAElC,IAAAC,EAAA,GAAgDD,OAAO,CAAAE,IAA5C;IAAXA,IAAI,GAAAD,EAAA,cAAG,IAAI,GAAAA,EAAA;IAAEE,EAAA,GAAmCH,OAAO,CAAAI,MAA7B;IAAbA,MAAM,GAAAD,EAAA,cAAG,IAAI,GAAAA,EAAA;IAAEE,EAAA,GAAoBL,OAAO,CAAAM,UAAZ;IAAfA,UAAU,GAAAD,EAAA,cAAG,EAAE,GAAAA,EAAA;EACnD,IAAME,gBAAgB,GAAG,OAAOR,QAAQ,KAAK,QAAQ;EACrD,IAAIL,CAAC,GAAGV,EAAE;EACV,IAAIa,CAAC,GAAGZ,EAAE;EACV,IAAIuB,MAAM,GAAG,CAAC;EACd,IAAIC,IAAI,GAAG,CAACf,CAAC,EAAEG,CAAC,EAAEW,MAAM,CAAC;EACzB,IAAIE,GAAG,GAAqB,CAAChB,CAAC,EAAEG,CAAC,CAAC;EAClC,IAAIL,CAAC,GAAG,CAAC;EACT,IAAImB,KAAK,GAAG;IAAEjB,CAAC,EAAE,CAAC;IAAEG,CAAC,EAAE;EAAC,CAAE;EAC1B,IAAMe,MAAM,GAAG,CAAC;IAAElB,CAAC,EAAAA,CAAA;IAAEG,CAAC,EAAAA;EAAA,CAAE,CAAC;EAEzB,IAAIU,gBAAgB,IAAIR,QAAQ,IAAI,CAAC,EAAE;IACrCY,KAAK,GAAG;MAAEjB,CAAC,EAAAA,CAAA;MAAEG,CAAC,EAAAA;IAAA,CAAE;EAClB;EAEA;EACA,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIP,UAAU,EAAEO,CAAC,IAAI,CAAC,EAAE;IACvCrB,CAAC,GAAGqB,CAAC,GAAGP,UAAU;IAEjBQ,EAAA,GAAW/B,4BAA4B,CAACC,EAAE,EAAEC,EAAE,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,EAAE,EAAEC,EAAE,EAAEC,CAAC,CAAC,EAA5EE,CAAC,GAAAoB,EAAA,CAAApB,CAAA,EAAEG,CAAC,GAAAiB,EAAA,CAAAjB,CAAA;IAEP,IAAIK,IAAI,EAAE;MACRU,MAAM,CAACG,IAAI,CAAC;QAAErB,CAAC,EAAAA,CAAA;QAAEG,CAAC,EAAAA;MAAA,CAAE,CAAC;IACvB;IAEA,IAAIO,MAAM,EAAE;MACVI,MAAM,IAAI1B,kBAAkB,CAAC4B,GAAG,EAAE,CAAChB,CAAC,EAAEG,CAAC,CAAC,CAAC;IAC3C;IACAa,GAAG,GAAG,CAAChB,CAAC,EAAEG,CAAC,CAAC;IAEZ,IAAIU,gBAAgB,IAAIC,MAAM,IAAIT,QAAQ,IAAIA,QAAQ,GAAGU,IAAI,CAAC,CAAC,CAAC,EAAE;MAChE,IAAMO,EAAE,GAAG,CAACR,MAAM,GAAGT,QAAQ,KAAKS,MAAM,GAAGC,IAAI,CAAC,CAAC,CAAC,CAAC;MAEnDE,KAAK,GAAG;QACNjB,CAAC,EAAEgB,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGM,EAAE,CAAC,GAAGP,IAAI,CAAC,CAAC,CAAC,GAAGO,EAAE;QACnCnB,CAAC,EAAEa,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGM,EAAE,CAAC,GAAGP,IAAI,CAAC,CAAC,CAAC,GAAGO;OAClC;IACH;IACAP,IAAI,GAAG,CAACf,CAAC,EAAEG,CAAC,EAAEW,MAAM,CAAC;EACvB;EAEA,IAAID,gBAAgB,IAAIR,QAAQ,IAAIS,MAAM,EAAE;IAC1CG,KAAK,GAAG;MAAEjB,CAAC,EAAEJ,EAAE;MAAEO,CAAC,EAAEN;IAAE,CAAE;EAC1B;EAEA,OAAO;IACLa,MAAM,EAAEI,MAAM;IACdS,KAAK,EAAEN,KAAK;IACZO,GAAG,EAAE;MACHxB,CAAC,EAAEC,IAAI,CAACuB,GAAG,CAACC,KAAK,CACf,IAAI,EACJP,MAAM,CAACQ,GAAG,CAAC,UAACC,CAAC;QAAK,OAAAA,CAAC,CAAC3B,CAAC;MAAH,CAAG,CAAC,CACvB;MACDG,CAAC,EAAEF,IAAI,CAACuB,GAAG,CAACC,KAAK,CACf,IAAI,EACJP,MAAM,CAACQ,GAAG,CAAC,UAACC,CAAC;QAAK,OAAAA,CAAC,CAACxB,CAAC;MAAH,CAAG,CAAC;KAEzB;IACDyB,GAAG,EAAE;MACH5B,CAAC,EAAEC,IAAI,CAAC2B,GAAG,CAACH,KAAK,CACf,IAAI,EACJP,MAAM,CAACQ,GAAG,CAAC,UAACC,CAAC;QAAK,OAAAA,CAAC,CAAC3B,CAAC;MAAH,CAAG,CAAC,CACvB;MACDG,CAAC,EAAEF,IAAI,CAAC2B,GAAG,CAACH,KAAK,CACf,IAAI,EACJP,MAAM,CAACQ,GAAG,CAAC,UAACC,CAAC;QAAK,OAAAA,CAAC,CAACxB,CAAC;MAAH,CAAG,CAAC;;GAG3B;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}