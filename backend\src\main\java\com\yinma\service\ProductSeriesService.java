package com.yinma.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yinma.entity.ProductSeries;
import com.yinma.dto.ProductSeriesDTO;
import com.yinma.vo.ProductSeriesVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 产品系列管理服务接口
 * 银马实业设备产品系列管理系统
 * 
 * <AUTHOR>
 * @since 2024-05-20
 */
public interface ProductSeriesService extends IService<ProductSeries> {

    /**
     * 分页查询产品系列
     * 
     * @param page 分页参数
     * @param seriesName 系列名称
     * @param seriesCode 系列代码
     * @param productType 产品类型
     * @param techLevel 技术等级
     * @param status 状态
     * @return 分页结果
     */
    IPage<ProductSeriesVO> getProductSeriesPage(Page<ProductSeries> page, String seriesName, 
                                              String seriesCode, String productType, 
                                              String techLevel, String status);

    /**
     * 根据ID查询产品系列详情
     * 
     * @param id 产品系列ID
     * @return 产品系列详情
     */
    ProductSeriesVO getProductSeriesDetailById(Long id);

    /**
     * 创建产品系列
     * 
     * @param productSeriesDTO 产品系列信息
     * @return 创建的产品系列
     */
    ProductSeries createProductSeries(ProductSeriesDTO productSeriesDTO);

    /**
     * 更新产品系列
     * 
     * @param id 产品系列ID
     * @param productSeriesDTO 产品系列信息
     * @return 更新后的产品系列
     */
    ProductSeries updateProductSeries(Long id, ProductSeriesDTO productSeriesDTO);

    /**
     * 删除产品系列
     * 
     * @param id 产品系列ID
     * @return 是否删除成功
     */
    boolean deleteProductSeries(Long id);

    /**
     * 批量删除产品系列
     * 
     * @param ids 产品系列ID列表
     * @return 是否删除成功
     */
    boolean batchDeleteProductSeries(List<Long> ids);

    /**
     * 更新产品系列状态
     * 
     * @param id 产品系列ID
     * @param status 新状态
     * @param remark 备注
     * @return 更新后的产品系列
     */
    ProductSeries updateProductSeriesStatus(Long id, String status, String remark);

    /**
     * 复制产品系列
     * 
     * @param id 原产品系列ID
     * @param newSeriesName 新系列名称
     * @param newSeriesCode 新系列代码
     * @return 复制的产品系列
     */
    ProductSeries copyProductSeries(Long id, String newSeriesName, String newSeriesCode);

    /**
     * 获取产品系列配置模板
     * 
     * @param id 产品系列ID
     * @return 配置模板
     */
    Map<String, Object> getProductSeriesTemplate(Long id);

    /**
     * 保存产品系列配置模板
     * 
     * @param id 产品系列ID
     * @param template 配置模板
     */
    void saveProductSeriesTemplate(Long id, Map<String, Object> template);

    /**
     * 获取产品系列技术参数
     * 
     * @param id 产品系列ID
     * @return 技术参数
     */
    Map<String, Object> getProductSeriesSpecifications(Long id);

    /**
     * 更新产品系列技术参数
     * 
     * @param id 产品系列ID
     * @param specifications 技术参数
     */
    void updateProductSeriesSpecifications(Long id, Map<String, Object> specifications);

    /**
     * 获取产品系列BOM清单
     * 
     * @param id 产品系列ID
     * @param version BOM版本
     * @return BOM清单
     */
    List<Map<String, Object>> getProductSeriesBom(Long id, String version);

    /**
     * 更新产品系列BOM清单
     * 
     * @param id 产品系列ID
     * @param bom BOM清单
     * @param version 版本号
     */
    void updateProductSeriesBom(Long id, List<Map<String, Object>> bom, String version);

    /**
     * 获取产品系列价格配置
     * 
     * @param id 产品系列ID
     * @return 价格配置
     */
    Map<String, Object> getProductSeriesPricing(Long id);

    /**
     * 更新产品系列价格配置
     * 
     * @param id 产品系列ID
     * @param pricing 价格配置
     */
    void updateProductSeriesPricing(Long id, Map<String, Object> pricing);

    /**
     * 上传产品系列图片
     * 
     * @param id 产品系列ID
     * @param file 图片文件
     * @param imageType 图片类型
     * @return 图片URL
     */
    String uploadProductSeriesImage(Long id, MultipartFile file, String imageType);

    /**
     * 获取产品系列图片列表
     * 
     * @param id 产品系列ID
     * @return 图片列表
     */
    List<Map<String, Object>> getProductSeriesImages(Long id);

    /**
     * 删除产品系列图片
     * 
     * @param id 产品系列ID
     * @param imageId 图片ID
     * @return 是否删除成功
     */
    boolean deleteProductSeriesImage(Long id, Long imageId);

    /**
     * 导出产品系列数据
     * 
     * @param seriesName 系列名称
     * @param productType 产品类型
     * @param status 状态
     * @param format 导出格式
     * @return 文件URL
     */
    String exportProductSeries(String seriesName, String productType, String status, String format);

    /**
     * 导入产品系列数据
     * 
     * @param file 导入文件
     * @return 导入结果
     */
    Map<String, Object> importProductSeries(MultipartFile file);

    /**
     * 获取产品系列统计数据
     * 
     * @return 统计数据
     */
    Map<String, Object> getProductSeriesStatistics();

    /**
     * 获取产品系列选项列表
     * 
     * @param productType 产品类型
     * @param techLevel 技术等级
     * @return 选项列表
     */
    List<Map<String, Object>> getProductSeriesOptions(String productType, String techLevel);

    /**
     * 验证产品系列代码唯一性
     * 
     * @param seriesCode 系列代码
     * @param excludeId 排除的ID
     * @return 是否唯一
     */
    boolean validateSeriesCode(String seriesCode, Long excludeId);

    /**
     * 获取产品系列推荐配置
     * 
     * @param id 产品系列ID
     * @param customerRequirements 客户需求
     * @return 推荐配置
     */
    Map<String, Object> getProductSeriesRecommendations(Long id, String customerRequirements);

    /**
     * 生成产品系列配置报告
     * 
     * @param id 产品系列ID
     * @param reportConfig 报告配置
     * @return 报告URL
     */
    String generateProductSeriesReport(Long id, Map<String, Object> reportConfig);

    /**
     * 获取产品系列变更历史
     * 
     * @param id 产品系列ID
     * @return 变更历史
     */
    List<Map<String, Object>> getProductSeriesChangeHistory(Long id);

    /**
     * 记录产品系列变更
     * 
     * @param id 产品系列ID
     * @param changeType 变更类型
     * @param changeContent 变更内容
     * @param operator 操作人
     */
    void recordProductSeriesChange(Long id, String changeType, String changeContent, String operator);

    /**
     * 获取产品系列关联的设备配置
     * 
     * @param id 产品系列ID
     * @return 设备配置列表
     */
    List<Map<String, Object>> getProductSeriesEquipmentConfigs(Long id);

    /**
     * 创建基于产品系列的设备配置
     * 
     * @param id 产品系列ID
     * @param configName 配置名称
     * @param customizations 定制化参数
     * @return 设备配置
     */
    Map<String, Object> createEquipmentConfigFromSeries(Long id, String configName, Map<String, Object> customizations);

    /**
     * 获取产品系列成本分析
     * 
     * @param id 产品系列ID
     * @return 成本分析
     */
    Map<String, Object> getProductSeriesCostAnalysis(Long id);

    /**
     * 更新产品系列成本信息
     * 
     * @param id 产品系列ID
     * @param costInfo 成本信息
     */
    void updateProductSeriesCostInfo(Long id, Map<String, Object> costInfo);

    /**
     * 获取产品系列市场分析
     * 
     * @param id 产品系列ID
     * @return 市场分析
     */
    Map<String, Object> getProductSeriesMarketAnalysis(Long id);

    /**
     * 获取产品系列竞争对比
     * 
     * @param id 产品系列ID
     * @param competitorIds 竞争对手产品ID列表
     * @return 竞争对比
     */
    Map<String, Object> getProductSeriesCompetitorComparison(Long id, List<Long> competitorIds);

    /**
     * 获取产品系列销售数据
     * 
     * @param id 产品系列ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 销售数据
     */
    Map<String, Object> getProductSeriesSalesData(Long id, String startDate, String endDate);

    /**
     * 获取产品系列客户反馈
     * 
     * @param id 产品系列ID
     * @return 客户反馈
     */
    List<Map<String, Object>> getProductSeriesCustomerFeedback(Long id);

    /**
     * 添加产品系列客户反馈
     * 
     * @param id 产品系列ID
     * @param feedback 反馈信息
     */
    void addProductSeriesCustomerFeedback(Long id, Map<String, Object> feedback);

    /**
     * 获取产品系列质量指标
     * 
     * @param id 产品系列ID
     * @return 质量指标
     */
    Map<String, Object> getProductSeriesQualityMetrics(Long id);

    /**
     * 更新产品系列质量指标
     * 
     * @param id 产品系列ID
     * @param qualityMetrics 质量指标
     */
    void updateProductSeriesQualityMetrics(Long id, Map<String, Object> qualityMetrics);

    /**
     * 获取产品系列生产计划
     * 
     * @param id 产品系列ID
     * @return 生产计划
     */
    List<Map<String, Object>> getProductSeriesProductionPlan(Long id);

    /**
     * 创建产品系列生产计划
     * 
     * @param id 产品系列ID
     * @param productionPlan 生产计划
     * @return 创建的生产计划
     */
    Map<String, Object> createProductSeriesProductionPlan(Long id, Map<String, Object> productionPlan);

    /**
     * 获取产品系列库存信息
     * 
     * @param id 产品系列ID
     * @return 库存信息
     */
    Map<String, Object> getProductSeriesInventoryInfo(Long id);

    /**
     * 更新产品系列库存信息
     * 
     * @param id 产品系列ID
     * @param inventoryInfo 库存信息
     */
    void updateProductSeriesInventoryInfo(Long id, Map<String, Object> inventoryInfo);

    /**
     * 获取产品系列供应商信息
     * 
     * @param id 产品系列ID
     * @return 供应商信息
     */
    List<Map<String, Object>> getProductSeriesSupplierInfo(Long id);

    /**
     * 添加产品系列供应商
     * 
     * @param id 产品系列ID
     * @param supplierInfo 供应商信息
     */
    void addProductSeriesSupplier(Long id, Map<String, Object> supplierInfo);

    /**
     * 移除产品系列供应商
     * 
     * @param id 产品系列ID
     * @param supplierId 供应商ID
     * @return 是否移除成功
     */
    boolean removeProductSeriesSupplier(Long id, Long supplierId);

    /**
     * 获取产品系列认证信息
     * 
     * @param id 产品系列ID
     * @return 认证信息
     */
    List<Map<String, Object>> getProductSeriesCertifications(Long id);

    /**
     * 添加产品系列认证
     * 
     * @param id 产品系列ID
     * @param certification 认证信息
     */
    void addProductSeriesCertification(Long id, Map<String, Object> certification);

    /**
     * 获取产品系列技术文档
     * 
     * @param id 产品系列ID
     * @return 技术文档列表
     */
    List<Map<String, Object>> getProductSeriesTechnicalDocs(Long id);

    /**
     * 上传产品系列技术文档
     * 
     * @param id 产品系列ID
     * @param file 文档文件
     * @param docType 文档类型
     * @return 文档URL
     */
    String uploadProductSeriesTechnicalDoc(Long id, MultipartFile file, String docType);

    /**
     * 删除产品系列技术文档
     * 
     * @param id 产品系列ID
     * @param docId 文档ID
     * @return 是否删除成功
     */
    boolean deleteProductSeriesTechnicalDoc(Long id, Long docId);

    /**
     * 获取产品系列维护手册
     * 
     * @param id 产品系列ID
     * @return 维护手册
     */
    Map<String, Object> getProductSeriesMaintenanceManual(Long id);

    /**
     * 更新产品系列维护手册
     * 
     * @param id 产品系列ID
     * @param maintenanceManual 维护手册
     */
    void updateProductSeriesMaintenanceManual(Long id, Map<String, Object> maintenanceManual);

    /**
     * 获取产品系列培训材料
     * 
     * @param id 产品系列ID
     * @return 培训材料列表
     */
    List<Map<String, Object>> getProductSeriesTrainingMaterials(Long id);

    /**
     * 添加产品系列培训材料
     * 
     * @param id 产品系列ID
     * @param trainingMaterial 培训材料
     */
    void addProductSeriesTrainingMaterial(Long id, Map<String, Object> trainingMaterial);

    /**
     * 生成产品系列二维码
     * 
     * @param id 产品系列ID
     * @return 二维码URL
     */
    String generateProductSeriesQRCode(Long id);

    /**
     * 获取产品系列数字化模型
     * 
     * @param id 产品系列ID
     * @return 数字化模型信息
     */
    Map<String, Object> getProductSeriesDigitalModel(Long id);

    /**
     * 上传产品系列数字化模型
     * 
     * @param id 产品系列ID
     * @param file 模型文件
     * @param modelType 模型类型
     * @return 模型URL
     */
    String uploadProductSeriesDigitalModel(Long id, MultipartFile file, String modelType);

    /**
     * 获取产品系列AR/VR展示配置
     * 
     * @param id 产品系列ID
     * @return AR/VR配置
     */
    Map<String, Object> getProductSeriesARVRConfig(Long id);

    /**
     * 更新产品系列AR/VR展示配置
     * 
     * @param id 产品系列ID
     * @param arvrConfig AR/VR配置
     */
    void updateProductSeriesARVRConfig(Long id, Map<String, Object> arvrConfig);
}