package com.yinma.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应状态码枚举
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    // 通用状态码
    SUCCESS(200, "操作成功"),
    ERROR(500, "操作失败"),
    PARAM_ERROR(400, "参数错误"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不支持"),
    UNSUPPORTED_MEDIA_TYPE(415, "不支持的媒体类型"),
    
    // 认证授权相关
    UNAUTHORIZED(401, "未认证"),
    FORBIDDEN(403, "无权限访问"),
    TOKEN_INVALID(4001, "Token无效"),
    TOKEN_EXPIRED(4002, "Token已过期"),
    LOGIN_FAILED(4003, "登录失败"),
    ACCOUNT_DISABLED(4004, "账户已禁用"),
    ACCOUNT_LOCKED(4005, "账户已锁定"),
    PASSWORD_ERROR(4006, "密码错误"),
    
    // 业务相关状态码
    
    // BOM管理模块 (5000-5099)
    BOM_NOT_FOUND(5001, "BOM不存在"),
    BOM_VERSION_CONFLICT(5002, "BOM版本冲突"),
    BOM_CONVERSION_FAILED(5003, "BOM转换失败"),
    MATERIAL_NOT_FOUND(5004, "物料不存在"),
    MATERIAL_DUPLICATE(5005, "物料编码重复"),
    
    // 智能制造模块 (5100-5199)
    PRODUCTION_PLAN_NOT_FOUND(5101, "生产计划不存在"),
    PRODUCTION_ORDER_NOT_FOUND(5102, "生产订单不存在"),
    WORKSHOP_NOT_AVAILABLE(5103, "车间不可用"),
    EQUIPMENT_BUSY(5104, "设备忙碌中"),
    MATERIAL_SHORTAGE(5105, "物料短缺"),
    
    // 项目交付模块 (5200-5299)
    PROJECT_NOT_FOUND(5201, "项目不存在"),
    PROJECT_STATUS_ERROR(5202, "项目状态错误"),
    MILESTONE_OVERDUE(5203, "里程碑逾期"),
    RESOURCE_CONFLICT(5204, "资源冲突"),
    BUDGET_EXCEEDED(5205, "预算超支"),
    
    // 智慧服务模块 (5300-5399)
    EQUIPMENT_NOT_FOUND(5301, "设备不存在"),
    SERVICE_ORDER_NOT_FOUND(5302, "服务工单不存在"),
    TECHNICIAN_NOT_AVAILABLE(5303, "技术员不可用"),
    SPARE_PART_SHORTAGE(5304, "备件库存不足"),
    DIAGNOSIS_FAILED(5305, "智能诊断失败"),
    
    // 财务管控模块 (5400-5499)
    COST_CENTER_NOT_FOUND(5401, "成本中心不存在"),
    BUDGET_NOT_FOUND(5402, "预算不存在"),
    FINANCIAL_PERIOD_CLOSED(5403, "财务期间已关闭"),
    INVOICE_DUPLICATE(5404, "发票重复"),
    PAYMENT_FAILED(5405, "付款失败"),
    
    // 协同决策模块 (5500-5599)
    WORKFLOW_NOT_FOUND(5501, "工作流不存在"),
    APPROVAL_REJECTED(5502, "审批被拒绝"),
    PROCESS_TIMEOUT(5503, "流程超时"),
    DELEGATE_FAILED(5504, "委托失败"),
    NOTIFICATION_FAILED(5505, "通知发送失败"),
    
    // 系统相关 (9000-9999)
    SYSTEM_BUSY(9001, "系统繁忙，请稍后重试"),
    SYSTEM_MAINTENANCE(9002, "系统维护中"),
    DATABASE_ERROR(9003, "数据库错误"),
    CACHE_ERROR(9004, "缓存错误"),
    FILE_UPLOAD_FAILED(9005, "文件上传失败"),
    FILE_DOWNLOAD_FAILED(9006, "文件下载失败"),
    EXPORT_FAILED(9007, "数据导出失败"),
    IMPORT_FAILED(9008, "数据导入失败");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态消息
     */
    private final String message;
}