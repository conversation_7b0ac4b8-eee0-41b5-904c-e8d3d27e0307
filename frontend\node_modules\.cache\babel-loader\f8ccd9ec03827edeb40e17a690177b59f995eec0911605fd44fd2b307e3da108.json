{"ast": null, "code": "import { defaultPrefixCls } from '../config-provider';\n// ================== Collapse Motion ==================\nconst getCollapsedHeight = () => ({\n  height: 0,\n  opacity: 0\n});\nconst getRealHeight = node => {\n  const {\n    scrollHeight\n  } = node;\n  return {\n    height: scrollHeight,\n    opacity: 1\n  };\n};\nconst getCurrentHeight = node => ({\n  height: node ? node.offsetHeight : 0\n});\nconst skipOpacityTransition = (_, event) => (event === null || event === void 0 ? void 0 : event.deadline) === true || event.propertyName === 'height';\nconst initCollapseMotion = (rootCls = defaultPrefixCls) => ({\n  motionName: `${rootCls}-motion-collapse`,\n  onAppearStart: getCollapsedHeight,\n  onEnterStart: getCollapsedHeight,\n  onAppearActive: getRealHeight,\n  onEnterActive: getRealHeight,\n  onLeaveStart: getCurrentHeight,\n  onLeaveActive: getCollapsedHeight,\n  onAppearEnd: skipOpacityTransition,\n  onEnterEnd: skipOpacityTransition,\n  onLeaveEnd: skipOpacityTransition,\n  motionDeadline: 500\n});\nconst _SelectPlacements = ['bottomLeft', 'bottomRight', 'topLeft', 'topRight'];\nconst getTransitionName = (rootPrefixCls, motion, transitionName) => {\n  if (transitionName !== undefined) {\n    return transitionName;\n  }\n  return `${rootPrefixCls}-${motion}`;\n};\nexport { getTransitionName };\nexport default initCollapseMotion;", "map": {"version": 3, "names": ["defaultPrefixCls", "getCollapsedHeight", "height", "opacity", "getRealHeight", "node", "scrollHeight", "getCurrentHeight", "offsetHeight", "skipOpacityTransition", "_", "event", "deadline", "propertyName", "initCollapseMotion", "rootCls", "motionName", "onAppearStart", "onEnterStart", "onAppearActive", "onEnterActive", "onLeaveStart", "onLeaveActive", "onAppearEnd", "onEnterEnd", "onLeaveEnd", "motionDeadline", "_SelectPlacements", "getTransitionName", "rootPrefixCls", "motion", "transitionName", "undefined"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/antd/es/_util/motion.js"], "sourcesContent": ["import { defaultPrefixCls } from '../config-provider';\n// ================== Collapse Motion ==================\nconst getCollapsedHeight = () => ({\n  height: 0,\n  opacity: 0\n});\nconst getRealHeight = node => {\n  const {\n    scrollHeight\n  } = node;\n  return {\n    height: scrollHeight,\n    opacity: 1\n  };\n};\nconst getCurrentHeight = node => ({\n  height: node ? node.offsetHeight : 0\n});\nconst skipOpacityTransition = (_, event) => (event === null || event === void 0 ? void 0 : event.deadline) === true || event.propertyName === 'height';\nconst initCollapseMotion = (rootCls = defaultPrefixCls) => ({\n  motionName: `${rootCls}-motion-collapse`,\n  onAppearStart: getCollapsedHeight,\n  onEnterStart: getCollapsedHeight,\n  onAppearActive: getRealHeight,\n  onEnterActive: getRealHeight,\n  onLeaveStart: getCurrentHeight,\n  onLeaveActive: getCollapsedHeight,\n  onAppearEnd: skipOpacityTransition,\n  onEnterEnd: skipOpacityTransition,\n  onLeaveEnd: skipOpacityTransition,\n  motionDeadline: 500\n});\nconst _SelectPlacements = ['bottomLeft', 'bottomRight', 'topLeft', 'topRight'];\nconst getTransitionName = (rootPrefixCls, motion, transitionName) => {\n  if (transitionName !== undefined) {\n    return transitionName;\n  }\n  return `${rootPrefixCls}-${motion}`;\n};\nexport { getTransitionName };\nexport default initCollapseMotion;"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,oBAAoB;AACrD;AACA,MAAMC,kBAAkB,GAAGA,CAAA,MAAO;EAChCC,MAAM,EAAE,CAAC;EACTC,OAAO,EAAE;AACX,CAAC,CAAC;AACF,MAAMC,aAAa,GAAGC,IAAI,IAAI;EAC5B,MAAM;IACJC;EACF,CAAC,GAAGD,IAAI;EACR,OAAO;IACLH,MAAM,EAAEI,YAAY;IACpBH,OAAO,EAAE;EACX,CAAC;AACH,CAAC;AACD,MAAMI,gBAAgB,GAAGF,IAAI,KAAK;EAChCH,MAAM,EAAEG,IAAI,GAAGA,IAAI,CAACG,YAAY,GAAG;AACrC,CAAC,CAAC;AACF,MAAMC,qBAAqB,GAAGA,CAACC,CAAC,EAAEC,KAAK,KAAK,CAACA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACC,QAAQ,MAAM,IAAI,IAAID,KAAK,CAACE,YAAY,KAAK,QAAQ;AACtJ,MAAMC,kBAAkB,GAAGA,CAACC,OAAO,GAAGf,gBAAgB,MAAM;EAC1DgB,UAAU,EAAE,GAAGD,OAAO,kBAAkB;EACxCE,aAAa,EAAEhB,kBAAkB;EACjCiB,YAAY,EAAEjB,kBAAkB;EAChCkB,cAAc,EAAEf,aAAa;EAC7BgB,aAAa,EAAEhB,aAAa;EAC5BiB,YAAY,EAAEd,gBAAgB;EAC9Be,aAAa,EAAErB,kBAAkB;EACjCsB,WAAW,EAAEd,qBAAqB;EAClCe,UAAU,EAAEf,qBAAqB;EACjCgB,UAAU,EAAEhB,qBAAqB;EACjCiB,cAAc,EAAE;AAClB,CAAC,CAAC;AACF,MAAMC,iBAAiB,GAAG,CAAC,YAAY,EAAE,aAAa,EAAE,SAAS,EAAE,UAAU,CAAC;AAC9E,MAAMC,iBAAiB,GAAGA,CAACC,aAAa,EAAEC,MAAM,EAAEC,cAAc,KAAK;EACnE,IAAIA,cAAc,KAAKC,SAAS,EAAE;IAChC,OAAOD,cAAc;EACvB;EACA,OAAO,GAAGF,aAAa,IAAIC,MAAM,EAAE;AACrC,CAAC;AACD,SAASF,iBAAiB;AAC1B,eAAed,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}