# 西安银马实业数字化管理系统 用户手册

## 目录

1. [系统概述](#系统概述)
2. [快速开始](#快速开始)
3. [用户登录](#用户登录)
4. [系统导航](#系统导航)
5. [BOM管理](#bom管理)
6. [物料管理](#物料管理)
7. [生产管理](#生产管理)
8. [项目交付](#项目交付)
9. [设备管理](#设备管理)
10. [系统设置](#系统设置)
11. [常见问题](#常见问题)
12. [技术支持](#技术支持)

## 系统概述

西安银马实业数字化管理系统是一套专为制造业企业设计的综合管理平台，主要功能包括：

- **BOM管理**: 产品结构管理、版本控制、变更追踪
- **物料管理**: 物料主数据、库存管理、采购管理
- **生产管理**: 生产计划、工艺路线、进度跟踪
- **项目交付**: 项目管理、交付跟踪、客户管理
- **设备管理**: 设备台账、维护计划、状态监控

### 系统特点

- 🚀 **现代化界面**: 基于React和Ant Design的响应式设计
- 🔒 **安全可靠**: JWT认证、角色权限控制
- 📊 **数据可视化**: 丰富的图表和统计分析
- 🔄 **实时同步**: 数据实时更新，多用户协同
- 📱 **移动友好**: 支持移动设备访问

## 快速开始

### 系统要求

- **浏览器**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **分辨率**: 1366x768 或更高
- **网络**: 稳定的网络连接

### 访问系统

1. 打开浏览器，访问系统地址：`http://your-domain.com`
2. 输入用户名和密码登录
3. 首次登录建议修改默认密码

## 用户登录

### 登录步骤

1. **访问登录页面**
   - 在浏览器中输入系统地址
   - 系统会自动跳转到登录页面

2. **输入登录信息**
   - 用户名：您的系统账号
   - 密码：对应的密码
   - 验证码：输入图片显示的验证码

3. **选择登录选项**
   - ☑️ 记住密码：下次自动填充密码
   - ☑️ 自动登录：7天内免登录

4. **点击登录按钮**

### 默认账号

| 角色 | 用户名 | 密码 | 权限说明 |
|------|--------|------|----------|
| 系统管理员 | admin | admin123 | 所有功能权限 |
| BOM工程师 | bom_engineer | bom123 | BOM管理权限 |
| 物料管理员 | material_admin | mat123 | 物料管理权限 |
| 生产计划员 | production_planner | prod123 | 生产管理权限 |
| 项目经理 | project_manager | proj123 | 项目管理权限 |

### 忘记密码

1. 点击登录页面的"忘记密码"链接
2. 输入注册邮箱或手机号
3. 系统发送重置链接到邮箱
4. 点击邮件中的链接重置密码

## 系统导航

### 主界面布局

```
┌─────────────────────────────────────────────────────────┐
│ 顶部导航栏 (Logo, 用户信息, 通知, 设置)                    │
├─────────────┬───────────────────────────────────────────┤
│             │                                           │
│   左侧      │                                           │
│   菜单      │            主要内容区域                    │
│   导航      │                                           │
│             │                                           │
│             │                                           │
├─────────────┴───────────────────────────────────────────┤
│ 底部状态栏 (版本信息, 在线用户数, 系统状态)                │
└─────────────────────────────────────────────────────────┘
```

### 菜单结构

- 📊 **工作台**: 系统概览、待办事项、统计图表
- 📋 **BOM管理**: BOM列表、BOM变更、版本管理
- 📦 **物料管理**: 物料主数据、分类管理、供应商管理
- 🏭 **生产管理**: 生产订单、工艺路线、进度跟踪
- 🎯 **项目交付**: 项目列表、交付计划、客户管理
- ⚙️ **设备管理**: 设备台账、维护计划、故障记录
- 👥 **系统管理**: 用户管理、角色权限、系统配置

### 快捷操作

- **Ctrl + /**: 打开快捷键帮助
- **Ctrl + K**: 全局搜索
- **F11**: 全屏模式
- **Esc**: 关闭弹窗

## BOM管理

### 功能概述

BOM（Bill of Materials）管理是系统的核心功能，支持多层级产品结构管理。

### 主要功能

#### 1. BOM列表管理

**访问路径**: 菜单 → BOM管理 → BOM列表

**功能说明**:
- 查看所有BOM信息
- 支持多条件搜索和筛选
- 批量操作（删除、导出等）

**操作步骤**:
1. 点击左侧菜单"BOM管理" → "BOM列表"
2. 使用搜索框输入BOM编码或名称
3. 选择BOM类型和状态进行筛选
4. 点击"搜索"按钮查看结果

#### 2. 创建新BOM

**操作步骤**:
1. 在BOM列表页面点击"新增BOM"按钮
2. 填写基本信息：
   - **BOM编码**: 唯一标识，建议使用规范编码
   - **BOM名称**: 描述性名称
   - **BOM类型**: 选择EBOM/PBOM/MBOM
   - **描述**: 详细说明
3. 点击"保存"完成创建

#### 3. BOM结构编辑

**操作步骤**:
1. 在BOM列表中点击"编辑"按钮
2. 在结构树中添加子项：
   - 点击"添加子项"按钮
   - 选择物料或子BOM
   - 设置数量和单位
   - 填写备注信息
3. 调整结构层级：
   - 拖拽节点改变层级关系
   - 使用右键菜单进行操作
4. 保存修改

#### 4. 版本管理

**版本状态说明**:
- **草稿(DRAFT)**: 编辑中，可修改
- **激活(ACTIVE)**: 正式版本，不可修改
- **已冻结(FROZEN)**: 临时冻结，不可使用
- **已废弃(OBSOLETE)**: 历史版本，仅供查看

**版本操作**:
1. **创建新版本**: 基于现有版本创建新版本
2. **激活版本**: 将草稿版本设为正式版本
3. **废弃版本**: 将旧版本标记为废弃

#### 5. BOM变更管理

**变更流程**:
1. 提交变更申请
2. 技术审核
3. 管理层审批
4. 执行变更
5. 变更确认

### 最佳实践

#### BOM编码规范

```
格式: [产品类型][序号][版本]
示例: 
- MAIN001V01 (主机产品001版本01)
- SUB002V03  (副机产品002版本03)
- PART123V01 (零件123版本01)
```

#### 结构设计原则

1. **层级清晰**: 不超过8层，每层含义明确
2. **编码唯一**: 每个物料编码在系统中唯一
3. **版本控制**: 重要变更必须创建新版本
4. **审批流程**: 严格按照变更流程执行

## 物料管理

### 功能概述

物料管理模块负责企业所有物料的主数据管理，包括原材料、半成品、成品等。

### 主要功能

#### 1. 物料主数据

**基本信息**:
- 物料编码、名称、规格型号
- 计量单位、标准价格
- 物料分类、供应商信息
- 技术参数、质量标准

**操作步骤**:
1. 访问"物料管理" → "物料列表"
2. 点击"新增物料"按钮
3. 填写物料信息：
   ```
   物料编码: MAT001
   物料名称: Q235钢板
   规格型号: 10mm×1000mm×2000mm
   计量单位: 张
   标准价格: 1500.00
   物料分类: 原材料-钢材
   主供应商: XX钢铁公司
   ```
4. 保存物料信息

#### 2. 物料分类管理

**分类结构示例**:
```
原材料
├── 钢材
│   ├── 板材
│   ├── 型材
│   └── 管材
├── 有色金属
│   ├── 铝材
│   └── 铜材
└── 标准件
    ├── 螺栓
    └── 螺母

半成品
├── 机加工件
├── 焊接件
└── 装配件

成品
├── 主机产品
└── 配套产品
```

#### 3. 供应商管理

**供应商信息**:
- 供应商编码、名称
- 联系方式、地址
- 资质证书、评级
- 供应物料清单

#### 4. 库存管理

**库存信息**:
- 当前库存数量
- 安全库存、最大库存
- 库存预警设置
- 出入库记录

### 物料编码规范

#### 编码结构

```
格式: [类别][材质][规格][序号]

示例:
STL-Q235-10-001  (钢材-Q235-10mm-序号001)
ALU-6061-5-002   (铝材-6061-5mm-序号002)
BOL-M8-50-001    (螺栓-M8-50mm-序号001)
```

#### 编码原则

1. **唯一性**: 每个物料编码全局唯一
2. **可读性**: 编码包含物料关键信息
3. **扩展性**: 预留扩展空间
4. **标准化**: 遵循企业编码标准

## 生产管理

### 功能概述

生产管理模块负责生产计划制定、执行跟踪和进度管理。

### 主要功能

#### 1. 生产订单管理

**订单信息**:
- 订单编号、产品信息
- 计划数量、计划工期
- 优先级、负责人
- 当前状态、完成进度

**订单状态**:
- **待开始**: 订单已创建，等待开始生产
- **进行中**: 正在生产
- **已暂停**: 临时暂停生产
- **已完成**: 生产完成
- **已取消**: 订单取消

#### 2. 工艺路线管理

**工艺路线定义**:
```
产品: 主机设备
工艺路线: ROUTE001

工序1: 下料 (车间A, 设备CNC001, 标准工时2h)
  ↓
工序2: 机加工 (车间B, 设备MILL002, 标准工时4h)
  ↓
工序3: 焊接 (车间C, 设备WELD003, 标准工时3h)
  ↓
工序4: 装配 (车间D, 人工作业, 标准工时6h)
  ↓
工序5: 检验 (质检部, 检测设备, 标准工时1h)
```

#### 3. 生产进度跟踪

**进度监控**:
- 实时查看各订单进度
- 工序完成情况统计
- 延期预警提醒
- 资源使用情况分析

**操作步骤**:
1. 访问"生产管理" → "生产订单"
2. 选择要跟踪的订单
3. 查看详细进度信息
4. 更新工序完成状态

### 生产计划制定

#### 计划制定流程

1. **需求分析**
   - 收集销售订单
   - 分析库存情况
   - 确定生产需求

2. **能力评估**
   - 评估设备能力
   - 评估人员配置
   - 识别瓶颈资源

3. **计划排程**
   - 制定主生产计划
   - 分解到具体工序
   - 安排资源配置

4. **计划发布**
   - 审核计划合理性
   - 发布到各部门
   - 开始执行跟踪

## 项目交付

### 功能概述

项目交付模块管理企业的项目型业务，从项目立项到最终交付的全过程管理。

### 主要功能

#### 1. 项目管理

**项目信息**:
- 项目编号、项目名称
- 客户信息、合同金额
- 项目经理、团队成员
- 开始时间、计划完成时间
- 当前状态、完成进度

**项目状态**:
- **立项**: 项目已立项，准备启动
- **执行中**: 项目正在执行
- **已暂停**: 项目暂停
- **已完成**: 项目完成
- **已取消**: 项目取消

#### 2. 交付计划

**里程碑管理**:
```
项目: 某工厂自动化改造

里程碑1: 需求调研完成 (2024-02-15)
里程碑2: 方案设计完成 (2024-03-15)
里程碑3: 设备采购完成 (2024-04-15)
里程碑4: 现场安装完成 (2024-05-15)
里程碑5: 调试验收完成 (2024-06-15)
里程碑6: 项目交付完成 (2024-06-30)
```

#### 3. 客户管理

**客户信息**:
- 客户编码、客户名称
- 联系人、联系方式
- 客户类型、信用等级
- 历史项目记录

### 项目执行流程

#### 1. 项目启动

**操作步骤**:
1. 创建项目档案
2. 分配项目经理
3. 组建项目团队
4. 制定项目计划
5. 召开启动会议

#### 2. 项目执行

**日常管理**:
- 定期更新项目进度
- 跟踪里程碑完成情况
- 管理项目风险
- 协调资源配置

#### 3. 项目交付

**交付检查清单**:
- ☑️ 产品质量检验合格
- ☑️ 技术文档齐全
- ☑️ 用户培训完成
- ☑️ 客户验收通过
- ☑️ 项目资料归档

## 设备管理

### 功能概述

设备管理模块负责企业生产设备的全生命周期管理。

### 主要功能

#### 1. 设备台账

**设备信息**:
- 设备编码、设备名称
- 设备型号、制造商
- 购买日期、安装位置
- 技术参数、操作手册
- 当前状态、责任人

**设备状态**:
- **运行中**: 设备正常运行
- **待机**: 设备空闲待用
- **维修中**: 设备正在维修
- **故障**: 设备发生故障
- **报废**: 设备已报废

#### 2. 维护计划

**维护类型**:
- **日常保养**: 每日例行检查
- **定期维护**: 按周期进行维护
- **预防性维护**: 基于状态的维护
- **故障维修**: 故障后的修复

**维护计划示例**:
```
设备: 数控机床CNC001

日常保养 (每日):
- 检查润滑油位
- 清洁设备表面
- 检查安全装置

定期维护 (每月):
- 更换切削液
- 检查传动系统
- 校准精度

年度大修 (每年):
- 全面拆检
- 更换易损件
- 精度恢复
```

#### 3. 故障管理

**故障记录**:
- 故障时间、故障现象
- 故障原因、处理过程
- 维修人员、维修时间
- 更换配件、维修费用

### 设备编码规范

#### 编码结构

```
格式: [设备类型][车间][序号]

示例:
CNC-A-001  (数控机床-车间A-序号001)
MILL-B-002 (铣床-车间B-序号002)
WELD-C-003 (焊机-车间C-序号003)
```

## 系统设置

### 用户管理

#### 1. 用户账号管理

**用户信息**:
- 用户名、真实姓名
- 邮箱、手机号
- 部门、职位
- 账号状态、最后登录时间

**操作步骤**:
1. 访问"系统管理" → "用户管理"
2. 点击"新增用户"按钮
3. 填写用户基本信息
4. 分配角色和权限
5. 保存用户信息

#### 2. 角色权限管理

**预设角色**:
- **系统管理员**: 所有功能权限
- **BOM工程师**: BOM管理相关权限
- **物料管理员**: 物料管理相关权限
- **生产计划员**: 生产管理相关权限
- **项目经理**: 项目管理相关权限
- **设备管理员**: 设备管理相关权限
- **普通用户**: 基础查看权限

**权限配置**:
```
权限模块: BOM管理
├── bom:read    (查看BOM)
├── bom:write   (编辑BOM)
├── bom:delete  (删除BOM)
├── bom:export  (导出BOM)
└── bom:import  (导入BOM)
```

### 系统配置

#### 1. 基础配置

**系统参数**:
- 系统名称、版本信息
- 公司信息、Logo设置
- 时区、语言设置
- 数据保留策略

#### 2. 安全配置

**安全策略**:
- 密码复杂度要求
- 登录失败锁定策略
- 会话超时设置
- 操作日志记录

#### 3. 通知配置

**通知类型**:
- 系统通知、邮件通知
- 短信通知、微信通知
- 待办提醒、异常告警

## 常见问题

### 登录问题

**Q: 忘记密码怎么办？**
A: 点击登录页面的"忘记密码"链接，输入注册邮箱，系统会发送重置链接。

**Q: 账号被锁定怎么办？**
A: 连续输错密码5次会被锁定30分钟，可联系管理员解锁或等待自动解锁。

**Q: 为什么登录后看不到某些菜单？**
A: 这是权限控制的结果，请联系管理员分配相应权限。

### 操作问题

**Q: 如何批量导入BOM数据？**
A: 
1. 下载BOM导入模板
2. 按模板格式填写数据
3. 在BOM列表页面点击"导入"按钮
4. 选择填写好的Excel文件上传

**Q: 如何查看操作历史？**
A: 在相关记录的详情页面，点击"操作历史"标签页查看。

**Q: 数据导出失败怎么办？**
A: 
1. 检查网络连接是否正常
2. 确认导出数据量不超过限制
3. 联系技术支持获取帮助

### 性能问题

**Q: 系统响应慢怎么办？**
A: 
1. 检查网络连接速度
2. 清除浏览器缓存
3. 关闭不必要的浏览器标签页
4. 联系技术支持检查服务器状态

**Q: 大量数据查询很慢？**
A: 
1. 使用更精确的搜索条件
2. 缩小时间范围
3. 分批次查询数据

### 数据问题

**Q: 误删除数据如何恢复？**
A: 
1. 立即联系系统管理员
2. 提供删除的具体时间和数据信息
3. 管理员可从备份中恢复数据

**Q: 数据同步异常怎么办？**
A: 
1. 刷新页面重新加载数据
2. 检查网络连接状态
3. 联系技术支持检查数据同步服务

## 技术支持

### 联系方式

- **技术热线**: 400-123-4567
- **技术邮箱**: <EMAIL>
- **在线客服**: 系统右下角聊天窗口
- **工作时间**: 周一至周五 9:00-18:00

### 远程支持

如需远程技术支持，请：
1. 提前预约远程支持时间
2. 准备好问题描述和截图
3. 确保网络连接稳定
4. 配合技术人员进行问题排查

### 培训服务

我们提供以下培训服务：
- **新用户入门培训**: 2小时基础操作培训
- **管理员培训**: 4小时系统管理培训
- **高级功能培训**: 按需定制培训内容
- **在线培训**: 远程视频培训

### 版本更新

- **更新通知**: 系统会提前通知版本更新
- **更新时间**: 通常在非工作时间进行
- **数据备份**: 更新前会自动备份数据
- **回滚机制**: 如有问题可快速回滚到上一版本

### 意见反馈

我们重视您的意见和建议：
- **功能建议**: <EMAIL>
- **问题报告**: <EMAIL>
- **用户体验**: <EMAIL>

---

**文档版本**: v1.0.0  
**最后更新**: 2024-01-01  
**适用系统版本**: v1.0.0及以上

*本手册会随系统版本更新而更新，请关注最新版本。*