{"ast": null, "code": "import isArrayLike from './is-array-like';\nvar contains = function (arr, value) {\n  if (!isArrayLike(arr)) {\n    return false;\n  }\n  return arr.indexOf(value) > -1;\n};\nexport default contains;", "map": {"version": 3, "names": ["isArrayLike", "contains", "arr", "value", "indexOf"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\util\\src\\lodash\\contains.ts"], "sourcesContent": ["import isArrayLike from './is-array-like';\n\nconst contains = function (arr: any[], value: any): boolean {\n  if (!isArrayLike(arr)) {\n    return false;\n  }\n  return arr.indexOf(value) > -1;\n};\n\nexport default contains;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,iBAAiB;AAEzC,IAAMC,QAAQ,GAAG,SAAAA,CAAUC,GAAU,EAAEC,KAAU;EAC/C,IAAI,CAACH,WAAW,CAACE,GAAG,CAAC,EAAE;IACrB,OAAO,KAAK;EACd;EACA,OAAOA,GAAG,CAACE,OAAO,CAACD,KAAK,CAAC,GAAG,CAAC,CAAC;AAChC,CAAC;AAED,eAAeF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}