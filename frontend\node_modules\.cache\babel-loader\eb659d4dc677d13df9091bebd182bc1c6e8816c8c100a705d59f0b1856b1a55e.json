{"ast": null, "code": "import { Polar } from './polar';\nexport const getRadialOptions = (options = {}) => {\n  const defaultOptions = {\n    startAngle: -Math.PI / 2,\n    endAngle: Math.PI * 3 / 2,\n    innerRadius: 0,\n    outerRadius: 1\n  };\n  return Object.assign(Object.assign({}, defaultOptions), options);\n};\n/**\n * Radial\n */\nexport const Radial = options => {\n  const {\n    startAngle,\n    endAngle,\n    innerRadius,\n    outerRadius\n  } = getRadialOptions(options);\n  return [['transpose'], ['translate', 0.5, 0.5], ['reflect'], ['translate', -0.5, -0.5], ...Polar({\n    startAngle,\n    endAngle,\n    innerRadius,\n    outerRadius\n  })];\n};\nRadial.props = {};", "map": {"version": 3, "names": ["Polar", "getRadialOptions", "options", "defaultOptions", "startAngle", "Math", "PI", "endAngle", "innerRadius", "outerRadius", "Object", "assign", "Radial", "props"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\g2\\src\\coordinate\\radial.ts"], "sourcesContent": ["import { CoordinateComponent as CC } from '../runtime';\nimport { RadialCoordinate } from '../spec';\nimport { Polar } from './polar';\n\nexport type RadialOptions = Omit<RadialCoordinate, 'type'>;\n\nexport const getRadialOptions = (options: RadialOptions = {}) => {\n  const defaultOptions = {\n    startAngle: -Math.PI / 2,\n    endAngle: (Math.PI * 3) / 2,\n    innerRadius: 0,\n    outerRadius: 1,\n  };\n  return { ...defaultOptions, ...options };\n};\n\n/**\n * Radial\n */\nexport const Radial: CC<RadialOptions> = (options) => {\n  const { startAngle, endAngle, innerRadius, outerRadius } =\n    getRadialOptions(options);\n  return [\n    ['transpose'],\n    ['translate', 0.5, 0.5],\n    ['reflect'],\n    ['translate', -0.5, -0.5],\n    ...Polar({ startAngle, endAngle, innerRadius, outerRadius }),\n  ];\n};\n\nRadial.props = {};\n"], "mappings": "AAEA,SAASA,KAAK,QAAQ,SAAS;AAI/B,OAAO,MAAMC,gBAAgB,GAAGA,CAACC,OAAA,GAAyB,EAAE,KAAI;EAC9D,MAAMC,cAAc,GAAG;IACrBC,UAAU,EAAE,CAACC,IAAI,CAACC,EAAE,GAAG,CAAC;IACxBC,QAAQ,EAAGF,IAAI,CAACC,EAAE,GAAG,CAAC,GAAI,CAAC;IAC3BE,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE;GACd;EACD,OAAAC,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAYR,cAAc,GAAKD,OAAO;AACxC,CAAC;AAED;;;AAGA,OAAO,MAAMU,MAAM,GAAuBV,OAAO,IAAI;EACnD,MAAM;IAAEE,UAAU;IAAEG,QAAQ;IAAEC,WAAW;IAAEC;EAAW,CAAE,GACtDR,gBAAgB,CAACC,OAAO,CAAC;EAC3B,OAAO,CACL,CAAC,WAAW,CAAC,EACb,CAAC,WAAW,EAAE,GAAG,EAAE,GAAG,CAAC,EACvB,CAAC,SAAS,CAAC,EACX,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EACzB,GAAGF,KAAK,CAAC;IAAEI,UAAU;IAAEG,QAAQ;IAAEC,WAAW;IAAEC;EAAW,CAAE,CAAC,CAC7D;AACH,CAAC;AAEDG,MAAM,CAACC,KAAK,GAAG,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}