{"ast": null, "code": "// scales\nexport { Band } from './scales/band';\nexport { Ordinal } from './scales/ordinal';\nexport { Constant } from './scales/constant';\nexport { Identity } from './scales/identity';\nexport { Linear } from './scales/linear';\nexport { Point } from './scales/point';\nexport { Pow } from './scales/pow';\nexport { Sqrt } from './scales/sqrt';\nexport { Threshold } from './scales/threshold';\nexport { Log } from './scales/log';\nexport { Quantize } from './scales/quantize';\nexport { Quantile } from './scales/quantile';\nexport { Time } from './scales/time';\nexport { Base } from './scales/base';\nexport { Continuous } from './scales/continuous';\nexport { Sequential } from './scales/sequential';\nexport { Diverging } from './scales/diverging';\n// tick-methods\nexport { d3Ticks } from './tick-methods/d3-ticks';\nexport { rPretty } from './tick-methods/r-pretty';\nexport { wilkinsonExtended } from './tick-methods/wilkinson-extended';\nexport { d3Log } from './tick-methods/d3-log';\nexport { d3Time } from './tick-methods/d3-time';\n// constants\nexport { DURATION_SECOND, DURATION_MINUTE, DURATION_HOUR, DURATION_DAY, DURATION_WEEK, DURATION_YEAR, DURATION_MONTH } from './utils';\n// interpolators\nexport { createInterpolateNumber, createInterpolateValue, createInterpolateColor } from './utils';", "map": {"version": 3, "names": ["Band", "Ordinal", "Constant", "Identity", "Linear", "Point", "<PERSON>w", "Sqrt", "<PERSON><PERSON><PERSON><PERSON>", "Log", "Quantize", "Quantile", "Time", "Base", "Continuous", "Sequential", "Diverging", "d3Ticks", "<PERSON><PERSON><PERSON><PERSON>", "wilkinsonExtended", "d3Log", "d3Time", "DURATION_SECOND", "DURATION_MINUTE", "DURATION_HOUR", "DURATION_DAY", "DURATION_WEEK", "DURATION_YEAR", "DURATION_MONTH", "createInterpolateNumber", "createInterpolateValue", "createInterpolateColor"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\scale\\src\\index.ts"], "sourcesContent": ["// scales\nexport { Band } from './scales/band';\nexport { Ordinal } from './scales/ordinal';\nexport { Constant } from './scales/constant';\nexport { Identity } from './scales/identity';\nexport { Linear } from './scales/linear';\nexport { Point } from './scales/point';\nexport { Pow } from './scales/pow';\nexport { Sqrt } from './scales/sqrt';\nexport { Threshold } from './scales/threshold';\nexport { Log } from './scales/log';\nexport { Quantize } from './scales/quantize';\nexport { Quantile } from './scales/quantile';\nexport { Time } from './scales/time';\nexport { Base } from './scales/base';\nexport { Continuous } from './scales/continuous';\nexport { Sequential } from './scales/sequential';\nexport { Diverging } from './scales/diverging';\n\n// tick-methods\nexport { d3Ticks } from './tick-methods/d3-ticks';\nexport { rPretty } from './tick-methods/r-pretty';\nexport { wilkinsonExtended } from './tick-methods/wilkinson-extended';\nexport { d3Log } from './tick-methods/d3-log';\nexport { d3Time } from './tick-methods/d3-time';\n\n// scales types\nexport type {\n  BaseOptions,\n  BandOptions,\n  OrdinalOptions,\n  ContinuousOptions,\n  ConstantOptions,\n  IdentityOptions,\n  LinearOptions,\n  PointOptions,\n  PowOptions,\n  TimeOptions,\n  ThresholdOptions,\n  QuantizeOptions,\n  SqrtOptions,\n  QuantileOptions,\n  LogOptions,\n  SequentialOptions,\n  DivergingOptions,\n} from './types';\n\n// others\nexport type { TickMethod, Interpolate, Comparator, Interpolates, Interpolator } from './types';\n\n// constants\nexport {\n  DURATION_SECOND,\n  DURATION_MINUTE,\n  DURATION_HOUR,\n  DURATION_DAY,\n  DURATION_WEEK,\n  DURATION_YEAR,\n  DURATION_MONTH,\n} from './utils';\n\n// interpolators\nexport { createInterpolateNumber, createInterpolateValue, createInterpolateColor } from './utils';\n"], "mappings": "AAAA;AACA,SAASA,IAAI,QAAQ,eAAe;AACpC,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,GAAG,QAAQ,cAAc;AAClC,SAASC,IAAI,QAAQ,eAAe;AACpC,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,GAAG,QAAQ,cAAc;AAClC,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,IAAI,QAAQ,eAAe;AACpC,SAASC,IAAI,QAAQ,eAAe;AACpC,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,SAAS,QAAQ,oBAAoB;AAE9C;AACA,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,iBAAiB,QAAQ,mCAAmC;AACrE,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,MAAM,QAAQ,wBAAwB;AA0B/C;AACA,SACEC,eAAe,EACfC,eAAe,EACfC,aAAa,EACbC,YAAY,EACZC,aAAa,EACbC,aAAa,EACbC,cAAc,QACT,SAAS;AAEhB;AACA,SAASC,uBAAuB,EAAEC,sBAAsB,EAAEC,sBAAsB,QAAQ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}