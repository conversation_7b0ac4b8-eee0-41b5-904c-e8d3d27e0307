package com.yinma.constant;

/**
 * 系统常量
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
public class Constants {

    /**
     * 系统相关常量
     */
    public static class System {
        /** 系统名称 */
        public static final String SYSTEM_NAME = "YinMa";
        
        /** 默认密码 */
        public static final String DEFAULT_PASSWORD = "123456";
        
        /** 超级管理员角色编码 */
        public static final String SUPER_ADMIN_ROLE = "SUPER_ADMIN";
        
        /** 管理员角色编码 */
        public static final String ADMIN_ROLE = "ADMIN";
        
        /** 普通用户角色编码 */
        public static final String USER_ROLE = "USER";
    }

    /**
     * JWT相关常量
     */
    public static class Jwt {
        /** JWT密钥 */
        public static final String SECRET = "YinMaSystemJwtSecretKey2024";
        
        /** 令牌前缀 */
        public static final String TOKEN_PREFIX = "Bearer ";
        
        /** 请求头名称 */
        public static final String HEADER_NAME = "Authorization";
        
        /** 用户ID声明 */
        public static final String USER_ID_CLAIM = "userId";
        
        /** 用户名声明 */
        public static final String USERNAME_CLAIM = "username";
        
        /** 权限声明 */
        public static final String AUTHORITIES_CLAIM = "authorities";
        
        /** 令牌类型声明 */
        public static final String TOKEN_TYPE_CLAIM = "tokenType";
        
        /** 访问令牌类型 */
        public static final String ACCESS_TOKEN_TYPE = "access";
        
        /** 刷新令牌类型 */
        public static final String REFRESH_TOKEN_TYPE = "refresh";
    }

    /**
     * Redis相关常量
     */
    public static class Redis {
        /** 用户信息缓存前缀 */
        public static final String USER_INFO_PREFIX = "user:info:";
        
        /** 用户权限缓存前缀 */
        public static final String USER_PERMISSIONS_PREFIX = "user:permissions:";
        
        /** 登录失败次数前缀 */
        public static final String LOGIN_FAIL_COUNT_PREFIX = "login:fail:count:";
        
        /** 账户锁定前缀 */
        public static final String ACCOUNT_LOCK_PREFIX = "account:lock:";
        
        /** 验证码前缀 */
        public static final String CAPTCHA_PREFIX = "captcha:";
        
        /** 刷新令牌前缀 */
        public static final String REFRESH_TOKEN_PREFIX = "refresh:token:";
        
        /** 在线用户前缀 */
        public static final String ONLINE_USER_PREFIX = "online:user:";
    }

    /**
     * 状态相关常量
     */
    public static class Status {
        /** 启用状态 */
        public static final Integer ENABLED = 1;
        
        /** 禁用状态 */
        public static final Integer DISABLED = 0;
        
        /** 删除状态 */
        public static final Integer DELETED = 1;
        
        /** 未删除状态 */
        public static final Integer NOT_DELETED = 0;
    }

    /**
     * 性别常量
     */
    public static class Gender {
        /** 男性 */
        public static final String MALE = "M";
        
        /** 女性 */
        public static final String FEMALE = "F";
        
        /** 未知 */
        public static final String UNKNOWN = "U";
    }

    /**
     * 操作类型常量
     */
    public static class OperationType {
        /** 查询 */
        public static final String SELECT = "SELECT";
        
        /** 新增 */
        public static final String INSERT = "INSERT";
        
        /** 更新 */
        public static final String UPDATE = "UPDATE";
        
        /** 删除 */
        public static final String DELETE = "DELETE";
        
        /** 登录 */
        public static final String LOGIN = "LOGIN";
        
        /** 登出 */
        public static final String LOGOUT = "LOGOUT";
        
        /** 导入 */
        public static final String IMPORT = "IMPORT";
        
        /** 导出 */
        public static final String EXPORT = "EXPORT";
    }

    /**
     * 文件相关常量
     */
    public static class File {
        /** 默认上传路径 */
        public static final String DEFAULT_UPLOAD_PATH = "./uploads";
        
        /** 图片文件类型 */
        public static final String[] IMAGE_TYPES = {"jpg", "jpeg", "png", "gif", "bmp", "webp"};
        
        /** 文档文件类型 */
        public static final String[] DOCUMENT_TYPES = {"pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt"};
        
        /** 最大文件大小（10MB） */
        public static final long MAX_FILE_SIZE = 10 * 1024 * 1024;
    }

    /**
     * 分页相关常量
     */
    public static class Page {
        /** 默认页码 */
        public static final int DEFAULT_PAGE_NUM = 1;
        
        /** 默认页大小 */
        public static final int DEFAULT_PAGE_SIZE = 10;
        
        /** 最大页大小 */
        public static final int MAX_PAGE_SIZE = 1000;
    }

    /**
     * 日期格式常量
     */
    public static class DateFormat {
        /** 标准日期时间格式 */
        public static final String STANDARD_DATETIME = "yyyy-MM-dd HH:mm:ss";
        
        /** 标准日期格式 */
        public static final String STANDARD_DATE = "yyyy-MM-dd";
        
        /** 标准时间格式 */
        public static final String STANDARD_TIME = "HH:mm:ss";
        
        /** 紧凑日期时间格式 */
        public static final String COMPACT_DATETIME = "yyyyMMddHHmmss";
        
        /** 紧凑日期格式 */
        public static final String COMPACT_DATE = "yyyyMMdd";
    }
}