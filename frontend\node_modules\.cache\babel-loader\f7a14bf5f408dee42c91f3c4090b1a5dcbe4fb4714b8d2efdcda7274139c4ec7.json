{"ast": null, "code": "import { deepMix } from '@antv/util';\nimport { column, columnOf } from './utils/helper';\nimport { rangeOf, interpolate } from './jitter';\n/**\n * The JitterY transform produce dy channels for marks (especially for point)\n * with ordinal x and y dimension, say to make them jitter in their own space.\n */\nexport const JitterY = (options = {}) => {\n  const {\n    padding = 0,\n    random = Math.random\n  } = options;\n  return (I, mark) => {\n    const {\n      encode,\n      scale\n    } = mark;\n    const {\n      y: scaleY\n    } = scale;\n    const [Y] = columnOf(encode, 'y');\n    const rangeY = rangeOf(Y, scaleY, padding);\n    const DY = I.map(() => interpolate(random(), ...rangeY));\n    return [I, deepMix({\n      scale: {\n        y: {\n          padding: 0.5\n        }\n      }\n    }, mark, {\n      encode: {\n        dy: column(DY)\n      }\n    })];\n  };\n};\nJitterY.props = {};", "map": {"version": 3, "names": ["deepMix", "column", "columnOf", "rangeOf", "interpolate", "JitterY", "options", "padding", "random", "Math", "I", "mark", "encode", "scale", "y", "scaleY", "Y", "rangeY", "DY", "map", "dy", "props"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\g2\\src\\transform\\jitterY.ts"], "sourcesContent": ["import { deepMix } from '@antv/util';\nimport { TransformComponent as TC } from '../runtime';\nimport { JitterYTransform } from '../spec';\nimport { column, columnOf } from './utils/helper';\nimport { rangeOf, interpolate } from './jitter';\n\nexport type JitterYOptions = Omit<JitterYTransform, 'type'>;\n\n/**\n * The JitterY transform produce dy channels for marks (especially for point)\n * with ordinal x and y dimension, say to make them jitter in their own space.\n */\nexport const JitterY: TC<JitterYOptions> = (options = {}) => {\n  const { padding = 0, random = Math.random } = options;\n  return (I, mark) => {\n    const { encode, scale } = mark;\n    const { y: scaleY } = scale;\n    const [Y] = columnOf(encode, 'y');\n    const rangeY = rangeOf(Y, scaleY, padding);\n    const DY = I.map(() => interpolate(random(), ...rangeY));\n    return [\n      I,\n      deepMix({ scale: { y: { padding: 0.5 } } }, mark, {\n        encode: { dy: column(DY) },\n      }),\n    ];\n  };\n};\n\nJitterY.props = {};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AAGpC,SAASC,MAAM,EAAEC,QAAQ,QAAQ,gBAAgB;AACjD,SAASC,OAAO,EAAEC,WAAW,QAAQ,UAAU;AAI/C;;;;AAIA,OAAO,MAAMC,OAAO,GAAuBA,CAACC,OAAO,GAAG,EAAE,KAAI;EAC1D,MAAM;IAAEC,OAAO,GAAG,CAAC;IAAEC,MAAM,GAAGC,IAAI,CAACD;EAAM,CAAE,GAAGF,OAAO;EACrD,OAAO,CAACI,CAAC,EAAEC,IAAI,KAAI;IACjB,MAAM;MAAEC,MAAM;MAAEC;IAAK,CAAE,GAAGF,IAAI;IAC9B,MAAM;MAAEG,CAAC,EAAEC;IAAM,CAAE,GAAGF,KAAK;IAC3B,MAAM,CAACG,CAAC,CAAC,GAAGd,QAAQ,CAACU,MAAM,EAAE,GAAG,CAAC;IACjC,MAAMK,MAAM,GAAGd,OAAO,CAACa,CAAC,EAAED,MAAM,EAAER,OAAO,CAAC;IAC1C,MAAMW,EAAE,GAAGR,CAAC,CAACS,GAAG,CAAC,MAAMf,WAAW,CAACI,MAAM,EAAE,EAAE,GAAGS,MAAM,CAAC,CAAC;IACxD,OAAO,CACLP,CAAC,EACDV,OAAO,CAAC;MAAEa,KAAK,EAAE;QAAEC,CAAC,EAAE;UAAEP,OAAO,EAAE;QAAG;MAAE;IAAE,CAAE,EAAEI,IAAI,EAAE;MAChDC,MAAM,EAAE;QAAEQ,EAAE,EAAEnB,MAAM,CAACiB,EAAE;MAAC;KACzB,CAAC,CACH;EACH,CAAC;AACH,CAAC;AAEDb,OAAO,CAACgB,KAAK,GAAG,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}