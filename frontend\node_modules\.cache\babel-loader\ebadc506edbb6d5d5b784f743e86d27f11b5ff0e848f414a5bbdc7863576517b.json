{"ast": null, "code": "function shuffle(arr) {\n  const result = arr.slice();\n  for (let i = result.length - 1; i >= 1; i--) {\n    const j = Math.floor(Math.random() * (i + 1));\n    [result[i], result[j]] = [result[j], result[i]];\n  }\n  return result;\n}\nexport { shuffle };", "map": {"version": 3, "names": ["shuffle", "arr", "result", "slice", "i", "length", "j", "Math", "floor", "random"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/es-toolkit/dist/array/shuffle.mjs"], "sourcesContent": ["function shuffle(arr) {\n    const result = arr.slice();\n    for (let i = result.length - 1; i >= 1; i--) {\n        const j = Math.floor(Math.random() * (i + 1));\n        [result[i], result[j]] = [result[j], result[i]];\n    }\n    return result;\n}\n\nexport { shuffle };\n"], "mappings": "AAAA,SAASA,OAAOA,CAACC,GAAG,EAAE;EAClB,MAAMC,MAAM,GAAGD,GAAG,CAACE,KAAK,CAAC,CAAC;EAC1B,KAAK,IAAIC,CAAC,GAAGF,MAAM,CAACG,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACzC,MAAME,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,IAAIL,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7C,CAACF,MAAM,CAACE,CAAC,CAAC,EAAEF,MAAM,CAACI,CAAC,CAAC,CAAC,GAAG,CAACJ,MAAM,CAACI,CAAC,CAAC,EAAEJ,MAAM,CAACE,CAAC,CAAC,CAAC;EACnD;EACA,OAAOF,MAAM;AACjB;AAEA,SAASF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}