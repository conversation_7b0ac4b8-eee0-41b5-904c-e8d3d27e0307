{"ast": null, "code": "export function traverseElements(element, visitor) {\n  if (visitor(element)) return true;\n  if (element.tagName === 'g') {\n    const {\n      childNodes = []\n    } = element;\n    for (const child of childNodes) {\n      if (traverseElements(child, visitor)) return true;\n    }\n  }\n  return false;\n}", "map": {"version": 3, "names": ["traverseElements", "element", "visitor", "tagName", "childNodes", "child"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\g2\\src\\utils\\traverse-elements.ts"], "sourcesContent": ["import { DisplayObject } from '@antv/g';\n\nexport function traverseElements(\n  element: DisplayObject,\n  visitor: (el: DisplayObject) => boolean | void,\n): boolean {\n  if (visitor(element)) return true;\n  if (element.tagName === 'g') {\n    const { childNodes = [] } = element;\n    for (const child of childNodes) {\n      if (traverseElements(child as DisplayObject, visitor)) return true;\n    }\n  }\n\n  return false;\n}\n"], "mappings": "AAEA,OAAM,SAAUA,gBAAgBA,CAC9BC,OAAsB,EACtBC,OAA8C;EAE9C,IAAIA,OAAO,CAACD,OAAO,CAAC,EAAE,OAAO,IAAI;EACjC,IAAIA,OAAO,CAACE,OAAO,KAAK,GAAG,EAAE;IAC3B,MAAM;MAAEC,UAAU,GAAG;IAAE,CAAE,GAAGH,OAAO;IACnC,KAAK,MAAMI,KAAK,IAAID,UAAU,EAAE;MAC9B,IAAIJ,gBAAgB,CAACK,KAAsB,EAAEH,OAAO,CAAC,EAAE,OAAO,IAAI;;;EAItE,OAAO,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}