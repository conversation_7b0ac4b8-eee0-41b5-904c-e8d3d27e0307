{"ast": null, "code": "/**\n * 计算二维向量的垂直向量\n * @param out\n * @param v\n * @param flag\n */\nexport function vertical(out, v, flag) {\n  if (flag) {\n    out[0] = v[1];\n    out[1] = -1 * v[0];\n  } else {\n    out[0] = -1 * v[1];\n    out[1] = v[0];\n  }\n  return out;\n}", "map": {"version": 3, "names": ["vertical", "out", "v", "flag"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\util\\src\\matrix\\vertical.ts"], "sourcesContent": ["/**\n * 计算二维向量的垂直向量\n * @param out\n * @param v\n * @param flag\n */\nexport function vertical(out: number[], v: number[], flag: boolean): number[] {\n  if (flag) {\n    out[0] = v[1];\n    out[1] = -1 * v[0];\n  } else {\n    out[0] = -1 * v[1];\n    out[1] = v[0];\n  }\n\n  return out;\n}\n"], "mappings": "AAAA;;;;;;AAMA,OAAM,SAAUA,QAAQA,CAACC,GAAa,EAAEC,CAAW,EAAEC,IAAa;EAChE,IAAIA,IAAI,EAAE;IACRF,GAAG,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC;IACbD,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC,MAAM;IACLD,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC;IAClBD,GAAG,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC;EACf;EAEA,OAAOD,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}