package com.yinma.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinma.dto.RoleDTO;
import com.yinma.entity.RoleEntity;
import com.yinma.mapper.RoleMapper;
import com.yinma.mapper.RolePermissionMapper;
import com.yinma.mapper.UserRoleMapper;
import com.yinma.service.RoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色信息Service实现类
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RoleServiceImpl extends ServiceImpl<RoleMapper, RoleEntity> implements RoleService {

    private final RoleMapper roleMapper;
    private final UserRoleMapper userRoleMapper;
    private final RolePermissionMapper rolePermissionMapper;

    @Override
    public IPage<RoleDTO> selectRolePage(RoleDTO.RoleQueryDTO queryDTO) {
        Page<RoleEntity> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        IPage<RoleEntity> entityPage = roleMapper.selectRolePage(page, queryDTO);
        
        IPage<RoleDTO> dtoPage = new Page<>();
        BeanUtils.copyProperties(entityPage, dtoPage);
        
        List<RoleDTO> dtoList = entityPage.getRecords().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        dtoPage.setRecords(dtoList);
        
        return dtoPage;
    }

    @Override
    @Cacheable(value = "role", key = "#roleId")
    public RoleDTO selectRoleById(Long roleId) {
        RoleEntity entity = roleMapper.selectById(roleId);
        return entity != null ? convertToDTO(entity) : null;
    }

    @Override
    @Cacheable(value = "role", key = "'code:' + #roleCode")
    public RoleDTO selectRoleByCode(String roleCode) {
        RoleEntity entity = roleMapper.selectRoleByCode(roleCode);
        return entity != null ? convertToDTO(entity) : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "role", allEntries = true)
    public Long createRole(RoleDTO.RoleCreateDTO createDTO) {
        // 检查角色编码是否存在
        if (checkRoleCodeExists(createDTO.getRoleCode(), null)) {
            throw new RuntimeException("角色编码已存在: " + createDTO.getRoleCode());
        }
        
        // 检查角色名称是否存在
        if (checkRoleNameExists(createDTO.getRoleName(), null)) {
            throw new RuntimeException("角色名称已存在: " + createDTO.getRoleName());
        }
        
        RoleEntity entity = new RoleEntity();
        BeanUtils.copyProperties(createDTO, entity);
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        
        roleMapper.insert(entity);
        
        log.info("创建角色成功, roleId: {}, roleCode: {}", entity.getRoleId(), entity.getRoleCode());
        return entity.getRoleId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "role", allEntries = true)
    public Boolean updateRole(RoleDTO.RoleUpdateDTO updateDTO) {
        RoleEntity existingEntity = roleMapper.selectById(updateDTO.getRoleId());
        if (existingEntity == null) {
            throw new RuntimeException("角色不存在");
        }
        
        // 检查角色编码是否存在（排除自己）
        if (checkRoleCodeExists(updateDTO.getRoleCode(), updateDTO.getRoleId())) {
            throw new RuntimeException("角色编码已存在: " + updateDTO.getRoleCode());
        }
        
        // 检查角色名称是否存在（排除自己）
        if (checkRoleNameExists(updateDTO.getRoleName(), updateDTO.getRoleId())) {
            throw new RuntimeException("角色名称已存在: " + updateDTO.getRoleName());
        }
        
        RoleEntity entity = new RoleEntity();
        BeanUtils.copyProperties(updateDTO, entity);
        entity.setUpdateTime(LocalDateTime.now());
        
        int result = roleMapper.updateById(entity);
        
        log.info("更新角色成功, roleId: {}, roleCode: {}", entity.getRoleId(), entity.getRoleCode());
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "role", allEntries = true)
    public Boolean deleteRole(Long roleId) {
        RoleEntity entity = roleMapper.selectById(roleId);
        if (entity == null) {
            throw new RuntimeException("角色不存在");
        }
        
        // 检查是否有用户关联此角色
        List<Long> userIds = userRoleMapper.selectUserIdsByRoleId(roleId);
        if (!CollectionUtils.isEmpty(userIds)) {
            throw new RuntimeException("角色下存在用户，无法删除");
        }
        
        // 删除角色权限关联
        rolePermissionMapper.deleteByRoleId(roleId);
        
        // 删除角色
        int result = roleMapper.deleteById(roleId);
        
        log.info("删除角色成功, roleId: {}, roleCode: {}", roleId, entity.getRoleCode());
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "role", allEntries = true)
    public Boolean batchDeleteRoles(List<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return false;
        }
        
        for (Long roleId : roleIds) {
            deleteRole(roleId);
        }
        
        log.info("批量删除角色成功, roleIds: {}", roleIds);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "role", allEntries = true)
    public Boolean enableRole(Long roleId) {
        return updateRoleStatus(roleId, 1);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "role", allEntries = true)
    public Boolean disableRole(Long roleId) {
        return updateRoleStatus(roleId, 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "role", allEntries = true)
    public Boolean batchUpdateRoleStatus(List<Long> roleIds, Integer status) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return false;
        }
        
        int result = roleMapper.batchUpdateRoleStatus(roleIds, status);
        
        log.info("批量更新角色状态成功, roleIds: {}, status: {}", roleIds, status);
        return result > 0;
    }

    @Override
    public Boolean checkRoleCodeExists(String roleCode, Long excludeRoleId) {
        return roleMapper.checkRoleCodeExists(roleCode, excludeRoleId) > 0;
    }

    @Override
    public Boolean checkRoleNameExists(String roleName, Long excludeRoleId) {
        return roleMapper.checkRoleNameExists(roleName, excludeRoleId) > 0;
    }

    @Override
    public List<Long> selectRoleUsers(Long roleId) {
        return userRoleMapper.selectUserIdsByRoleId(roleId);
    }

    @Override
    public List<Long> selectRolePermissions(Long roleId) {
        return rolePermissionMapper.selectPermissionIdsByRoleId(roleId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "role", allEntries = true)
    public Boolean assignRolePermissions(Long roleId, List<Long> permissionIds) {
        if (CollectionUtils.isEmpty(permissionIds)) {
            return false;
        }
        
        // 先删除原有权限关联
        rolePermissionMapper.deleteByRoleId(roleId);
        
        // 批量插入新的权限关联
        int result = rolePermissionMapper.batchInsertRolePermissions(roleId, permissionIds);
        
        log.info("分配角色权限成功, roleId: {}, permissionIds: {}", roleId, permissionIds);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "role", allEntries = true)
    public Boolean removeRolePermissions(Long roleId, List<Long> permissionIds) {
        if (CollectionUtils.isEmpty(permissionIds)) {
            return false;
        }
        
        int result = rolePermissionMapper.batchDeleteRolePermissions(roleId, permissionIds);
        
        log.info("移除角色权限成功, roleId: {}, permissionIds: {}", roleId, permissionIds);
        return result > 0;
    }

    @Override
    @Cacheable(value = "role", key = "'enabled'")
    public List<RoleDTO> selectEnabledRoles() {
        List<RoleEntity> entities = roleMapper.selectEnabledRoles();
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "role", key = "'system'")
    public List<RoleDTO> selectSystemRoles() {
        List<RoleEntity> entities = roleMapper.selectSystemRoles();
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public RoleDTO.RoleStatisticsDTO selectRoleStatistics() {
        return roleMapper.selectRoleStatistics();
    }

    @Override
    public List<RoleDTO> exportRoles(RoleDTO.RoleQueryDTO queryDTO) {
        List<RoleEntity> entities = roleMapper.selectRoleList(queryDTO);
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "role", allEntries = true)
    public RoleDTO.ImportResultDTO importRoles(List<RoleDTO.RoleImportDTO> roles) {
        RoleDTO.ImportResultDTO result = new RoleDTO.ImportResultDTO();
        result.setTotalCount(roles.size());
        
        int successCount = 0;
        int failureCount = 0;
        
        for (RoleDTO.RoleImportDTO importDTO : roles) {
            try {
                // 检查角色编码是否存在
                if (checkRoleCodeExists(importDTO.getRoleCode(), null)) {
                    failureCount++;
                    continue;
                }
                
                RoleEntity entity = new RoleEntity();
                BeanUtils.copyProperties(importDTO, entity);
                entity.setCreateTime(LocalDateTime.now());
                entity.setUpdateTime(LocalDateTime.now());
                
                roleMapper.insert(entity);
                successCount++;
                
            } catch (Exception e) {
                log.error("导入角色失败, roleCode: {}", importDTO.getRoleCode(), e);
                failureCount++;
            }
        }
        
        result.setSuccessCount(successCount);
        result.setFailureCount(failureCount);
        
        log.info("导入角色完成, 总数: {}, 成功: {}, 失败: {}", result.getTotalCount(), successCount, failureCount);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "role", allEntries = true)
    public Long copyRole(Long sourceRoleId, String newRoleName, String newRoleCode) {
        RoleEntity sourceRole = roleMapper.selectById(sourceRoleId);
        if (sourceRole == null) {
            throw new RuntimeException("源角色不存在");
        }
        
        // 检查新角色编码是否存在
        if (checkRoleCodeExists(newRoleCode, null)) {
            throw new RuntimeException("角色编码已存在: " + newRoleCode);
        }
        
        // 检查新角色名称是否存在
        if (checkRoleNameExists(newRoleName, null)) {
            throw new RuntimeException("角色名称已存在: " + newRoleName);
        }
        
        // 创建新角色
        RoleEntity newRole = new RoleEntity();
        BeanUtils.copyProperties(sourceRole, newRole);
        newRole.setRoleId(null);
        newRole.setRoleCode(newRoleCode);
        newRole.setRoleName(newRoleName);
        newRole.setCreateTime(LocalDateTime.now());
        newRole.setUpdateTime(LocalDateTime.now());
        
        roleMapper.insert(newRole);
        
        // 复制权限关联
        List<Long> permissionIds = rolePermissionMapper.selectPermissionIdsByRoleId(sourceRoleId);
        if (!CollectionUtils.isEmpty(permissionIds)) {
            rolePermissionMapper.batchInsertRolePermissions(newRole.getRoleId(), permissionIds);
        }
        
        log.info("复制角色成功, sourceRoleId: {}, newRoleId: {}, newRoleCode: {}", 
                sourceRoleId, newRole.getRoleId(), newRoleCode);
        return newRole.getRoleId();
    }

    @Override
    @CacheEvict(value = "role", allEntries = true)
    public Boolean syncRoleCache() {
        // TODO: 实现角色缓存同步逻辑
        log.info("同步角色缓存成功");
        return true;
    }

    /**
     * 更新角色状态
     */
    private Boolean updateRoleStatus(Long roleId, Integer status) {
        RoleEntity entity = roleMapper.selectById(roleId);
        if (entity == null) {
            throw new RuntimeException("角色不存在");
        }
        
        entity.setStatus(status);
        entity.setUpdateTime(LocalDateTime.now());
        
        int result = roleMapper.updateById(entity);
        
        log.info("更新角色状态成功, roleId: {}, status: {}", roleId, status);
        return result > 0;
    }

    /**
     * 实体转DTO
     */
    private RoleDTO convertToDTO(RoleEntity entity) {
        if (entity == null) {
            return null;
        }
        
        RoleDTO dto = new RoleDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}