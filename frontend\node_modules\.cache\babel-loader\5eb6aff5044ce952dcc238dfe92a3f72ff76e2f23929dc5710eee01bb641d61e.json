{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-YinMa\\\\frontend\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { ConfigProvider } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport { Provider } from 'react-redux';\nimport { store } from './store';\nimport Layout from './components/Layout';\nimport Login from './pages/Login';\nimport Dashboard from './pages/Dashboard';\nimport BomManagement from './pages/BomManagement';\nimport BomDetailManagement from './pages/BomDetailManagement';\nimport BomChangeLogManagement from './pages/BomChangeLogManagement';\nimport MaterialManagement from './pages/MaterialManagement';\nimport Manufacturing from './pages/Manufacturing';\nimport ProjectDelivery from './pages/ProjectDelivery';\nimport SmartService from './pages/SmartService';\nimport FinancialControl from './pages/FinancialControl';\nimport CollaborativeDecision from './pages/CollaborativeDecision';\nimport ErrorBoundary from './components/ErrorBoundary';\nimport { LoadingProvider } from './components/GlobalLoading';\nimport './App.css';\n\n/**\n * 西安银马实业数字化管理系统主应用组件\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Provider, {\n    store: store,\n    children: /*#__PURE__*/_jsxDEV(ConfigProvider, {\n      locale: zhCN,\n      children: /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n        children: /*#__PURE__*/_jsxDEV(LoadingProvider, {\n          children: /*#__PURE__*/_jsxDEV(Router, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"App\",\n              children: /*#__PURE__*/_jsxDEV(Routes, {\n                children: [/*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/login\",\n                  element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 36,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 36,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/\",\n                  element: /*#__PURE__*/_jsxDEV(Layout, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 39,\n                    columnNumber: 40\n                  }, this),\n                  children: [/*#__PURE__*/_jsxDEV(Route, {\n                    index: true,\n                    element: /*#__PURE__*/_jsxDEV(Navigate, {\n                      to: \"/dashboard\",\n                      replace: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 41,\n                      columnNumber: 39\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 41,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"dashboard\",\n                    element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 44,\n                      columnNumber: 50\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 44,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"bom\",\n                    element: /*#__PURE__*/_jsxDEV(BomManagement, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 47,\n                      columnNumber: 44\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 47,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"bom-detail\",\n                    element: /*#__PURE__*/_jsxDEV(BomDetailManagement, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 50,\n                      columnNumber: 51\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 50,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"bom-change-log\",\n                    element: /*#__PURE__*/_jsxDEV(BomChangeLogManagement, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 53,\n                      columnNumber: 55\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 53,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"material\",\n                    element: /*#__PURE__*/_jsxDEV(MaterialManagement, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 56,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 56,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"manufacturing/*\",\n                    element: /*#__PURE__*/_jsxDEV(Manufacturing, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 59,\n                      columnNumber: 56\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 59,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"project/*\",\n                    element: /*#__PURE__*/_jsxDEV(ProjectDelivery, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 62,\n                      columnNumber: 50\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 62,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"service/*\",\n                    element: /*#__PURE__*/_jsxDEV(SmartService, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 65,\n                      columnNumber: 50\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 65,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"financial\",\n                    element: /*#__PURE__*/_jsxDEV(FinancialControl, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 68,\n                      columnNumber: 50\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 68,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"decision\",\n                    element: /*#__PURE__*/_jsxDEV(CollaborativeDecision, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 71,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 71,\n                    columnNumber: 17\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 39,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"*\",\n                  element: /*#__PURE__*/_jsxDEV(Navigate, {\n                    to: \"/dashboard\",\n                    replace: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 75,\n                    columnNumber: 40\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 75,\n                  columnNumber: 15\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 34,\n                columnNumber: 13\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zhCN", "Provider", "store", "Layout", "<PERSON><PERSON>", "Dashboard", "BomManagement", "BomDetailManagement", "BomChangeLogManagement", "MaterialManagement", "Manufacturing", "ProjectDelivery", "SmartService", "FinancialControl", "CollaborativeDecision", "Error<PERSON>ou<PERSON><PERSON>", "LoadingProvider", "jsxDEV", "_jsxDEV", "App", "children", "locale", "className", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "index", "to", "replace", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-YinMa/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { ConfigProvider } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport { Provider } from 'react-redux';\nimport { store } from './store';\nimport Layout from './components/Layout';\nimport Login from './pages/Login';\nimport Dashboard from './pages/Dashboard';\nimport BomManagement from './pages/BomManagement';\nimport BomDetailManagement from './pages/BomDetailManagement';\nimport BomChangeLogManagement from './pages/BomChangeLogManagement';\nimport MaterialManagement from './pages/MaterialManagement';\nimport Manufacturing from './pages/Manufacturing';\nimport ProjectDelivery from './pages/ProjectDelivery';\nimport SmartService from './pages/SmartService';\nimport FinancialControl from './pages/FinancialControl';\nimport CollaborativeDecision from './pages/CollaborativeDecision';\nimport ErrorBoundary from './components/ErrorBoundary';\nimport { LoadingProvider } from './components/GlobalLoading';\nimport './App.css';\n\n/**\n * 西安银马实业数字化管理系统主应用组件\n */\nfunction App() {\n  return (\n    <Provider store={store}>\n      <ConfigProvider locale={zhCN}>\n        <ErrorBoundary>\n          <LoadingProvider>\n            <Router>\n              <div className=\"App\">\n            <Routes>\n              {/* 登录页面 */}\n              <Route path=\"/login\" element={<Login />} />\n              \n              {/* 主应用布局 */}\n              <Route path=\"/\" element={<Layout />}>\n                {/* 默认重定向到仪表盘 */}\n                <Route index element={<Navigate to=\"/dashboard\" replace />} />\n\n                {/* 仪表盘 */}\n                <Route path=\"dashboard\" element={<Dashboard />} />\n\n                {/* BOM管理中心 */}\n                <Route path=\"bom\" element={<BomManagement />} />\n\n                {/* BOM明细管理 */}\n                <Route path=\"bom-detail\" element={<BomDetailManagement />} />\n\n                {/* BOM变更日志管理 */}\n                <Route path=\"bom-change-log\" element={<BomChangeLogManagement />} />\n\n                {/* 物料主数据管理 */}\n                <Route path=\"material\" element={<MaterialManagement />} />\n\n                {/* 智能制造模块 */}\n                <Route path=\"manufacturing/*\" element={<Manufacturing />} />\n\n                {/* 项目交付平台 */}\n                <Route path=\"project/*\" element={<ProjectDelivery />} />\n\n                {/* 智慧服务系统 */}\n                <Route path=\"service/*\" element={<SmartService />} />\n\n                {/* 财务管控中心 */}\n                <Route path=\"financial\" element={<FinancialControl />} />\n\n                {/* 协同决策平台 */}\n                <Route path=\"decision\" element={<CollaborativeDecision />} />\n              </Route>\n              \n              {/* 404页面 */}\n              <Route path=\"*\" element={<Navigate to=\"/dashboard\" replace />} />\n            </Routes>\n              </div>\n            </Router>\n          </LoadingProvider>\n        </ErrorBoundary>\n      </ConfigProvider>\n    </Provider>\n  );\n}\n\nexport default App;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,cAAc,QAAQ,MAAM;AACrC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,KAAK,QAAQ,SAAS;AAC/B,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,mBAAmB,MAAM,6BAA6B;AAC7D,OAAOC,sBAAsB,MAAM,gCAAgC;AACnE,OAAOC,kBAAkB,MAAM,4BAA4B;AAC3D,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,qBAAqB,MAAM,+BAA+B;AACjE,OAAOC,aAAa,MAAM,4BAA4B;AACtD,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,OAAO,WAAW;;AAElB;AACA;AACA;AAFA,SAAAC,MAAA,IAAAC,OAAA;AAGA,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACjB,QAAQ;IAACC,KAAK,EAAEA,KAAM;IAAAkB,QAAA,eACrBF,OAAA,CAACnB,cAAc;MAACsB,MAAM,EAAErB,IAAK;MAAAoB,QAAA,eAC3BF,OAAA,CAACH,aAAa;QAAAK,QAAA,eACZF,OAAA,CAACF,eAAe;UAAAI,QAAA,eACdF,OAAA,CAACvB,MAAM;YAAAyB,QAAA,eACLF,OAAA;cAAKI,SAAS,EAAC,KAAK;cAAAF,QAAA,eACtBF,OAAA,CAACtB,MAAM;gBAAAwB,QAAA,gBAELF,OAAA,CAACrB,KAAK;kBAAC0B,IAAI,EAAC,QAAQ;kBAACC,OAAO,eAAEN,OAAA,CAACd,KAAK;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAG3CV,OAAA,CAACrB,KAAK;kBAAC0B,IAAI,EAAC,GAAG;kBAACC,OAAO,eAAEN,OAAA,CAACf,MAAM;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAR,QAAA,gBAElCF,OAAA,CAACrB,KAAK;oBAACgC,KAAK;oBAACL,OAAO,eAAEN,OAAA,CAACpB,QAAQ;sBAACgC,EAAE,EAAC,YAAY;sBAACC,OAAO;oBAAA;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAG9DV,OAAA,CAACrB,KAAK;oBAAC0B,IAAI,EAAC,WAAW;oBAACC,OAAO,eAAEN,OAAA,CAACb,SAAS;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAGlDV,OAAA,CAACrB,KAAK;oBAAC0B,IAAI,EAAC,KAAK;oBAACC,OAAO,eAAEN,OAAA,CAACZ,aAAa;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAGhDV,OAAA,CAACrB,KAAK;oBAAC0B,IAAI,EAAC,YAAY;oBAACC,OAAO,eAAEN,OAAA,CAACX,mBAAmB;sBAAAkB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAG7DV,OAAA,CAACrB,KAAK;oBAAC0B,IAAI,EAAC,gBAAgB;oBAACC,OAAO,eAAEN,OAAA,CAACV,sBAAsB;sBAAAiB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAGpEV,OAAA,CAACrB,KAAK;oBAAC0B,IAAI,EAAC,UAAU;oBAACC,OAAO,eAAEN,OAAA,CAACT,kBAAkB;sBAAAgB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAG1DV,OAAA,CAACrB,KAAK;oBAAC0B,IAAI,EAAC,iBAAiB;oBAACC,OAAO,eAAEN,OAAA,CAACR,aAAa;sBAAAe,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAG5DV,OAAA,CAACrB,KAAK;oBAAC0B,IAAI,EAAC,WAAW;oBAACC,OAAO,eAAEN,OAAA,CAACP,eAAe;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAGxDV,OAAA,CAACrB,KAAK;oBAAC0B,IAAI,EAAC,WAAW;oBAACC,OAAO,eAAEN,OAAA,CAACN,YAAY;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAGrDV,OAAA,CAACrB,KAAK;oBAAC0B,IAAI,EAAC,WAAW;oBAACC,OAAO,eAAEN,OAAA,CAACL,gBAAgB;sBAAAY,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAGzDV,OAAA,CAACrB,KAAK;oBAAC0B,IAAI,EAAC,UAAU;oBAACC,OAAO,eAAEN,OAAA,CAACJ,qBAAqB;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC,eAGRV,OAAA,CAACrB,KAAK;kBAAC0B,IAAI,EAAC,GAAG;kBAACC,OAAO,eAAEN,OAAA,CAACpB,QAAQ;oBAACgC,EAAE,EAAC,YAAY;oBAACC,OAAO;kBAAA;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEf;AAACI,EAAA,GA1DQb,GAAG;AA4DZ,eAAeA,GAAG;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}