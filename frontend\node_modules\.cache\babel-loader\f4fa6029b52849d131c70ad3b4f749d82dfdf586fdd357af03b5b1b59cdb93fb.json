{"ast": null, "code": "var _excluded = [\"id\"],\n  _excluded2 = [\"activeDot\", \"animationBegin\", \"animationDuration\", \"animationEasing\", \"connectNulls\", \"dot\", \"fill\", \"fillOpacity\", \"hide\", \"isAnimationActive\", \"legendType\", \"stroke\", \"xAxisId\", \"yAxisId\"];\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport { PureComponent, useCallback, useRef, useState } from 'react';\nimport { clsx } from 'clsx';\nimport { Curve } from '../shape/Curve';\nimport { Dot } from '../shape/Dot';\nimport { Layer } from '../container/Layer';\nimport { CartesianLabelListContextProvider, LabelListFromLabelProp } from '../component/LabelList';\nimport { Global } from '../util/Global';\nimport { interpolate, isNan, isNullish, isNumber } from '../util/DataUtils';\nimport { getCateCoordinateOfLine, getNormalizedStackId, getTooltipNameProp, getValueByDataKey } from '../util/ChartUtils';\nimport { filterProps, isClipDot } from '../util/ReactUtils';\nimport { ActivePoints } from '../component/ActivePoints';\nimport { SetTooltipEntrySettings } from '../state/SetTooltipEntrySettings';\nimport { GraphicalItemClipPath, useNeedsClip } from './GraphicalItemClipPath';\nimport { selectArea } from '../state/selectors/areaSelectors';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { useChartLayout } from '../context/chartLayoutContext';\nimport { useChartName } from '../state/selectors/selectors';\nimport { SetLegendPayload } from '../state/SetLegendPayload';\nimport { useAppSelector } from '../state/hooks';\nimport { useAnimationId } from '../util/useAnimationId';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { isWellBehavedNumber } from '../util/isWellBehavedNumber';\nimport { usePlotArea } from '../hooks';\nimport { RegisterGraphicalItemId } from '../context/RegisterGraphicalItemId';\nimport { SetCartesianGraphicalItem } from '../state/SetGraphicalItem';\nimport { svgPropertiesNoEvents } from '../util/svgPropertiesNoEvents';\nimport { JavascriptAnimate } from '../animation/JavascriptAnimate';\n\n/**\n * Internal props, combination of external props + defaultProps + private Recharts state\n */\n\n/**\n * External props, intended for end users to fill in\n */\n\n/**\n * Because of naming conflict, we are forced to ignore certain (valid) SVG attributes.\n */\n\nfunction getLegendItemColor(stroke, fill) {\n  return stroke && stroke !== 'none' ? stroke : fill;\n}\nvar computeLegendPayloadFromAreaData = props => {\n  var {\n    dataKey,\n    name,\n    stroke,\n    fill,\n    legendType,\n    hide\n  } = props;\n  return [{\n    inactive: hide,\n    dataKey,\n    type: legendType,\n    color: getLegendItemColor(stroke, fill),\n    value: getTooltipNameProp(name, dataKey),\n    payload: props\n  }];\n};\nfunction getTooltipEntrySettings(props) {\n  var {\n    dataKey,\n    data,\n    stroke,\n    strokeWidth,\n    fill,\n    name,\n    hide,\n    unit\n  } = props;\n  return {\n    dataDefinedOnItem: data,\n    positions: undefined,\n    settings: {\n      stroke,\n      strokeWidth,\n      fill,\n      dataKey,\n      nameKey: undefined,\n      name: getTooltipNameProp(name, dataKey),\n      hide,\n      type: props.tooltipType,\n      color: getLegendItemColor(stroke, fill),\n      unit\n    }\n  };\n}\nvar renderDotItem = (option, props) => {\n  var dotItem;\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    dotItem = /*#__PURE__*/React.cloneElement(option, props);\n  } else if (typeof option === 'function') {\n    dotItem = option(props);\n  } else {\n    var className = clsx('recharts-area-dot', typeof option !== 'boolean' ? option.className : '');\n    dotItem = /*#__PURE__*/React.createElement(Dot, _extends({}, props, {\n      className: className\n    }));\n  }\n  return dotItem;\n};\nfunction shouldRenderDots(points, dot) {\n  if (points == null) {\n    return false;\n  }\n  if (dot) {\n    return true;\n  }\n  return points.length === 1;\n}\nfunction Dots(_ref) {\n  var {\n    clipPathId,\n    points,\n    props\n  } = _ref;\n  var {\n    needClip,\n    dot,\n    dataKey\n  } = props;\n  if (!shouldRenderDots(points, dot)) {\n    return null;\n  }\n  var clipDot = isClipDot(dot);\n  var areaProps = svgPropertiesNoEvents(props);\n  var customDotProps = filterProps(dot, true);\n  var dots = points.map((entry, i) => {\n    var dotProps = _objectSpread(_objectSpread(_objectSpread({\n      key: \"dot-\".concat(i),\n      r: 3\n    }, areaProps), customDotProps), {}, {\n      index: i,\n      cx: entry.x,\n      cy: entry.y,\n      dataKey,\n      value: entry.value,\n      payload: entry.payload,\n      points\n    });\n    return renderDotItem(dot, dotProps);\n  });\n  var dotsProps = {\n    clipPath: needClip ? \"url(#clipPath-\".concat(clipDot ? '' : 'dots-').concat(clipPathId, \")\") : undefined\n  };\n  return /*#__PURE__*/React.createElement(Layer, _extends({\n    className: \"recharts-area-dots\"\n  }, dotsProps), dots);\n}\nfunction AreaLabelListProvider(_ref2) {\n  var {\n    showLabels,\n    children,\n    points\n  } = _ref2;\n  var labelListEntries = points.map(point => {\n    var viewBox = {\n      x: point.x,\n      y: point.y,\n      width: 0,\n      height: 0\n    };\n    return _objectSpread(_objectSpread({}, viewBox), {}, {\n      value: point.value,\n      payload: point.payload,\n      parentViewBox: undefined,\n      viewBox,\n      fill: undefined\n    });\n  });\n  return /*#__PURE__*/React.createElement(CartesianLabelListContextProvider, {\n    value: showLabels ? labelListEntries : null\n  }, children);\n}\nfunction StaticArea(_ref3) {\n  var {\n    points,\n    baseLine,\n    needClip,\n    clipPathId,\n    props\n  } = _ref3;\n  var {\n    layout,\n    type,\n    stroke,\n    connectNulls,\n    isRange\n  } = props;\n  var {\n      id\n    } = props,\n    propsWithoutId = _objectWithoutProperties(props, _excluded);\n  var allOtherProps = svgPropertiesNoEvents(propsWithoutId);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, (points === null || points === void 0 ? void 0 : points.length) > 1 && /*#__PURE__*/React.createElement(Layer, {\n    clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : undefined\n  }, /*#__PURE__*/React.createElement(Curve, _extends({}, allOtherProps, {\n    id: id,\n    points: points,\n    connectNulls: connectNulls,\n    type: type,\n    baseLine: baseLine,\n    layout: layout,\n    stroke: \"none\",\n    className: \"recharts-area-area\"\n  })), stroke !== 'none' && /*#__PURE__*/React.createElement(Curve, _extends({}, allOtherProps, {\n    className: \"recharts-area-curve\",\n    layout: layout,\n    type: type,\n    connectNulls: connectNulls,\n    fill: \"none\",\n    points: points\n  })), stroke !== 'none' && isRange && /*#__PURE__*/React.createElement(Curve, _extends({}, allOtherProps, {\n    className: \"recharts-area-curve\",\n    layout: layout,\n    type: type,\n    connectNulls: connectNulls,\n    fill: \"none\",\n    points: baseLine\n  }))), /*#__PURE__*/React.createElement(Dots, {\n    points: points,\n    props: propsWithoutId,\n    clipPathId: clipPathId\n  }));\n}\nfunction VerticalRect(_ref4) {\n  var {\n    alpha,\n    baseLine,\n    points,\n    strokeWidth\n  } = _ref4;\n  var startY = points[0].y;\n  var endY = points[points.length - 1].y;\n  if (!isWellBehavedNumber(startY) || !isWellBehavedNumber(endY)) {\n    return null;\n  }\n  var height = alpha * Math.abs(startY - endY);\n  var maxX = Math.max(...points.map(entry => entry.x || 0));\n  if (isNumber(baseLine)) {\n    maxX = Math.max(baseLine, maxX);\n  } else if (baseLine && Array.isArray(baseLine) && baseLine.length) {\n    maxX = Math.max(...baseLine.map(entry => entry.x || 0), maxX);\n  }\n  if (isNumber(maxX)) {\n    return /*#__PURE__*/React.createElement(\"rect\", {\n      x: 0,\n      y: startY < endY ? startY : startY - height,\n      width: maxX + (strokeWidth ? parseInt(\"\".concat(strokeWidth), 10) : 1),\n      height: Math.floor(height)\n    });\n  }\n  return null;\n}\nfunction HorizontalRect(_ref5) {\n  var {\n    alpha,\n    baseLine,\n    points,\n    strokeWidth\n  } = _ref5;\n  var startX = points[0].x;\n  var endX = points[points.length - 1].x;\n  if (!isWellBehavedNumber(startX) || !isWellBehavedNumber(endX)) {\n    return null;\n  }\n  var width = alpha * Math.abs(startX - endX);\n  var maxY = Math.max(...points.map(entry => entry.y || 0));\n  if (isNumber(baseLine)) {\n    maxY = Math.max(baseLine, maxY);\n  } else if (baseLine && Array.isArray(baseLine) && baseLine.length) {\n    maxY = Math.max(...baseLine.map(entry => entry.y || 0), maxY);\n  }\n  if (isNumber(maxY)) {\n    return /*#__PURE__*/React.createElement(\"rect\", {\n      x: startX < endX ? startX : startX - width,\n      y: 0,\n      width: width,\n      height: Math.floor(maxY + (strokeWidth ? parseInt(\"\".concat(strokeWidth), 10) : 1))\n    });\n  }\n  return null;\n}\nfunction ClipRect(_ref6) {\n  var {\n    alpha,\n    layout,\n    points,\n    baseLine,\n    strokeWidth\n  } = _ref6;\n  if (layout === 'vertical') {\n    return /*#__PURE__*/React.createElement(VerticalRect, {\n      alpha: alpha,\n      points: points,\n      baseLine: baseLine,\n      strokeWidth: strokeWidth\n    });\n  }\n  return /*#__PURE__*/React.createElement(HorizontalRect, {\n    alpha: alpha,\n    points: points,\n    baseLine: baseLine,\n    strokeWidth: strokeWidth\n  });\n}\nfunction AreaWithAnimation(_ref7) {\n  var {\n    needClip,\n    clipPathId,\n    props,\n    previousPointsRef,\n    previousBaselineRef\n  } = _ref7;\n  var {\n    points,\n    baseLine,\n    isAnimationActive,\n    animationBegin,\n    animationDuration,\n    animationEasing,\n    onAnimationStart,\n    onAnimationEnd\n  } = props;\n  var animationId = useAnimationId(props, 'recharts-area-');\n  var [isAnimating, setIsAnimating] = useState(false);\n  var showLabels = !isAnimating;\n  var handleAnimationEnd = useCallback(() => {\n    if (typeof onAnimationEnd === 'function') {\n      onAnimationEnd();\n    }\n    setIsAnimating(false);\n  }, [onAnimationEnd]);\n  var handleAnimationStart = useCallback(() => {\n    if (typeof onAnimationStart === 'function') {\n      onAnimationStart();\n    }\n    setIsAnimating(true);\n  }, [onAnimationStart]);\n  var prevPoints = previousPointsRef.current;\n  var prevBaseLine = previousBaselineRef.current;\n  return /*#__PURE__*/React.createElement(AreaLabelListProvider, {\n    showLabels: showLabels,\n    points: points\n  }, props.children, /*#__PURE__*/React.createElement(JavascriptAnimate, {\n    animationId: animationId,\n    begin: animationBegin,\n    duration: animationDuration,\n    isActive: isAnimationActive,\n    easing: animationEasing,\n    onAnimationEnd: handleAnimationEnd,\n    onAnimationStart: handleAnimationStart,\n    key: animationId\n  }, t => {\n    if (prevPoints) {\n      var prevPointsDiffFactor = prevPoints.length / points.length;\n      var stepPoints =\n      /*\n       * Here it is important that at the very end of the animation, on the last frame,\n       * we render the original points without any interpolation.\n       * This is needed because the code above is checking for reference equality to decide if the animation should run\n       * and if we create a new array instance (even if the numbers were the same)\n       * then we would break animations.\n       */\n      t === 1 ? points : points.map((entry, index) => {\n        var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n        if (prevPoints[prevPointIndex]) {\n          var prev = prevPoints[prevPointIndex];\n          return _objectSpread(_objectSpread({}, entry), {}, {\n            x: interpolate(prev.x, entry.x, t),\n            y: interpolate(prev.y, entry.y, t)\n          });\n        }\n        return entry;\n      });\n      var stepBaseLine;\n      if (isNumber(baseLine)) {\n        stepBaseLine = interpolate(prevBaseLine, baseLine, t);\n      } else if (isNullish(baseLine) || isNan(baseLine)) {\n        stepBaseLine = interpolate(prevBaseLine, 0, t);\n      } else {\n        stepBaseLine = baseLine.map((entry, index) => {\n          var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n          if (Array.isArray(prevBaseLine) && prevBaseLine[prevPointIndex]) {\n            var prev = prevBaseLine[prevPointIndex];\n            return _objectSpread(_objectSpread({}, entry), {}, {\n              x: interpolate(prev.x, entry.x, t),\n              y: interpolate(prev.y, entry.y, t)\n            });\n          }\n          return entry;\n        });\n      }\n      if (t > 0) {\n        /*\n         * We need to keep the refs in the parent component because we need to remember the last shape of the animation\n         * even if AreaWithAnimation is unmounted as that happens when changing props.\n         *\n         * And we need to update the refs here because here is where the interpolation is computed.\n         * Eslint doesn't like changing function arguments, but we need it so here is an eslint-disable.\n         */\n        // eslint-disable-next-line no-param-reassign\n        previousPointsRef.current = stepPoints;\n        // eslint-disable-next-line no-param-reassign\n        previousBaselineRef.current = stepBaseLine;\n      }\n      return /*#__PURE__*/React.createElement(StaticArea, {\n        points: stepPoints,\n        baseLine: stepBaseLine,\n        needClip: needClip,\n        clipPathId: clipPathId,\n        props: props\n      });\n    }\n    if (t > 0) {\n      // eslint-disable-next-line no-param-reassign\n      previousPointsRef.current = points;\n      // eslint-disable-next-line no-param-reassign\n      previousBaselineRef.current = baseLine;\n    }\n    return /*#__PURE__*/React.createElement(Layer, null, isAnimationActive && /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n      id: \"animationClipPath-\".concat(clipPathId)\n    }, /*#__PURE__*/React.createElement(ClipRect, {\n      alpha: t,\n      points: points,\n      baseLine: baseLine,\n      layout: props.layout,\n      strokeWidth: props.strokeWidth\n    }))), /*#__PURE__*/React.createElement(Layer, {\n      clipPath: \"url(#animationClipPath-\".concat(clipPathId, \")\")\n    }, /*#__PURE__*/React.createElement(StaticArea, {\n      points: points,\n      baseLine: baseLine,\n      needClip: needClip,\n      clipPathId: clipPathId,\n      props: props\n    })));\n  }), /*#__PURE__*/React.createElement(LabelListFromLabelProp, {\n    label: props.label\n  }));\n}\n\n/*\n * This components decides if the area should be animated or not.\n * It also holds the state of the animation.\n */\nfunction RenderArea(_ref8) {\n  var {\n    needClip,\n    clipPathId,\n    props\n  } = _ref8;\n  /*\n   * These two must be refs, not state!\n   * Because we want to store the most recent shape of the animation in case we have to interrupt the animation;\n   * that happens when user initiates another animation before the current one finishes.\n   *\n   * If this was a useState, then every step in the animation would trigger a re-render.\n   * So, useRef it is.\n   */\n  var previousPointsRef = useRef(null);\n  var previousBaselineRef = useRef();\n  return /*#__PURE__*/React.createElement(AreaWithAnimation, {\n    needClip: needClip,\n    clipPathId: clipPathId,\n    props: props,\n    previousPointsRef: previousPointsRef,\n    previousBaselineRef: previousBaselineRef\n  });\n}\nclass AreaWithState extends PureComponent {\n  render() {\n    var _filterProps;\n    var {\n      hide,\n      dot,\n      points,\n      className,\n      top,\n      left,\n      needClip,\n      xAxisId,\n      yAxisId,\n      width,\n      height,\n      id,\n      baseLine\n    } = this.props;\n    if (hide) {\n      return null;\n    }\n    var layerClass = clsx('recharts-area', className);\n    var clipPathId = id;\n    var {\n      r = 3,\n      strokeWidth = 2\n    } = (_filterProps = filterProps(dot, false)) !== null && _filterProps !== void 0 ? _filterProps : {\n      r: 3,\n      strokeWidth: 2\n    };\n    var clipDot = isClipDot(dot);\n    var dotSize = r * 2 + strokeWidth;\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Layer, {\n      className: layerClass\n    }, needClip && /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(GraphicalItemClipPath, {\n      clipPathId: clipPathId,\n      xAxisId: xAxisId,\n      yAxisId: yAxisId\n    }), !clipDot && /*#__PURE__*/React.createElement(\"clipPath\", {\n      id: \"clipPath-dots-\".concat(clipPathId)\n    }, /*#__PURE__*/React.createElement(\"rect\", {\n      x: left - dotSize / 2,\n      y: top - dotSize / 2,\n      width: width + dotSize,\n      height: height + dotSize\n    }))), /*#__PURE__*/React.createElement(RenderArea, {\n      needClip: needClip,\n      clipPathId: clipPathId,\n      props: this.props\n    })), /*#__PURE__*/React.createElement(ActivePoints, {\n      points: points,\n      mainColor: getLegendItemColor(this.props.stroke, this.props.fill),\n      itemDataKey: this.props.dataKey,\n      activeDot: this.props.activeDot\n    }), this.props.isRange && Array.isArray(baseLine) && /*#__PURE__*/React.createElement(ActivePoints, {\n      points: baseLine,\n      mainColor: getLegendItemColor(this.props.stroke, this.props.fill),\n      itemDataKey: this.props.dataKey,\n      activeDot: this.props.activeDot\n    }));\n  }\n}\nvar defaultAreaProps = {\n  activeDot: true,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease',\n  connectNulls: false,\n  dot: false,\n  fill: '#3182bd',\n  fillOpacity: 0.6,\n  hide: false,\n  isAnimationActive: !Global.isSsr,\n  legendType: 'line',\n  stroke: '#3182bd',\n  xAxisId: 0,\n  yAxisId: 0\n};\nfunction AreaImpl(props) {\n  var _useAppSelector;\n  var _resolveDefaultProps = resolveDefaultProps(props, defaultAreaProps),\n    {\n      activeDot,\n      animationBegin,\n      animationDuration,\n      animationEasing,\n      connectNulls,\n      dot,\n      fill,\n      fillOpacity,\n      hide,\n      isAnimationActive,\n      legendType,\n      stroke,\n      xAxisId,\n      yAxisId\n    } = _resolveDefaultProps,\n    everythingElse = _objectWithoutProperties(_resolveDefaultProps, _excluded2);\n  var layout = useChartLayout();\n  var chartName = useChartName();\n  var {\n    needClip\n  } = useNeedsClip(xAxisId, yAxisId);\n  var isPanorama = useIsPanorama();\n  var {\n    points,\n    isRange,\n    baseLine\n  } = (_useAppSelector = useAppSelector(state => selectArea(state, xAxisId, yAxisId, isPanorama, props.id))) !== null && _useAppSelector !== void 0 ? _useAppSelector : {};\n  var plotArea = usePlotArea();\n  if (layout !== 'horizontal' && layout !== 'vertical' || plotArea == null) {\n    // Can't render Area in an unsupported layout\n    return null;\n  }\n  if (chartName !== 'AreaChart' && chartName !== 'ComposedChart') {\n    // There is nothing stopping us from rendering Area in other charts, except for historical reasons. Do we want to allow that?\n    return null;\n  }\n  var {\n    height,\n    width,\n    x: left,\n    y: top\n  } = plotArea;\n  if (!points || !points.length) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(AreaWithState, _extends({}, everythingElse, {\n    activeDot: activeDot,\n    animationBegin: animationBegin,\n    animationDuration: animationDuration,\n    animationEasing: animationEasing,\n    baseLine: baseLine,\n    connectNulls: connectNulls,\n    dot: dot,\n    fill: fill,\n    fillOpacity: fillOpacity,\n    height: height,\n    hide: hide,\n    layout: layout,\n    isAnimationActive: isAnimationActive,\n    isRange: isRange,\n    legendType: legendType,\n    needClip: needClip,\n    points: points,\n    stroke: stroke,\n    width: width,\n    left: left,\n    top: top,\n    xAxisId: xAxisId,\n    yAxisId: yAxisId\n  }));\n}\nexport var getBaseValue = (layout, chartBaseValue, itemBaseValue, xAxis, yAxis) => {\n  // The baseValue can be defined both on the AreaChart, and on the Area.\n  // The value for the item takes precedence.\n  var baseValue = itemBaseValue !== null && itemBaseValue !== void 0 ? itemBaseValue : chartBaseValue;\n  if (isNumber(baseValue)) {\n    return baseValue;\n  }\n  var numericAxis = layout === 'horizontal' ? yAxis : xAxis;\n  // @ts-expect-error d3scale .domain() returns unknown, Math.max expects number\n  var domain = numericAxis.scale.domain();\n  if (numericAxis.type === 'number') {\n    var domainMax = Math.max(domain[0], domain[1]);\n    var domainMin = Math.min(domain[0], domain[1]);\n    if (baseValue === 'dataMin') {\n      return domainMin;\n    }\n    if (baseValue === 'dataMax') {\n      return domainMax;\n    }\n    return domainMax < 0 ? domainMax : Math.max(Math.min(domain[0], domain[1]), 0);\n  }\n  if (baseValue === 'dataMin') {\n    return domain[0];\n  }\n  if (baseValue === 'dataMax') {\n    return domain[1];\n  }\n  return domain[0];\n};\nexport function computeArea(_ref9) {\n  var {\n    areaSettings: {\n      connectNulls,\n      baseValue: itemBaseValue,\n      dataKey\n    },\n    stackedData,\n    layout,\n    chartBaseValue,\n    xAxis,\n    yAxis,\n    displayedData,\n    dataStartIndex,\n    xAxisTicks,\n    yAxisTicks,\n    bandSize\n  } = _ref9;\n  var hasStack = stackedData && stackedData.length;\n  var baseValue = getBaseValue(layout, chartBaseValue, itemBaseValue, xAxis, yAxis);\n  var isHorizontalLayout = layout === 'horizontal';\n  var isRange = false;\n  var points = displayedData.map((entry, index) => {\n    var value;\n    if (hasStack) {\n      value = stackedData[dataStartIndex + index];\n    } else {\n      value = getValueByDataKey(entry, dataKey);\n      if (!Array.isArray(value)) {\n        value = [baseValue, value];\n      } else {\n        isRange = true;\n      }\n    }\n    var isBreakPoint = value[1] == null || hasStack && !connectNulls && getValueByDataKey(entry, dataKey) == null;\n    if (isHorizontalLayout) {\n      return {\n        x: getCateCoordinateOfLine({\n          axis: xAxis,\n          ticks: xAxisTicks,\n          bandSize,\n          entry,\n          index\n        }),\n        y: isBreakPoint ? null : yAxis.scale(value[1]),\n        value,\n        payload: entry\n      };\n    }\n    return {\n      x: isBreakPoint ? null : xAxis.scale(value[1]),\n      y: getCateCoordinateOfLine({\n        axis: yAxis,\n        ticks: yAxisTicks,\n        bandSize,\n        entry,\n        index\n      }),\n      value,\n      payload: entry\n    };\n  });\n  var baseLine;\n  if (hasStack || isRange) {\n    baseLine = points.map(entry => {\n      var x = Array.isArray(entry.value) ? entry.value[0] : null;\n      if (isHorizontalLayout) {\n        return {\n          x: entry.x,\n          y: x != null && entry.y != null ? yAxis.scale(x) : null,\n          payload: entry.payload\n        };\n      }\n      return {\n        x: x != null ? xAxis.scale(x) : null,\n        y: entry.y,\n        payload: entry.payload\n      };\n    });\n  } else {\n    baseLine = isHorizontalLayout ? yAxis.scale(baseValue) : xAxis.scale(baseValue);\n  }\n  return {\n    points,\n    baseLine,\n    isRange\n  };\n}\nexport function Area(outsideProps) {\n  var props = resolveDefaultProps(outsideProps, defaultAreaProps);\n  var isPanorama = useIsPanorama();\n  // Report all props to Redux store first, before calling any hooks, to avoid circular dependencies.\n  return /*#__PURE__*/React.createElement(RegisterGraphicalItemId, {\n    id: props.id,\n    type: \"area\"\n  }, id => /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(SetLegendPayload, {\n    legendPayload: computeLegendPayloadFromAreaData(props)\n  }), /*#__PURE__*/React.createElement(SetTooltipEntrySettings, {\n    fn: getTooltipEntrySettings,\n    args: props\n  }), /*#__PURE__*/React.createElement(SetCartesianGraphicalItem, {\n    type: \"area\",\n    id: id,\n    data: props.data,\n    dataKey: props.dataKey,\n    xAxisId: props.xAxisId,\n    yAxisId: props.yAxisId,\n    zAxisId: 0,\n    stackId: getNormalizedStackId(props.stackId),\n    hide: props.hide,\n    barSize: undefined,\n    baseValue: props.baseValue,\n    isPanorama: isPanorama,\n    connectNulls: props.connectNulls\n  }), /*#__PURE__*/React.createElement(AreaImpl, _extends({}, props, {\n    id: id\n  }))));\n}\nArea.displayName = 'Area';", "map": {"version": 3, "names": ["_excluded", "_excluded2", "_objectWithoutProperties", "e", "t", "o", "r", "i", "_objectWithoutPropertiesLoose", "Object", "getOwnPropertySymbols", "n", "length", "indexOf", "propertyIsEnumerable", "call", "hasOwnProperty", "ownKeys", "keys", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "_toPrimitive", "Symbol", "toPrimitive", "TypeError", "String", "Number", "_extends", "assign", "bind", "React", "PureComponent", "useCallback", "useRef", "useState", "clsx", "Curve", "Dot", "Layer", "CartesianLabelListContextProvider", "LabelListFromLabelProp", "Global", "interpolate", "isNan", "<PERSON><PERSON><PERSON><PERSON>", "isNumber", "getCateCoordinateOfLine", "getNormalizedStackId", "getTooltipNameProp", "getValueByDataKey", "filterProps", "isClipDot", "ActivePoints", "SetTooltipEntrySettings", "GraphicalItemClipPath", "useNeedsClip", "selectArea", "useIsPanorama", "useChartLayout", "useChartName", "SetLegendPayload", "useAppSelector", "useAnimationId", "resolveDefaultProps", "isWellBehavedNumber", "usePlotArea", "RegisterGraphicalItemId", "SetCartesianGraphicalItem", "svgPropertiesNoEvents", "JavascriptAnimate", "getLegendItemColor", "stroke", "fill", "computeLegendPayloadFromAreaData", "props", "dataKey", "name", "legendType", "hide", "inactive", "type", "color", "payload", "getTooltipEntrySettings", "data", "strokeWidth", "unit", "dataDefinedOnItem", "positions", "undefined", "settings", "<PERSON><PERSON><PERSON>", "tooltipType", "renderDotItem", "option", "dotItem", "isValidElement", "cloneElement", "className", "createElement", "shouldRenderDots", "points", "dot", "Dots", "_ref", "clipPathId", "needClip", "clipDot", "areaProps", "customDotProps", "dots", "map", "entry", "dotProps", "key", "concat", "index", "cx", "x", "cy", "y", "dotsProps", "clipPath", "AreaLabelListProvider", "_ref2", "showLabels", "children", "labelListEntries", "point", "viewBox", "width", "height", "parentViewBox", "StaticArea", "_ref3", "baseLine", "layout", "connectNulls", "isRange", "id", "propsWithoutId", "allOtherProps", "Fragment", "VerticalRect", "_ref4", "alpha", "startY", "endY", "Math", "abs", "maxX", "max", "Array", "isArray", "parseInt", "floor", "HorizontalRect", "_ref5", "startX", "endX", "maxY", "ClipRect", "_ref6", "AreaWithAnimation", "_ref7", "previousPointsRef", "previousBaselineRef", "isAnimationActive", "animationBegin", "animationDuration", "animationEasing", "onAnimationStart", "onAnimationEnd", "animationId", "isAnimating", "setIsAnimating", "handleAnimationEnd", "handleAnimationStart", "prevPoints", "current", "prevBaseLine", "begin", "duration", "isActive", "easing", "prevPointsDiffFactor", "stepPoints", "prevPointIndex", "prev", "stepBaseLine", "label", "RenderArea", "_ref8", "AreaWithState", "render", "_filterProps", "top", "left", "xAxisId", "yAxisId", "layerClass", "dotSize", "mainColor", "itemDataKey", "activeDot", "defaultAreaProps", "fillOpacity", "isSsr", "AreaImpl", "_useAppSelector", "_resolveDefaultProps", "everythingElse", "chartName", "isPanorama", "state", "<PERSON><PERSON><PERSON>", "getBaseValue", "chartBaseValue", "itemBaseValue", "xAxis", "yAxis", "baseValue", "numericAxis", "domain", "scale", "domainMax", "domainMin", "min", "computeArea", "_ref9", "areaSettings", "stackedData", "displayedData", "dataStartIndex", "xAxisTicks", "yAxisTicks", "bandSize", "hasStack", "isHorizontalLayout", "isBreakPoint", "axis", "ticks", "Area", "outsideProps", "legendPayload", "fn", "args", "zAxisId", "stackId", "barSize", "displayName"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/recharts/es6/cartesian/Area.js"], "sourcesContent": ["var _excluded = [\"id\"],\n  _excluded2 = [\"activeDot\", \"animationBegin\", \"animationDuration\", \"animationEasing\", \"connectNulls\", \"dot\", \"fill\", \"fillOpacity\", \"hide\", \"isAnimationActive\", \"legendType\", \"stroke\", \"xAxisId\", \"yAxisId\"];\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nimport * as React from 'react';\nimport { PureComponent, useCallback, useRef, useState } from 'react';\nimport { clsx } from 'clsx';\nimport { Curve } from '../shape/Curve';\nimport { Dot } from '../shape/Dot';\nimport { Layer } from '../container/Layer';\nimport { CartesianLabelListContextProvider, LabelListFromLabelProp } from '../component/LabelList';\nimport { Global } from '../util/Global';\nimport { interpolate, isNan, isNullish, isNumber } from '../util/DataUtils';\nimport { getCateCoordinateOfLine, getNormalizedStackId, getTooltipNameProp, getValueByDataKey } from '../util/ChartUtils';\nimport { filterProps, isClipDot } from '../util/ReactUtils';\nimport { ActivePoints } from '../component/ActivePoints';\nimport { SetTooltipEntrySettings } from '../state/SetTooltipEntrySettings';\nimport { GraphicalItemClipPath, useNeedsClip } from './GraphicalItemClipPath';\nimport { selectArea } from '../state/selectors/areaSelectors';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { useChartLayout } from '../context/chartLayoutContext';\nimport { useChartName } from '../state/selectors/selectors';\nimport { SetLegendPayload } from '../state/SetLegendPayload';\nimport { useAppSelector } from '../state/hooks';\nimport { useAnimationId } from '../util/useAnimationId';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { isWellBehavedNumber } from '../util/isWellBehavedNumber';\nimport { usePlotArea } from '../hooks';\nimport { RegisterGraphicalItemId } from '../context/RegisterGraphicalItemId';\nimport { SetCartesianGraphicalItem } from '../state/SetGraphicalItem';\nimport { svgPropertiesNoEvents } from '../util/svgPropertiesNoEvents';\nimport { JavascriptAnimate } from '../animation/JavascriptAnimate';\n\n/**\n * Internal props, combination of external props + defaultProps + private Recharts state\n */\n\n/**\n * External props, intended for end users to fill in\n */\n\n/**\n * Because of naming conflict, we are forced to ignore certain (valid) SVG attributes.\n */\n\nfunction getLegendItemColor(stroke, fill) {\n  return stroke && stroke !== 'none' ? stroke : fill;\n}\nvar computeLegendPayloadFromAreaData = props => {\n  var {\n    dataKey,\n    name,\n    stroke,\n    fill,\n    legendType,\n    hide\n  } = props;\n  return [{\n    inactive: hide,\n    dataKey,\n    type: legendType,\n    color: getLegendItemColor(stroke, fill),\n    value: getTooltipNameProp(name, dataKey),\n    payload: props\n  }];\n};\nfunction getTooltipEntrySettings(props) {\n  var {\n    dataKey,\n    data,\n    stroke,\n    strokeWidth,\n    fill,\n    name,\n    hide,\n    unit\n  } = props;\n  return {\n    dataDefinedOnItem: data,\n    positions: undefined,\n    settings: {\n      stroke,\n      strokeWidth,\n      fill,\n      dataKey,\n      nameKey: undefined,\n      name: getTooltipNameProp(name, dataKey),\n      hide,\n      type: props.tooltipType,\n      color: getLegendItemColor(stroke, fill),\n      unit\n    }\n  };\n}\nvar renderDotItem = (option, props) => {\n  var dotItem;\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    dotItem = /*#__PURE__*/React.cloneElement(option, props);\n  } else if (typeof option === 'function') {\n    dotItem = option(props);\n  } else {\n    var className = clsx('recharts-area-dot', typeof option !== 'boolean' ? option.className : '');\n    dotItem = /*#__PURE__*/React.createElement(Dot, _extends({}, props, {\n      className: className\n    }));\n  }\n  return dotItem;\n};\nfunction shouldRenderDots(points, dot) {\n  if (points == null) {\n    return false;\n  }\n  if (dot) {\n    return true;\n  }\n  return points.length === 1;\n}\nfunction Dots(_ref) {\n  var {\n    clipPathId,\n    points,\n    props\n  } = _ref;\n  var {\n    needClip,\n    dot,\n    dataKey\n  } = props;\n  if (!shouldRenderDots(points, dot)) {\n    return null;\n  }\n  var clipDot = isClipDot(dot);\n  var areaProps = svgPropertiesNoEvents(props);\n  var customDotProps = filterProps(dot, true);\n  var dots = points.map((entry, i) => {\n    var dotProps = _objectSpread(_objectSpread(_objectSpread({\n      key: \"dot-\".concat(i),\n      r: 3\n    }, areaProps), customDotProps), {}, {\n      index: i,\n      cx: entry.x,\n      cy: entry.y,\n      dataKey,\n      value: entry.value,\n      payload: entry.payload,\n      points\n    });\n    return renderDotItem(dot, dotProps);\n  });\n  var dotsProps = {\n    clipPath: needClip ? \"url(#clipPath-\".concat(clipDot ? '' : 'dots-').concat(clipPathId, \")\") : undefined\n  };\n  return /*#__PURE__*/React.createElement(Layer, _extends({\n    className: \"recharts-area-dots\"\n  }, dotsProps), dots);\n}\nfunction AreaLabelListProvider(_ref2) {\n  var {\n    showLabels,\n    children,\n    points\n  } = _ref2;\n  var labelListEntries = points.map(point => {\n    var viewBox = {\n      x: point.x,\n      y: point.y,\n      width: 0,\n      height: 0\n    };\n    return _objectSpread(_objectSpread({}, viewBox), {}, {\n      value: point.value,\n      payload: point.payload,\n      parentViewBox: undefined,\n      viewBox,\n      fill: undefined\n    });\n  });\n  return /*#__PURE__*/React.createElement(CartesianLabelListContextProvider, {\n    value: showLabels ? labelListEntries : null\n  }, children);\n}\nfunction StaticArea(_ref3) {\n  var {\n    points,\n    baseLine,\n    needClip,\n    clipPathId,\n    props\n  } = _ref3;\n  var {\n    layout,\n    type,\n    stroke,\n    connectNulls,\n    isRange\n  } = props;\n  var {\n      id\n    } = props,\n    propsWithoutId = _objectWithoutProperties(props, _excluded);\n  var allOtherProps = svgPropertiesNoEvents(propsWithoutId);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, (points === null || points === void 0 ? void 0 : points.length) > 1 && /*#__PURE__*/React.createElement(Layer, {\n    clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : undefined\n  }, /*#__PURE__*/React.createElement(Curve, _extends({}, allOtherProps, {\n    id: id,\n    points: points,\n    connectNulls: connectNulls,\n    type: type,\n    baseLine: baseLine,\n    layout: layout,\n    stroke: \"none\",\n    className: \"recharts-area-area\"\n  })), stroke !== 'none' && /*#__PURE__*/React.createElement(Curve, _extends({}, allOtherProps, {\n    className: \"recharts-area-curve\",\n    layout: layout,\n    type: type,\n    connectNulls: connectNulls,\n    fill: \"none\",\n    points: points\n  })), stroke !== 'none' && isRange && /*#__PURE__*/React.createElement(Curve, _extends({}, allOtherProps, {\n    className: \"recharts-area-curve\",\n    layout: layout,\n    type: type,\n    connectNulls: connectNulls,\n    fill: \"none\",\n    points: baseLine\n  }))), /*#__PURE__*/React.createElement(Dots, {\n    points: points,\n    props: propsWithoutId,\n    clipPathId: clipPathId\n  }));\n}\nfunction VerticalRect(_ref4) {\n  var {\n    alpha,\n    baseLine,\n    points,\n    strokeWidth\n  } = _ref4;\n  var startY = points[0].y;\n  var endY = points[points.length - 1].y;\n  if (!isWellBehavedNumber(startY) || !isWellBehavedNumber(endY)) {\n    return null;\n  }\n  var height = alpha * Math.abs(startY - endY);\n  var maxX = Math.max(...points.map(entry => entry.x || 0));\n  if (isNumber(baseLine)) {\n    maxX = Math.max(baseLine, maxX);\n  } else if (baseLine && Array.isArray(baseLine) && baseLine.length) {\n    maxX = Math.max(...baseLine.map(entry => entry.x || 0), maxX);\n  }\n  if (isNumber(maxX)) {\n    return /*#__PURE__*/React.createElement(\"rect\", {\n      x: 0,\n      y: startY < endY ? startY : startY - height,\n      width: maxX + (strokeWidth ? parseInt(\"\".concat(strokeWidth), 10) : 1),\n      height: Math.floor(height)\n    });\n  }\n  return null;\n}\nfunction HorizontalRect(_ref5) {\n  var {\n    alpha,\n    baseLine,\n    points,\n    strokeWidth\n  } = _ref5;\n  var startX = points[0].x;\n  var endX = points[points.length - 1].x;\n  if (!isWellBehavedNumber(startX) || !isWellBehavedNumber(endX)) {\n    return null;\n  }\n  var width = alpha * Math.abs(startX - endX);\n  var maxY = Math.max(...points.map(entry => entry.y || 0));\n  if (isNumber(baseLine)) {\n    maxY = Math.max(baseLine, maxY);\n  } else if (baseLine && Array.isArray(baseLine) && baseLine.length) {\n    maxY = Math.max(...baseLine.map(entry => entry.y || 0), maxY);\n  }\n  if (isNumber(maxY)) {\n    return /*#__PURE__*/React.createElement(\"rect\", {\n      x: startX < endX ? startX : startX - width,\n      y: 0,\n      width: width,\n      height: Math.floor(maxY + (strokeWidth ? parseInt(\"\".concat(strokeWidth), 10) : 1))\n    });\n  }\n  return null;\n}\nfunction ClipRect(_ref6) {\n  var {\n    alpha,\n    layout,\n    points,\n    baseLine,\n    strokeWidth\n  } = _ref6;\n  if (layout === 'vertical') {\n    return /*#__PURE__*/React.createElement(VerticalRect, {\n      alpha: alpha,\n      points: points,\n      baseLine: baseLine,\n      strokeWidth: strokeWidth\n    });\n  }\n  return /*#__PURE__*/React.createElement(HorizontalRect, {\n    alpha: alpha,\n    points: points,\n    baseLine: baseLine,\n    strokeWidth: strokeWidth\n  });\n}\nfunction AreaWithAnimation(_ref7) {\n  var {\n    needClip,\n    clipPathId,\n    props,\n    previousPointsRef,\n    previousBaselineRef\n  } = _ref7;\n  var {\n    points,\n    baseLine,\n    isAnimationActive,\n    animationBegin,\n    animationDuration,\n    animationEasing,\n    onAnimationStart,\n    onAnimationEnd\n  } = props;\n  var animationId = useAnimationId(props, 'recharts-area-');\n  var [isAnimating, setIsAnimating] = useState(false);\n  var showLabels = !isAnimating;\n  var handleAnimationEnd = useCallback(() => {\n    if (typeof onAnimationEnd === 'function') {\n      onAnimationEnd();\n    }\n    setIsAnimating(false);\n  }, [onAnimationEnd]);\n  var handleAnimationStart = useCallback(() => {\n    if (typeof onAnimationStart === 'function') {\n      onAnimationStart();\n    }\n    setIsAnimating(true);\n  }, [onAnimationStart]);\n  var prevPoints = previousPointsRef.current;\n  var prevBaseLine = previousBaselineRef.current;\n  return /*#__PURE__*/React.createElement(AreaLabelListProvider, {\n    showLabels: showLabels,\n    points: points\n  }, props.children, /*#__PURE__*/React.createElement(JavascriptAnimate, {\n    animationId: animationId,\n    begin: animationBegin,\n    duration: animationDuration,\n    isActive: isAnimationActive,\n    easing: animationEasing,\n    onAnimationEnd: handleAnimationEnd,\n    onAnimationStart: handleAnimationStart,\n    key: animationId\n  }, t => {\n    if (prevPoints) {\n      var prevPointsDiffFactor = prevPoints.length / points.length;\n      var stepPoints =\n      /*\n       * Here it is important that at the very end of the animation, on the last frame,\n       * we render the original points without any interpolation.\n       * This is needed because the code above is checking for reference equality to decide if the animation should run\n       * and if we create a new array instance (even if the numbers were the same)\n       * then we would break animations.\n       */\n      t === 1 ? points : points.map((entry, index) => {\n        var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n        if (prevPoints[prevPointIndex]) {\n          var prev = prevPoints[prevPointIndex];\n          return _objectSpread(_objectSpread({}, entry), {}, {\n            x: interpolate(prev.x, entry.x, t),\n            y: interpolate(prev.y, entry.y, t)\n          });\n        }\n        return entry;\n      });\n      var stepBaseLine;\n      if (isNumber(baseLine)) {\n        stepBaseLine = interpolate(prevBaseLine, baseLine, t);\n      } else if (isNullish(baseLine) || isNan(baseLine)) {\n        stepBaseLine = interpolate(prevBaseLine, 0, t);\n      } else {\n        stepBaseLine = baseLine.map((entry, index) => {\n          var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n          if (Array.isArray(prevBaseLine) && prevBaseLine[prevPointIndex]) {\n            var prev = prevBaseLine[prevPointIndex];\n            return _objectSpread(_objectSpread({}, entry), {}, {\n              x: interpolate(prev.x, entry.x, t),\n              y: interpolate(prev.y, entry.y, t)\n            });\n          }\n          return entry;\n        });\n      }\n      if (t > 0) {\n        /*\n         * We need to keep the refs in the parent component because we need to remember the last shape of the animation\n         * even if AreaWithAnimation is unmounted as that happens when changing props.\n         *\n         * And we need to update the refs here because here is where the interpolation is computed.\n         * Eslint doesn't like changing function arguments, but we need it so here is an eslint-disable.\n         */\n        // eslint-disable-next-line no-param-reassign\n        previousPointsRef.current = stepPoints;\n        // eslint-disable-next-line no-param-reassign\n        previousBaselineRef.current = stepBaseLine;\n      }\n      return /*#__PURE__*/React.createElement(StaticArea, {\n        points: stepPoints,\n        baseLine: stepBaseLine,\n        needClip: needClip,\n        clipPathId: clipPathId,\n        props: props\n      });\n    }\n    if (t > 0) {\n      // eslint-disable-next-line no-param-reassign\n      previousPointsRef.current = points;\n      // eslint-disable-next-line no-param-reassign\n      previousBaselineRef.current = baseLine;\n    }\n    return /*#__PURE__*/React.createElement(Layer, null, isAnimationActive && /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n      id: \"animationClipPath-\".concat(clipPathId)\n    }, /*#__PURE__*/React.createElement(ClipRect, {\n      alpha: t,\n      points: points,\n      baseLine: baseLine,\n      layout: props.layout,\n      strokeWidth: props.strokeWidth\n    }))), /*#__PURE__*/React.createElement(Layer, {\n      clipPath: \"url(#animationClipPath-\".concat(clipPathId, \")\")\n    }, /*#__PURE__*/React.createElement(StaticArea, {\n      points: points,\n      baseLine: baseLine,\n      needClip: needClip,\n      clipPathId: clipPathId,\n      props: props\n    })));\n  }), /*#__PURE__*/React.createElement(LabelListFromLabelProp, {\n    label: props.label\n  }));\n}\n\n/*\n * This components decides if the area should be animated or not.\n * It also holds the state of the animation.\n */\nfunction RenderArea(_ref8) {\n  var {\n    needClip,\n    clipPathId,\n    props\n  } = _ref8;\n  /*\n   * These two must be refs, not state!\n   * Because we want to store the most recent shape of the animation in case we have to interrupt the animation;\n   * that happens when user initiates another animation before the current one finishes.\n   *\n   * If this was a useState, then every step in the animation would trigger a re-render.\n   * So, useRef it is.\n   */\n  var previousPointsRef = useRef(null);\n  var previousBaselineRef = useRef();\n  return /*#__PURE__*/React.createElement(AreaWithAnimation, {\n    needClip: needClip,\n    clipPathId: clipPathId,\n    props: props,\n    previousPointsRef: previousPointsRef,\n    previousBaselineRef: previousBaselineRef\n  });\n}\nclass AreaWithState extends PureComponent {\n  render() {\n    var _filterProps;\n    var {\n      hide,\n      dot,\n      points,\n      className,\n      top,\n      left,\n      needClip,\n      xAxisId,\n      yAxisId,\n      width,\n      height,\n      id,\n      baseLine\n    } = this.props;\n    if (hide) {\n      return null;\n    }\n    var layerClass = clsx('recharts-area', className);\n    var clipPathId = id;\n    var {\n      r = 3,\n      strokeWidth = 2\n    } = (_filterProps = filterProps(dot, false)) !== null && _filterProps !== void 0 ? _filterProps : {\n      r: 3,\n      strokeWidth: 2\n    };\n    var clipDot = isClipDot(dot);\n    var dotSize = r * 2 + strokeWidth;\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Layer, {\n      className: layerClass\n    }, needClip && /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(GraphicalItemClipPath, {\n      clipPathId: clipPathId,\n      xAxisId: xAxisId,\n      yAxisId: yAxisId\n    }), !clipDot && /*#__PURE__*/React.createElement(\"clipPath\", {\n      id: \"clipPath-dots-\".concat(clipPathId)\n    }, /*#__PURE__*/React.createElement(\"rect\", {\n      x: left - dotSize / 2,\n      y: top - dotSize / 2,\n      width: width + dotSize,\n      height: height + dotSize\n    }))), /*#__PURE__*/React.createElement(RenderArea, {\n      needClip: needClip,\n      clipPathId: clipPathId,\n      props: this.props\n    })), /*#__PURE__*/React.createElement(ActivePoints, {\n      points: points,\n      mainColor: getLegendItemColor(this.props.stroke, this.props.fill),\n      itemDataKey: this.props.dataKey,\n      activeDot: this.props.activeDot\n    }), this.props.isRange && Array.isArray(baseLine) && /*#__PURE__*/React.createElement(ActivePoints, {\n      points: baseLine,\n      mainColor: getLegendItemColor(this.props.stroke, this.props.fill),\n      itemDataKey: this.props.dataKey,\n      activeDot: this.props.activeDot\n    }));\n  }\n}\nvar defaultAreaProps = {\n  activeDot: true,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease',\n  connectNulls: false,\n  dot: false,\n  fill: '#3182bd',\n  fillOpacity: 0.6,\n  hide: false,\n  isAnimationActive: !Global.isSsr,\n  legendType: 'line',\n  stroke: '#3182bd',\n  xAxisId: 0,\n  yAxisId: 0\n};\nfunction AreaImpl(props) {\n  var _useAppSelector;\n  var _resolveDefaultProps = resolveDefaultProps(props, defaultAreaProps),\n    {\n      activeDot,\n      animationBegin,\n      animationDuration,\n      animationEasing,\n      connectNulls,\n      dot,\n      fill,\n      fillOpacity,\n      hide,\n      isAnimationActive,\n      legendType,\n      stroke,\n      xAxisId,\n      yAxisId\n    } = _resolveDefaultProps,\n    everythingElse = _objectWithoutProperties(_resolveDefaultProps, _excluded2);\n  var layout = useChartLayout();\n  var chartName = useChartName();\n  var {\n    needClip\n  } = useNeedsClip(xAxisId, yAxisId);\n  var isPanorama = useIsPanorama();\n  var {\n    points,\n    isRange,\n    baseLine\n  } = (_useAppSelector = useAppSelector(state => selectArea(state, xAxisId, yAxisId, isPanorama, props.id))) !== null && _useAppSelector !== void 0 ? _useAppSelector : {};\n  var plotArea = usePlotArea();\n  if (layout !== 'horizontal' && layout !== 'vertical' || plotArea == null) {\n    // Can't render Area in an unsupported layout\n    return null;\n  }\n  if (chartName !== 'AreaChart' && chartName !== 'ComposedChart') {\n    // There is nothing stopping us from rendering Area in other charts, except for historical reasons. Do we want to allow that?\n    return null;\n  }\n  var {\n    height,\n    width,\n    x: left,\n    y: top\n  } = plotArea;\n  if (!points || !points.length) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(AreaWithState, _extends({}, everythingElse, {\n    activeDot: activeDot,\n    animationBegin: animationBegin,\n    animationDuration: animationDuration,\n    animationEasing: animationEasing,\n    baseLine: baseLine,\n    connectNulls: connectNulls,\n    dot: dot,\n    fill: fill,\n    fillOpacity: fillOpacity,\n    height: height,\n    hide: hide,\n    layout: layout,\n    isAnimationActive: isAnimationActive,\n    isRange: isRange,\n    legendType: legendType,\n    needClip: needClip,\n    points: points,\n    stroke: stroke,\n    width: width,\n    left: left,\n    top: top,\n    xAxisId: xAxisId,\n    yAxisId: yAxisId\n  }));\n}\nexport var getBaseValue = (layout, chartBaseValue, itemBaseValue, xAxis, yAxis) => {\n  // The baseValue can be defined both on the AreaChart, and on the Area.\n  // The value for the item takes precedence.\n  var baseValue = itemBaseValue !== null && itemBaseValue !== void 0 ? itemBaseValue : chartBaseValue;\n  if (isNumber(baseValue)) {\n    return baseValue;\n  }\n  var numericAxis = layout === 'horizontal' ? yAxis : xAxis;\n  // @ts-expect-error d3scale .domain() returns unknown, Math.max expects number\n  var domain = numericAxis.scale.domain();\n  if (numericAxis.type === 'number') {\n    var domainMax = Math.max(domain[0], domain[1]);\n    var domainMin = Math.min(domain[0], domain[1]);\n    if (baseValue === 'dataMin') {\n      return domainMin;\n    }\n    if (baseValue === 'dataMax') {\n      return domainMax;\n    }\n    return domainMax < 0 ? domainMax : Math.max(Math.min(domain[0], domain[1]), 0);\n  }\n  if (baseValue === 'dataMin') {\n    return domain[0];\n  }\n  if (baseValue === 'dataMax') {\n    return domain[1];\n  }\n  return domain[0];\n};\nexport function computeArea(_ref9) {\n  var {\n    areaSettings: {\n      connectNulls,\n      baseValue: itemBaseValue,\n      dataKey\n    },\n    stackedData,\n    layout,\n    chartBaseValue,\n    xAxis,\n    yAxis,\n    displayedData,\n    dataStartIndex,\n    xAxisTicks,\n    yAxisTicks,\n    bandSize\n  } = _ref9;\n  var hasStack = stackedData && stackedData.length;\n  var baseValue = getBaseValue(layout, chartBaseValue, itemBaseValue, xAxis, yAxis);\n  var isHorizontalLayout = layout === 'horizontal';\n  var isRange = false;\n  var points = displayedData.map((entry, index) => {\n    var value;\n    if (hasStack) {\n      value = stackedData[dataStartIndex + index];\n    } else {\n      value = getValueByDataKey(entry, dataKey);\n      if (!Array.isArray(value)) {\n        value = [baseValue, value];\n      } else {\n        isRange = true;\n      }\n    }\n    var isBreakPoint = value[1] == null || hasStack && !connectNulls && getValueByDataKey(entry, dataKey) == null;\n    if (isHorizontalLayout) {\n      return {\n        x: getCateCoordinateOfLine({\n          axis: xAxis,\n          ticks: xAxisTicks,\n          bandSize,\n          entry,\n          index\n        }),\n        y: isBreakPoint ? null : yAxis.scale(value[1]),\n        value,\n        payload: entry\n      };\n    }\n    return {\n      x: isBreakPoint ? null : xAxis.scale(value[1]),\n      y: getCateCoordinateOfLine({\n        axis: yAxis,\n        ticks: yAxisTicks,\n        bandSize,\n        entry,\n        index\n      }),\n      value,\n      payload: entry\n    };\n  });\n  var baseLine;\n  if (hasStack || isRange) {\n    baseLine = points.map(entry => {\n      var x = Array.isArray(entry.value) ? entry.value[0] : null;\n      if (isHorizontalLayout) {\n        return {\n          x: entry.x,\n          y: x != null && entry.y != null ? yAxis.scale(x) : null,\n          payload: entry.payload\n        };\n      }\n      return {\n        x: x != null ? xAxis.scale(x) : null,\n        y: entry.y,\n        payload: entry.payload\n      };\n    });\n  } else {\n    baseLine = isHorizontalLayout ? yAxis.scale(baseValue) : xAxis.scale(baseValue);\n  }\n  return {\n    points,\n    baseLine,\n    isRange\n  };\n}\nexport function Area(outsideProps) {\n  var props = resolveDefaultProps(outsideProps, defaultAreaProps);\n  var isPanorama = useIsPanorama();\n  // Report all props to Redux store first, before calling any hooks, to avoid circular dependencies.\n  return /*#__PURE__*/React.createElement(RegisterGraphicalItemId, {\n    id: props.id,\n    type: \"area\"\n  }, id => /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(SetLegendPayload, {\n    legendPayload: computeLegendPayloadFromAreaData(props)\n  }), /*#__PURE__*/React.createElement(SetTooltipEntrySettings, {\n    fn: getTooltipEntrySettings,\n    args: props\n  }), /*#__PURE__*/React.createElement(SetCartesianGraphicalItem, {\n    type: \"area\",\n    id: id,\n    data: props.data,\n    dataKey: props.dataKey,\n    xAxisId: props.xAxisId,\n    yAxisId: props.yAxisId,\n    zAxisId: 0,\n    stackId: getNormalizedStackId(props.stackId),\n    hide: props.hide,\n    barSize: undefined,\n    baseValue: props.baseValue,\n    isPanorama: isPanorama,\n    connectNulls: props.connectNulls\n  }), /*#__PURE__*/React.createElement(AreaImpl, _extends({}, props, {\n    id: id\n  }))));\n}\nArea.displayName = 'Area';"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,IAAI,CAAC;EACpBC,UAAU,GAAG,CAAC,WAAW,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,cAAc,EAAE,KAAK,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,mBAAmB,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC;AAC/M,SAASC,wBAAwBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,IAAI,IAAID,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,CAAC;IAAEC,CAAC;IAAEC,CAAC,GAAGC,6BAA6B,CAACL,CAAC,EAAEC,CAAC,CAAC;EAAE,IAAIK,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGF,MAAM,CAACC,qBAAqB,CAACP,CAAC,CAAC;IAAE,KAAKG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,CAAC,CAACC,MAAM,EAAEN,CAAC,EAAE,EAAED,CAAC,GAAGM,CAAC,CAACL,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKF,CAAC,CAACS,OAAO,CAACR,CAAC,CAAC,IAAI,CAAC,CAAC,CAACS,oBAAoB,CAACC,IAAI,CAACZ,CAAC,EAAEE,CAAC,CAAC,KAAKE,CAAC,CAACF,CAAC,CAAC,GAAGF,CAAC,CAACE,CAAC,CAAC,CAAC;EAAE;EAAE,OAAOE,CAAC;AAAE;AACrU,SAASC,6BAA6BA,CAACF,CAAC,EAAEH,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIG,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIF,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIO,CAAC,IAAIL,CAAC,EAAE,IAAI,CAAC,CAAC,CAACU,cAAc,CAACD,IAAI,CAACT,CAAC,EAAEK,CAAC,CAAC,EAAE;IAAE,IAAI,CAAC,CAAC,KAAKR,CAAC,CAACU,OAAO,CAACF,CAAC,CAAC,EAAE;IAAUP,CAAC,CAACO,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;EAAE;EAAE,OAAOP,CAAC;AAAE;AACtM,SAASa,OAAOA,CAACd,CAAC,EAAEG,CAAC,EAAE;EAAE,IAAIF,CAAC,GAAGK,MAAM,CAACS,IAAI,CAACf,CAAC,CAAC;EAAE,IAAIM,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIL,CAAC,GAAGI,MAAM,CAACC,qBAAqB,CAACP,CAAC,CAAC;IAAEG,CAAC,KAAKD,CAAC,GAAGA,CAAC,CAACc,MAAM,CAAC,UAAUb,CAAC,EAAE;MAAE,OAAOG,MAAM,CAACW,wBAAwB,CAACjB,CAAC,EAAEG,CAAC,CAAC,CAACe,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEjB,CAAC,CAACkB,IAAI,CAACC,KAAK,CAACnB,CAAC,EAAEC,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AAC9P,SAASoB,aAAaA,CAACrB,CAAC,EAAE;EAAE,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,SAAS,CAACb,MAAM,EAAEN,CAAC,EAAE,EAAE;IAAE,IAAIF,CAAC,GAAG,IAAI,IAAIqB,SAAS,CAACnB,CAAC,CAAC,GAAGmB,SAAS,CAACnB,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGW,OAAO,CAACR,MAAM,CAACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACsB,OAAO,CAAC,UAAUpB,CAAC,EAAE;MAAEqB,eAAe,CAACxB,CAAC,EAAEG,CAAC,EAAEF,CAAC,CAACE,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGG,MAAM,CAACmB,yBAAyB,GAAGnB,MAAM,CAACoB,gBAAgB,CAAC1B,CAAC,EAAEM,MAAM,CAACmB,yBAAyB,CAACxB,CAAC,CAAC,CAAC,GAAGa,OAAO,CAACR,MAAM,CAACL,CAAC,CAAC,CAAC,CAACsB,OAAO,CAAC,UAAUpB,CAAC,EAAE;MAAEG,MAAM,CAACqB,cAAc,CAAC3B,CAAC,EAAEG,CAAC,EAAEG,MAAM,CAACW,wBAAwB,CAAChB,CAAC,EAAEE,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOH,CAAC;AAAE;AACtb,SAASwB,eAAeA,CAACxB,CAAC,EAAEG,CAAC,EAAEF,CAAC,EAAE;EAAE,OAAO,CAACE,CAAC,GAAGyB,cAAc,CAACzB,CAAC,CAAC,KAAKH,CAAC,GAAGM,MAAM,CAACqB,cAAc,CAAC3B,CAAC,EAAEG,CAAC,EAAE;IAAE0B,KAAK,EAAE5B,CAAC;IAAEiB,UAAU,EAAE,CAAC,CAAC;IAAEY,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAG/B,CAAC,CAACG,CAAC,CAAC,GAAGF,CAAC,EAAED,CAAC;AAAE;AACnL,SAAS4B,cAAcA,CAAC3B,CAAC,EAAE;EAAE,IAAIG,CAAC,GAAG4B,YAAY,CAAC/B,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOG,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAAS4B,YAAYA,CAAC/B,CAAC,EAAEE,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOF,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAID,CAAC,GAAGC,CAAC,CAACgC,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKlC,CAAC,EAAE;IAAE,IAAII,CAAC,GAAGJ,CAAC,CAACY,IAAI,CAACX,CAAC,EAAEE,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAI+B,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKhC,CAAC,GAAGiC,MAAM,GAAGC,MAAM,EAAEpC,CAAC,CAAC;AAAE;AACvT,SAASqC,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGhC,MAAM,CAACiC,MAAM,GAAGjC,MAAM,CAACiC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUhC,CAAC,EAAE;IAAE,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,SAAS,CAACb,MAAM,EAAET,CAAC,EAAE,EAAE;MAAE,IAAIC,CAAC,GAAGqB,SAAS,CAACtB,CAAC,CAAC;MAAE,KAAK,IAAIG,CAAC,IAAIF,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEY,cAAc,CAACD,IAAI,CAACX,CAAC,EAAEE,CAAC,CAAC,KAAKK,CAAC,CAACL,CAAC,CAAC,GAAGF,CAAC,CAACE,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOK,CAAC;EAAE,CAAC,EAAE8B,QAAQ,CAAClB,KAAK,CAAC,IAAI,EAAEE,SAAS,CAAC;AAAE;AACnR,OAAO,KAAKmB,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,EAAEC,WAAW,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACpE,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,GAAG,QAAQ,cAAc;AAClC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,iCAAiC,EAAEC,sBAAsB,QAAQ,wBAAwB;AAClG,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,WAAW,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,mBAAmB;AAC3E,SAASC,uBAAuB,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,iBAAiB,QAAQ,oBAAoB;AACzH,SAASC,WAAW,EAAEC,SAAS,QAAQ,oBAAoB;AAC3D,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,qBAAqB,EAAEC,YAAY,QAAQ,yBAAyB;AAC7E,SAASC,UAAU,QAAQ,kCAAkC;AAC7D,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,mBAAmB,QAAQ,6BAA6B;AACjE,SAASC,mBAAmB,QAAQ,6BAA6B;AACjE,SAASC,WAAW,QAAQ,UAAU;AACtC,SAASC,uBAAuB,QAAQ,oCAAoC;AAC5E,SAASC,yBAAyB,QAAQ,2BAA2B;AACrE,SAASC,qBAAqB,QAAQ,+BAA+B;AACrE,SAASC,iBAAiB,QAAQ,gCAAgC;;AAElE;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASC,kBAAkBA,CAACC,MAAM,EAAEC,IAAI,EAAE;EACxC,OAAOD,MAAM,IAAIA,MAAM,KAAK,MAAM,GAAGA,MAAM,GAAGC,IAAI;AACpD;AACA,IAAIC,gCAAgC,GAAGC,KAAK,IAAI;EAC9C,IAAI;IACFC,OAAO;IACPC,IAAI;IACJL,MAAM;IACNC,IAAI;IACJK,UAAU;IACVC;EACF,CAAC,GAAGJ,KAAK;EACT,OAAO,CAAC;IACNK,QAAQ,EAAED,IAAI;IACdH,OAAO;IACPK,IAAI,EAAEH,UAAU;IAChBI,KAAK,EAAEX,kBAAkB,CAACC,MAAM,EAAEC,IAAI,CAAC;IACvCtD,KAAK,EAAE8B,kBAAkB,CAAC4B,IAAI,EAAED,OAAO,CAAC;IACxCO,OAAO,EAAER;EACX,CAAC,CAAC;AACJ,CAAC;AACD,SAASS,uBAAuBA,CAACT,KAAK,EAAE;EACtC,IAAI;IACFC,OAAO;IACPS,IAAI;IACJb,MAAM;IACNc,WAAW;IACXb,IAAI;IACJI,IAAI;IACJE,IAAI;IACJQ;EACF,CAAC,GAAGZ,KAAK;EACT,OAAO;IACLa,iBAAiB,EAAEH,IAAI;IACvBI,SAAS,EAAEC,SAAS;IACpBC,QAAQ,EAAE;MACRnB,MAAM;MACNc,WAAW;MACXb,IAAI;MACJG,OAAO;MACPgB,OAAO,EAAEF,SAAS;MAClBb,IAAI,EAAE5B,kBAAkB,CAAC4B,IAAI,EAAED,OAAO,CAAC;MACvCG,IAAI;MACJE,IAAI,EAAEN,KAAK,CAACkB,WAAW;MACvBX,KAAK,EAAEX,kBAAkB,CAACC,MAAM,EAAEC,IAAI,CAAC;MACvCc;IACF;EACF,CAAC;AACH;AACA,IAAIO,aAAa,GAAGA,CAACC,MAAM,EAAEpB,KAAK,KAAK;EACrC,IAAIqB,OAAO;EACX,IAAI,aAAajE,KAAK,CAACkE,cAAc,CAACF,MAAM,CAAC,EAAE;IAC7CC,OAAO,GAAG,aAAajE,KAAK,CAACmE,YAAY,CAACH,MAAM,EAAEpB,KAAK,CAAC;EAC1D,CAAC,MAAM,IAAI,OAAOoB,MAAM,KAAK,UAAU,EAAE;IACvCC,OAAO,GAAGD,MAAM,CAACpB,KAAK,CAAC;EACzB,CAAC,MAAM;IACL,IAAIwB,SAAS,GAAG/D,IAAI,CAAC,mBAAmB,EAAE,OAAO2D,MAAM,KAAK,SAAS,GAAGA,MAAM,CAACI,SAAS,GAAG,EAAE,CAAC;IAC9FH,OAAO,GAAG,aAAajE,KAAK,CAACqE,aAAa,CAAC9D,GAAG,EAAEV,QAAQ,CAAC,CAAC,CAAC,EAAE+C,KAAK,EAAE;MAClEwB,SAAS,EAAEA;IACb,CAAC,CAAC,CAAC;EACL;EACA,OAAOH,OAAO;AAChB,CAAC;AACD,SAASK,gBAAgBA,CAACC,MAAM,EAAEC,GAAG,EAAE;EACrC,IAAID,MAAM,IAAI,IAAI,EAAE;IAClB,OAAO,KAAK;EACd;EACA,IAAIC,GAAG,EAAE;IACP,OAAO,IAAI;EACb;EACA,OAAOD,MAAM,CAACvG,MAAM,KAAK,CAAC;AAC5B;AACA,SAASyG,IAAIA,CAACC,IAAI,EAAE;EAClB,IAAI;IACFC,UAAU;IACVJ,MAAM;IACN3B;EACF,CAAC,GAAG8B,IAAI;EACR,IAAI;IACFE,QAAQ;IACRJ,GAAG;IACH3B;EACF,CAAC,GAAGD,KAAK;EACT,IAAI,CAAC0B,gBAAgB,CAACC,MAAM,EAAEC,GAAG,CAAC,EAAE;IAClC,OAAO,IAAI;EACb;EACA,IAAIK,OAAO,GAAGxD,SAAS,CAACmD,GAAG,CAAC;EAC5B,IAAIM,SAAS,GAAGxC,qBAAqB,CAACM,KAAK,CAAC;EAC5C,IAAImC,cAAc,GAAG3D,WAAW,CAACoD,GAAG,EAAE,IAAI,CAAC;EAC3C,IAAIQ,IAAI,GAAGT,MAAM,CAACU,GAAG,CAAC,CAACC,KAAK,EAAEvH,CAAC,KAAK;IAClC,IAAIwH,QAAQ,GAAGvG,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;MACvDwG,GAAG,EAAE,MAAM,CAACC,MAAM,CAAC1H,CAAC,CAAC;MACrBD,CAAC,EAAE;IACL,CAAC,EAAEoH,SAAS,CAAC,EAAEC,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE;MAClCO,KAAK,EAAE3H,CAAC;MACR4H,EAAE,EAAEL,KAAK,CAACM,CAAC;MACXC,EAAE,EAAEP,KAAK,CAACQ,CAAC;MACX7C,OAAO;MACPzD,KAAK,EAAE8F,KAAK,CAAC9F,KAAK;MAClBgE,OAAO,EAAE8B,KAAK,CAAC9B,OAAO;MACtBmB;IACF,CAAC,CAAC;IACF,OAAOR,aAAa,CAACS,GAAG,EAAEW,QAAQ,CAAC;EACrC,CAAC,CAAC;EACF,IAAIQ,SAAS,GAAG;IACdC,QAAQ,EAAEhB,QAAQ,GAAG,gBAAgB,CAACS,MAAM,CAACR,OAAO,GAAG,EAAE,GAAG,OAAO,CAAC,CAACQ,MAAM,CAACV,UAAU,EAAE,GAAG,CAAC,GAAGhB;EACjG,CAAC;EACD,OAAO,aAAa3D,KAAK,CAACqE,aAAa,CAAC7D,KAAK,EAAEX,QAAQ,CAAC;IACtDuE,SAAS,EAAE;EACb,CAAC,EAAEuB,SAAS,CAAC,EAAEX,IAAI,CAAC;AACtB;AACA,SAASa,qBAAqBA,CAACC,KAAK,EAAE;EACpC,IAAI;IACFC,UAAU;IACVC,QAAQ;IACRzB;EACF,CAAC,GAAGuB,KAAK;EACT,IAAIG,gBAAgB,GAAG1B,MAAM,CAACU,GAAG,CAACiB,KAAK,IAAI;IACzC,IAAIC,OAAO,GAAG;MACZX,CAAC,EAAEU,KAAK,CAACV,CAAC;MACVE,CAAC,EAAEQ,KAAK,CAACR,CAAC;MACVU,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE;IACV,CAAC;IACD,OAAOzH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEuH,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;MACnD/G,KAAK,EAAE8G,KAAK,CAAC9G,KAAK;MAClBgE,OAAO,EAAE8C,KAAK,CAAC9C,OAAO;MACtBkD,aAAa,EAAE3C,SAAS;MACxBwC,OAAO;MACPzD,IAAI,EAAEiB;IACR,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO,aAAa3D,KAAK,CAACqE,aAAa,CAAC5D,iCAAiC,EAAE;IACzErB,KAAK,EAAE2G,UAAU,GAAGE,gBAAgB,GAAG;EACzC,CAAC,EAAED,QAAQ,CAAC;AACd;AACA,SAASO,UAAUA,CAACC,KAAK,EAAE;EACzB,IAAI;IACFjC,MAAM;IACNkC,QAAQ;IACR7B,QAAQ;IACRD,UAAU;IACV/B;EACF,CAAC,GAAG4D,KAAK;EACT,IAAI;IACFE,MAAM;IACNxD,IAAI;IACJT,MAAM;IACNkE,YAAY;IACZC;EACF,CAAC,GAAGhE,KAAK;EACT,IAAI;MACAiE;IACF,CAAC,GAAGjE,KAAK;IACTkE,cAAc,GAAGxJ,wBAAwB,CAACsF,KAAK,EAAExF,SAAS,CAAC;EAC7D,IAAI2J,aAAa,GAAGzE,qBAAqB,CAACwE,cAAc,CAAC;EACzD,OAAO,aAAa9G,KAAK,CAACqE,aAAa,CAACrE,KAAK,CAACgH,QAAQ,EAAE,IAAI,EAAE,CAACzC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACvG,MAAM,IAAI,CAAC,IAAI,aAAagC,KAAK,CAACqE,aAAa,CAAC7D,KAAK,EAAE;IAC3KoF,QAAQ,EAAEhB,QAAQ,GAAG,gBAAgB,CAACS,MAAM,CAACV,UAAU,EAAE,GAAG,CAAC,GAAGhB;EAClE,CAAC,EAAE,aAAa3D,KAAK,CAACqE,aAAa,CAAC/D,KAAK,EAAET,QAAQ,CAAC,CAAC,CAAC,EAAEkH,aAAa,EAAE;IACrEF,EAAE,EAAEA,EAAE;IACNtC,MAAM,EAAEA,MAAM;IACdoC,YAAY,EAAEA,YAAY;IAC1BzD,IAAI,EAAEA,IAAI;IACVuD,QAAQ,EAAEA,QAAQ;IAClBC,MAAM,EAAEA,MAAM;IACdjE,MAAM,EAAE,MAAM;IACd2B,SAAS,EAAE;EACb,CAAC,CAAC,CAAC,EAAE3B,MAAM,KAAK,MAAM,IAAI,aAAazC,KAAK,CAACqE,aAAa,CAAC/D,KAAK,EAAET,QAAQ,CAAC,CAAC,CAAC,EAAEkH,aAAa,EAAE;IAC5F3C,SAAS,EAAE,qBAAqB;IAChCsC,MAAM,EAAEA,MAAM;IACdxD,IAAI,EAAEA,IAAI;IACVyD,YAAY,EAAEA,YAAY;IAC1BjE,IAAI,EAAE,MAAM;IACZ6B,MAAM,EAAEA;EACV,CAAC,CAAC,CAAC,EAAE9B,MAAM,KAAK,MAAM,IAAImE,OAAO,IAAI,aAAa5G,KAAK,CAACqE,aAAa,CAAC/D,KAAK,EAAET,QAAQ,CAAC,CAAC,CAAC,EAAEkH,aAAa,EAAE;IACvG3C,SAAS,EAAE,qBAAqB;IAChCsC,MAAM,EAAEA,MAAM;IACdxD,IAAI,EAAEA,IAAI;IACVyD,YAAY,EAAEA,YAAY;IAC1BjE,IAAI,EAAE,MAAM;IACZ6B,MAAM,EAAEkC;EACV,CAAC,CAAC,CAAC,CAAC,EAAE,aAAazG,KAAK,CAACqE,aAAa,CAACI,IAAI,EAAE;IAC3CF,MAAM,EAAEA,MAAM;IACd3B,KAAK,EAAEkE,cAAc;IACrBnC,UAAU,EAAEA;EACd,CAAC,CAAC,CAAC;AACL;AACA,SAASsC,YAAYA,CAACC,KAAK,EAAE;EAC3B,IAAI;IACFC,KAAK;IACLV,QAAQ;IACRlC,MAAM;IACNhB;EACF,CAAC,GAAG2D,KAAK;EACT,IAAIE,MAAM,GAAG7C,MAAM,CAAC,CAAC,CAAC,CAACmB,CAAC;EACxB,IAAI2B,IAAI,GAAG9C,MAAM,CAACA,MAAM,CAACvG,MAAM,GAAG,CAAC,CAAC,CAAC0H,CAAC;EACtC,IAAI,CAACxD,mBAAmB,CAACkF,MAAM,CAAC,IAAI,CAAClF,mBAAmB,CAACmF,IAAI,CAAC,EAAE;IAC9D,OAAO,IAAI;EACb;EACA,IAAIhB,MAAM,GAAGc,KAAK,GAAGG,IAAI,CAACC,GAAG,CAACH,MAAM,GAAGC,IAAI,CAAC;EAC5C,IAAIG,IAAI,GAAGF,IAAI,CAACG,GAAG,CAAC,GAAGlD,MAAM,CAACU,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACM,CAAC,IAAI,CAAC,CAAC,CAAC;EACzD,IAAIzE,QAAQ,CAAC0F,QAAQ,CAAC,EAAE;IACtBe,IAAI,GAAGF,IAAI,CAACG,GAAG,CAAChB,QAAQ,EAAEe,IAAI,CAAC;EACjC,CAAC,MAAM,IAAIf,QAAQ,IAAIiB,KAAK,CAACC,OAAO,CAAClB,QAAQ,CAAC,IAAIA,QAAQ,CAACzI,MAAM,EAAE;IACjEwJ,IAAI,GAAGF,IAAI,CAACG,GAAG,CAAC,GAAGhB,QAAQ,CAACxB,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACM,CAAC,IAAI,CAAC,CAAC,EAAEgC,IAAI,CAAC;EAC/D;EACA,IAAIzG,QAAQ,CAACyG,IAAI,CAAC,EAAE;IAClB,OAAO,aAAaxH,KAAK,CAACqE,aAAa,CAAC,MAAM,EAAE;MAC9CmB,CAAC,EAAE,CAAC;MACJE,CAAC,EAAE0B,MAAM,GAAGC,IAAI,GAAGD,MAAM,GAAGA,MAAM,GAAGf,MAAM;MAC3CD,KAAK,EAAEoB,IAAI,IAAIjE,WAAW,GAAGqE,QAAQ,CAAC,EAAE,CAACvC,MAAM,CAAC9B,WAAW,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;MACtE8C,MAAM,EAAEiB,IAAI,CAACO,KAAK,CAACxB,MAAM;IAC3B,CAAC,CAAC;EACJ;EACA,OAAO,IAAI;AACb;AACA,SAASyB,cAAcA,CAACC,KAAK,EAAE;EAC7B,IAAI;IACFZ,KAAK;IACLV,QAAQ;IACRlC,MAAM;IACNhB;EACF,CAAC,GAAGwE,KAAK;EACT,IAAIC,MAAM,GAAGzD,MAAM,CAAC,CAAC,CAAC,CAACiB,CAAC;EACxB,IAAIyC,IAAI,GAAG1D,MAAM,CAACA,MAAM,CAACvG,MAAM,GAAG,CAAC,CAAC,CAACwH,CAAC;EACtC,IAAI,CAACtD,mBAAmB,CAAC8F,MAAM,CAAC,IAAI,CAAC9F,mBAAmB,CAAC+F,IAAI,CAAC,EAAE;IAC9D,OAAO,IAAI;EACb;EACA,IAAI7B,KAAK,GAAGe,KAAK,GAAGG,IAAI,CAACC,GAAG,CAACS,MAAM,GAAGC,IAAI,CAAC;EAC3C,IAAIC,IAAI,GAAGZ,IAAI,CAACG,GAAG,CAAC,GAAGlD,MAAM,CAACU,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACQ,CAAC,IAAI,CAAC,CAAC,CAAC;EACzD,IAAI3E,QAAQ,CAAC0F,QAAQ,CAAC,EAAE;IACtByB,IAAI,GAAGZ,IAAI,CAACG,GAAG,CAAChB,QAAQ,EAAEyB,IAAI,CAAC;EACjC,CAAC,MAAM,IAAIzB,QAAQ,IAAIiB,KAAK,CAACC,OAAO,CAAClB,QAAQ,CAAC,IAAIA,QAAQ,CAACzI,MAAM,EAAE;IACjEkK,IAAI,GAAGZ,IAAI,CAACG,GAAG,CAAC,GAAGhB,QAAQ,CAACxB,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACQ,CAAC,IAAI,CAAC,CAAC,EAAEwC,IAAI,CAAC;EAC/D;EACA,IAAInH,QAAQ,CAACmH,IAAI,CAAC,EAAE;IAClB,OAAO,aAAalI,KAAK,CAACqE,aAAa,CAAC,MAAM,EAAE;MAC9CmB,CAAC,EAAEwC,MAAM,GAAGC,IAAI,GAAGD,MAAM,GAAGA,MAAM,GAAG5B,KAAK;MAC1CV,CAAC,EAAE,CAAC;MACJU,KAAK,EAAEA,KAAK;MACZC,MAAM,EAAEiB,IAAI,CAACO,KAAK,CAACK,IAAI,IAAI3E,WAAW,GAAGqE,QAAQ,CAAC,EAAE,CAACvC,MAAM,CAAC9B,WAAW,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;IACpF,CAAC,CAAC;EACJ;EACA,OAAO,IAAI;AACb;AACA,SAAS4E,QAAQA,CAACC,KAAK,EAAE;EACvB,IAAI;IACFjB,KAAK;IACLT,MAAM;IACNnC,MAAM;IACNkC,QAAQ;IACRlD;EACF,CAAC,GAAG6E,KAAK;EACT,IAAI1B,MAAM,KAAK,UAAU,EAAE;IACzB,OAAO,aAAa1G,KAAK,CAACqE,aAAa,CAAC4C,YAAY,EAAE;MACpDE,KAAK,EAAEA,KAAK;MACZ5C,MAAM,EAAEA,MAAM;MACdkC,QAAQ,EAAEA,QAAQ;MAClBlD,WAAW,EAAEA;IACf,CAAC,CAAC;EACJ;EACA,OAAO,aAAavD,KAAK,CAACqE,aAAa,CAACyD,cAAc,EAAE;IACtDX,KAAK,EAAEA,KAAK;IACZ5C,MAAM,EAAEA,MAAM;IACdkC,QAAQ,EAAEA,QAAQ;IAClBlD,WAAW,EAAEA;EACf,CAAC,CAAC;AACJ;AACA,SAAS8E,iBAAiBA,CAACC,KAAK,EAAE;EAChC,IAAI;IACF1D,QAAQ;IACRD,UAAU;IACV/B,KAAK;IACL2F,iBAAiB;IACjBC;EACF,CAAC,GAAGF,KAAK;EACT,IAAI;IACF/D,MAAM;IACNkC,QAAQ;IACRgC,iBAAiB;IACjBC,cAAc;IACdC,iBAAiB;IACjBC,eAAe;IACfC,gBAAgB;IAChBC;EACF,CAAC,GAAGlG,KAAK;EACT,IAAImG,WAAW,GAAG/G,cAAc,CAACY,KAAK,EAAE,gBAAgB,CAAC;EACzD,IAAI,CAACoG,WAAW,EAAEC,cAAc,CAAC,GAAG7I,QAAQ,CAAC,KAAK,CAAC;EACnD,IAAI2F,UAAU,GAAG,CAACiD,WAAW;EAC7B,IAAIE,kBAAkB,GAAGhJ,WAAW,CAAC,MAAM;IACzC,IAAI,OAAO4I,cAAc,KAAK,UAAU,EAAE;MACxCA,cAAc,CAAC,CAAC;IAClB;IACAG,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC,EAAE,CAACH,cAAc,CAAC,CAAC;EACpB,IAAIK,oBAAoB,GAAGjJ,WAAW,CAAC,MAAM;IAC3C,IAAI,OAAO2I,gBAAgB,KAAK,UAAU,EAAE;MAC1CA,gBAAgB,CAAC,CAAC;IACpB;IACAI,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC,EAAE,CAACJ,gBAAgB,CAAC,CAAC;EACtB,IAAIO,UAAU,GAAGb,iBAAiB,CAACc,OAAO;EAC1C,IAAIC,YAAY,GAAGd,mBAAmB,CAACa,OAAO;EAC9C,OAAO,aAAarJ,KAAK,CAACqE,aAAa,CAACwB,qBAAqB,EAAE;IAC7DE,UAAU,EAAEA,UAAU;IACtBxB,MAAM,EAAEA;EACV,CAAC,EAAE3B,KAAK,CAACoD,QAAQ,EAAE,aAAahG,KAAK,CAACqE,aAAa,CAAC9B,iBAAiB,EAAE;IACrEwG,WAAW,EAAEA,WAAW;IACxBQ,KAAK,EAAEb,cAAc;IACrBc,QAAQ,EAAEb,iBAAiB;IAC3Bc,QAAQ,EAAEhB,iBAAiB;IAC3BiB,MAAM,EAAEd,eAAe;IACvBE,cAAc,EAAEI,kBAAkB;IAClCL,gBAAgB,EAAEM,oBAAoB;IACtC/D,GAAG,EAAE2D;EACP,CAAC,EAAEvL,CAAC,IAAI;IACN,IAAI4L,UAAU,EAAE;MACd,IAAIO,oBAAoB,GAAGP,UAAU,CAACpL,MAAM,GAAGuG,MAAM,CAACvG,MAAM;MAC5D,IAAI4L,UAAU;MACd;AACN;AACA;AACA;AACA;AACA;AACA;MACMpM,CAAC,KAAK,CAAC,GAAG+G,MAAM,GAAGA,MAAM,CAACU,GAAG,CAAC,CAACC,KAAK,EAAEI,KAAK,KAAK;QAC9C,IAAIuE,cAAc,GAAGvC,IAAI,CAACO,KAAK,CAACvC,KAAK,GAAGqE,oBAAoB,CAAC;QAC7D,IAAIP,UAAU,CAACS,cAAc,CAAC,EAAE;UAC9B,IAAIC,IAAI,GAAGV,UAAU,CAACS,cAAc,CAAC;UACrC,OAAOjL,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;YACjDM,CAAC,EAAE5E,WAAW,CAACkJ,IAAI,CAACtE,CAAC,EAAEN,KAAK,CAACM,CAAC,EAAEhI,CAAC,CAAC;YAClCkI,CAAC,EAAE9E,WAAW,CAACkJ,IAAI,CAACpE,CAAC,EAAER,KAAK,CAACQ,CAAC,EAAElI,CAAC;UACnC,CAAC,CAAC;QACJ;QACA,OAAO0H,KAAK;MACd,CAAC,CAAC;MACF,IAAI6E,YAAY;MAChB,IAAIhJ,QAAQ,CAAC0F,QAAQ,CAAC,EAAE;QACtBsD,YAAY,GAAGnJ,WAAW,CAAC0I,YAAY,EAAE7C,QAAQ,EAAEjJ,CAAC,CAAC;MACvD,CAAC,MAAM,IAAIsD,SAAS,CAAC2F,QAAQ,CAAC,IAAI5F,KAAK,CAAC4F,QAAQ,CAAC,EAAE;QACjDsD,YAAY,GAAGnJ,WAAW,CAAC0I,YAAY,EAAE,CAAC,EAAE9L,CAAC,CAAC;MAChD,CAAC,MAAM;QACLuM,YAAY,GAAGtD,QAAQ,CAACxB,GAAG,CAAC,CAACC,KAAK,EAAEI,KAAK,KAAK;UAC5C,IAAIuE,cAAc,GAAGvC,IAAI,CAACO,KAAK,CAACvC,KAAK,GAAGqE,oBAAoB,CAAC;UAC7D,IAAIjC,KAAK,CAACC,OAAO,CAAC2B,YAAY,CAAC,IAAIA,YAAY,CAACO,cAAc,CAAC,EAAE;YAC/D,IAAIC,IAAI,GAAGR,YAAY,CAACO,cAAc,CAAC;YACvC,OAAOjL,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;cACjDM,CAAC,EAAE5E,WAAW,CAACkJ,IAAI,CAACtE,CAAC,EAAEN,KAAK,CAACM,CAAC,EAAEhI,CAAC,CAAC;cAClCkI,CAAC,EAAE9E,WAAW,CAACkJ,IAAI,CAACpE,CAAC,EAAER,KAAK,CAACQ,CAAC,EAAElI,CAAC;YACnC,CAAC,CAAC;UACJ;UACA,OAAO0H,KAAK;QACd,CAAC,CAAC;MACJ;MACA,IAAI1H,CAAC,GAAG,CAAC,EAAE;QACT;AACR;AACA;AACA;AACA;AACA;AACA;QACQ;QACA+K,iBAAiB,CAACc,OAAO,GAAGO,UAAU;QACtC;QACApB,mBAAmB,CAACa,OAAO,GAAGU,YAAY;MAC5C;MACA,OAAO,aAAa/J,KAAK,CAACqE,aAAa,CAACkC,UAAU,EAAE;QAClDhC,MAAM,EAAEqF,UAAU;QAClBnD,QAAQ,EAAEsD,YAAY;QACtBnF,QAAQ,EAAEA,QAAQ;QAClBD,UAAU,EAAEA,UAAU;QACtB/B,KAAK,EAAEA;MACT,CAAC,CAAC;IACJ;IACA,IAAIpF,CAAC,GAAG,CAAC,EAAE;MACT;MACA+K,iBAAiB,CAACc,OAAO,GAAG9E,MAAM;MAClC;MACAiE,mBAAmB,CAACa,OAAO,GAAG5C,QAAQ;IACxC;IACA,OAAO,aAAazG,KAAK,CAACqE,aAAa,CAAC7D,KAAK,EAAE,IAAI,EAAEiI,iBAAiB,IAAI,aAAazI,KAAK,CAACqE,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAarE,KAAK,CAACqE,aAAa,CAAC,UAAU,EAAE;MACpKwC,EAAE,EAAE,oBAAoB,CAACxB,MAAM,CAACV,UAAU;IAC5C,CAAC,EAAE,aAAa3E,KAAK,CAACqE,aAAa,CAAC8D,QAAQ,EAAE;MAC5ChB,KAAK,EAAE3J,CAAC;MACR+G,MAAM,EAAEA,MAAM;MACdkC,QAAQ,EAAEA,QAAQ;MAClBC,MAAM,EAAE9D,KAAK,CAAC8D,MAAM;MACpBnD,WAAW,EAAEX,KAAK,CAACW;IACrB,CAAC,CAAC,CAAC,CAAC,EAAE,aAAavD,KAAK,CAACqE,aAAa,CAAC7D,KAAK,EAAE;MAC5CoF,QAAQ,EAAE,yBAAyB,CAACP,MAAM,CAACV,UAAU,EAAE,GAAG;IAC5D,CAAC,EAAE,aAAa3E,KAAK,CAACqE,aAAa,CAACkC,UAAU,EAAE;MAC9ChC,MAAM,EAAEA,MAAM;MACdkC,QAAQ,EAAEA,QAAQ;MAClB7B,QAAQ,EAAEA,QAAQ;MAClBD,UAAU,EAAEA,UAAU;MACtB/B,KAAK,EAAEA;IACT,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,CAAC,EAAE,aAAa5C,KAAK,CAACqE,aAAa,CAAC3D,sBAAsB,EAAE;IAC3DsJ,KAAK,EAAEpH,KAAK,CAACoH;EACf,CAAC,CAAC,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzB,IAAI;IACFtF,QAAQ;IACRD,UAAU;IACV/B;EACF,CAAC,GAAGsH,KAAK;EACT;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAI3B,iBAAiB,GAAGpI,MAAM,CAAC,IAAI,CAAC;EACpC,IAAIqI,mBAAmB,GAAGrI,MAAM,CAAC,CAAC;EAClC,OAAO,aAAaH,KAAK,CAACqE,aAAa,CAACgE,iBAAiB,EAAE;IACzDzD,QAAQ,EAAEA,QAAQ;IAClBD,UAAU,EAAEA,UAAU;IACtB/B,KAAK,EAAEA,KAAK;IACZ2F,iBAAiB,EAAEA,iBAAiB;IACpCC,mBAAmB,EAAEA;EACvB,CAAC,CAAC;AACJ;AACA,MAAM2B,aAAa,SAASlK,aAAa,CAAC;EACxCmK,MAAMA,CAAA,EAAG;IACP,IAAIC,YAAY;IAChB,IAAI;MACFrH,IAAI;MACJwB,GAAG;MACHD,MAAM;MACNH,SAAS;MACTkG,GAAG;MACHC,IAAI;MACJ3F,QAAQ;MACR4F,OAAO;MACPC,OAAO;MACPrE,KAAK;MACLC,MAAM;MACNQ,EAAE;MACFJ;IACF,CAAC,GAAG,IAAI,CAAC7D,KAAK;IACd,IAAII,IAAI,EAAE;MACR,OAAO,IAAI;IACb;IACA,IAAI0H,UAAU,GAAGrK,IAAI,CAAC,eAAe,EAAE+D,SAAS,CAAC;IACjD,IAAIO,UAAU,GAAGkC,EAAE;IACnB,IAAI;MACFnJ,CAAC,GAAG,CAAC;MACL6F,WAAW,GAAG;IAChB,CAAC,GAAG,CAAC8G,YAAY,GAAGjJ,WAAW,CAACoD,GAAG,EAAE,KAAK,CAAC,MAAM,IAAI,IAAI6F,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAG;MAChG3M,CAAC,EAAE,CAAC;MACJ6F,WAAW,EAAE;IACf,CAAC;IACD,IAAIsB,OAAO,GAAGxD,SAAS,CAACmD,GAAG,CAAC;IAC5B,IAAImG,OAAO,GAAGjN,CAAC,GAAG,CAAC,GAAG6F,WAAW;IACjC,OAAO,aAAavD,KAAK,CAACqE,aAAa,CAACrE,KAAK,CAACgH,QAAQ,EAAE,IAAI,EAAE,aAAahH,KAAK,CAACqE,aAAa,CAAC7D,KAAK,EAAE;MACpG4D,SAAS,EAAEsG;IACb,CAAC,EAAE9F,QAAQ,IAAI,aAAa5E,KAAK,CAACqE,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAarE,KAAK,CAACqE,aAAa,CAAC7C,qBAAqB,EAAE;MACpHmD,UAAU,EAAEA,UAAU;MACtB6F,OAAO,EAAEA,OAAO;MAChBC,OAAO,EAAEA;IACX,CAAC,CAAC,EAAE,CAAC5F,OAAO,IAAI,aAAa7E,KAAK,CAACqE,aAAa,CAAC,UAAU,EAAE;MAC3DwC,EAAE,EAAE,gBAAgB,CAACxB,MAAM,CAACV,UAAU;IACxC,CAAC,EAAE,aAAa3E,KAAK,CAACqE,aAAa,CAAC,MAAM,EAAE;MAC1CmB,CAAC,EAAE+E,IAAI,GAAGI,OAAO,GAAG,CAAC;MACrBjF,CAAC,EAAE4E,GAAG,GAAGK,OAAO,GAAG,CAAC;MACpBvE,KAAK,EAAEA,KAAK,GAAGuE,OAAO;MACtBtE,MAAM,EAAEA,MAAM,GAAGsE;IACnB,CAAC,CAAC,CAAC,CAAC,EAAE,aAAa3K,KAAK,CAACqE,aAAa,CAAC4F,UAAU,EAAE;MACjDrF,QAAQ,EAAEA,QAAQ;MAClBD,UAAU,EAAEA,UAAU;MACtB/B,KAAK,EAAE,IAAI,CAACA;IACd,CAAC,CAAC,CAAC,EAAE,aAAa5C,KAAK,CAACqE,aAAa,CAAC/C,YAAY,EAAE;MAClDiD,MAAM,EAAEA,MAAM;MACdqG,SAAS,EAAEpI,kBAAkB,CAAC,IAAI,CAACI,KAAK,CAACH,MAAM,EAAE,IAAI,CAACG,KAAK,CAACF,IAAI,CAAC;MACjEmI,WAAW,EAAE,IAAI,CAACjI,KAAK,CAACC,OAAO;MAC/BiI,SAAS,EAAE,IAAI,CAAClI,KAAK,CAACkI;IACxB,CAAC,CAAC,EAAE,IAAI,CAAClI,KAAK,CAACgE,OAAO,IAAIc,KAAK,CAACC,OAAO,CAAClB,QAAQ,CAAC,IAAI,aAAazG,KAAK,CAACqE,aAAa,CAAC/C,YAAY,EAAE;MAClGiD,MAAM,EAAEkC,QAAQ;MAChBmE,SAAS,EAAEpI,kBAAkB,CAAC,IAAI,CAACI,KAAK,CAACH,MAAM,EAAE,IAAI,CAACG,KAAK,CAACF,IAAI,CAAC;MACjEmI,WAAW,EAAE,IAAI,CAACjI,KAAK,CAACC,OAAO;MAC/BiI,SAAS,EAAE,IAAI,CAAClI,KAAK,CAACkI;IACxB,CAAC,CAAC,CAAC;EACL;AACF;AACA,IAAIC,gBAAgB,GAAG;EACrBD,SAAS,EAAE,IAAI;EACfpC,cAAc,EAAE,CAAC;EACjBC,iBAAiB,EAAE,IAAI;EACvBC,eAAe,EAAE,MAAM;EACvBjC,YAAY,EAAE,KAAK;EACnBnC,GAAG,EAAE,KAAK;EACV9B,IAAI,EAAE,SAAS;EACfsI,WAAW,EAAE,GAAG;EAChBhI,IAAI,EAAE,KAAK;EACXyF,iBAAiB,EAAE,CAAC9H,MAAM,CAACsK,KAAK;EAChClI,UAAU,EAAE,MAAM;EAClBN,MAAM,EAAE,SAAS;EACjB+H,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE;AACX,CAAC;AACD,SAASS,QAAQA,CAACtI,KAAK,EAAE;EACvB,IAAIuI,eAAe;EACnB,IAAIC,oBAAoB,GAAGnJ,mBAAmB,CAACW,KAAK,EAAEmI,gBAAgB,CAAC;IACrE;MACED,SAAS;MACTpC,cAAc;MACdC,iBAAiB;MACjBC,eAAe;MACfjC,YAAY;MACZnC,GAAG;MACH9B,IAAI;MACJsI,WAAW;MACXhI,IAAI;MACJyF,iBAAiB;MACjB1F,UAAU;MACVN,MAAM;MACN+H,OAAO;MACPC;IACF,CAAC,GAAGW,oBAAoB;IACxBC,cAAc,GAAG/N,wBAAwB,CAAC8N,oBAAoB,EAAE/N,UAAU,CAAC;EAC7E,IAAIqJ,MAAM,GAAG9E,cAAc,CAAC,CAAC;EAC7B,IAAI0J,SAAS,GAAGzJ,YAAY,CAAC,CAAC;EAC9B,IAAI;IACF+C;EACF,CAAC,GAAGnD,YAAY,CAAC+I,OAAO,EAAEC,OAAO,CAAC;EAClC,IAAIc,UAAU,GAAG5J,aAAa,CAAC,CAAC;EAChC,IAAI;IACF4C,MAAM;IACNqC,OAAO;IACPH;EACF,CAAC,GAAG,CAAC0E,eAAe,GAAGpJ,cAAc,CAACyJ,KAAK,IAAI9J,UAAU,CAAC8J,KAAK,EAAEhB,OAAO,EAAEC,OAAO,EAAEc,UAAU,EAAE3I,KAAK,CAACiE,EAAE,CAAC,CAAC,MAAM,IAAI,IAAIsE,eAAe,KAAK,KAAK,CAAC,GAAGA,eAAe,GAAG,CAAC,CAAC;EACxK,IAAIM,QAAQ,GAAGtJ,WAAW,CAAC,CAAC;EAC5B,IAAIuE,MAAM,KAAK,YAAY,IAAIA,MAAM,KAAK,UAAU,IAAI+E,QAAQ,IAAI,IAAI,EAAE;IACxE;IACA,OAAO,IAAI;EACb;EACA,IAAIH,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAK,eAAe,EAAE;IAC9D;IACA,OAAO,IAAI;EACb;EACA,IAAI;IACFjF,MAAM;IACND,KAAK;IACLZ,CAAC,EAAE+E,IAAI;IACP7E,CAAC,EAAE4E;EACL,CAAC,GAAGmB,QAAQ;EACZ,IAAI,CAAClH,MAAM,IAAI,CAACA,MAAM,CAACvG,MAAM,EAAE;IAC7B,OAAO,IAAI;EACb;EACA,OAAO,aAAagC,KAAK,CAACqE,aAAa,CAAC8F,aAAa,EAAEtK,QAAQ,CAAC,CAAC,CAAC,EAAEwL,cAAc,EAAE;IAClFP,SAAS,EAAEA,SAAS;IACpBpC,cAAc,EAAEA,cAAc;IAC9BC,iBAAiB,EAAEA,iBAAiB;IACpCC,eAAe,EAAEA,eAAe;IAChCnC,QAAQ,EAAEA,QAAQ;IAClBE,YAAY,EAAEA,YAAY;IAC1BnC,GAAG,EAAEA,GAAG;IACR9B,IAAI,EAAEA,IAAI;IACVsI,WAAW,EAAEA,WAAW;IACxB3E,MAAM,EAAEA,MAAM;IACdrD,IAAI,EAAEA,IAAI;IACV0D,MAAM,EAAEA,MAAM;IACd+B,iBAAiB,EAAEA,iBAAiB;IACpC7B,OAAO,EAAEA,OAAO;IAChB7D,UAAU,EAAEA,UAAU;IACtB6B,QAAQ,EAAEA,QAAQ;IAClBL,MAAM,EAAEA,MAAM;IACd9B,MAAM,EAAEA,MAAM;IACd2D,KAAK,EAAEA,KAAK;IACZmE,IAAI,EAAEA,IAAI;IACVD,GAAG,EAAEA,GAAG;IACRE,OAAO,EAAEA,OAAO;IAChBC,OAAO,EAAEA;EACX,CAAC,CAAC,CAAC;AACL;AACA,OAAO,IAAIiB,YAAY,GAAGA,CAAChF,MAAM,EAAEiF,cAAc,EAAEC,aAAa,EAAEC,KAAK,EAAEC,KAAK,KAAK;EACjF;EACA;EACA,IAAIC,SAAS,GAAGH,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAGD,cAAc;EACnG,IAAI5K,QAAQ,CAACgL,SAAS,CAAC,EAAE;IACvB,OAAOA,SAAS;EAClB;EACA,IAAIC,WAAW,GAAGtF,MAAM,KAAK,YAAY,GAAGoF,KAAK,GAAGD,KAAK;EACzD;EACA,IAAII,MAAM,GAAGD,WAAW,CAACE,KAAK,CAACD,MAAM,CAAC,CAAC;EACvC,IAAID,WAAW,CAAC9I,IAAI,KAAK,QAAQ,EAAE;IACjC,IAAIiJ,SAAS,GAAG7E,IAAI,CAACG,GAAG,CAACwE,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IAC9C,IAAIG,SAAS,GAAG9E,IAAI,CAAC+E,GAAG,CAACJ,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IAC9C,IAAIF,SAAS,KAAK,SAAS,EAAE;MAC3B,OAAOK,SAAS;IAClB;IACA,IAAIL,SAAS,KAAK,SAAS,EAAE;MAC3B,OAAOI,SAAS;IAClB;IACA,OAAOA,SAAS,GAAG,CAAC,GAAGA,SAAS,GAAG7E,IAAI,CAACG,GAAG,CAACH,IAAI,CAAC+E,GAAG,CAACJ,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EAChF;EACA,IAAIF,SAAS,KAAK,SAAS,EAAE;IAC3B,OAAOE,MAAM,CAAC,CAAC,CAAC;EAClB;EACA,IAAIF,SAAS,KAAK,SAAS,EAAE;IAC3B,OAAOE,MAAM,CAAC,CAAC,CAAC;EAClB;EACA,OAAOA,MAAM,CAAC,CAAC,CAAC;AAClB,CAAC;AACD,OAAO,SAASK,WAAWA,CAACC,KAAK,EAAE;EACjC,IAAI;IACFC,YAAY,EAAE;MACZ7F,YAAY;MACZoF,SAAS,EAAEH,aAAa;MACxB/I;IACF,CAAC;IACD4J,WAAW;IACX/F,MAAM;IACNiF,cAAc;IACdE,KAAK;IACLC,KAAK;IACLY,aAAa;IACbC,cAAc;IACdC,UAAU;IACVC,UAAU;IACVC;EACF,CAAC,GAAGP,KAAK;EACT,IAAIQ,QAAQ,GAAGN,WAAW,IAAIA,WAAW,CAACzO,MAAM;EAChD,IAAI+N,SAAS,GAAGL,YAAY,CAAChF,MAAM,EAAEiF,cAAc,EAAEC,aAAa,EAAEC,KAAK,EAAEC,KAAK,CAAC;EACjF,IAAIkB,kBAAkB,GAAGtG,MAAM,KAAK,YAAY;EAChD,IAAIE,OAAO,GAAG,KAAK;EACnB,IAAIrC,MAAM,GAAGmI,aAAa,CAACzH,GAAG,CAAC,CAACC,KAAK,EAAEI,KAAK,KAAK;IAC/C,IAAIlG,KAAK;IACT,IAAI2N,QAAQ,EAAE;MACZ3N,KAAK,GAAGqN,WAAW,CAACE,cAAc,GAAGrH,KAAK,CAAC;IAC7C,CAAC,MAAM;MACLlG,KAAK,GAAG+B,iBAAiB,CAAC+D,KAAK,EAAErC,OAAO,CAAC;MACzC,IAAI,CAAC6E,KAAK,CAACC,OAAO,CAACvI,KAAK,CAAC,EAAE;QACzBA,KAAK,GAAG,CAAC2M,SAAS,EAAE3M,KAAK,CAAC;MAC5B,CAAC,MAAM;QACLwH,OAAO,GAAG,IAAI;MAChB;IACF;IACA,IAAIqG,YAAY,GAAG7N,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI2N,QAAQ,IAAI,CAACpG,YAAY,IAAIxF,iBAAiB,CAAC+D,KAAK,EAAErC,OAAO,CAAC,IAAI,IAAI;IAC7G,IAAImK,kBAAkB,EAAE;MACtB,OAAO;QACLxH,CAAC,EAAExE,uBAAuB,CAAC;UACzBkM,IAAI,EAAErB,KAAK;UACXsB,KAAK,EAAEP,UAAU;UACjBE,QAAQ;UACR5H,KAAK;UACLI;QACF,CAAC,CAAC;QACFI,CAAC,EAAEuH,YAAY,GAAG,IAAI,GAAGnB,KAAK,CAACI,KAAK,CAAC9M,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9CA,KAAK;QACLgE,OAAO,EAAE8B;MACX,CAAC;IACH;IACA,OAAO;MACLM,CAAC,EAAEyH,YAAY,GAAG,IAAI,GAAGpB,KAAK,CAACK,KAAK,CAAC9M,KAAK,CAAC,CAAC,CAAC,CAAC;MAC9CsG,CAAC,EAAE1E,uBAAuB,CAAC;QACzBkM,IAAI,EAAEpB,KAAK;QACXqB,KAAK,EAAEN,UAAU;QACjBC,QAAQ;QACR5H,KAAK;QACLI;MACF,CAAC,CAAC;MACFlG,KAAK;MACLgE,OAAO,EAAE8B;IACX,CAAC;EACH,CAAC,CAAC;EACF,IAAIuB,QAAQ;EACZ,IAAIsG,QAAQ,IAAInG,OAAO,EAAE;IACvBH,QAAQ,GAAGlC,MAAM,CAACU,GAAG,CAACC,KAAK,IAAI;MAC7B,IAAIM,CAAC,GAAGkC,KAAK,CAACC,OAAO,CAACzC,KAAK,CAAC9F,KAAK,CAAC,GAAG8F,KAAK,CAAC9F,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI;MAC1D,IAAI4N,kBAAkB,EAAE;QACtB,OAAO;UACLxH,CAAC,EAAEN,KAAK,CAACM,CAAC;UACVE,CAAC,EAAEF,CAAC,IAAI,IAAI,IAAIN,KAAK,CAACQ,CAAC,IAAI,IAAI,GAAGoG,KAAK,CAACI,KAAK,CAAC1G,CAAC,CAAC,GAAG,IAAI;UACvDpC,OAAO,EAAE8B,KAAK,CAAC9B;QACjB,CAAC;MACH;MACA,OAAO;QACLoC,CAAC,EAAEA,CAAC,IAAI,IAAI,GAAGqG,KAAK,CAACK,KAAK,CAAC1G,CAAC,CAAC,GAAG,IAAI;QACpCE,CAAC,EAAER,KAAK,CAACQ,CAAC;QACVtC,OAAO,EAAE8B,KAAK,CAAC9B;MACjB,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,MAAM;IACLqD,QAAQ,GAAGuG,kBAAkB,GAAGlB,KAAK,CAACI,KAAK,CAACH,SAAS,CAAC,GAAGF,KAAK,CAACK,KAAK,CAACH,SAAS,CAAC;EACjF;EACA,OAAO;IACLxH,MAAM;IACNkC,QAAQ;IACRG;EACF,CAAC;AACH;AACA,OAAO,SAASwG,IAAIA,CAACC,YAAY,EAAE;EACjC,IAAIzK,KAAK,GAAGX,mBAAmB,CAACoL,YAAY,EAAEtC,gBAAgB,CAAC;EAC/D,IAAIQ,UAAU,GAAG5J,aAAa,CAAC,CAAC;EAChC;EACA,OAAO,aAAa3B,KAAK,CAACqE,aAAa,CAACjC,uBAAuB,EAAE;IAC/DyE,EAAE,EAAEjE,KAAK,CAACiE,EAAE;IACZ3D,IAAI,EAAE;EACR,CAAC,EAAE2D,EAAE,IAAI,aAAa7G,KAAK,CAACqE,aAAa,CAACrE,KAAK,CAACgH,QAAQ,EAAE,IAAI,EAAE,aAAahH,KAAK,CAACqE,aAAa,CAACvC,gBAAgB,EAAE;IACjHwL,aAAa,EAAE3K,gCAAgC,CAACC,KAAK;EACvD,CAAC,CAAC,EAAE,aAAa5C,KAAK,CAACqE,aAAa,CAAC9C,uBAAuB,EAAE;IAC5DgM,EAAE,EAAElK,uBAAuB;IAC3BmK,IAAI,EAAE5K;EACR,CAAC,CAAC,EAAE,aAAa5C,KAAK,CAACqE,aAAa,CAAChC,yBAAyB,EAAE;IAC9Da,IAAI,EAAE,MAAM;IACZ2D,EAAE,EAAEA,EAAE;IACNvD,IAAI,EAAEV,KAAK,CAACU,IAAI;IAChBT,OAAO,EAAED,KAAK,CAACC,OAAO;IACtB2H,OAAO,EAAE5H,KAAK,CAAC4H,OAAO;IACtBC,OAAO,EAAE7H,KAAK,CAAC6H,OAAO;IACtBgD,OAAO,EAAE,CAAC;IACVC,OAAO,EAAEzM,oBAAoB,CAAC2B,KAAK,CAAC8K,OAAO,CAAC;IAC5C1K,IAAI,EAAEJ,KAAK,CAACI,IAAI;IAChB2K,OAAO,EAAEhK,SAAS;IAClBoI,SAAS,EAAEnJ,KAAK,CAACmJ,SAAS;IAC1BR,UAAU,EAAEA,UAAU;IACtB5E,YAAY,EAAE/D,KAAK,CAAC+D;EACtB,CAAC,CAAC,EAAE,aAAa3G,KAAK,CAACqE,aAAa,CAAC6G,QAAQ,EAAErL,QAAQ,CAAC,CAAC,CAAC,EAAE+C,KAAK,EAAE;IACjEiE,EAAE,EAAEA;EACN,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AACAuG,IAAI,CAACQ,WAAW,GAAG,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}