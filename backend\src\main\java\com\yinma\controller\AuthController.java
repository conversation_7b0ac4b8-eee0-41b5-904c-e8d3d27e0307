package com.yinma.controller;

import com.yinma.common.Result;
import com.yinma.dto.LoginRequest;
import com.yinma.dto.LoginResponse;
import com.yinma.dto.RefreshTokenRequest;
import com.yinma.security.UserPrincipal;
import com.yinma.service.AuthService;
import com.yinma.util.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
@Tag(name = "认证管理", description = "用户登录、登出、Token刷新等认证相关接口")
public class AuthController {

    private final AuthService authService;
    private final JwtUtil jwtUtil;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户名密码登录，返回访问令牌和刷新令牌")
    public Result<LoginResponse> login(@Valid @RequestBody LoginRequest request) {
        log.info("用户登录请求: {}", request.getUsername());
        
        try {
            LoginResponse response = authService.login(request);
            log.info("用户 {} 登录成功", request.getUsername());
            return Result.success(response);
        } catch (Exception e) {
            log.error("用户 {} 登录失败: {}", request.getUsername(), e.getMessage());
            return Result.error("登录失败: " + e.getMessage());
        }
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    @Operation(summary = "用户登出", description = "用户登出，清除认证信息")
    public Result<Void> logout() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof UserPrincipal) {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            log.info("用户 {} 登出", userPrincipal.getUsername());
        }
        
        // 清除安全上下文
        SecurityContextHolder.clearContext();
        
        return Result.success("登出成功");
    }

    /**
     * 刷新访问令牌
     */
    @PostMapping("/refresh")
    @Operation(summary = "刷新令牌", description = "使用刷新令牌获取新的访问令牌")
    public Result<LoginResponse> refreshToken(@Valid @RequestBody RefreshTokenRequest request) {
        log.debug("刷新令牌请求");
        
        try {
            LoginResponse response = authService.refreshToken(request.getRefreshToken());
            return Result.success(response);
        } catch (Exception e) {
            log.error("刷新令牌失败: {}", e.getMessage());
            return Result.error("刷新令牌失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的基本信息")
    public Result<UserPrincipal> getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        if (authentication == null || !(authentication.getPrincipal() instanceof UserPrincipal)) {
            return Result.error("未找到当前用户信息");
        }
        
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        // 清除敏感信息
        userPrincipal.setPassword(null);
        
        return Result.success(userPrincipal);
    }

    /**
     * 验证令牌有效性
     */
    @GetMapping("/validate")
    @Operation(summary = "验证令牌", description = "验证当前令牌是否有效")
    public Result<Boolean> validateToken(@RequestHeader("Authorization") String authHeader) {
        try {
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return Result.success(false);
            }
            
            String token = authHeader.substring(7);
            boolean isValid = jwtUtil.validateToken(token);
            
            return Result.success(isValid);
        } catch (Exception e) {
            log.error("验证令牌失败: {}", e.getMessage());
            return Result.success(false);
        }
    }

    /**
     * 获取令牌剩余有效时间
     */
    @GetMapping("/token-info")
    @Operation(summary = "获取令牌信息", description = "获取当前令牌的剩余有效时间等信息")
    public Result<Object> getTokenInfo(@RequestHeader("Authorization") String authHeader) {
        try {
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return Result.error("无效的令牌格式");
            }
            
            String token = authHeader.substring(7);
            
            if (!jwtUtil.validateToken(token)) {
                return Result.error("令牌已失效");
            }
            
            long remainingTime = jwtUtil.getRemainingTime(token);
            String username = jwtUtil.getUsernameFromToken(token);
            
            return Result.success(new Object() {
                public final String username = AuthController.this.getUsername(token);
                public final long remainingTimeMs = remainingTime;
                public final long remainingTimeMin = remainingTime / (1000 * 60);
                public final boolean isValid = remainingTime > 0;
                
                private String getUsername(String token) {
                    return jwtUtil.getUsernameFromToken(token);
                }
            });
        } catch (Exception e) {
            log.error("获取令牌信息失败: {}", e.getMessage());
            return Result.error("获取令牌信息失败: " + e.getMessage());
        }
    }
}