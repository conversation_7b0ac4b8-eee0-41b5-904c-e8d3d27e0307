package com.yinma.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinma.entity.ProductSeries;
import com.yinma.dto.ProductSeriesDTO;
import com.yinma.vo.ProductSeriesVO;
import com.yinma.mapper.ProductSeriesMapper;
import com.yinma.service.ProductSeriesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品系列管理服务实现类
 * 银马实业设备产品系列管理系统
 * 
 * <AUTHOR>
 * @since 2024-05-20
 */
@Slf4j
@Service
public class ProductSeriesServiceImpl extends ServiceImpl<ProductSeriesMapper, ProductSeries> implements ProductSeriesService {

    @Override
    public IPage<ProductSeriesVO> getProductSeriesPage(Page<ProductSeries> page, String seriesName, 
                                                     String seriesCode, String productType, 
                                                     String techLevel, String status) {
        QueryWrapper<ProductSeries> queryWrapper = new QueryWrapper<>();
        
        if (StringUtils.hasText(seriesName)) {
            queryWrapper.like("series_name", seriesName);
        }
        if (StringUtils.hasText(seriesCode)) {
            queryWrapper.like("series_code", seriesCode);
        }
        if (StringUtils.hasText(productType)) {
            queryWrapper.eq("product_type", productType);
        }
        if (StringUtils.hasText(techLevel)) {
            queryWrapper.eq("tech_level", techLevel);
        }
        if (StringUtils.hasText(status)) {
            queryWrapper.eq("status", status);
        }
        
        queryWrapper.orderByDesc("create_time");
        
        IPage<ProductSeries> productSeriesPage = this.page(page, queryWrapper);
        
        // 转换为VO
        IPage<ProductSeriesVO> voPage = new Page<>();
        BeanUtils.copyProperties(productSeriesPage, voPage);
        
        List<ProductSeriesVO> voList = productSeriesPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        
        voPage.setRecords(voList);
        return voPage;
    }

    @Override
    public ProductSeriesVO getProductSeriesDetailById(Long id) {
        ProductSeries productSeries = this.getById(id);
        if (productSeries == null) {
            throw new RuntimeException("产品系列不存在");
        }
        return convertToVO(productSeries);
    }

    @Override
    @Transactional
    public ProductSeries createProductSeries(ProductSeriesDTO productSeriesDTO) {
        // 验证系列代码唯一性
        if (!validateSeriesCode(productSeriesDTO.getSeriesCode(), null)) {
            throw new RuntimeException("产品系列代码已存在");
        }
        
        ProductSeries productSeries = new ProductSeries();
        BeanUtils.copyProperties(productSeriesDTO, productSeries);
        
        productSeries.setCreateTime(LocalDateTime.now());
        productSeries.setUpdateTime(LocalDateTime.now());
        productSeries.setStatus("ACTIVE");
        
        this.save(productSeries);
        
        // 记录变更历史
        recordProductSeriesChange(productSeries.getId(), "CREATE", "创建产品系列", "system");
        
        return productSeries;
    }

    @Override
    @Transactional
    public ProductSeries updateProductSeries(Long id, ProductSeriesDTO productSeriesDTO) {
        ProductSeries existingProductSeries = this.getById(id);
        if (existingProductSeries == null) {
            throw new RuntimeException("产品系列不存在");
        }
        
        // 验证系列代码唯一性（排除当前记录）
        if (!validateSeriesCode(productSeriesDTO.getSeriesCode(), id)) {
            throw new RuntimeException("产品系列代码已存在");
        }
        
        BeanUtils.copyProperties(productSeriesDTO, existingProductSeries, "id", "createTime");
        existingProductSeries.setUpdateTime(LocalDateTime.now());
        
        this.updateById(existingProductSeries);
        
        // 记录变更历史
        recordProductSeriesChange(id, "UPDATE", "更新产品系列信息", "system");
        
        return existingProductSeries;
    }

    @Override
    @Transactional
    public boolean deleteProductSeries(Long id) {
        ProductSeries productSeries = this.getById(id);
        if (productSeries == null) {
            return false;
        }
        
        // 软删除
        productSeries.setStatus("DELETED");
        productSeries.setUpdateTime(LocalDateTime.now());
        
        boolean result = this.updateById(productSeries);
        
        if (result) {
            // 记录变更历史
            recordProductSeriesChange(id, "DELETE", "删除产品系列", "system");
        }
        
        return result;
    }

    @Override
    @Transactional
    public boolean batchDeleteProductSeries(List<Long> ids) {
        for (Long id : ids) {
            deleteProductSeries(id);
        }
        return true;
    }

    @Override
    @Transactional
    public ProductSeries updateProductSeriesStatus(Long id, String status, String remark) {
        ProductSeries productSeries = this.getById(id);
        if (productSeries == null) {
            throw new RuntimeException("产品系列不存在");
        }
        
        String oldStatus = productSeries.getStatus();
        productSeries.setStatus(status);
        productSeries.setRemark(remark);
        productSeries.setUpdateTime(LocalDateTime.now());
        
        this.updateById(productSeries);
        
        // 记录变更历史
        recordProductSeriesChange(id, "STATUS_CHANGE", 
                String.format("状态从 %s 变更为 %s，备注：%s", oldStatus, status, remark), "system");
        
        return productSeries;
    }

    @Override
    @Transactional
    public ProductSeries copyProductSeries(Long id, String newSeriesName, String newSeriesCode) {
        ProductSeries originalSeries = this.getById(id);
        if (originalSeries == null) {
            throw new RuntimeException("原产品系列不存在");
        }
        
        // 验证新系列代码唯一性
        if (!validateSeriesCode(newSeriesCode, null)) {
            throw new RuntimeException("新产品系列代码已存在");
        }
        
        ProductSeries newSeries = new ProductSeries();
        BeanUtils.copyProperties(originalSeries, newSeries, "id", "seriesName", "seriesCode", "createTime", "updateTime");
        
        newSeries.setSeriesName(newSeriesName);
        newSeries.setSeriesCode(newSeriesCode);
        newSeries.setCreateTime(LocalDateTime.now());
        newSeries.setUpdateTime(LocalDateTime.now());
        newSeries.setStatus("DRAFT");
        
        this.save(newSeries);
        
        // 记录变更历史
        recordProductSeriesChange(newSeries.getId(), "COPY", 
                String.format("从产品系列 %s 复制创建", originalSeries.getSeriesName()), "system");
        
        return newSeries;
    }

    @Override
    public Map<String, Object> getProductSeriesTemplate(Long id) {
        // 模拟获取配置模板
        Map<String, Object> template = new HashMap<>();
        template.put("id", id);
        template.put("basicConfig", getBasicConfigTemplate());
        template.put("technicalParams", getTechnicalParamsTemplate());
        template.put("bomTemplate", getBomTemplate());
        template.put("pricingTemplate", getPricingTemplate());
        return template;
    }

    @Override
    @Transactional
    public void saveProductSeriesTemplate(Long id, Map<String, Object> template) {
        // 模拟保存配置模板
        log.info("保存产品系列 {} 的配置模板: {}", id, template);
        
        // 记录变更历史
        recordProductSeriesChange(id, "TEMPLATE_UPDATE", "更新配置模板", "system");
    }

    @Override
    public Map<String, Object> getProductSeriesSpecifications(Long id) {
        // 模拟获取技术参数
        Map<String, Object> specifications = new HashMap<>();
        specifications.put("id", id);
        specifications.put("dimensions", Map.of("length", 12000, "width", 8000, "height", 4500));
        specifications.put("weight", 25000);
        specifications.put("power", Map.of("total", 150, "main", 90, "auxiliary", 60));
        specifications.put("capacity", Map.of("hourly", 3000, "daily", 24000));
        specifications.put("materials", Arrays.asList("混凝土", "水泥", "砂石", "添加剂"));
        return specifications;
    }

    @Override
    @Transactional
    public void updateProductSeriesSpecifications(Long id, Map<String, Object> specifications) {
        log.info("更新产品系列 {} 的技术参数: {}", id, specifications);
        
        // 记录变更历史
        recordProductSeriesChange(id, "SPEC_UPDATE", "更新技术参数", "system");
    }

    @Override
    public List<Map<String, Object>> getProductSeriesBom(Long id, String version) {
        // 模拟获取BOM清单
        List<Map<String, Object>> bom = new ArrayList<>();
        
        Map<String, Object> item1 = new HashMap<>();
        item1.put("componentId", "C001");
        item1.put("componentName", "主机架");
        item1.put("specification", "Q345B钢材");
        item1.put("quantity", 1);
        item1.put("unit", "套");
        item1.put("unitPrice", 15000.0);
        item1.put("totalPrice", 15000.0);
        bom.add(item1);
        
        Map<String, Object> item2 = new HashMap<>();
        item2.put("componentId", "C002");
        item2.put("componentName", "振动系统");
        item2.put("specification", "双轴振动器");
        item2.put("quantity", 2);
        item2.put("unit", "台");
        item2.put("unitPrice", 8000.0);
        item2.put("totalPrice", 16000.0);
        bom.add(item2);
        
        return bom;
    }

    @Override
    @Transactional
    public void updateProductSeriesBom(Long id, List<Map<String, Object>> bom, String version) {
        log.info("更新产品系列 {} 的BOM清单，版本: {}", id, version);
        
        // 记录变更历史
        recordProductSeriesChange(id, "BOM_UPDATE", 
                String.format("更新BOM清单，版本: %s", version), "system");
    }

    @Override
    public Map<String, Object> getProductSeriesPricing(Long id) {
        // 模拟获取价格配置
        Map<String, Object> pricing = new HashMap<>();
        pricing.put("id", id);
        pricing.put("basePriceRange", Map.of("min", 800000, "max", 1200000));
        pricing.put("priceFactors", Arrays.asList(
                Map.of("factor", "产量配置", "impact", 0.15),
                Map.of("factor", "自动化程度", "impact", 0.25),
                Map.of("factor", "定制化程度", "impact", 0.20)
        ));
        pricing.put("discountPolicy", Map.of("volume", 0.05, "loyalty", 0.03));
        return pricing;
    }

    @Override
    @Transactional
    public void updateProductSeriesPricing(Long id, Map<String, Object> pricing) {
        log.info("更新产品系列 {} 的价格配置: {}", id, pricing);
        
        // 记录变更历史
        recordProductSeriesChange(id, "PRICING_UPDATE", "更新价格配置", "system");
    }

    @Override
    public String uploadProductSeriesImage(Long id, MultipartFile file, String imageType) {
        // 模拟文件上传
        String fileName = file.getOriginalFilename();
        String imageUrl = "/uploads/product-series/" + id + "/" + imageType + "/" + fileName;
        
        log.info("上传产品系列 {} 的图片: {}, 类型: {}", id, fileName, imageType);
        
        return imageUrl;
    }

    @Override
    public List<Map<String, Object>> getProductSeriesImages(Long id) {
        // 模拟获取图片列表
        List<Map<String, Object>> images = new ArrayList<>();
        
        Map<String, Object> image1 = new HashMap<>();
        image1.put("id", 1L);
        image1.put("imageType", "主图");
        image1.put("imageUrl", "/uploads/product-series/" + id + "/main/main.jpg");
        image1.put("uploadTime", LocalDateTime.now());
        images.add(image1);
        
        return images;
    }

    @Override
    @Transactional
    public boolean deleteProductSeriesImage(Long id, Long imageId) {
        log.info("删除产品系列 {} 的图片: {}", id, imageId);
        return true;
    }

    @Override
    public String exportProductSeries(String seriesName, String productType, String status, String format) {
        // 模拟导出功能
        String fileName = "product_series_export_" + System.currentTimeMillis() + "." + format;
        String fileUrl = "/exports/" + fileName;
        
        log.info("导出产品系列数据: {}", fileName);
        
        return fileUrl;
    }

    @Override
    @Transactional
    public Map<String, Object> importProductSeries(MultipartFile file) {
        // 模拟导入功能
        Map<String, Object> result = new HashMap<>();
        result.put("totalCount", 10);
        result.put("successCount", 8);
        result.put("failureCount", 2);
        result.put("errors", Arrays.asList("第3行：系列代码重复", "第7行：产品类型无效"));
        
        log.info("导入产品系列数据: {}", file.getOriginalFilename());
        
        return result;
    }

    @Override
    public Map<String, Object> getProductSeriesStatistics() {
        // 模拟统计数据
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalSeries", 25);
        statistics.put("activeSeries", 20);
        statistics.put("draftSeries", 3);
        statistics.put("discontinuedSeries", 2);
        
        Map<String, Object> typeDistribution = new HashMap<>();
        typeDistribution.put("砖机设备", 15);
        typeDistribution.put("石材设备", 8);
        typeDistribution.put("一体机", 2);
        statistics.put("typeDistribution", typeDistribution);
        
        return statistics;
    }

    @Override
    public List<Map<String, Object>> getProductSeriesOptions(String productType, String techLevel) {
        // 模拟获取选项列表
        List<Map<String, Object>> options = new ArrayList<>();
        
        Map<String, Object> option1 = new HashMap<>();
        option1.put("value", 1L);
        option1.put("label", "银马2025压振全能砖/石一体机");
        option1.put("productType", "一体机");
        option1.put("techLevel", "国际先进");
        options.add(option1);
        
        return options;
    }

    @Override
    public boolean validateSeriesCode(String seriesCode, Long excludeId) {
        QueryWrapper<ProductSeries> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("series_code", seriesCode);
        queryWrapper.ne("status", "DELETED");
        
        if (excludeId != null) {
            queryWrapper.ne("id", excludeId);
        }
        
        return this.count(queryWrapper) == 0;
    }

    @Override
    public Map<String, Object> getProductSeriesRecommendations(Long id, String customerRequirements) {
        // 模拟推荐配置
        Map<String, Object> recommendations = new HashMap<>();
        recommendations.put("seriesId", id);
        recommendations.put("recommendedConfig", "高产能配置");
        recommendations.put("estimatedPrice", 950000);
        recommendations.put("deliveryTime", "45-60天");
        recommendations.put("features", Arrays.asList("智能控制", "远程监控", "节能环保"));
        
        return recommendations;
    }

    @Override
    public String generateProductSeriesReport(Long id, Map<String, Object> reportConfig) {
        // 模拟生成报告
        String reportUrl = "/reports/product-series-" + id + "-" + System.currentTimeMillis() + ".pdf";
        log.info("生成产品系列 {} 的配置报告: {}", id, reportUrl);
        return reportUrl;
    }

    @Override
    public List<Map<String, Object>> getProductSeriesChangeHistory(Long id) {
        // 模拟获取变更历史
        List<Map<String, Object>> history = new ArrayList<>();
        
        Map<String, Object> change1 = new HashMap<>();
        change1.put("id", 1L);
        change1.put("changeType", "CREATE");
        change1.put("changeContent", "创建产品系列");
        change1.put("operator", "system");
        change1.put("changeTime", LocalDateTime.now().minusDays(10));
        history.add(change1);
        
        return history;
    }

    @Override
    @Transactional
    public void recordProductSeriesChange(Long id, String changeType, String changeContent, String operator) {
        // 模拟记录变更历史
        log.info("记录产品系列 {} 变更: 类型={}, 内容={}, 操作人={}", id, changeType, changeContent, operator);
    }

    // 其他方法的实现...
    @Override
    public List<Map<String, Object>> getProductSeriesEquipmentConfigs(Long id) {
        return new ArrayList<>();
    }

    @Override
    public Map<String, Object> createEquipmentConfigFromSeries(Long id, String configName, Map<String, Object> customizations) {
        return new HashMap<>();
    }

    @Override
    public Map<String, Object> getProductSeriesCostAnalysis(Long id) {
        return new HashMap<>();
    }

    @Override
    public void updateProductSeriesCostInfo(Long id, Map<String, Object> costInfo) {
        log.info("更新产品系列 {} 成本信息", id);
    }

    @Override
    public Map<String, Object> getProductSeriesMarketAnalysis(Long id) {
        return new HashMap<>();
    }

    @Override
    public Map<String, Object> getProductSeriesCompetitorComparison(Long id, List<Long> competitorIds) {
        return new HashMap<>();
    }

    @Override
    public Map<String, Object> getProductSeriesSalesData(Long id, String startDate, String endDate) {
        return new HashMap<>();
    }

    @Override
    public List<Map<String, Object>> getProductSeriesCustomerFeedback(Long id) {
        return new ArrayList<>();
    }

    @Override
    public void addProductSeriesCustomerFeedback(Long id, Map<String, Object> feedback) {
        log.info("添加产品系列 {} 客户反馈", id);
    }

    @Override
    public Map<String, Object> getProductSeriesQualityMetrics(Long id) {
        return new HashMap<>();
    }

    @Override
    public void updateProductSeriesQualityMetrics(Long id, Map<String, Object> qualityMetrics) {
        log.info("更新产品系列 {} 质量指标", id);
    }

    @Override
    public List<Map<String, Object>> getProductSeriesProductionPlan(Long id) {
        return new ArrayList<>();
    }

    @Override
    public Map<String, Object> createProductSeriesProductionPlan(Long id, Map<String, Object> productionPlan) {
        return new HashMap<>();
    }

    @Override
    public Map<String, Object> getProductSeriesInventoryInfo(Long id) {
        return new HashMap<>();
    }

    @Override
    public void updateProductSeriesInventoryInfo(Long id, Map<String, Object> inventoryInfo) {
        log.info("更新产品系列 {} 库存信息", id);
    }

    @Override
    public List<Map<String, Object>> getProductSeriesSupplierInfo(Long id) {
        return new ArrayList<>();
    }

    @Override
    public void addProductSeriesSupplier(Long id, Map<String, Object> supplierInfo) {
        log.info("添加产品系列 {} 供应商", id);
    }

    @Override
    public boolean removeProductSeriesSupplier(Long id, Long supplierId) {
        log.info("移除产品系列 {} 供应商 {}", id, supplierId);
        return true;
    }

    @Override
    public List<Map<String, Object>> getProductSeriesCertifications(Long id) {
        return new ArrayList<>();
    }

    @Override
    public void addProductSeriesCertification(Long id, Map<String, Object> certification) {
        log.info("添加产品系列 {} 认证", id);
    }

    @Override
    public List<Map<String, Object>> getProductSeriesTechnicalDocs(Long id) {
        return new ArrayList<>();
    }

    @Override
    public String uploadProductSeriesTechnicalDoc(Long id, MultipartFile file, String docType) {
        return "/uploads/docs/" + id + "/" + file.getOriginalFilename();
    }

    @Override
    public boolean deleteProductSeriesTechnicalDoc(Long id, Long docId) {
        return true;
    }

    @Override
    public Map<String, Object> getProductSeriesMaintenanceManual(Long id) {
        return new HashMap<>();
    }

    @Override
    public void updateProductSeriesMaintenanceManual(Long id, Map<String, Object> maintenanceManual) {
        log.info("更新产品系列 {} 维护手册", id);
    }

    @Override
    public List<Map<String, Object>> getProductSeriesTrainingMaterials(Long id) {
        return new ArrayList<>();
    }

    @Override
    public void addProductSeriesTrainingMaterial(Long id, Map<String, Object> trainingMaterial) {
        log.info("添加产品系列 {} 培训材料", id);
    }

    @Override
    public String generateProductSeriesQRCode(Long id) {
        return "/qrcodes/product-series-" + id + ".png";
    }

    @Override
    public Map<String, Object> getProductSeriesDigitalModel(Long id) {
        return new HashMap<>();
    }

    @Override
    public String uploadProductSeriesDigitalModel(Long id, MultipartFile file, String modelType) {
        return "/uploads/models/" + id + "/" + file.getOriginalFilename();
    }

    @Override
    public Map<String, Object> getProductSeriesARVRConfig(Long id) {
        return new HashMap<>();
    }

    @Override
    public void updateProductSeriesARVRConfig(Long id, Map<String, Object> arvrConfig) {
        log.info("更新产品系列 {} AR/VR配置", id);
    }

    /**
     * 转换为VO对象
     */
    private ProductSeriesVO convertToVO(ProductSeries productSeries) {
        ProductSeriesVO vo = new ProductSeriesVO();
        BeanUtils.copyProperties(productSeries, vo);
        
        // 添加额外的展示信息
        vo.setStatusText(getStatusText(productSeries.getStatus()));
        vo.setProductTypeText(getProductTypeText(productSeries.getProductType()));
        vo.setTechLevelText(getTechLevelText(productSeries.getTechLevel()));
        
        return vo;
    }

    /**
     * 获取状态文本
     */
    private String getStatusText(String status) {
        switch (status) {
            case "ACTIVE": return "启用";
            case "DRAFT": return "草稿";
            case "DISCONTINUED": return "停产";
            case "DELETED": return "已删除";
            default: return status;
        }
    }

    /**
     * 获取产品类型文本
     */
    private String getProductTypeText(String productType) {
        switch (productType) {
            case "BRICK_MACHINE": return "砖机设备";
            case "STONE_MACHINE": return "石材设备";
            case "INTEGRATED_MACHINE": return "一体机";
            default: return productType;
        }
    }

    /**
     * 获取技术等级文本
     */
    private String getTechLevelText(String techLevel) {
        switch (techLevel) {
            case "INTERNATIONAL_ADVANCED": return "国际先进";
            case "DOMESTIC_LEADING": return "国内领先";
            case "INDUSTRY_STANDARD": return "行业标准";
            default: return techLevel;
        }
    }

    /**
     * 获取基础配置模板
     */
    private Map<String, Object> getBasicConfigTemplate() {
        Map<String, Object> config = new HashMap<>();
        config.put("productionCapacity", Map.of("min", 1000, "max", 5000, "unit", "块/小时"));
        config.put("powerRequirement", Map.of("voltage", 380, "frequency", 50, "power", 150));
        config.put("dimensions", Map.of("length", 12000, "width", 8000, "height", 4500));
        return config;
    }

    /**
     * 获取技术参数模板
     */
    private Map<String, Object> getTechnicalParamsTemplate() {
        Map<String, Object> params = new HashMap<>();
        params.put("vibrationFrequency", Map.of("min", 2800, "max", 3200, "unit", "次/分钟"));
        params.put("formingPressure", Map.of("min", 15, "max", 25, "unit", "MPa"));
        params.put("moldingCycle", Map.of("min", 15, "max", 30, "unit", "秒"));
        return params;
    }

    /**
     * 获取BOM模板
     */
    private Map<String, Object> getBomTemplate() {
        Map<String, Object> bom = new HashMap<>();
        bom.put("mainComponents", Arrays.asList("主机架", "振动系统", "液压系统", "控制系统"));
        bom.put("auxiliaryComponents", Arrays.asList("输送系统", "码垛系统", "养护系统"));
        bom.put("standardParts", Arrays.asList("螺栓", "轴承", "密封件", "电气元件"));
        return bom;
    }

    /**
     * 获取价格模板
     */
    private Map<String, Object> getPricingTemplate() {
        Map<String, Object> pricing = new HashMap<>();
        pricing.put("basePrice", 800000);
        pricing.put("optionalFeatures", Map.of("自动化升级", 150000, "远程监控", 50000, "节能系统", 80000));
        pricing.put("services", Map.of("安装调试", 30000, "培训服务", 20000, "质保延长", 40000));
        return pricing;
    }
}