package com.yinma.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinma.dto.BomDetailDTO;
import com.yinma.entity.BomDetailEntity;
import com.yinma.entity.MaterialEntity;
import com.yinma.mapper.BomDetailMapper;
import com.yinma.mapper.MaterialMapper;
import com.yinma.service.BomDetailService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * BOM明细管理Service实现类
 * 
 * <AUTHOR>
 * @since 2024-01-20
 */
@Service
public class BomDetailServiceImpl extends ServiceImpl<BomDetailMapper, BomDetailEntity> implements BomDetailService {

    @Autowired
    private BomDetailMapper bomDetailMapper;
    
    @Autowired
    private MaterialMapper materialMapper;

    @Override
    public List<BomDetailDTO> getDetailList(Long bomId) {
        List<BomDetailEntity> detailList = bomDetailMapper.selectDetailList(bomId);
        return convertToDetailDTOList(detailList);
    }

    @Override
    public List<BomDetailDTO> getDetailTree(Long bomId) {
        List<BomDetailEntity> allDetails = bomDetailMapper.selectDetailList(bomId);
        return buildDetailTree(allDetails, null);
    }

    @Override
    public BomDetailDTO getDetailInfo(Long detailId) {
        BomDetailEntity detail = bomDetailMapper.selectDetailWithMaterial(detailId);
        if (detail == null) {
            return null;
        }
        return convertToDetailDTO(detail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomDetailDTO createDetail(BomDetailDTO detailDTO) {
        // 参数校验
        validateDetailData(detailDTO);
        
        // 检查物料是否存在
        MaterialEntity material = materialMapper.selectById(detailDTO.getMaterialId());
        if (material == null) {
            throw new RuntimeException("物料不存在");
        }
        
        // 检查是否会形成循环引用
        if (detailDTO.getSubBomId() != null) {
            checkCircularReference(detailDTO.getBomId(), detailDTO.getSubBomId());
        }
        
        BomDetailEntity detail = new BomDetailEntity();
        BeanUtils.copyProperties(detailDTO, detail);
        
        // 设置序号
        if (detail.getSequenceNo() == null) {
            Integer maxSequence = bomDetailMapper.selectMaxSequenceNo(detail.getBomId(), detail.getParentDetailId());
            detail.setSequenceNo(maxSequence == null ? 1 : maxSequence + 1);
        }
        
        detail.setCreateTime(LocalDateTime.now());
        detail.setUpdateTime(LocalDateTime.now());
        
        bomDetailMapper.insert(detail);
        
        return getDetailInfo(detail.getDetailId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomDetailDTO updateDetail(BomDetailDTO detailDTO) {
        // 参数校验
        validateDetailData(detailDTO);
        
        BomDetailEntity existingDetail = bomDetailMapper.selectById(detailDTO.getDetailId());
        if (existingDetail == null) {
            throw new RuntimeException("BOM明细不存在");
        }
        
        // 检查物料是否存在
        MaterialEntity material = materialMapper.selectById(detailDTO.getMaterialId());
        if (material == null) {
            throw new RuntimeException("物料不存在");
        }
        
        // 检查是否会形成循环引用
        if (detailDTO.getSubBomId() != null && !detailDTO.getSubBomId().equals(existingDetail.getSubBomId())) {
            checkCircularReference(detailDTO.getBomId(), detailDTO.getSubBomId());
        }
        
        BomDetailEntity detail = new BomDetailEntity();
        BeanUtils.copyProperties(detailDTO, detail);
        detail.setUpdateTime(LocalDateTime.now());
        
        bomDetailMapper.updateById(detail);
        
        return getDetailInfo(detail.getDetailId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDetail(Long detailId, Long userId) {
        BomDetailEntity detail = bomDetailMapper.selectById(detailId);
        if (detail == null) {
            throw new RuntimeException("BOM明细不存在");
        }
        
        // 检查是否有子明细
        List<BomDetailEntity> subDetails = bomDetailMapper.selectList(
            new LambdaQueryWrapper<BomDetailEntity>()
                .eq(BomDetailEntity::getParentDetailId, detailId)
        );
        
        if (!CollectionUtils.isEmpty(subDetails)) {
            throw new RuntimeException("存在子明细，无法删除");
        }
        
        return bomDetailMapper.deleteById(detailId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteDetail(List<Long> detailIds, Long userId) {
        if (CollectionUtils.isEmpty(detailIds)) {
            return false;
        }
        
        // 检查是否有子明细
        for (Long detailId : detailIds) {
            List<BomDetailEntity> subDetails = bomDetailMapper.selectList(
                new LambdaQueryWrapper<BomDetailEntity>()
                    .eq(BomDetailEntity::getParentDetailId, detailId)
            );
            
            if (!CollectionUtils.isEmpty(subDetails)) {
                throw new RuntimeException("存在子明细，无法删除");
            }
        }
        
        return bomDetailMapper.deleteBatchIds(detailIds) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomDetailDTO copyDetail(Long detailId, Long targetBomId, Long userId) {
        BomDetailEntity sourceDetail = bomDetailMapper.selectById(detailId);
        if (sourceDetail == null) {
            throw new RuntimeException("源BOM明细不存在");
        }
        
        BomDetailEntity newDetail = new BomDetailEntity();
        BeanUtils.copyProperties(sourceDetail, newDetail);
        newDetail.setDetailId(null);
        newDetail.setBomId(targetBomId);
        newDetail.setParentDetailId(null); // 复制到根级别
        newDetail.setCreateTime(LocalDateTime.now());
        newDetail.setUpdateTime(LocalDateTime.now());
        newDetail.setCreateUserId(userId);
        newDetail.setUpdateUserId(userId);
        
        // 设置序号
        Integer maxSequence = bomDetailMapper.selectMaxSequenceNo(targetBomId, null);
        newDetail.setSequenceNo(maxSequence == null ? 1 : maxSequence + 1);
        
        bomDetailMapper.insert(newDetail);
        
        // 递归复制子明细
        copySubDetails(detailId, newDetail.getDetailId(), targetBomId, userId);
        
        return getDetailInfo(newDetail.getDetailId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean moveDetail(Long detailId, Long targetParentId, Long userId) {
        BomDetailEntity detail = bomDetailMapper.selectById(detailId);
        if (detail == null) {
            throw new RuntimeException("BOM明细不存在");
        }
        
        // 检查目标父节点是否存在
        if (targetParentId != null) {
            BomDetailEntity targetParent = bomDetailMapper.selectById(targetParentId);
            if (targetParent == null) {
                throw new RuntimeException("目标父节点不存在");
            }
            
            // 检查是否会形成循环引用
            if (isDescendant(detailId, targetParentId)) {
                throw new RuntimeException("不能移动到自己的子节点下");
            }
        }
        
        detail.setParentDetailId(targetParentId);
        detail.setUpdateTime(LocalDateTime.now());
        detail.setUpdateUserId(userId);
        
        return bomDetailMapper.updateById(detail) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean sortDetail(Long bomId, List<Long> detailIds, Long userId) {
        if (CollectionUtils.isEmpty(detailIds)) {
            return false;
        }
        
        for (int i = 0; i < detailIds.size(); i++) {
            BomDetailEntity detail = new BomDetailEntity();
            detail.setDetailId(detailIds.get(i));
            detail.setSequenceNo(i + 1);
            detail.setUpdateTime(LocalDateTime.now());
            detail.setUpdateUserId(userId);
            
            bomDetailMapper.updateById(detail);
        }
        
        return true;
    }

    @Override
    public List<BomDetailDTO.MaterialRequirementDTO> getMaterialRequirement(Long bomId, BigDecimal quantity) {
        return bomDetailMapper.selectMaterialRequirement(bomId, quantity);
    }

    @Override
    public List<BomDetailDTO.CostAnalysisDTO> getCostAnalysis(Long bomId) {
        return bomDetailMapper.selectCostAnalysis(bomId);
    }

    @Override
    public List<BomDetailDTO> getCriticalMaterials(Long bomId) {
        List<BomDetailEntity> criticalDetails = bomDetailMapper.selectCriticalMaterials(bomId);
        return convertToDetailDTOList(criticalDetails);
    }

    @Override
    public List<BomDetailDTO.SubstituteMaterialDTO> getSubstituteMaterials(Long materialId) {
        return bomDetailMapper.selectSubstituteMaterials(materialId);
    }

    @Override
    public List<BomDetailDTO> getUsageAnalysis(Long materialId) {
        List<BomDetailEntity> usageDetails = bomDetailMapper.selectUsageAnalysis(materialId);
        return convertToDetailDTOList(usageDetails);
    }

    @Override
    public byte[] exportDetail(Long bomId) {
        // TODO: 实现导出功能
        throw new RuntimeException("导出功能待实现");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean importDetail(Long bomId, byte[] fileData, Long userId) {
        // TODO: 实现导入功能
        throw new RuntimeException("导入功能待实现");
    }

    /**
     * 参数校验
     */
    private void validateDetailData(BomDetailDTO detailDTO) {
        if (detailDTO.getBomId() == null) {
            throw new RuntimeException("BOM ID不能为空");
        }
        
        if (detailDTO.getMaterialId() == null) {
            throw new RuntimeException("物料ID不能为空");
        }
        
        if (detailDTO.getQuantity() == null || detailDTO.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("数量必须大于0");
        }
        
        if (!StringUtils.hasText(detailDTO.getUnit())) {
            throw new RuntimeException("单位不能为空");
        }
    }

    /**
     * 检查循环引用
     */
    private void checkCircularReference(Long bomId, Long subBomId) {
        if (bomId.equals(subBomId)) {
            throw new RuntimeException("不能引用自己");
        }
        
        // 递归检查是否存在循环引用
        Set<Long> visited = new HashSet<>();
        if (hasCircularReference(bomId, subBomId, visited)) {
            throw new RuntimeException("存在循环引用");
        }
    }

    /**
     * 递归检查循环引用
     */
    private boolean hasCircularReference(Long bomId, Long checkBomId, Set<Long> visited) {
        if (visited.contains(checkBomId)) {
            return true;
        }
        
        visited.add(checkBomId);
        
        List<BomDetailEntity> subBoms = bomDetailMapper.selectList(
            new LambdaQueryWrapper<BomDetailEntity>()
                .eq(BomDetailEntity::getBomId, checkBomId)
                .isNotNull(BomDetailEntity::getSubBomId)
        );
        
        for (BomDetailEntity subBom : subBoms) {
            if (subBom.getSubBomId().equals(bomId)) {
                return true;
            }
            
            if (hasCircularReference(bomId, subBom.getSubBomId(), visited)) {
                return true;
            }
        }
        
        visited.remove(checkBomId);
        return false;
    }

    /**
     * 递归复制子明细
     */
    private void copySubDetails(Long sourceParentId, Long targetParentId, Long targetBomId, Long userId) {
        List<BomDetailEntity> subDetails = bomDetailMapper.selectList(
            new LambdaQueryWrapper<BomDetailEntity>()
                .eq(BomDetailEntity::getParentDetailId, sourceParentId)
        );
        
        for (BomDetailEntity subDetail : subDetails) {
            BomDetailEntity newSubDetail = new BomDetailEntity();
            BeanUtils.copyProperties(subDetail, newSubDetail);
            newSubDetail.setDetailId(null);
            newSubDetail.setBomId(targetBomId);
            newSubDetail.setParentDetailId(targetParentId);
            newSubDetail.setCreateTime(LocalDateTime.now());
            newSubDetail.setUpdateTime(LocalDateTime.now());
            newSubDetail.setCreateUserId(userId);
            newSubDetail.setUpdateUserId(userId);
            
            bomDetailMapper.insert(newSubDetail);
            
            // 递归复制子明细的子明细
            copySubDetails(subDetail.getDetailId(), newSubDetail.getDetailId(), targetBomId, userId);
        }
    }

    /**
     * 检查是否为后代节点
     */
    private boolean isDescendant(Long ancestorId, Long descendantId) {
        List<BomDetailEntity> children = bomDetailMapper.selectList(
            new LambdaQueryWrapper<BomDetailEntity>()
                .eq(BomDetailEntity::getParentDetailId, ancestorId)
        );
        
        for (BomDetailEntity child : children) {
            if (child.getDetailId().equals(descendantId)) {
                return true;
            }
            
            if (isDescendant(child.getDetailId(), descendantId)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 构建明细树
     */
    private List<BomDetailDTO> buildDetailTree(List<BomDetailEntity> allDetails, Long parentId) {
        return allDetails.stream()
            .filter(detail -> Objects.equals(detail.getParentDetailId(), parentId))
            .map(detail -> {
                BomDetailDTO dto = convertToDetailDTO(detail);
                List<BomDetailDTO> children = buildDetailTree(allDetails, detail.getDetailId());
                dto.setSubDetailList(children);
                return dto;
            })
            .sorted(Comparator.comparing(BomDetailDTO::getSequenceNo, Comparator.nullsLast(Comparator.naturalOrder())))
            .collect(Collectors.toList());
    }

    /**
     * 转换为DTO列表
     */
    private List<BomDetailDTO> convertToDetailDTOList(List<BomDetailEntity> detailList) {
        if (CollectionUtils.isEmpty(detailList)) {
            return new ArrayList<>();
        }
        
        return detailList.stream()
            .map(this::convertToDetailDTO)
            .collect(Collectors.toList());
    }

    /**
     * 转换为DTO
     */
    private BomDetailDTO convertToDetailDTO(BomDetailEntity detail) {
        BomDetailDTO dto = new BomDetailDTO();
        BeanUtils.copyProperties(detail, dto);
        
        // 设置物料信息
        if (detail.getMaterialId() != null) {
            MaterialEntity material = materialMapper.selectById(detail.getMaterialId());
            if (material != null) {
                BomDetailDTO.MaterialDTO materialDTO = new BomDetailDTO.MaterialDTO();
                BeanUtils.copyProperties(material, materialDTO);
                dto.setMaterial(materialDTO);
            }
        }
        
        return dto;
    }
}