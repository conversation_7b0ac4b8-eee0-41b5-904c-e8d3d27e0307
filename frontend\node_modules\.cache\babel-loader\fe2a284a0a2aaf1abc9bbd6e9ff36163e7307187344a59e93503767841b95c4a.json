{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CloseSquareOutlinedSvg from \"@ant-design/icons-svg/es/asn/CloseSquareOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CloseSquareOutlined = function CloseSquareOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CloseSquareOutlinedSvg\n  }));\n};\n\n/**![close-square](data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(CloseSquareOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CloseSquareOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "CloseSquareOutlinedSvg", "AntdIcon", "CloseSquareOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/@ant-design/icons/es/icons/CloseSquareOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CloseSquareOutlinedSvg from \"@ant-design/icons-svg/es/asn/CloseSquareOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CloseSquareOutlined = function CloseSquareOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CloseSquareOutlinedSvg\n  }));\n};\n\n/**![close-square](data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(CloseSquareOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CloseSquareOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjE,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,mBAAmB,CAAC;AAChE,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,qBAAqB;AAC7C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}