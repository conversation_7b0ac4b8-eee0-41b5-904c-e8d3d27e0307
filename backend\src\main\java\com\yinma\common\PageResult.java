package com.yinma.common;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 分页结果类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Accessors(chain = true)
public class PageResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 当前页码
     */
    private Integer current;

    /**
     * 每页大小
     */
    private Integer size;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 总页数
     */
    private Long pages;

    /**
     * 数据列表
     */
    private List<T> records;

    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;

    /**
     * 是否有下一页
     */
    private Boolean hasNext;

    /**
     * 是否为第一页
     */
    private Boolean isFirst;

    /**
     * 是否为最后一页
     */
    private Boolean isLast;

    public PageResult() {
    }

    public PageResult(Integer current, Integer size, Long total, List<T> records) {
        this.current = current;
        this.size = size;
        this.total = total;
        this.records = records;
        this.pages = (total + size - 1) / size;
        this.hasPrevious = current > 1;
        this.hasNext = current < pages;
        this.isFirst = current == 1;
        this.isLast = current.equals(pages.intValue());
    }

    /**
     * 创建空的分页结果
     */
    public static <T> PageResult<T> empty() {
        return new PageResult<>(1, 10, 0L, List.of());
    }

    /**
     * 创建空的分页结果（指定页码和大小）
     */
    public static <T> PageResult<T> empty(Integer current, Integer size) {
        return new PageResult<>(current, size, 0L, List.of());
    }

    /**
     * 创建分页结果
     */
    public static <T> PageResult<T> of(Integer current, Integer size, Long total, List<T> records) {
        return new PageResult<>(current, size, total, records);
    }

    /**
     * 从PageQuery创建分页结果
     */
    public static <T> PageResult<T> of(PageQuery pageQuery, Long total, List<T> records) {
        return new PageResult<>(pageQuery.getCurrent(), pageQuery.getSize(), total, records);
    }

    /**
     * 获取开始记录号
     */
    public Long getStartRecord() {
        if (total == 0) {
            return 0L;
        }
        return (long) (current - 1) * size + 1;
    }

    /**
     * 获取结束记录号
     */
    public Long getEndRecord() {
        if (total == 0) {
            return 0L;
        }
        long endRecord = (long) current * size;
        return Math.min(endRecord, total);
    }

    /**
     * 是否为空
     */
    public boolean isEmpty() {
        return records == null || records.isEmpty();
    }

    /**
     * 是否不为空
     */
    public boolean isNotEmpty() {
        return !isEmpty();
    }

    /**
     * 获取记录数量
     */
    public int getRecordCount() {
        return records == null ? 0 : records.size();
    }
}