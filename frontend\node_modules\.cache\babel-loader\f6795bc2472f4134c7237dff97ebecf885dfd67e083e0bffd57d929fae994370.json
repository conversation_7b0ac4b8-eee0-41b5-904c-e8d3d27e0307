{"ast": null, "code": "import { default as polygonContains } from \"./polygonContains.js\";\nimport { default as distance } from \"./distance.js\";\nimport { epsilon2, radians } from \"./math.js\";\nvar containsObjectType = {\n  Feature: function (object, point) {\n    return containsGeometry(object.geometry, point);\n  },\n  FeatureCollection: function (object, point) {\n    var features = object.features,\n      i = -1,\n      n = features.length;\n    while (++i < n) if (containsGeometry(features[i].geometry, point)) return true;\n    return false;\n  }\n};\nvar containsGeometryType = {\n  Sphere: function () {\n    return true;\n  },\n  Point: function (object, point) {\n    return containsPoint(object.coordinates, point);\n  },\n  MultiPoint: function (object, point) {\n    var coordinates = object.coordinates,\n      i = -1,\n      n = coordinates.length;\n    while (++i < n) if (containsPoint(coordinates[i], point)) return true;\n    return false;\n  },\n  LineString: function (object, point) {\n    return containsLine(object.coordinates, point);\n  },\n  MultiLineString: function (object, point) {\n    var coordinates = object.coordinates,\n      i = -1,\n      n = coordinates.length;\n    while (++i < n) if (containsLine(coordinates[i], point)) return true;\n    return false;\n  },\n  Polygon: function (object, point) {\n    return containsPolygon(object.coordinates, point);\n  },\n  MultiPolygon: function (object, point) {\n    var coordinates = object.coordinates,\n      i = -1,\n      n = coordinates.length;\n    while (++i < n) if (containsPolygon(coordinates[i], point)) return true;\n    return false;\n  },\n  GeometryCollection: function (object, point) {\n    var geometries = object.geometries,\n      i = -1,\n      n = geometries.length;\n    while (++i < n) if (containsGeometry(geometries[i], point)) return true;\n    return false;\n  }\n};\nfunction containsGeometry(geometry, point) {\n  return geometry && containsGeometryType.hasOwnProperty(geometry.type) ? containsGeometryType[geometry.type](geometry, point) : false;\n}\nfunction containsPoint(coordinates, point) {\n  return distance(coordinates, point) === 0;\n}\nfunction containsLine(coordinates, point) {\n  var ao, bo, ab;\n  for (var i = 0, n = coordinates.length; i < n; i++) {\n    bo = distance(coordinates[i], point);\n    if (bo === 0) return true;\n    if (i > 0) {\n      ab = distance(coordinates[i], coordinates[i - 1]);\n      if (ab > 0 && ao <= ab && bo <= ab && (ao + bo - ab) * (1 - Math.pow((ao - bo) / ab, 2)) < epsilon2 * ab) return true;\n    }\n    ao = bo;\n  }\n  return false;\n}\nfunction containsPolygon(coordinates, point) {\n  return !!polygonContains(coordinates.map(ringRadians), pointRadians(point));\n}\nfunction ringRadians(ring) {\n  return ring = ring.map(pointRadians), ring.pop(), ring;\n}\nfunction pointRadians(point) {\n  return [point[0] * radians, point[1] * radians];\n}\nexport default function (object, point) {\n  return (object && containsObjectType.hasOwnProperty(object.type) ? containsObjectType[object.type] : containsGeometry)(object, point);\n}", "map": {"version": 3, "names": ["default", "polygonContains", "distance", "epsilon2", "radians", "containsObjectType", "Feature", "object", "point", "containsGeometry", "geometry", "FeatureCollection", "features", "i", "n", "length", "containsGeometryType", "Sphere", "Point", "containsPoint", "coordinates", "MultiPoint", "LineString", "containsLine", "MultiLineString", "Polygon", "containsPolygon", "MultiPolygon", "GeometryCollection", "geometries", "hasOwnProperty", "type", "ao", "bo", "ab", "Math", "pow", "map", "ringRadians", "pointRadians", "ring", "pop"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/d3-geo/src/contains.js"], "sourcesContent": ["import {default as polygonContains} from \"./polygonContains.js\";\nimport {default as distance} from \"./distance.js\";\nimport {epsilon2, radians} from \"./math.js\";\n\nvar containsObjectType = {\n  Feature: function(object, point) {\n    return containsGeometry(object.geometry, point);\n  },\n  FeatureCollection: function(object, point) {\n    var features = object.features, i = -1, n = features.length;\n    while (++i < n) if (containsGeometry(features[i].geometry, point)) return true;\n    return false;\n  }\n};\n\nvar containsGeometryType = {\n  Sphere: function() {\n    return true;\n  },\n  Point: function(object, point) {\n    return containsPoint(object.coordinates, point);\n  },\n  MultiPoint: function(object, point) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) if (containsPoint(coordinates[i], point)) return true;\n    return false;\n  },\n  LineString: function(object, point) {\n    return containsLine(object.coordinates, point);\n  },\n  MultiLineString: function(object, point) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) if (containsLine(coordinates[i], point)) return true;\n    return false;\n  },\n  Polygon: function(object, point) {\n    return containsPolygon(object.coordinates, point);\n  },\n  MultiPolygon: function(object, point) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) if (containsPolygon(coordinates[i], point)) return true;\n    return false;\n  },\n  GeometryCollection: function(object, point) {\n    var geometries = object.geometries, i = -1, n = geometries.length;\n    while (++i < n) if (containsGeometry(geometries[i], point)) return true;\n    return false;\n  }\n};\n\nfunction containsGeometry(geometry, point) {\n  return geometry && containsGeometryType.hasOwnProperty(geometry.type)\n      ? containsGeometryType[geometry.type](geometry, point)\n      : false;\n}\n\nfunction containsPoint(coordinates, point) {\n  return distance(coordinates, point) === 0;\n}\n\nfunction containsLine(coordinates, point) {\n  var ao, bo, ab;\n  for (var i = 0, n = coordinates.length; i < n; i++) {\n    bo = distance(coordinates[i], point);\n    if (bo === 0) return true;\n    if (i > 0) {\n      ab = distance(coordinates[i], coordinates[i - 1]);\n      if (\n        ab > 0 &&\n        ao <= ab &&\n        bo <= ab &&\n        (ao + bo - ab) * (1 - Math.pow((ao - bo) / ab, 2)) < epsilon2 * ab\n      )\n        return true;\n    }\n    ao = bo;\n  }\n  return false;\n}\n\nfunction containsPolygon(coordinates, point) {\n  return !!polygonContains(coordinates.map(ringRadians), pointRadians(point));\n}\n\nfunction ringRadians(ring) {\n  return ring = ring.map(pointRadians), ring.pop(), ring;\n}\n\nfunction pointRadians(point) {\n  return [point[0] * radians, point[1] * radians];\n}\n\nexport default function(object, point) {\n  return (object && containsObjectType.hasOwnProperty(object.type)\n      ? containsObjectType[object.type]\n      : containsGeometry)(object, point);\n}\n"], "mappings": "AAAA,SAAQA,OAAO,IAAIC,eAAe,QAAO,sBAAsB;AAC/D,SAAQD,OAAO,IAAIE,QAAQ,QAAO,eAAe;AACjD,SAAQC,QAAQ,EAAEC,OAAO,QAAO,WAAW;AAE3C,IAAIC,kBAAkB,GAAG;EACvBC,OAAO,EAAE,SAAAA,CAASC,MAAM,EAAEC,KAAK,EAAE;IAC/B,OAAOC,gBAAgB,CAACF,MAAM,CAACG,QAAQ,EAAEF,KAAK,CAAC;EACjD,CAAC;EACDG,iBAAiB,EAAE,SAAAA,CAASJ,MAAM,EAAEC,KAAK,EAAE;IACzC,IAAII,QAAQ,GAAGL,MAAM,CAACK,QAAQ;MAAEC,CAAC,GAAG,CAAC,CAAC;MAAEC,CAAC,GAAGF,QAAQ,CAACG,MAAM;IAC3D,OAAO,EAAEF,CAAC,GAAGC,CAAC,EAAE,IAAIL,gBAAgB,CAACG,QAAQ,CAACC,CAAC,CAAC,CAACH,QAAQ,EAAEF,KAAK,CAAC,EAAE,OAAO,IAAI;IAC9E,OAAO,KAAK;EACd;AACF,CAAC;AAED,IAAIQ,oBAAoB,GAAG;EACzBC,MAAM,EAAE,SAAAA,CAAA,EAAW;IACjB,OAAO,IAAI;EACb,CAAC;EACDC,KAAK,EAAE,SAAAA,CAASX,MAAM,EAAEC,KAAK,EAAE;IAC7B,OAAOW,aAAa,CAACZ,MAAM,CAACa,WAAW,EAAEZ,KAAK,CAAC;EACjD,CAAC;EACDa,UAAU,EAAE,SAAAA,CAASd,MAAM,EAAEC,KAAK,EAAE;IAClC,IAAIY,WAAW,GAAGb,MAAM,CAACa,WAAW;MAAEP,CAAC,GAAG,CAAC,CAAC;MAAEC,CAAC,GAAGM,WAAW,CAACL,MAAM;IACpE,OAAO,EAAEF,CAAC,GAAGC,CAAC,EAAE,IAAIK,aAAa,CAACC,WAAW,CAACP,CAAC,CAAC,EAAEL,KAAK,CAAC,EAAE,OAAO,IAAI;IACrE,OAAO,KAAK;EACd,CAAC;EACDc,UAAU,EAAE,SAAAA,CAASf,MAAM,EAAEC,KAAK,EAAE;IAClC,OAAOe,YAAY,CAAChB,MAAM,CAACa,WAAW,EAAEZ,KAAK,CAAC;EAChD,CAAC;EACDgB,eAAe,EAAE,SAAAA,CAASjB,MAAM,EAAEC,KAAK,EAAE;IACvC,IAAIY,WAAW,GAAGb,MAAM,CAACa,WAAW;MAAEP,CAAC,GAAG,CAAC,CAAC;MAAEC,CAAC,GAAGM,WAAW,CAACL,MAAM;IACpE,OAAO,EAAEF,CAAC,GAAGC,CAAC,EAAE,IAAIS,YAAY,CAACH,WAAW,CAACP,CAAC,CAAC,EAAEL,KAAK,CAAC,EAAE,OAAO,IAAI;IACpE,OAAO,KAAK;EACd,CAAC;EACDiB,OAAO,EAAE,SAAAA,CAASlB,MAAM,EAAEC,KAAK,EAAE;IAC/B,OAAOkB,eAAe,CAACnB,MAAM,CAACa,WAAW,EAAEZ,KAAK,CAAC;EACnD,CAAC;EACDmB,YAAY,EAAE,SAAAA,CAASpB,MAAM,EAAEC,KAAK,EAAE;IACpC,IAAIY,WAAW,GAAGb,MAAM,CAACa,WAAW;MAAEP,CAAC,GAAG,CAAC,CAAC;MAAEC,CAAC,GAAGM,WAAW,CAACL,MAAM;IACpE,OAAO,EAAEF,CAAC,GAAGC,CAAC,EAAE,IAAIY,eAAe,CAACN,WAAW,CAACP,CAAC,CAAC,EAAEL,KAAK,CAAC,EAAE,OAAO,IAAI;IACvE,OAAO,KAAK;EACd,CAAC;EACDoB,kBAAkB,EAAE,SAAAA,CAASrB,MAAM,EAAEC,KAAK,EAAE;IAC1C,IAAIqB,UAAU,GAAGtB,MAAM,CAACsB,UAAU;MAAEhB,CAAC,GAAG,CAAC,CAAC;MAAEC,CAAC,GAAGe,UAAU,CAACd,MAAM;IACjE,OAAO,EAAEF,CAAC,GAAGC,CAAC,EAAE,IAAIL,gBAAgB,CAACoB,UAAU,CAAChB,CAAC,CAAC,EAAEL,KAAK,CAAC,EAAE,OAAO,IAAI;IACvE,OAAO,KAAK;EACd;AACF,CAAC;AAED,SAASC,gBAAgBA,CAACC,QAAQ,EAAEF,KAAK,EAAE;EACzC,OAAOE,QAAQ,IAAIM,oBAAoB,CAACc,cAAc,CAACpB,QAAQ,CAACqB,IAAI,CAAC,GAC/Df,oBAAoB,CAACN,QAAQ,CAACqB,IAAI,CAAC,CAACrB,QAAQ,EAAEF,KAAK,CAAC,GACpD,KAAK;AACb;AAEA,SAASW,aAAaA,CAACC,WAAW,EAAEZ,KAAK,EAAE;EACzC,OAAON,QAAQ,CAACkB,WAAW,EAAEZ,KAAK,CAAC,KAAK,CAAC;AAC3C;AAEA,SAASe,YAAYA,CAACH,WAAW,EAAEZ,KAAK,EAAE;EACxC,IAAIwB,EAAE,EAAEC,EAAE,EAAEC,EAAE;EACd,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGM,WAAW,CAACL,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;IAClDoB,EAAE,GAAG/B,QAAQ,CAACkB,WAAW,CAACP,CAAC,CAAC,EAAEL,KAAK,CAAC;IACpC,IAAIyB,EAAE,KAAK,CAAC,EAAE,OAAO,IAAI;IACzB,IAAIpB,CAAC,GAAG,CAAC,EAAE;MACTqB,EAAE,GAAGhC,QAAQ,CAACkB,WAAW,CAACP,CAAC,CAAC,EAAEO,WAAW,CAACP,CAAC,GAAG,CAAC,CAAC,CAAC;MACjD,IACEqB,EAAE,GAAG,CAAC,IACNF,EAAE,IAAIE,EAAE,IACRD,EAAE,IAAIC,EAAE,IACR,CAACF,EAAE,GAAGC,EAAE,GAAGC,EAAE,KAAK,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAACJ,EAAE,GAAGC,EAAE,IAAIC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG/B,QAAQ,GAAG+B,EAAE,EAElE,OAAO,IAAI;IACf;IACAF,EAAE,GAAGC,EAAE;EACT;EACA,OAAO,KAAK;AACd;AAEA,SAASP,eAAeA,CAACN,WAAW,EAAEZ,KAAK,EAAE;EAC3C,OAAO,CAAC,CAACP,eAAe,CAACmB,WAAW,CAACiB,GAAG,CAACC,WAAW,CAAC,EAAEC,YAAY,CAAC/B,KAAK,CAAC,CAAC;AAC7E;AAEA,SAAS8B,WAAWA,CAACE,IAAI,EAAE;EACzB,OAAOA,IAAI,GAAGA,IAAI,CAACH,GAAG,CAACE,YAAY,CAAC,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI;AACxD;AAEA,SAASD,YAAYA,CAAC/B,KAAK,EAAE;EAC3B,OAAO,CAACA,KAAK,CAAC,CAAC,CAAC,GAAGJ,OAAO,EAAEI,KAAK,CAAC,CAAC,CAAC,GAAGJ,OAAO,CAAC;AACjD;AAEA,eAAe,UAASG,MAAM,EAAEC,KAAK,EAAE;EACrC,OAAO,CAACD,MAAM,IAAIF,kBAAkB,CAACyB,cAAc,CAACvB,MAAM,CAACwB,IAAI,CAAC,GAC1D1B,kBAAkB,CAACE,MAAM,CAACwB,IAAI,CAAC,GAC/BtB,gBAAgB,EAAEF,MAAM,EAAEC,KAAK,CAAC;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}