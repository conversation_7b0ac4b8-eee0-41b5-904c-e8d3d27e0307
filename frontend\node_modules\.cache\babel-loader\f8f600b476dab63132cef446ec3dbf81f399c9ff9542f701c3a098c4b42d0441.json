{"ast": null, "code": "// `@antv/vendor/d3-force` (ESM)\n// See upstream license: https://github.com/d3/d3-force/blob/main/LICENSE\n//\n// Our ESM package uses the underlying installed dependencies of `node_modules/d3-force`\nexport * from \"d3-force\";", "map": {"version": 3, "names": [], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/@antv/vendor/es/d3-force.mjs"], "sourcesContent": ["\n// `@antv/vendor/d3-force` (ESM)\n// See upstream license: https://github.com/d3/d3-force/blob/main/LICENSE\n//\n// Our ESM package uses the underlying installed dependencies of `node_modules/d3-force`\nexport * from \"d3-force\";\n"], "mappings": "AACA;AACA;AACA;AACA;AACA,cAAc,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}