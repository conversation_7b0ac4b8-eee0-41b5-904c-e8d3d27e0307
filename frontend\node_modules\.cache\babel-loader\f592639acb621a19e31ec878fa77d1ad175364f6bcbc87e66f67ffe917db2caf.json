{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { genStyleHooks } from '../../theme/internal';\nconst genLayoutStyle = token => {\n  const {\n    antCls,\n    // .ant\n    componentCls,\n    // .ant-layout\n    colorText,\n    footerBg,\n    headerHeight,\n    headerPadding,\n    headerColor,\n    footerPadding,\n    fontSize,\n    bodyBg,\n    headerBg\n  } = token;\n  return {\n    [componentCls]: {\n      display: 'flex',\n      flex: 'auto',\n      flexDirection: 'column',\n      /* fix firefox can't set height smaller than content on flex item */\n      minHeight: 0,\n      background: bodyBg,\n      '&, *': {\n        boxSizing: 'border-box'\n      },\n      [`&${componentCls}-has-sider`]: {\n        flexDirection: 'row',\n        [`> ${componentCls}, > ${componentCls}-content`]: {\n          // https://segmentfault.com/a/1190000019498300\n          width: 0\n        }\n      },\n      [`${componentCls}-header, &${componentCls}-footer`]: {\n        flex: '0 0 auto'\n      },\n      // RTL\n      '&-rtl': {\n        direction: 'rtl'\n      }\n    },\n    // ==================== Header ====================\n    [`${componentCls}-header`]: {\n      height: headerHeight,\n      padding: headerPadding,\n      color: headerColor,\n      lineHeight: unit(headerHeight),\n      background: headerBg,\n      // Other components/menu/style/index.less line:686\n      // Integration with header element so menu items have the same height\n      [`${antCls}-menu`]: {\n        lineHeight: 'inherit'\n      }\n    },\n    // ==================== Footer ====================\n    [`${componentCls}-footer`]: {\n      padding: footerPadding,\n      color: colorText,\n      fontSize,\n      background: footerBg\n    },\n    // =================== Content ====================\n    [`${componentCls}-content`]: {\n      flex: 'auto',\n      color: colorText,\n      // fix firefox can't set height smaller than content on flex item\n      minHeight: 0\n    }\n  };\n};\nexport const prepareComponentToken = token => {\n  const {\n    colorBgLayout,\n    controlHeight,\n    controlHeightLG,\n    colorText,\n    controlHeightSM,\n    marginXXS,\n    colorTextLightSolid,\n    colorBgContainer\n  } = token;\n  const paddingInline = controlHeightLG * 1.25;\n  return {\n    // Deprecated\n    colorBgHeader: '#001529',\n    colorBgBody: colorBgLayout,\n    colorBgTrigger: '#002140',\n    bodyBg: colorBgLayout,\n    headerBg: '#001529',\n    headerHeight: controlHeight * 2,\n    headerPadding: `0 ${paddingInline}px`,\n    headerColor: colorText,\n    footerPadding: `${controlHeightSM}px ${paddingInline}px`,\n    footerBg: colorBgLayout,\n    siderBg: '#001529',\n    triggerHeight: controlHeightLG + marginXXS * 2,\n    triggerBg: '#002140',\n    triggerColor: colorTextLightSolid,\n    zeroTriggerWidth: controlHeightLG,\n    zeroTriggerHeight: controlHeightLG,\n    lightSiderBg: colorBgContainer,\n    lightTriggerBg: colorBgContainer,\n    lightTriggerColor: colorText\n  };\n};\n// ============================== Export ==============================\nexport const DEPRECATED_TOKENS = [['colorBgBody', 'bodyBg'], ['colorBgHeader', 'headerBg'], ['colorBgTrigger', 'triggerBg']];\nexport default genStyleHooks('Layout', genLayoutStyle, prepareComponentToken, {\n  deprecatedTokens: DEPRECATED_TOKENS\n});", "map": {"version": 3, "names": ["unit", "genStyleHooks", "genLayoutStyle", "token", "antCls", "componentCls", "colorText", "footerBg", "headerHeight", "headerPadding", "headerColor", "footerPadding", "fontSize", "bodyBg", "headerBg", "display", "flex", "flexDirection", "minHeight", "background", "boxSizing", "width", "direction", "height", "padding", "color", "lineHeight", "prepareComponentToken", "colorBgLayout", "controlHeight", "controlHeightLG", "controlHeightSM", "marginXXS", "colorTextLightSolid", "colorBgContainer", "paddingInline", "colorBgHeader", "colorBgBody", "colorBgTrigger", "siderBg", "triggerHeight", "triggerBg", "triggerColor", "zeroTriggerWidth", "zeroTriggerHeight", "lightSiderBg", "lightTriggerBg", "lightTriggerColor", "DEPRECATED_TOKENS", "deprecatedTokens"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/antd/es/layout/style/index.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { genStyleHooks } from '../../theme/internal';\nconst genLayoutStyle = token => {\n  const {\n    antCls,\n    // .ant\n    componentCls,\n    // .ant-layout\n    colorText,\n    footerBg,\n    headerHeight,\n    headerPadding,\n    headerColor,\n    footerPadding,\n    fontSize,\n    bodyBg,\n    headerBg\n  } = token;\n  return {\n    [componentCls]: {\n      display: 'flex',\n      flex: 'auto',\n      flexDirection: 'column',\n      /* fix firefox can't set height smaller than content on flex item */\n      minHeight: 0,\n      background: bodyBg,\n      '&, *': {\n        boxSizing: 'border-box'\n      },\n      [`&${componentCls}-has-sider`]: {\n        flexDirection: 'row',\n        [`> ${componentCls}, > ${componentCls}-content`]: {\n          // https://segmentfault.com/a/1190000019498300\n          width: 0\n        }\n      },\n      [`${componentCls}-header, &${componentCls}-footer`]: {\n        flex: '0 0 auto'\n      },\n      // RTL\n      '&-rtl': {\n        direction: 'rtl'\n      }\n    },\n    // ==================== Header ====================\n    [`${componentCls}-header`]: {\n      height: headerHeight,\n      padding: headerPadding,\n      color: headerColor,\n      lineHeight: unit(headerHeight),\n      background: headerBg,\n      // Other components/menu/style/index.less line:686\n      // Integration with header element so menu items have the same height\n      [`${antCls}-menu`]: {\n        lineHeight: 'inherit'\n      }\n    },\n    // ==================== Footer ====================\n    [`${componentCls}-footer`]: {\n      padding: footerPadding,\n      color: colorText,\n      fontSize,\n      background: footerBg\n    },\n    // =================== Content ====================\n    [`${componentCls}-content`]: {\n      flex: 'auto',\n      color: colorText,\n      // fix firefox can't set height smaller than content on flex item\n      minHeight: 0\n    }\n  };\n};\nexport const prepareComponentToken = token => {\n  const {\n    colorBgLayout,\n    controlHeight,\n    controlHeightLG,\n    colorText,\n    controlHeightSM,\n    marginXXS,\n    colorTextLightSolid,\n    colorBgContainer\n  } = token;\n  const paddingInline = controlHeightLG * 1.25;\n  return {\n    // Deprecated\n    colorBgHeader: '#001529',\n    colorBgBody: colorBgLayout,\n    colorBgTrigger: '#002140',\n    bodyBg: colorBgLayout,\n    headerBg: '#001529',\n    headerHeight: controlHeight * 2,\n    headerPadding: `0 ${paddingInline}px`,\n    headerColor: colorText,\n    footerPadding: `${controlHeightSM}px ${paddingInline}px`,\n    footerBg: colorBgLayout,\n    siderBg: '#001529',\n    triggerHeight: controlHeightLG + marginXXS * 2,\n    triggerBg: '#002140',\n    triggerColor: colorTextLightSolid,\n    zeroTriggerWidth: controlHeightLG,\n    zeroTriggerHeight: controlHeightLG,\n    lightSiderBg: colorBgContainer,\n    lightTriggerBg: colorBgContainer,\n    lightTriggerColor: colorText\n  };\n};\n// ============================== Export ==============================\nexport const DEPRECATED_TOKENS = [['colorBgBody', 'bodyBg'], ['colorBgHeader', 'headerBg'], ['colorBgTrigger', 'triggerBg']];\nexport default genStyleHooks('Layout', genLayoutStyle, prepareComponentToken, {\n  deprecatedTokens: DEPRECATED_TOKENS\n});"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,aAAa,QAAQ,sBAAsB;AACpD,MAAMC,cAAc,GAAGC,KAAK,IAAI;EAC9B,MAAM;IACJC,MAAM;IACN;IACAC,YAAY;IACZ;IACAC,SAAS;IACTC,QAAQ;IACRC,YAAY;IACZC,aAAa;IACbC,WAAW;IACXC,aAAa;IACbC,QAAQ;IACRC,MAAM;IACNC;EACF,CAAC,GAAGX,KAAK;EACT,OAAO;IACL,CAACE,YAAY,GAAG;MACdU,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,MAAM;MACZC,aAAa,EAAE,QAAQ;MACvB;MACAC,SAAS,EAAE,CAAC;MACZC,UAAU,EAAEN,MAAM;MAClB,MAAM,EAAE;QACNO,SAAS,EAAE;MACb,CAAC;MACD,CAAC,IAAIf,YAAY,YAAY,GAAG;QAC9BY,aAAa,EAAE,KAAK;QACpB,CAAC,KAAKZ,YAAY,OAAOA,YAAY,UAAU,GAAG;UAChD;UACAgB,KAAK,EAAE;QACT;MACF,CAAC;MACD,CAAC,GAAGhB,YAAY,aAAaA,YAAY,SAAS,GAAG;QACnDW,IAAI,EAAE;MACR,CAAC;MACD;MACA,OAAO,EAAE;QACPM,SAAS,EAAE;MACb;IACF,CAAC;IACD;IACA,CAAC,GAAGjB,YAAY,SAAS,GAAG;MAC1BkB,MAAM,EAAEf,YAAY;MACpBgB,OAAO,EAAEf,aAAa;MACtBgB,KAAK,EAAEf,WAAW;MAClBgB,UAAU,EAAE1B,IAAI,CAACQ,YAAY,CAAC;MAC9BW,UAAU,EAAEL,QAAQ;MACpB;MACA;MACA,CAAC,GAAGV,MAAM,OAAO,GAAG;QAClBsB,UAAU,EAAE;MACd;IACF,CAAC;IACD;IACA,CAAC,GAAGrB,YAAY,SAAS,GAAG;MAC1BmB,OAAO,EAAEb,aAAa;MACtBc,KAAK,EAAEnB,SAAS;MAChBM,QAAQ;MACRO,UAAU,EAAEZ;IACd,CAAC;IACD;IACA,CAAC,GAAGF,YAAY,UAAU,GAAG;MAC3BW,IAAI,EAAE,MAAM;MACZS,KAAK,EAAEnB,SAAS;MAChB;MACAY,SAAS,EAAE;IACb;EACF,CAAC;AACH,CAAC;AACD,OAAO,MAAMS,qBAAqB,GAAGxB,KAAK,IAAI;EAC5C,MAAM;IACJyB,aAAa;IACbC,aAAa;IACbC,eAAe;IACfxB,SAAS;IACTyB,eAAe;IACfC,SAAS;IACTC,mBAAmB;IACnBC;EACF,CAAC,GAAG/B,KAAK;EACT,MAAMgC,aAAa,GAAGL,eAAe,GAAG,IAAI;EAC5C,OAAO;IACL;IACAM,aAAa,EAAE,SAAS;IACxBC,WAAW,EAAET,aAAa;IAC1BU,cAAc,EAAE,SAAS;IACzBzB,MAAM,EAAEe,aAAa;IACrBd,QAAQ,EAAE,SAAS;IACnBN,YAAY,EAAEqB,aAAa,GAAG,CAAC;IAC/BpB,aAAa,EAAE,KAAK0B,aAAa,IAAI;IACrCzB,WAAW,EAAEJ,SAAS;IACtBK,aAAa,EAAE,GAAGoB,eAAe,MAAMI,aAAa,IAAI;IACxD5B,QAAQ,EAAEqB,aAAa;IACvBW,OAAO,EAAE,SAAS;IAClBC,aAAa,EAAEV,eAAe,GAAGE,SAAS,GAAG,CAAC;IAC9CS,SAAS,EAAE,SAAS;IACpBC,YAAY,EAAET,mBAAmB;IACjCU,gBAAgB,EAAEb,eAAe;IACjCc,iBAAiB,EAAEd,eAAe;IAClCe,YAAY,EAAEX,gBAAgB;IAC9BY,cAAc,EAAEZ,gBAAgB;IAChCa,iBAAiB,EAAEzC;EACrB,CAAC;AACH,CAAC;AACD;AACA,OAAO,MAAM0C,iBAAiB,GAAG,CAAC,CAAC,aAAa,EAAE,QAAQ,CAAC,EAAE,CAAC,eAAe,EAAE,UAAU,CAAC,EAAE,CAAC,gBAAgB,EAAE,WAAW,CAAC,CAAC;AAC5H,eAAe/C,aAAa,CAAC,QAAQ,EAAEC,cAAc,EAAEyB,qBAAqB,EAAE;EAC5EsB,gBAAgB,EAAED;AACpB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}