package com.yinma.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yinma.entity.BomEntity;
import com.yinma.entity.BomDetailEntity;
import com.yinma.entity.BomChangeLogEntity;
import com.yinma.dto.BomDTO;
import com.yinma.mapper.BomMapper;
import com.yinma.mapper.BomDetailMapper;
import com.yinma.mapper.BomChangeLogMapper;
import com.yinma.service.BomService;
import com.yinma.service.BomDetailService;
import com.yinma.service.BomChangeLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * BOM管理Service实现类
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
@Service
public class BomServiceImpl extends ServiceImpl<BomMapper, BomEntity> implements BomService {

    @Autowired
    private BomMapper bomMapper;
    
    @Autowired
    private BomDetailMapper bomDetailMapper;
    
    @Autowired
    private BomChangeLogMapper bomChangeLogMapper;
    
    @Autowired
    private BomDetailService bomDetailService;
    
    @Autowired
    private BomChangeLogService bomChangeLogService;

    @Override
    public Page<BomDTO> queryPage(BomDTO.BomQueryDTO queryDTO) {
        Page<BomEntity> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        Page<BomEntity> result = bomMapper.queryPage(page, queryDTO);
        
        Page<BomDTO> dtoPage = new Page<>();
        dtoPage.setCurrent(result.getCurrent());
        dtoPage.setSize(result.getSize());
        dtoPage.setTotal(result.getTotal());
        
        List<BomDTO> dtoList = result.getRecords().stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
        dtoPage.setRecords(dtoList);
        
        return dtoPage;
    }

    @Override
    public BomDTO getBomDetail(Long bomId) {
        BomEntity entity = bomMapper.selectById(bomId);
        if (entity == null) {
            return null;
        }
        
        BomDTO dto = convertToDTO(entity);
        
        // 加载BOM明细
        List<BomDetailEntity> details = bomDetailMapper.selectByBomId(bomId);
        dto.setDetailList(details.stream()
            .map(this::convertDetailToDTO)
            .collect(Collectors.toList()));
        
        // 加载子级BOM
        List<BomEntity> subBoms = bomMapper.selectSubBomsByParentId(bomId);
        dto.setSubBomList(subBoms.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList()));
        
        return dto;
    }

    @Override
    public List<BomDTO> getBomTree(Long rootBomId) {
        List<BomEntity> allBoms = bomMapper.selectBomTree(rootBomId);
        return buildBomTree(allBoms, rootBomId);
    }

    @Override
    public List<BomDTO> getMainBomList(BomDTO.BomQueryDTO queryDTO) {
        List<BomEntity> entities = bomMapper.selectMainBomList(queryDTO);
        return entities.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createBom(BomDTO bomDTO) {
        // 校验BOM编码唯一性
        if (checkBomCodeExists(bomDTO.getBomCode(), null)) {
            throw new RuntimeException("BOM编码已存在: " + bomDTO.getBomCode());
        }
        
        BomEntity entity = convertToEntity(bomDTO);
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setVersion("1.0");
        entity.setVersionStatus("DRAFT");
        
        bomMapper.insert(entity);
        
        // 创建变更日志
        createChangeLog(entity.getBomId(), "CREATE", "BOM", "创建BOM", bomDTO.getCreateUserId());
        
        return entity.getBomId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateBom(BomDTO bomDTO) {
        BomEntity existingEntity = bomMapper.selectById(bomDTO.getBomId());
        if (existingEntity == null) {
            throw new RuntimeException("BOM不存在: " + bomDTO.getBomId());
        }
        
        // 校验BOM编码唯一性
        if (checkBomCodeExists(bomDTO.getBomCode(), bomDTO.getBomId())) {
            throw new RuntimeException("BOM编码已存在: " + bomDTO.getBomCode());
        }
        
        BomEntity entity = convertToEntity(bomDTO);
        entity.setUpdateTime(LocalDateTime.now());
        
        bomMapper.updateById(entity);
        
        // 创建变更日志
        createChangeLog(entity.getBomId(), "UPDATE", "BOM", "更新BOM", bomDTO.getUpdateUserId());
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteBom(Long bomId, Long deleteUserId) {
        BomEntity entity = bomMapper.selectById(bomId);
        if (entity == null) {
            return false;
        }
        
        // 检查是否有子级BOM
        List<BomEntity> subBoms = bomMapper.selectSubBomsByParentId(bomId);
        if (!subBoms.isEmpty()) {
            throw new RuntimeException("存在子级BOM，无法删除");
        }
        
        // 检查是否被其他BOM引用
        List<BomDetailEntity> references = bomDetailMapper.selectBySubBomId(bomId);
        if (!references.isEmpty()) {
            throw new RuntimeException("BOM被其他BOM引用，无法删除");
        }
        
        // 删除BOM明细
        bomDetailMapper.deleteByBomId(bomId);
        
        // 删除BOM主表
        bomMapper.deleteById(bomId);
        
        // 创建变更日志
        createChangeLog(bomId, "DELETE", "BOM", "删除BOM", deleteUserId);
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDeleteBom(List<Long> bomIds, Long deleteUserId) {
        for (Long bomId : bomIds) {
            deleteBom(bomId, deleteUserId);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long copyBom(Long sourceBomId, String newBomCode, String newBomName, Long createUserId) {
        BomEntity sourceBom = bomMapper.selectById(sourceBomId);
        if (sourceBom == null) {
            throw new RuntimeException("源BOM不存在: " + sourceBomId);
        }
        
        // 校验新BOM编码唯一性
        if (checkBomCodeExists(newBomCode, null)) {
            throw new RuntimeException("BOM编码已存在: " + newBomCode);
        }
        
        // 复制BOM主表
        BomEntity newBom = new BomEntity();
        copyBomProperties(sourceBom, newBom);
        newBom.setBomId(null);
        newBom.setBomCode(newBomCode);
        newBom.setBomName(newBomName);
        newBom.setVersion("1.0");
        newBom.setVersionStatus("DRAFT");
        newBom.setCreateUserId(createUserId);
        newBom.setCreateTime(LocalDateTime.now());
        newBom.setUpdateTime(LocalDateTime.now());
        
        bomMapper.insert(newBom);
        
        // 复制BOM明细
        List<BomDetailEntity> sourceDetails = bomDetailMapper.selectByBomId(sourceBomId);
        for (BomDetailEntity sourceDetail : sourceDetails) {
            BomDetailEntity newDetail = new BomDetailEntity();
            copyDetailProperties(sourceDetail, newDetail);
            newDetail.setDetailId(null);
            newDetail.setBomId(newBom.getBomId());
            newDetail.setCreateUserId(createUserId);
            newDetail.setCreateTime(LocalDateTime.now());
            bomDetailMapper.insert(newDetail);
        }
        
        // 创建变更日志
        createChangeLog(newBom.getBomId(), "COPY", "BOM", "复制BOM，源BOM: " + sourceBom.getBomCode(), createUserId);
        
        return newBom.getBomId();
    }

    @Override
    public List<BomDTO> getBomVersionHistory(Long bomId) {
        List<BomEntity> versions = bomMapper.selectVersionHistory(bomId);
        return versions.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createNewVersion(Long bomId, String newVersion, String versionDescription, Long createUserId) {
        BomEntity currentBom = bomMapper.selectById(bomId);
        if (currentBom == null) {
            throw new RuntimeException("BOM不存在: " + bomId);
        }
        
        // 生成新版本BOM编码
        String newBomCode = generateVersionBomCode(currentBom.getBomCode(), newVersion);
        
        // 复制当前版本创建新版本
        Long newBomId = copyBom(bomId, newBomCode, currentBom.getBomName() + " v" + newVersion, createUserId);
        
        // 更新新版本信息
        BomEntity newVersionBom = bomMapper.selectById(newBomId);
        newVersionBom.setVersion(newVersion);
        newVersionBom.setVersionDescription(versionDescription);
        newVersionBom.setParentBomId(currentBom.getParentBomId() != null ? currentBom.getParentBomId() : bomId);
        bomMapper.updateById(newVersionBom);
        
        // 创建变更日志
        createChangeLog(newBomId, "VERSION", "BOM", "创建新版本: " + newVersion, createUserId);
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean activateVersion(Long bomId, Long activateUserId) {
        BomEntity bom = bomMapper.selectById(bomId);
        if (bom == null) {
            throw new RuntimeException("BOM不存在: " + bomId);
        }
        
        // 将同一BOM的其他版本设为非激活
        bomMapper.deactivateOtherVersions(bom.getBomCode());
        
        // 激活当前版本
        bom.setVersionStatus("ACTIVE");
        bom.setUpdateUserId(activateUserId);
        bom.setUpdateTime(LocalDateTime.now());
        bomMapper.updateById(bom);
        
        // 创建变更日志
        createChangeLog(bomId, "ACTIVATE", "BOM", "激活版本: " + bom.getVersion(), activateUserId);
        
        return true;
    }

    @Override
    public Boolean checkBomCodeExists(String bomCode, Long excludeId) {
        QueryWrapper<BomEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("bom_code", bomCode);
        if (excludeId != null) {
            wrapper.ne("bom_id", excludeId);
        }
        return bomMapper.selectCount(wrapper) > 0;
    }

    @Override
    public String generateBomCodeSuggestion(String bomType, String productCode) {
        String prefix = "BOM";
        if (StringUtils.hasText(bomType)) {
            prefix = bomType.toUpperCase();
        }
        if (StringUtils.hasText(productCode)) {
            prefix += "-" + productCode;
        }
        
        // 查找最大序号
        String pattern = prefix + "-%";
        Integer maxSequence = bomMapper.selectMaxSequenceByPattern(pattern);
        
        return prefix + "-" + String.format("%04d", (maxSequence != null ? maxSequence : 0) + 1);
    }

    @Override
    public List<BomDTO.MaterialRequirementDTO> calculateMaterialRequirement(Long bomId, Double quantity) {
        return bomMapper.calculateMaterialRequirement(bomId, quantity);
    }

    @Override
    public List<BomDTO.CostAnalysisDTO> analyzeBomCost(Long bomId) {
        return bomMapper.analyzeBomCost(bomId);
    }

    @Override
    public String exportBom(BomDTO.BomQueryDTO queryDTO) {
        // TODO: 实现BOM导出功能
        return "/exports/bom_" + System.currentTimeMillis() + ".xlsx";
    }

    @Override
    public Map<String, Object> importBom(String filePath, Long importUserId) {
        // TODO: 实现BOM导入功能
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "导入成功");
        result.put("importCount", 0);
        result.put("errorCount", 0);
        return result;
    }

    @Override
    public Map<String, Object> validateBomData(BomDTO bomDTO) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        
        // 校验BOM编码
        if (!StringUtils.hasText(bomDTO.getBomCode())) {
            errors.add("BOM编码不能为空");
        } else if (checkBomCodeExists(bomDTO.getBomCode(), bomDTO.getBomId())) {
            errors.add("BOM编码已存在");
        }
        
        // 校验BOM名称
        if (!StringUtils.hasText(bomDTO.getBomName())) {
            errors.add("BOM名称不能为空");
        }
        
        // 校验BOM类型
        if (!StringUtils.hasText(bomDTO.getBomType())) {
            errors.add("BOM类型不能为空");
        }
        
        result.put("valid", errors.isEmpty());
        result.put("errors", errors);
        return result;
    }

    @Override
    public Map<String, Object> getBomDashboard() {
        Map<String, Object> dashboard = new HashMap<>();
        
        // 统计数据
        dashboard.put("totalCount", bomMapper.selectCount(null));
        dashboard.put("activeCount", bomMapper.selectCountByStatus("ACTIVE"));
        dashboard.put("draftCount", bomMapper.selectCountByStatus("DRAFT"));
        dashboard.put("obsoleteCount", bomMapper.selectCountByStatus("OBSOLETE"));
        
        // 类型分布
        dashboard.put("typeDistribution", bomMapper.selectTypeDistribution());
        
        // 最近更新
        dashboard.put("recentUpdates", bomMapper.selectRecentUpdates(10));
        
        return dashboard;
    }

    // 私有方法
    
    private BomDTO convertToDTO(BomEntity entity) {
        if (entity == null) {
            return null;
        }
        
        BomDTO dto = new BomDTO();
        dto.setBomId(entity.getBomId());
        dto.setBomCode(entity.getBomCode());
        dto.setBomName(entity.getBomName());
        dto.setBomType(entity.getBomType());
        dto.setProductCode(entity.getProductCode());
        dto.setProductName(entity.getProductName());
        dto.setVersion(entity.getVersion());
        dto.setVersionStatus(entity.getVersionStatus());
        dto.setVersionDescription(entity.getVersionDescription());
        dto.setEffectiveDate(entity.getEffectiveDate());
        dto.setExpiryDate(entity.getExpiryDate());
        dto.setParentBomId(entity.getParentBomId());
        dto.setConversionSource(entity.getConversionSource());
        dto.setConversionDate(entity.getConversionDate());
        dto.setRemark(entity.getRemark());
        dto.setCreateUserId(entity.getCreateUserId());
        dto.setCreateTime(entity.getCreateTime());
        dto.setUpdateUserId(entity.getUpdateUserId());
        dto.setUpdateTime(entity.getUpdateTime());
        
        return dto;
    }
    
    private BomEntity convertToEntity(BomDTO dto) {
        if (dto == null) {
            return null;
        }
        
        BomEntity entity = new BomEntity();
        entity.setBomId(dto.getBomId());
        entity.setBomCode(dto.getBomCode());
        entity.setBomName(dto.getBomName());
        entity.setBomType(dto.getBomType());
        entity.setProductCode(dto.getProductCode());
        entity.setProductName(dto.getProductName());
        entity.setVersion(dto.getVersion());
        entity.setVersionStatus(dto.getVersionStatus());
        entity.setVersionDescription(dto.getVersionDescription());
        entity.setEffectiveDate(dto.getEffectiveDate());
        entity.setExpiryDate(dto.getExpiryDate());
        entity.setParentBomId(dto.getParentBomId());
        entity.setConversionSource(dto.getConversionSource());
        entity.setConversionDate(dto.getConversionDate());
        entity.setRemark(dto.getRemark());
        entity.setCreateUserId(dto.getCreateUserId());
        entity.setUpdateUserId(dto.getUpdateUserId());
        
        return entity;
    }
    
    private BomDTO.BomDetailDTO convertDetailToDTO(BomDetailEntity entity) {
        // TODO: 实现BomDetailEntity到BomDetailDTO的转换
        return new BomDTO.BomDetailDTO();
    }
    
    private List<BomDTO> buildBomTree(List<BomEntity> allBoms, Long parentId) {
        return allBoms.stream()
            .filter(bom -> Objects.equals(bom.getParentBomId(), parentId))
            .map(bom -> {
                BomDTO dto = convertToDTO(bom);
                dto.setSubBomList(buildBomTree(allBoms, bom.getBomId()));
                return dto;
            })
            .collect(Collectors.toList());
    }
    
    private void createChangeLog(Long bomId, String changeType, String objectType, String description, Long userId) {
        BomChangeLogEntity changeLog = new BomChangeLogEntity();
        changeLog.setBomId(bomId);
        changeLog.setChangeType(changeType);
        changeLog.setOperation(changeType);
        changeLog.setObjectType(objectType);
        changeLog.setDescription(description);
        changeLog.setChangeUserId(userId);
        changeLog.setChangeTime(LocalDateTime.now());
        changeLog.setStatus("COMPLETED");
        bomChangeLogMapper.insert(changeLog);
    }
    
    private void copyBomProperties(BomEntity source, BomEntity target) {
        target.setBomName(source.getBomName());
        target.setBomType(source.getBomType());
        target.setProductCode(source.getProductCode());
        target.setProductName(source.getProductName());
        target.setEffectiveDate(source.getEffectiveDate());
        target.setExpiryDate(source.getExpiryDate());
        target.setRemark(source.getRemark());
    }
    
    private void copyDetailProperties(BomDetailEntity source, BomDetailEntity target) {
        target.setMaterialCode(source.getMaterialCode());
        target.setMaterialName(source.getMaterialName());
        target.setMaterialType(source.getMaterialType());
        target.setQuantity(source.getQuantity());
        target.setUnit(source.getUnit());
        target.setUsagePoint(source.getUsagePoint());
        target.setSequenceNo(source.getSequenceNo());
        target.setIsOptional(source.getIsOptional());
        target.setSubstitutable(source.getSubstitutable());
        target.setRemark(source.getRemark());
    }
    
    private String generateVersionBomCode(String baseBomCode, String version) {
        return baseBomCode + "-V" + version;
    }
}