{"ast": null, "code": "import * as glMatrix from \"./common.js\";\n\n/**\n * 3 Dimensional Vector\n * @module vec3\n */\n\n/**\n * Creates a new, empty vec3\n *\n * @returns {vec3} a new 3D vector\n */\nexport function create() {\n  var out = new glMatrix.ARRAY_TYPE(3);\n  if (glMatrix.ARRAY_TYPE != Float32Array) {\n    out[0] = 0;\n    out[1] = 0;\n    out[2] = 0;\n  }\n  return out;\n}\n\n/**\n * Creates a new vec3 initialized with values from an existing vector\n *\n * @param {ReadonlyVec3} a vector to clone\n * @returns {vec3} a new 3D vector\n */\nexport function clone(a) {\n  var out = new glMatrix.ARRAY_TYPE(3);\n  out[0] = a[0];\n  out[1] = a[1];\n  out[2] = a[2];\n  return out;\n}\n\n/**\n * Calculates the length of a vec3\n *\n * @param {ReadonlyVec3} a vector to calculate length of\n * @returns {Number} length of a\n */\nexport function length(a) {\n  var x = a[0];\n  var y = a[1];\n  var z = a[2];\n  return Math.sqrt(x * x + y * y + z * z);\n}\n\n/**\n * Creates a new vec3 initialized with the given values\n *\n * @param {Number} x X component\n * @param {Number} y Y component\n * @param {Number} z Z component\n * @returns {vec3} a new 3D vector\n */\nexport function fromValues(x, y, z) {\n  var out = new glMatrix.ARRAY_TYPE(3);\n  out[0] = x;\n  out[1] = y;\n  out[2] = z;\n  return out;\n}\n\n/**\n * Copy the values from one vec3 to another\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the source vector\n * @returns {vec3} out\n */\nexport function copy(out, a) {\n  out[0] = a[0];\n  out[1] = a[1];\n  out[2] = a[2];\n  return out;\n}\n\n/**\n * Set the components of a vec3 to the given values\n *\n * @param {vec3} out the receiving vector\n * @param {Number} x X component\n * @param {Number} y Y component\n * @param {Number} z Z component\n * @returns {vec3} out\n */\nexport function set(out, x, y, z) {\n  out[0] = x;\n  out[1] = y;\n  out[2] = z;\n  return out;\n}\n\n/**\n * Adds two vec3's\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {vec3} out\n */\nexport function add(out, a, b) {\n  out[0] = a[0] + b[0];\n  out[1] = a[1] + b[1];\n  out[2] = a[2] + b[2];\n  return out;\n}\n\n/**\n * Subtracts vector b from vector a\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {vec3} out\n */\nexport function subtract(out, a, b) {\n  out[0] = a[0] - b[0];\n  out[1] = a[1] - b[1];\n  out[2] = a[2] - b[2];\n  return out;\n}\n\n/**\n * Multiplies two vec3's\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {vec3} out\n */\nexport function multiply(out, a, b) {\n  out[0] = a[0] * b[0];\n  out[1] = a[1] * b[1];\n  out[2] = a[2] * b[2];\n  return out;\n}\n\n/**\n * Divides two vec3's\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {vec3} out\n */\nexport function divide(out, a, b) {\n  out[0] = a[0] / b[0];\n  out[1] = a[1] / b[1];\n  out[2] = a[2] / b[2];\n  return out;\n}\n\n/**\n * Math.ceil the components of a vec3\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a vector to ceil\n * @returns {vec3} out\n */\nexport function ceil(out, a) {\n  out[0] = Math.ceil(a[0]);\n  out[1] = Math.ceil(a[1]);\n  out[2] = Math.ceil(a[2]);\n  return out;\n}\n\n/**\n * Math.floor the components of a vec3\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a vector to floor\n * @returns {vec3} out\n */\nexport function floor(out, a) {\n  out[0] = Math.floor(a[0]);\n  out[1] = Math.floor(a[1]);\n  out[2] = Math.floor(a[2]);\n  return out;\n}\n\n/**\n * Returns the minimum of two vec3's\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {vec3} out\n */\nexport function min(out, a, b) {\n  out[0] = Math.min(a[0], b[0]);\n  out[1] = Math.min(a[1], b[1]);\n  out[2] = Math.min(a[2], b[2]);\n  return out;\n}\n\n/**\n * Returns the maximum of two vec3's\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {vec3} out\n */\nexport function max(out, a, b) {\n  out[0] = Math.max(a[0], b[0]);\n  out[1] = Math.max(a[1], b[1]);\n  out[2] = Math.max(a[2], b[2]);\n  return out;\n}\n\n/**\n * symmetric round the components of a vec3\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a vector to round\n * @returns {vec3} out\n */\nexport function round(out, a) {\n  out[0] = glMatrix.round(a[0]);\n  out[1] = glMatrix.round(a[1]);\n  out[2] = glMatrix.round(a[2]);\n  return out;\n}\n\n/**\n * Scales a vec3 by a scalar number\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the vector to scale\n * @param {Number} b amount to scale the vector by\n * @returns {vec3} out\n */\nexport function scale(out, a, b) {\n  out[0] = a[0] * b;\n  out[1] = a[1] * b;\n  out[2] = a[2] * b;\n  return out;\n}\n\n/**\n * Adds two vec3's after scaling the second operand by a scalar value\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @param {Number} scale the amount to scale b by before adding\n * @returns {vec3} out\n */\nexport function scaleAndAdd(out, a, b, scale) {\n  out[0] = a[0] + b[0] * scale;\n  out[1] = a[1] + b[1] * scale;\n  out[2] = a[2] + b[2] * scale;\n  return out;\n}\n\n/**\n * Calculates the euclidian distance between two vec3's\n *\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {Number} distance between a and b\n */\nexport function distance(a, b) {\n  var x = b[0] - a[0];\n  var y = b[1] - a[1];\n  var z = b[2] - a[2];\n  return Math.sqrt(x * x + y * y + z * z);\n}\n\n/**\n * Calculates the squared euclidian distance between two vec3's\n *\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {Number} squared distance between a and b\n */\nexport function squaredDistance(a, b) {\n  var x = b[0] - a[0];\n  var y = b[1] - a[1];\n  var z = b[2] - a[2];\n  return x * x + y * y + z * z;\n}\n\n/**\n * Calculates the squared length of a vec3\n *\n * @param {ReadonlyVec3} a vector to calculate squared length of\n * @returns {Number} squared length of a\n */\nexport function squaredLength(a) {\n  var x = a[0];\n  var y = a[1];\n  var z = a[2];\n  return x * x + y * y + z * z;\n}\n\n/**\n * Negates the components of a vec3\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a vector to negate\n * @returns {vec3} out\n */\nexport function negate(out, a) {\n  out[0] = -a[0];\n  out[1] = -a[1];\n  out[2] = -a[2];\n  return out;\n}\n\n/**\n * Returns the inverse of the components of a vec3\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a vector to invert\n * @returns {vec3} out\n */\nexport function inverse(out, a) {\n  out[0] = 1.0 / a[0];\n  out[1] = 1.0 / a[1];\n  out[2] = 1.0 / a[2];\n  return out;\n}\n\n/**\n * Normalize a vec3\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a vector to normalize\n * @returns {vec3} out\n */\nexport function normalize(out, a) {\n  var x = a[0];\n  var y = a[1];\n  var z = a[2];\n  var len = x * x + y * y + z * z;\n  if (len > 0) {\n    //TODO: evaluate use of glm_invsqrt here?\n    len = 1 / Math.sqrt(len);\n  }\n  out[0] = a[0] * len;\n  out[1] = a[1] * len;\n  out[2] = a[2] * len;\n  return out;\n}\n\n/**\n * Calculates the dot product of two vec3's\n *\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {Number} dot product of a and b\n */\nexport function dot(a, b) {\n  return a[0] * b[0] + a[1] * b[1] + a[2] * b[2];\n}\n\n/**\n * Computes the cross product of two vec3's\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {vec3} out\n */\nexport function cross(out, a, b) {\n  var ax = a[0],\n    ay = a[1],\n    az = a[2];\n  var bx = b[0],\n    by = b[1],\n    bz = b[2];\n  out[0] = ay * bz - az * by;\n  out[1] = az * bx - ax * bz;\n  out[2] = ax * by - ay * bx;\n  return out;\n}\n\n/**\n * Performs a linear interpolation between two vec3's\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @param {Number} t interpolation amount, in the range [0-1], between the two inputs\n * @returns {vec3} out\n */\nexport function lerp(out, a, b, t) {\n  var ax = a[0];\n  var ay = a[1];\n  var az = a[2];\n  out[0] = ax + t * (b[0] - ax);\n  out[1] = ay + t * (b[1] - ay);\n  out[2] = az + t * (b[2] - az);\n  return out;\n}\n\n/**\n * Performs a spherical linear interpolation between two vec3's\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @param {Number} t interpolation amount, in the range [0-1], between the two inputs\n * @returns {vec3} out\n */\nexport function slerp(out, a, b, t) {\n  var angle = Math.acos(Math.min(Math.max(dot(a, b), -1), 1));\n  var sinTotal = Math.sin(angle);\n  var ratioA = Math.sin((1 - t) * angle) / sinTotal;\n  var ratioB = Math.sin(t * angle) / sinTotal;\n  out[0] = ratioA * a[0] + ratioB * b[0];\n  out[1] = ratioA * a[1] + ratioB * b[1];\n  out[2] = ratioA * a[2] + ratioB * b[2];\n  return out;\n}\n\n/**\n * Performs a hermite interpolation with two control points\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @param {ReadonlyVec3} c the third operand\n * @param {ReadonlyVec3} d the fourth operand\n * @param {Number} t interpolation amount, in the range [0-1], between the two inputs\n * @returns {vec3} out\n */\nexport function hermite(out, a, b, c, d, t) {\n  var factorTimes2 = t * t;\n  var factor1 = factorTimes2 * (2 * t - 3) + 1;\n  var factor2 = factorTimes2 * (t - 2) + t;\n  var factor3 = factorTimes2 * (t - 1);\n  var factor4 = factorTimes2 * (3 - 2 * t);\n  out[0] = a[0] * factor1 + b[0] * factor2 + c[0] * factor3 + d[0] * factor4;\n  out[1] = a[1] * factor1 + b[1] * factor2 + c[1] * factor3 + d[1] * factor4;\n  out[2] = a[2] * factor1 + b[2] * factor2 + c[2] * factor3 + d[2] * factor4;\n  return out;\n}\n\n/**\n * Performs a bezier interpolation with two control points\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @param {ReadonlyVec3} c the third operand\n * @param {ReadonlyVec3} d the fourth operand\n * @param {Number} t interpolation amount, in the range [0-1], between the two inputs\n * @returns {vec3} out\n */\nexport function bezier(out, a, b, c, d, t) {\n  var inverseFactor = 1 - t;\n  var inverseFactorTimesTwo = inverseFactor * inverseFactor;\n  var factorTimes2 = t * t;\n  var factor1 = inverseFactorTimesTwo * inverseFactor;\n  var factor2 = 3 * t * inverseFactorTimesTwo;\n  var factor3 = 3 * factorTimes2 * inverseFactor;\n  var factor4 = factorTimes2 * t;\n  out[0] = a[0] * factor1 + b[0] * factor2 + c[0] * factor3 + d[0] * factor4;\n  out[1] = a[1] * factor1 + b[1] * factor2 + c[1] * factor3 + d[1] * factor4;\n  out[2] = a[2] * factor1 + b[2] * factor2 + c[2] * factor3 + d[2] * factor4;\n  return out;\n}\n\n/**\n * Generates a random vector with the given scale\n *\n * @param {vec3} out the receiving vector\n * @param {Number} [scale] Length of the resulting vector. If omitted, a unit vector will be returned\n * @returns {vec3} out\n */\nexport function random(out, scale) {\n  scale = scale === undefined ? 1.0 : scale;\n  var r = glMatrix.RANDOM() * 2.0 * Math.PI;\n  var z = glMatrix.RANDOM() * 2.0 - 1.0;\n  var zScale = Math.sqrt(1.0 - z * z) * scale;\n  out[0] = Math.cos(r) * zScale;\n  out[1] = Math.sin(r) * zScale;\n  out[2] = z * scale;\n  return out;\n}\n\n/**\n * Transforms the vec3 with a mat4.\n * 4th vector component is implicitly '1'\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the vector to transform\n * @param {ReadonlyMat4} m matrix to transform with\n * @returns {vec3} out\n */\nexport function transformMat4(out, a, m) {\n  var x = a[0],\n    y = a[1],\n    z = a[2];\n  var w = m[3] * x + m[7] * y + m[11] * z + m[15];\n  w = w || 1.0;\n  out[0] = (m[0] * x + m[4] * y + m[8] * z + m[12]) / w;\n  out[1] = (m[1] * x + m[5] * y + m[9] * z + m[13]) / w;\n  out[2] = (m[2] * x + m[6] * y + m[10] * z + m[14]) / w;\n  return out;\n}\n\n/**\n * Transforms the vec3 with a mat3.\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the vector to transform\n * @param {ReadonlyMat3} m the 3x3 matrix to transform with\n * @returns {vec3} out\n */\nexport function transformMat3(out, a, m) {\n  var x = a[0],\n    y = a[1],\n    z = a[2];\n  out[0] = x * m[0] + y * m[3] + z * m[6];\n  out[1] = x * m[1] + y * m[4] + z * m[7];\n  out[2] = x * m[2] + y * m[5] + z * m[8];\n  return out;\n}\n\n/**\n * Transforms the vec3 with a quat\n * Can also be used for dual quaternions. (Multiply it with the real part)\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the vector to transform\n * @param {ReadonlyQuat} q normalized quaternion to transform with\n * @returns {vec3} out\n */\nexport function transformQuat(out, a, q) {\n  // Fast Vector Rotation using Quaternions by Robert Eisele\n  // https://raw.org/proof/vector-rotation-using-quaternions/\n\n  var qx = q[0],\n    qy = q[1],\n    qz = q[2],\n    qw = q[3];\n  var vx = a[0],\n    vy = a[1],\n    vz = a[2];\n\n  // t = q x v\n  var tx = qy * vz - qz * vy;\n  var ty = qz * vx - qx * vz;\n  var tz = qx * vy - qy * vx;\n\n  // t = 2t\n  tx = tx + tx;\n  ty = ty + ty;\n  tz = tz + tz;\n\n  // v + w t + q x t\n  out[0] = vx + qw * tx + qy * tz - qz * ty;\n  out[1] = vy + qw * ty + qz * tx - qx * tz;\n  out[2] = vz + qw * tz + qx * ty - qy * tx;\n  return out;\n}\n\n/**\n * Rotate a 3D vector around the x-axis\n * @param {vec3} out The receiving vec3\n * @param {ReadonlyVec3} a The vec3 point to rotate\n * @param {ReadonlyVec3} b The origin of the rotation\n * @param {Number} rad The angle of rotation in radians\n * @returns {vec3} out\n */\nexport function rotateX(out, a, b, rad) {\n  var p = [],\n    r = [];\n  //Translate point to the origin\n  p[0] = a[0] - b[0];\n  p[1] = a[1] - b[1];\n  p[2] = a[2] - b[2];\n\n  //perform rotation\n  r[0] = p[0];\n  r[1] = p[1] * Math.cos(rad) - p[2] * Math.sin(rad);\n  r[2] = p[1] * Math.sin(rad) + p[2] * Math.cos(rad);\n\n  //translate to correct position\n  out[0] = r[0] + b[0];\n  out[1] = r[1] + b[1];\n  out[2] = r[2] + b[2];\n  return out;\n}\n\n/**\n * Rotate a 3D vector around the y-axis\n * @param {vec3} out The receiving vec3\n * @param {ReadonlyVec3} a The vec3 point to rotate\n * @param {ReadonlyVec3} b The origin of the rotation\n * @param {Number} rad The angle of rotation in radians\n * @returns {vec3} out\n */\nexport function rotateY(out, a, b, rad) {\n  var p = [],\n    r = [];\n  //Translate point to the origin\n  p[0] = a[0] - b[0];\n  p[1] = a[1] - b[1];\n  p[2] = a[2] - b[2];\n\n  //perform rotation\n  r[0] = p[2] * Math.sin(rad) + p[0] * Math.cos(rad);\n  r[1] = p[1];\n  r[2] = p[2] * Math.cos(rad) - p[0] * Math.sin(rad);\n\n  //translate to correct position\n  out[0] = r[0] + b[0];\n  out[1] = r[1] + b[1];\n  out[2] = r[2] + b[2];\n  return out;\n}\n\n/**\n * Rotate a 3D vector around the z-axis\n * @param {vec3} out The receiving vec3\n * @param {ReadonlyVec3} a The vec3 point to rotate\n * @param {ReadonlyVec3} b The origin of the rotation\n * @param {Number} rad The angle of rotation in radians\n * @returns {vec3} out\n */\nexport function rotateZ(out, a, b, rad) {\n  var p = [],\n    r = [];\n  //Translate point to the origin\n  p[0] = a[0] - b[0];\n  p[1] = a[1] - b[1];\n  p[2] = a[2] - b[2];\n\n  //perform rotation\n  r[0] = p[0] * Math.cos(rad) - p[1] * Math.sin(rad);\n  r[1] = p[0] * Math.sin(rad) + p[1] * Math.cos(rad);\n  r[2] = p[2];\n\n  //translate to correct position\n  out[0] = r[0] + b[0];\n  out[1] = r[1] + b[1];\n  out[2] = r[2] + b[2];\n  return out;\n}\n\n/**\n * Get the angle between two 3D vectors\n * @param {ReadonlyVec3} a The first operand\n * @param {ReadonlyVec3} b The second operand\n * @returns {Number} The angle in radians\n */\nexport function angle(a, b) {\n  var ax = a[0],\n    ay = a[1],\n    az = a[2],\n    bx = b[0],\n    by = b[1],\n    bz = b[2],\n    mag = Math.sqrt((ax * ax + ay * ay + az * az) * (bx * bx + by * by + bz * bz)),\n    cosine = mag && dot(a, b) / mag;\n  return Math.acos(Math.min(Math.max(cosine, -1), 1));\n}\n\n/**\n * Set the components of a vec3 to zero\n *\n * @param {vec3} out the receiving vector\n * @returns {vec3} out\n */\nexport function zero(out) {\n  out[0] = 0.0;\n  out[1] = 0.0;\n  out[2] = 0.0;\n  return out;\n}\n\n/**\n * Returns a string representation of a vector\n *\n * @param {ReadonlyVec3} a vector to represent as a string\n * @returns {String} string representation of the vector\n */\nexport function str(a) {\n  return \"vec3(\" + a[0] + \", \" + a[1] + \", \" + a[2] + \")\";\n}\n\n/**\n * Returns whether or not the vectors have exactly the same elements in the same position (when compared with ===)\n *\n * @param {ReadonlyVec3} a The first vector.\n * @param {ReadonlyVec3} b The second vector.\n * @returns {Boolean} True if the vectors are equal, false otherwise.\n */\nexport function exactEquals(a, b) {\n  return a[0] === b[0] && a[1] === b[1] && a[2] === b[2];\n}\n\n/**\n * Returns whether or not the vectors have approximately the same elements in the same position.\n *\n * @param {ReadonlyVec3} a The first vector.\n * @param {ReadonlyVec3} b The second vector.\n * @returns {Boolean} True if the vectors are equal, false otherwise.\n */\nexport function equals(a, b) {\n  var a0 = a[0],\n    a1 = a[1],\n    a2 = a[2];\n  var b0 = b[0],\n    b1 = b[1],\n    b2 = b[2];\n  return Math.abs(a0 - b0) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a0), Math.abs(b0)) && Math.abs(a1 - b1) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a1), Math.abs(b1)) && Math.abs(a2 - b2) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a2), Math.abs(b2));\n}\n\n/**\n * Alias for {@link vec3.subtract}\n * @function\n */\nexport var sub = subtract;\n\n/**\n * Alias for {@link vec3.multiply}\n * @function\n */\nexport var mul = multiply;\n\n/**\n * Alias for {@link vec3.divide}\n * @function\n */\nexport var div = divide;\n\n/**\n * Alias for {@link vec3.distance}\n * @function\n */\nexport var dist = distance;\n\n/**\n * Alias for {@link vec3.squaredDistance}\n * @function\n */\nexport var sqrDist = squaredDistance;\n\n/**\n * Alias for {@link vec3.length}\n * @function\n */\nexport var len = length;\n\n/**\n * Alias for {@link vec3.squaredLength}\n * @function\n */\nexport var sqrLen = squaredLength;\n\n/**\n * Perform some operation over an array of vec3s.\n *\n * @param {Array} a the array of vectors to iterate over\n * @param {Number} stride Number of elements between the start of each vec3. If 0 assumes tightly packed\n * @param {Number} offset Number of elements to skip at the beginning of the array\n * @param {Number} count Number of vec3s to iterate over. If 0 iterates over entire array\n * @param {Function} fn Function to call for each vector in the array\n * @param {Object} [arg] additional argument to pass to fn\n * @returns {Array} a\n * @function\n */\nexport var forEach = function () {\n  var vec = create();\n  return function (a, stride, offset, count, fn, arg) {\n    var i, l;\n    if (!stride) {\n      stride = 3;\n    }\n    if (!offset) {\n      offset = 0;\n    }\n    if (count) {\n      l = Math.min(count * stride + offset, a.length);\n    } else {\n      l = a.length;\n    }\n    for (i = offset; i < l; i += stride) {\n      vec[0] = a[i];\n      vec[1] = a[i + 1];\n      vec[2] = a[i + 2];\n      fn(vec, vec, arg);\n      a[i] = vec[0];\n      a[i + 1] = vec[1];\n      a[i + 2] = vec[2];\n    }\n    return a;\n  };\n}();", "map": {"version": 3, "names": ["glMatrix", "create", "out", "ARRAY_TYPE", "Float32Array", "clone", "a", "length", "x", "y", "z", "Math", "sqrt", "fromValues", "copy", "set", "add", "b", "subtract", "multiply", "divide", "ceil", "floor", "min", "max", "round", "scale", "scaleAndAdd", "distance", "squaredDistance", "squared<PERSON>ength", "negate", "inverse", "normalize", "len", "dot", "cross", "ax", "ay", "az", "bx", "by", "bz", "lerp", "t", "slerp", "angle", "acos", "sinTotal", "sin", "ratioA", "ratioB", "hermite", "c", "d", "factorTimes2", "factor1", "factor2", "factor3", "factor4", "bezier", "inverseFactor", "inverseFactorTimesTwo", "random", "undefined", "r", "RANDOM", "PI", "zScale", "cos", "transformMat4", "m", "w", "transformMat3", "transformQuat", "q", "qx", "qy", "qz", "qw", "vx", "vy", "vz", "tx", "ty", "tz", "rotateX", "rad", "p", "rotateY", "rotateZ", "mag", "cosine", "zero", "str", "exactEquals", "equals", "a0", "a1", "a2", "b0", "b1", "b2", "abs", "EPSILON", "sub", "mul", "div", "dist", "sqrDist", "sqrLen", "for<PERSON>ach", "vec", "stride", "offset", "count", "fn", "arg", "i", "l"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/gl-matrix/esm/vec3.js"], "sourcesContent": ["import * as glMatrix from \"./common.js\";\n\n/**\n * 3 Dimensional Vector\n * @module vec3\n */\n\n/**\n * Creates a new, empty vec3\n *\n * @returns {vec3} a new 3D vector\n */\nexport function create() {\n  var out = new glMatrix.ARRAY_TYPE(3);\n  if (glMatrix.ARRAY_TYPE != Float32Array) {\n    out[0] = 0;\n    out[1] = 0;\n    out[2] = 0;\n  }\n  return out;\n}\n\n/**\n * Creates a new vec3 initialized with values from an existing vector\n *\n * @param {ReadonlyVec3} a vector to clone\n * @returns {vec3} a new 3D vector\n */\nexport function clone(a) {\n  var out = new glMatrix.ARRAY_TYPE(3);\n  out[0] = a[0];\n  out[1] = a[1];\n  out[2] = a[2];\n  return out;\n}\n\n/**\n * Calculates the length of a vec3\n *\n * @param {ReadonlyVec3} a vector to calculate length of\n * @returns {Number} length of a\n */\nexport function length(a) {\n  var x = a[0];\n  var y = a[1];\n  var z = a[2];\n  return Math.sqrt(x * x + y * y + z * z);\n}\n\n/**\n * Creates a new vec3 initialized with the given values\n *\n * @param {Number} x X component\n * @param {Number} y Y component\n * @param {Number} z Z component\n * @returns {vec3} a new 3D vector\n */\nexport function fromValues(x, y, z) {\n  var out = new glMatrix.ARRAY_TYPE(3);\n  out[0] = x;\n  out[1] = y;\n  out[2] = z;\n  return out;\n}\n\n/**\n * Copy the values from one vec3 to another\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the source vector\n * @returns {vec3} out\n */\nexport function copy(out, a) {\n  out[0] = a[0];\n  out[1] = a[1];\n  out[2] = a[2];\n  return out;\n}\n\n/**\n * Set the components of a vec3 to the given values\n *\n * @param {vec3} out the receiving vector\n * @param {Number} x X component\n * @param {Number} y Y component\n * @param {Number} z Z component\n * @returns {vec3} out\n */\nexport function set(out, x, y, z) {\n  out[0] = x;\n  out[1] = y;\n  out[2] = z;\n  return out;\n}\n\n/**\n * Adds two vec3's\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {vec3} out\n */\nexport function add(out, a, b) {\n  out[0] = a[0] + b[0];\n  out[1] = a[1] + b[1];\n  out[2] = a[2] + b[2];\n  return out;\n}\n\n/**\n * Subtracts vector b from vector a\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {vec3} out\n */\nexport function subtract(out, a, b) {\n  out[0] = a[0] - b[0];\n  out[1] = a[1] - b[1];\n  out[2] = a[2] - b[2];\n  return out;\n}\n\n/**\n * Multiplies two vec3's\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {vec3} out\n */\nexport function multiply(out, a, b) {\n  out[0] = a[0] * b[0];\n  out[1] = a[1] * b[1];\n  out[2] = a[2] * b[2];\n  return out;\n}\n\n/**\n * Divides two vec3's\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {vec3} out\n */\nexport function divide(out, a, b) {\n  out[0] = a[0] / b[0];\n  out[1] = a[1] / b[1];\n  out[2] = a[2] / b[2];\n  return out;\n}\n\n/**\n * Math.ceil the components of a vec3\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a vector to ceil\n * @returns {vec3} out\n */\nexport function ceil(out, a) {\n  out[0] = Math.ceil(a[0]);\n  out[1] = Math.ceil(a[1]);\n  out[2] = Math.ceil(a[2]);\n  return out;\n}\n\n/**\n * Math.floor the components of a vec3\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a vector to floor\n * @returns {vec3} out\n */\nexport function floor(out, a) {\n  out[0] = Math.floor(a[0]);\n  out[1] = Math.floor(a[1]);\n  out[2] = Math.floor(a[2]);\n  return out;\n}\n\n/**\n * Returns the minimum of two vec3's\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {vec3} out\n */\nexport function min(out, a, b) {\n  out[0] = Math.min(a[0], b[0]);\n  out[1] = Math.min(a[1], b[1]);\n  out[2] = Math.min(a[2], b[2]);\n  return out;\n}\n\n/**\n * Returns the maximum of two vec3's\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {vec3} out\n */\nexport function max(out, a, b) {\n  out[0] = Math.max(a[0], b[0]);\n  out[1] = Math.max(a[1], b[1]);\n  out[2] = Math.max(a[2], b[2]);\n  return out;\n}\n\n/**\n * symmetric round the components of a vec3\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a vector to round\n * @returns {vec3} out\n */\nexport function round(out, a) {\n  out[0] = glMatrix.round(a[0]);\n  out[1] = glMatrix.round(a[1]);\n  out[2] = glMatrix.round(a[2]);\n  return out;\n}\n\n/**\n * Scales a vec3 by a scalar number\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the vector to scale\n * @param {Number} b amount to scale the vector by\n * @returns {vec3} out\n */\nexport function scale(out, a, b) {\n  out[0] = a[0] * b;\n  out[1] = a[1] * b;\n  out[2] = a[2] * b;\n  return out;\n}\n\n/**\n * Adds two vec3's after scaling the second operand by a scalar value\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @param {Number} scale the amount to scale b by before adding\n * @returns {vec3} out\n */\nexport function scaleAndAdd(out, a, b, scale) {\n  out[0] = a[0] + b[0] * scale;\n  out[1] = a[1] + b[1] * scale;\n  out[2] = a[2] + b[2] * scale;\n  return out;\n}\n\n/**\n * Calculates the euclidian distance between two vec3's\n *\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {Number} distance between a and b\n */\nexport function distance(a, b) {\n  var x = b[0] - a[0];\n  var y = b[1] - a[1];\n  var z = b[2] - a[2];\n  return Math.sqrt(x * x + y * y + z * z);\n}\n\n/**\n * Calculates the squared euclidian distance between two vec3's\n *\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {Number} squared distance between a and b\n */\nexport function squaredDistance(a, b) {\n  var x = b[0] - a[0];\n  var y = b[1] - a[1];\n  var z = b[2] - a[2];\n  return x * x + y * y + z * z;\n}\n\n/**\n * Calculates the squared length of a vec3\n *\n * @param {ReadonlyVec3} a vector to calculate squared length of\n * @returns {Number} squared length of a\n */\nexport function squaredLength(a) {\n  var x = a[0];\n  var y = a[1];\n  var z = a[2];\n  return x * x + y * y + z * z;\n}\n\n/**\n * Negates the components of a vec3\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a vector to negate\n * @returns {vec3} out\n */\nexport function negate(out, a) {\n  out[0] = -a[0];\n  out[1] = -a[1];\n  out[2] = -a[2];\n  return out;\n}\n\n/**\n * Returns the inverse of the components of a vec3\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a vector to invert\n * @returns {vec3} out\n */\nexport function inverse(out, a) {\n  out[0] = 1.0 / a[0];\n  out[1] = 1.0 / a[1];\n  out[2] = 1.0 / a[2];\n  return out;\n}\n\n/**\n * Normalize a vec3\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a vector to normalize\n * @returns {vec3} out\n */\nexport function normalize(out, a) {\n  var x = a[0];\n  var y = a[1];\n  var z = a[2];\n  var len = x * x + y * y + z * z;\n  if (len > 0) {\n    //TODO: evaluate use of glm_invsqrt here?\n    len = 1 / Math.sqrt(len);\n  }\n  out[0] = a[0] * len;\n  out[1] = a[1] * len;\n  out[2] = a[2] * len;\n  return out;\n}\n\n/**\n * Calculates the dot product of two vec3's\n *\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {Number} dot product of a and b\n */\nexport function dot(a, b) {\n  return a[0] * b[0] + a[1] * b[1] + a[2] * b[2];\n}\n\n/**\n * Computes the cross product of two vec3's\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {vec3} out\n */\nexport function cross(out, a, b) {\n  var ax = a[0],\n    ay = a[1],\n    az = a[2];\n  var bx = b[0],\n    by = b[1],\n    bz = b[2];\n  out[0] = ay * bz - az * by;\n  out[1] = az * bx - ax * bz;\n  out[2] = ax * by - ay * bx;\n  return out;\n}\n\n/**\n * Performs a linear interpolation between two vec3's\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @param {Number} t interpolation amount, in the range [0-1], between the two inputs\n * @returns {vec3} out\n */\nexport function lerp(out, a, b, t) {\n  var ax = a[0];\n  var ay = a[1];\n  var az = a[2];\n  out[0] = ax + t * (b[0] - ax);\n  out[1] = ay + t * (b[1] - ay);\n  out[2] = az + t * (b[2] - az);\n  return out;\n}\n\n/**\n * Performs a spherical linear interpolation between two vec3's\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @param {Number} t interpolation amount, in the range [0-1], between the two inputs\n * @returns {vec3} out\n */\nexport function slerp(out, a, b, t) {\n  var angle = Math.acos(Math.min(Math.max(dot(a, b), -1), 1));\n  var sinTotal = Math.sin(angle);\n  var ratioA = Math.sin((1 - t) * angle) / sinTotal;\n  var ratioB = Math.sin(t * angle) / sinTotal;\n  out[0] = ratioA * a[0] + ratioB * b[0];\n  out[1] = ratioA * a[1] + ratioB * b[1];\n  out[2] = ratioA * a[2] + ratioB * b[2];\n  return out;\n}\n\n/**\n * Performs a hermite interpolation with two control points\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @param {ReadonlyVec3} c the third operand\n * @param {ReadonlyVec3} d the fourth operand\n * @param {Number} t interpolation amount, in the range [0-1], between the two inputs\n * @returns {vec3} out\n */\nexport function hermite(out, a, b, c, d, t) {\n  var factorTimes2 = t * t;\n  var factor1 = factorTimes2 * (2 * t - 3) + 1;\n  var factor2 = factorTimes2 * (t - 2) + t;\n  var factor3 = factorTimes2 * (t - 1);\n  var factor4 = factorTimes2 * (3 - 2 * t);\n  out[0] = a[0] * factor1 + b[0] * factor2 + c[0] * factor3 + d[0] * factor4;\n  out[1] = a[1] * factor1 + b[1] * factor2 + c[1] * factor3 + d[1] * factor4;\n  out[2] = a[2] * factor1 + b[2] * factor2 + c[2] * factor3 + d[2] * factor4;\n  return out;\n}\n\n/**\n * Performs a bezier interpolation with two control points\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @param {ReadonlyVec3} c the third operand\n * @param {ReadonlyVec3} d the fourth operand\n * @param {Number} t interpolation amount, in the range [0-1], between the two inputs\n * @returns {vec3} out\n */\nexport function bezier(out, a, b, c, d, t) {\n  var inverseFactor = 1 - t;\n  var inverseFactorTimesTwo = inverseFactor * inverseFactor;\n  var factorTimes2 = t * t;\n  var factor1 = inverseFactorTimesTwo * inverseFactor;\n  var factor2 = 3 * t * inverseFactorTimesTwo;\n  var factor3 = 3 * factorTimes2 * inverseFactor;\n  var factor4 = factorTimes2 * t;\n  out[0] = a[0] * factor1 + b[0] * factor2 + c[0] * factor3 + d[0] * factor4;\n  out[1] = a[1] * factor1 + b[1] * factor2 + c[1] * factor3 + d[1] * factor4;\n  out[2] = a[2] * factor1 + b[2] * factor2 + c[2] * factor3 + d[2] * factor4;\n  return out;\n}\n\n/**\n * Generates a random vector with the given scale\n *\n * @param {vec3} out the receiving vector\n * @param {Number} [scale] Length of the resulting vector. If omitted, a unit vector will be returned\n * @returns {vec3} out\n */\nexport function random(out, scale) {\n  scale = scale === undefined ? 1.0 : scale;\n  var r = glMatrix.RANDOM() * 2.0 * Math.PI;\n  var z = glMatrix.RANDOM() * 2.0 - 1.0;\n  var zScale = Math.sqrt(1.0 - z * z) * scale;\n  out[0] = Math.cos(r) * zScale;\n  out[1] = Math.sin(r) * zScale;\n  out[2] = z * scale;\n  return out;\n}\n\n/**\n * Transforms the vec3 with a mat4.\n * 4th vector component is implicitly '1'\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the vector to transform\n * @param {ReadonlyMat4} m matrix to transform with\n * @returns {vec3} out\n */\nexport function transformMat4(out, a, m) {\n  var x = a[0],\n    y = a[1],\n    z = a[2];\n  var w = m[3] * x + m[7] * y + m[11] * z + m[15];\n  w = w || 1.0;\n  out[0] = (m[0] * x + m[4] * y + m[8] * z + m[12]) / w;\n  out[1] = (m[1] * x + m[5] * y + m[9] * z + m[13]) / w;\n  out[2] = (m[2] * x + m[6] * y + m[10] * z + m[14]) / w;\n  return out;\n}\n\n/**\n * Transforms the vec3 with a mat3.\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the vector to transform\n * @param {ReadonlyMat3} m the 3x3 matrix to transform with\n * @returns {vec3} out\n */\nexport function transformMat3(out, a, m) {\n  var x = a[0],\n    y = a[1],\n    z = a[2];\n  out[0] = x * m[0] + y * m[3] + z * m[6];\n  out[1] = x * m[1] + y * m[4] + z * m[7];\n  out[2] = x * m[2] + y * m[5] + z * m[8];\n  return out;\n}\n\n/**\n * Transforms the vec3 with a quat\n * Can also be used for dual quaternions. (Multiply it with the real part)\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the vector to transform\n * @param {ReadonlyQuat} q normalized quaternion to transform with\n * @returns {vec3} out\n */\nexport function transformQuat(out, a, q) {\n  // Fast Vector Rotation using Quaternions by Robert Eisele\n  // https://raw.org/proof/vector-rotation-using-quaternions/\n\n  var qx = q[0],\n    qy = q[1],\n    qz = q[2],\n    qw = q[3];\n  var vx = a[0],\n    vy = a[1],\n    vz = a[2];\n\n  // t = q x v\n  var tx = qy * vz - qz * vy;\n  var ty = qz * vx - qx * vz;\n  var tz = qx * vy - qy * vx;\n\n  // t = 2t\n  tx = tx + tx;\n  ty = ty + ty;\n  tz = tz + tz;\n\n  // v + w t + q x t\n  out[0] = vx + qw * tx + qy * tz - qz * ty;\n  out[1] = vy + qw * ty + qz * tx - qx * tz;\n  out[2] = vz + qw * tz + qx * ty - qy * tx;\n  return out;\n}\n\n/**\n * Rotate a 3D vector around the x-axis\n * @param {vec3} out The receiving vec3\n * @param {ReadonlyVec3} a The vec3 point to rotate\n * @param {ReadonlyVec3} b The origin of the rotation\n * @param {Number} rad The angle of rotation in radians\n * @returns {vec3} out\n */\nexport function rotateX(out, a, b, rad) {\n  var p = [],\n    r = [];\n  //Translate point to the origin\n  p[0] = a[0] - b[0];\n  p[1] = a[1] - b[1];\n  p[2] = a[2] - b[2];\n\n  //perform rotation\n  r[0] = p[0];\n  r[1] = p[1] * Math.cos(rad) - p[2] * Math.sin(rad);\n  r[2] = p[1] * Math.sin(rad) + p[2] * Math.cos(rad);\n\n  //translate to correct position\n  out[0] = r[0] + b[0];\n  out[1] = r[1] + b[1];\n  out[2] = r[2] + b[2];\n  return out;\n}\n\n/**\n * Rotate a 3D vector around the y-axis\n * @param {vec3} out The receiving vec3\n * @param {ReadonlyVec3} a The vec3 point to rotate\n * @param {ReadonlyVec3} b The origin of the rotation\n * @param {Number} rad The angle of rotation in radians\n * @returns {vec3} out\n */\nexport function rotateY(out, a, b, rad) {\n  var p = [],\n    r = [];\n  //Translate point to the origin\n  p[0] = a[0] - b[0];\n  p[1] = a[1] - b[1];\n  p[2] = a[2] - b[2];\n\n  //perform rotation\n  r[0] = p[2] * Math.sin(rad) + p[0] * Math.cos(rad);\n  r[1] = p[1];\n  r[2] = p[2] * Math.cos(rad) - p[0] * Math.sin(rad);\n\n  //translate to correct position\n  out[0] = r[0] + b[0];\n  out[1] = r[1] + b[1];\n  out[2] = r[2] + b[2];\n  return out;\n}\n\n/**\n * Rotate a 3D vector around the z-axis\n * @param {vec3} out The receiving vec3\n * @param {ReadonlyVec3} a The vec3 point to rotate\n * @param {ReadonlyVec3} b The origin of the rotation\n * @param {Number} rad The angle of rotation in radians\n * @returns {vec3} out\n */\nexport function rotateZ(out, a, b, rad) {\n  var p = [],\n    r = [];\n  //Translate point to the origin\n  p[0] = a[0] - b[0];\n  p[1] = a[1] - b[1];\n  p[2] = a[2] - b[2];\n\n  //perform rotation\n  r[0] = p[0] * Math.cos(rad) - p[1] * Math.sin(rad);\n  r[1] = p[0] * Math.sin(rad) + p[1] * Math.cos(rad);\n  r[2] = p[2];\n\n  //translate to correct position\n  out[0] = r[0] + b[0];\n  out[1] = r[1] + b[1];\n  out[2] = r[2] + b[2];\n  return out;\n}\n\n/**\n * Get the angle between two 3D vectors\n * @param {ReadonlyVec3} a The first operand\n * @param {ReadonlyVec3} b The second operand\n * @returns {Number} The angle in radians\n */\nexport function angle(a, b) {\n  var ax = a[0],\n    ay = a[1],\n    az = a[2],\n    bx = b[0],\n    by = b[1],\n    bz = b[2],\n    mag = Math.sqrt((ax * ax + ay * ay + az * az) * (bx * bx + by * by + bz * bz)),\n    cosine = mag && dot(a, b) / mag;\n  return Math.acos(Math.min(Math.max(cosine, -1), 1));\n}\n\n/**\n * Set the components of a vec3 to zero\n *\n * @param {vec3} out the receiving vector\n * @returns {vec3} out\n */\nexport function zero(out) {\n  out[0] = 0.0;\n  out[1] = 0.0;\n  out[2] = 0.0;\n  return out;\n}\n\n/**\n * Returns a string representation of a vector\n *\n * @param {ReadonlyVec3} a vector to represent as a string\n * @returns {String} string representation of the vector\n */\nexport function str(a) {\n  return \"vec3(\" + a[0] + \", \" + a[1] + \", \" + a[2] + \")\";\n}\n\n/**\n * Returns whether or not the vectors have exactly the same elements in the same position (when compared with ===)\n *\n * @param {ReadonlyVec3} a The first vector.\n * @param {ReadonlyVec3} b The second vector.\n * @returns {Boolean} True if the vectors are equal, false otherwise.\n */\nexport function exactEquals(a, b) {\n  return a[0] === b[0] && a[1] === b[1] && a[2] === b[2];\n}\n\n/**\n * Returns whether or not the vectors have approximately the same elements in the same position.\n *\n * @param {ReadonlyVec3} a The first vector.\n * @param {ReadonlyVec3} b The second vector.\n * @returns {Boolean} True if the vectors are equal, false otherwise.\n */\nexport function equals(a, b) {\n  var a0 = a[0],\n    a1 = a[1],\n    a2 = a[2];\n  var b0 = b[0],\n    b1 = b[1],\n    b2 = b[2];\n  return Math.abs(a0 - b0) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a0), Math.abs(b0)) && Math.abs(a1 - b1) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a1), Math.abs(b1)) && Math.abs(a2 - b2) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a2), Math.abs(b2));\n}\n\n/**\n * Alias for {@link vec3.subtract}\n * @function\n */\nexport var sub = subtract;\n\n/**\n * Alias for {@link vec3.multiply}\n * @function\n */\nexport var mul = multiply;\n\n/**\n * Alias for {@link vec3.divide}\n * @function\n */\nexport var div = divide;\n\n/**\n * Alias for {@link vec3.distance}\n * @function\n */\nexport var dist = distance;\n\n/**\n * Alias for {@link vec3.squaredDistance}\n * @function\n */\nexport var sqrDist = squaredDistance;\n\n/**\n * Alias for {@link vec3.length}\n * @function\n */\nexport var len = length;\n\n/**\n * Alias for {@link vec3.squaredLength}\n * @function\n */\nexport var sqrLen = squaredLength;\n\n/**\n * Perform some operation over an array of vec3s.\n *\n * @param {Array} a the array of vectors to iterate over\n * @param {Number} stride Number of elements between the start of each vec3. If 0 assumes tightly packed\n * @param {Number} offset Number of elements to skip at the beginning of the array\n * @param {Number} count Number of vec3s to iterate over. If 0 iterates over entire array\n * @param {Function} fn Function to call for each vector in the array\n * @param {Object} [arg] additional argument to pass to fn\n * @returns {Array} a\n * @function\n */\nexport var forEach = function () {\n  var vec = create();\n  return function (a, stride, offset, count, fn, arg) {\n    var i, l;\n    if (!stride) {\n      stride = 3;\n    }\n    if (!offset) {\n      offset = 0;\n    }\n    if (count) {\n      l = Math.min(count * stride + offset, a.length);\n    } else {\n      l = a.length;\n    }\n    for (i = offset; i < l; i += stride) {\n      vec[0] = a[i];\n      vec[1] = a[i + 1];\n      vec[2] = a[i + 2];\n      fn(vec, vec, arg);\n      a[i] = vec[0];\n      a[i + 1] = vec[1];\n      a[i + 2] = vec[2];\n    }\n    return a;\n  };\n}();"], "mappings": "AAAA,OAAO,KAAKA,QAAQ,MAAM,aAAa;;AAEvC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,MAAMA,CAAA,EAAG;EACvB,IAAIC,GAAG,GAAG,IAAIF,QAAQ,CAACG,UAAU,CAAC,CAAC,CAAC;EACpC,IAAIH,QAAQ,CAACG,UAAU,IAAIC,YAAY,EAAE;IACvCF,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IACVA,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IACVA,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;EACZ;EACA,OAAOA,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,KAAKA,CAACC,CAAC,EAAE;EACvB,IAAIJ,GAAG,GAAG,IAAIF,QAAQ,CAACG,UAAU,CAAC,CAAC,CAAC;EACpCD,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC;EACbJ,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC;EACbJ,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC;EACb,OAAOJ,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASK,MAAMA,CAACD,CAAC,EAAE;EACxB,IAAIE,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC;EACZ,IAAIG,CAAC,GAAGH,CAAC,CAAC,CAAC,CAAC;EACZ,IAAII,CAAC,GAAGJ,CAAC,CAAC,CAAC,CAAC;EACZ,OAAOK,IAAI,CAACC,IAAI,CAACJ,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC,CAAC;AACzC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,UAAUA,CAACL,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAClC,IAAIR,GAAG,GAAG,IAAIF,QAAQ,CAACG,UAAU,CAAC,CAAC,CAAC;EACpCD,GAAG,CAAC,CAAC,CAAC,GAAGM,CAAC;EACVN,GAAG,CAAC,CAAC,CAAC,GAAGO,CAAC;EACVP,GAAG,CAAC,CAAC,CAAC,GAAGQ,CAAC;EACV,OAAOR,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASY,IAAIA,CAACZ,GAAG,EAAEI,CAAC,EAAE;EAC3BJ,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC;EACbJ,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC;EACbJ,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC;EACb,OAAOJ,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASa,GAAGA,CAACb,GAAG,EAAEM,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAChCR,GAAG,CAAC,CAAC,CAAC,GAAGM,CAAC;EACVN,GAAG,CAAC,CAAC,CAAC,GAAGO,CAAC;EACVP,GAAG,CAAC,CAAC,CAAC,GAAGQ,CAAC;EACV,OAAOR,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASc,GAAGA,CAACd,GAAG,EAAEI,CAAC,EAAEW,CAAC,EAAE;EAC7Bf,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC;EACpBf,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC;EACpBf,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC;EACpB,OAAOf,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASgB,QAAQA,CAAChB,GAAG,EAAEI,CAAC,EAAEW,CAAC,EAAE;EAClCf,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC;EACpBf,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC;EACpBf,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC;EACpB,OAAOf,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASiB,QAAQA,CAACjB,GAAG,EAAEI,CAAC,EAAEW,CAAC,EAAE;EAClCf,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC;EACpBf,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC;EACpBf,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC;EACpB,OAAOf,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASkB,MAAMA,CAAClB,GAAG,EAAEI,CAAC,EAAEW,CAAC,EAAE;EAChCf,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC;EACpBf,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC;EACpBf,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC;EACpB,OAAOf,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASmB,IAAIA,CAACnB,GAAG,EAAEI,CAAC,EAAE;EAC3BJ,GAAG,CAAC,CAAC,CAAC,GAAGS,IAAI,CAACU,IAAI,CAACf,CAAC,CAAC,CAAC,CAAC,CAAC;EACxBJ,GAAG,CAAC,CAAC,CAAC,GAAGS,IAAI,CAACU,IAAI,CAACf,CAAC,CAAC,CAAC,CAAC,CAAC;EACxBJ,GAAG,CAAC,CAAC,CAAC,GAAGS,IAAI,CAACU,IAAI,CAACf,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB,OAAOJ,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASoB,KAAKA,CAACpB,GAAG,EAAEI,CAAC,EAAE;EAC5BJ,GAAG,CAAC,CAAC,CAAC,GAAGS,IAAI,CAACW,KAAK,CAAChB,CAAC,CAAC,CAAC,CAAC,CAAC;EACzBJ,GAAG,CAAC,CAAC,CAAC,GAAGS,IAAI,CAACW,KAAK,CAAChB,CAAC,CAAC,CAAC,CAAC,CAAC;EACzBJ,GAAG,CAAC,CAAC,CAAC,GAAGS,IAAI,CAACW,KAAK,CAAChB,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,OAAOJ,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASqB,GAAGA,CAACrB,GAAG,EAAEI,CAAC,EAAEW,CAAC,EAAE;EAC7Bf,GAAG,CAAC,CAAC,CAAC,GAAGS,IAAI,CAACY,GAAG,CAACjB,CAAC,CAAC,CAAC,CAAC,EAAEW,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7Bf,GAAG,CAAC,CAAC,CAAC,GAAGS,IAAI,CAACY,GAAG,CAACjB,CAAC,CAAC,CAAC,CAAC,EAAEW,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7Bf,GAAG,CAAC,CAAC,CAAC,GAAGS,IAAI,CAACY,GAAG,CAACjB,CAAC,CAAC,CAAC,CAAC,EAAEW,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,OAAOf,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASsB,GAAGA,CAACtB,GAAG,EAAEI,CAAC,EAAEW,CAAC,EAAE;EAC7Bf,GAAG,CAAC,CAAC,CAAC,GAAGS,IAAI,CAACa,GAAG,CAAClB,CAAC,CAAC,CAAC,CAAC,EAAEW,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7Bf,GAAG,CAAC,CAAC,CAAC,GAAGS,IAAI,CAACa,GAAG,CAAClB,CAAC,CAAC,CAAC,CAAC,EAAEW,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7Bf,GAAG,CAAC,CAAC,CAAC,GAAGS,IAAI,CAACa,GAAG,CAAClB,CAAC,CAAC,CAAC,CAAC,EAAEW,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,OAAOf,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASuB,KAAKA,CAACvB,GAAG,EAAEI,CAAC,EAAE;EAC5BJ,GAAG,CAAC,CAAC,CAAC,GAAGF,QAAQ,CAACyB,KAAK,CAACnB,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7BJ,GAAG,CAAC,CAAC,CAAC,GAAGF,QAAQ,CAACyB,KAAK,CAACnB,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7BJ,GAAG,CAAC,CAAC,CAAC,GAAGF,QAAQ,CAACyB,KAAK,CAACnB,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,OAAOJ,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASwB,KAAKA,CAACxB,GAAG,EAAEI,CAAC,EAAEW,CAAC,EAAE;EAC/Bf,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC;EACjBf,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC;EACjBf,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC;EACjB,OAAOf,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASyB,WAAWA,CAACzB,GAAG,EAAEI,CAAC,EAAEW,CAAC,EAAES,KAAK,EAAE;EAC5CxB,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC,GAAGS,KAAK;EAC5BxB,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC,GAAGS,KAAK;EAC5BxB,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC,GAAGS,KAAK;EAC5B,OAAOxB,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS0B,QAAQA,CAACtB,CAAC,EAAEW,CAAC,EAAE;EAC7B,IAAIT,CAAC,GAAGS,CAAC,CAAC,CAAC,CAAC,GAAGX,CAAC,CAAC,CAAC,CAAC;EACnB,IAAIG,CAAC,GAAGQ,CAAC,CAAC,CAAC,CAAC,GAAGX,CAAC,CAAC,CAAC,CAAC;EACnB,IAAII,CAAC,GAAGO,CAAC,CAAC,CAAC,CAAC,GAAGX,CAAC,CAAC,CAAC,CAAC;EACnB,OAAOK,IAAI,CAACC,IAAI,CAACJ,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC,CAAC;AACzC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASmB,eAAeA,CAACvB,CAAC,EAAEW,CAAC,EAAE;EACpC,IAAIT,CAAC,GAAGS,CAAC,CAAC,CAAC,CAAC,GAAGX,CAAC,CAAC,CAAC,CAAC;EACnB,IAAIG,CAAC,GAAGQ,CAAC,CAAC,CAAC,CAAC,GAAGX,CAAC,CAAC,CAAC,CAAC;EACnB,IAAII,CAAC,GAAGO,CAAC,CAAC,CAAC,CAAC,GAAGX,CAAC,CAAC,CAAC,CAAC;EACnB,OAAOE,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC;AAC9B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASoB,aAAaA,CAACxB,CAAC,EAAE;EAC/B,IAAIE,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC;EACZ,IAAIG,CAAC,GAAGH,CAAC,CAAC,CAAC,CAAC;EACZ,IAAII,CAAC,GAAGJ,CAAC,CAAC,CAAC,CAAC;EACZ,OAAOE,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC;AAC9B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASqB,MAAMA,CAAC7B,GAAG,EAAEI,CAAC,EAAE;EAC7BJ,GAAG,CAAC,CAAC,CAAC,GAAG,CAACI,CAAC,CAAC,CAAC,CAAC;EACdJ,GAAG,CAAC,CAAC,CAAC,GAAG,CAACI,CAAC,CAAC,CAAC,CAAC;EACdJ,GAAG,CAAC,CAAC,CAAC,GAAG,CAACI,CAAC,CAAC,CAAC,CAAC;EACd,OAAOJ,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS8B,OAAOA,CAAC9B,GAAG,EAAEI,CAAC,EAAE;EAC9BJ,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGI,CAAC,CAAC,CAAC,CAAC;EACnBJ,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGI,CAAC,CAAC,CAAC,CAAC;EACnBJ,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGI,CAAC,CAAC,CAAC,CAAC;EACnB,OAAOJ,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS+B,SAASA,CAAC/B,GAAG,EAAEI,CAAC,EAAE;EAChC,IAAIE,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC;EACZ,IAAIG,CAAC,GAAGH,CAAC,CAAC,CAAC,CAAC;EACZ,IAAII,CAAC,GAAGJ,CAAC,CAAC,CAAC,CAAC;EACZ,IAAI4B,GAAG,GAAG1B,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC;EAC/B,IAAIwB,GAAG,GAAG,CAAC,EAAE;IACX;IACAA,GAAG,GAAG,CAAC,GAAGvB,IAAI,CAACC,IAAI,CAACsB,GAAG,CAAC;EAC1B;EACAhC,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,GAAG4B,GAAG;EACnBhC,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,GAAG4B,GAAG;EACnBhC,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,GAAG4B,GAAG;EACnB,OAAOhC,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASiC,GAAGA,CAAC7B,CAAC,EAAEW,CAAC,EAAE;EACxB,OAAOX,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC,GAAGX,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC,GAAGX,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC;AAChD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASmB,KAAKA,CAAClC,GAAG,EAAEI,CAAC,EAAEW,CAAC,EAAE;EAC/B,IAAIoB,EAAE,GAAG/B,CAAC,CAAC,CAAC,CAAC;IACXgC,EAAE,GAAGhC,CAAC,CAAC,CAAC,CAAC;IACTiC,EAAE,GAAGjC,CAAC,CAAC,CAAC,CAAC;EACX,IAAIkC,EAAE,GAAGvB,CAAC,CAAC,CAAC,CAAC;IACXwB,EAAE,GAAGxB,CAAC,CAAC,CAAC,CAAC;IACTyB,EAAE,GAAGzB,CAAC,CAAC,CAAC,CAAC;EACXf,GAAG,CAAC,CAAC,CAAC,GAAGoC,EAAE,GAAGI,EAAE,GAAGH,EAAE,GAAGE,EAAE;EAC1BvC,GAAG,CAAC,CAAC,CAAC,GAAGqC,EAAE,GAAGC,EAAE,GAAGH,EAAE,GAAGK,EAAE;EAC1BxC,GAAG,CAAC,CAAC,CAAC,GAAGmC,EAAE,GAAGI,EAAE,GAAGH,EAAE,GAAGE,EAAE;EAC1B,OAAOtC,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASyC,IAAIA,CAACzC,GAAG,EAAEI,CAAC,EAAEW,CAAC,EAAE2B,CAAC,EAAE;EACjC,IAAIP,EAAE,GAAG/B,CAAC,CAAC,CAAC,CAAC;EACb,IAAIgC,EAAE,GAAGhC,CAAC,CAAC,CAAC,CAAC;EACb,IAAIiC,EAAE,GAAGjC,CAAC,CAAC,CAAC,CAAC;EACbJ,GAAG,CAAC,CAAC,CAAC,GAAGmC,EAAE,GAAGO,CAAC,IAAI3B,CAAC,CAAC,CAAC,CAAC,GAAGoB,EAAE,CAAC;EAC7BnC,GAAG,CAAC,CAAC,CAAC,GAAGoC,EAAE,GAAGM,CAAC,IAAI3B,CAAC,CAAC,CAAC,CAAC,GAAGqB,EAAE,CAAC;EAC7BpC,GAAG,CAAC,CAAC,CAAC,GAAGqC,EAAE,GAAGK,CAAC,IAAI3B,CAAC,CAAC,CAAC,CAAC,GAAGsB,EAAE,CAAC;EAC7B,OAAOrC,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS2C,KAAKA,CAAC3C,GAAG,EAAEI,CAAC,EAAEW,CAAC,EAAE2B,CAAC,EAAE;EAClC,IAAIE,KAAK,GAAGnC,IAAI,CAACoC,IAAI,CAACpC,IAAI,CAACY,GAAG,CAACZ,IAAI,CAACa,GAAG,CAACW,GAAG,CAAC7B,CAAC,EAAEW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC3D,IAAI+B,QAAQ,GAAGrC,IAAI,CAACsC,GAAG,CAACH,KAAK,CAAC;EAC9B,IAAII,MAAM,GAAGvC,IAAI,CAACsC,GAAG,CAAC,CAAC,CAAC,GAAGL,CAAC,IAAIE,KAAK,CAAC,GAAGE,QAAQ;EACjD,IAAIG,MAAM,GAAGxC,IAAI,CAACsC,GAAG,CAACL,CAAC,GAAGE,KAAK,CAAC,GAAGE,QAAQ;EAC3C9C,GAAG,CAAC,CAAC,CAAC,GAAGgD,MAAM,GAAG5C,CAAC,CAAC,CAAC,CAAC,GAAG6C,MAAM,GAAGlC,CAAC,CAAC,CAAC,CAAC;EACtCf,GAAG,CAAC,CAAC,CAAC,GAAGgD,MAAM,GAAG5C,CAAC,CAAC,CAAC,CAAC,GAAG6C,MAAM,GAAGlC,CAAC,CAAC,CAAC,CAAC;EACtCf,GAAG,CAAC,CAAC,CAAC,GAAGgD,MAAM,GAAG5C,CAAC,CAAC,CAAC,CAAC,GAAG6C,MAAM,GAAGlC,CAAC,CAAC,CAAC,CAAC;EACtC,OAAOf,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASkD,OAAOA,CAAClD,GAAG,EAAEI,CAAC,EAAEW,CAAC,EAAEoC,CAAC,EAAEC,CAAC,EAAEV,CAAC,EAAE;EAC1C,IAAIW,YAAY,GAAGX,CAAC,GAAGA,CAAC;EACxB,IAAIY,OAAO,GAAGD,YAAY,IAAI,CAAC,GAAGX,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EAC5C,IAAIa,OAAO,GAAGF,YAAY,IAAIX,CAAC,GAAG,CAAC,CAAC,GAAGA,CAAC;EACxC,IAAIc,OAAO,GAAGH,YAAY,IAAIX,CAAC,GAAG,CAAC,CAAC;EACpC,IAAIe,OAAO,GAAGJ,YAAY,IAAI,CAAC,GAAG,CAAC,GAAGX,CAAC,CAAC;EACxC1C,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,GAAGkD,OAAO,GAAGvC,CAAC,CAAC,CAAC,CAAC,GAAGwC,OAAO,GAAGJ,CAAC,CAAC,CAAC,CAAC,GAAGK,OAAO,GAAGJ,CAAC,CAAC,CAAC,CAAC,GAAGK,OAAO;EAC1EzD,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,GAAGkD,OAAO,GAAGvC,CAAC,CAAC,CAAC,CAAC,GAAGwC,OAAO,GAAGJ,CAAC,CAAC,CAAC,CAAC,GAAGK,OAAO,GAAGJ,CAAC,CAAC,CAAC,CAAC,GAAGK,OAAO;EAC1EzD,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,GAAGkD,OAAO,GAAGvC,CAAC,CAAC,CAAC,CAAC,GAAGwC,OAAO,GAAGJ,CAAC,CAAC,CAAC,CAAC,GAAGK,OAAO,GAAGJ,CAAC,CAAC,CAAC,CAAC,GAAGK,OAAO;EAC1E,OAAOzD,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS0D,MAAMA,CAAC1D,GAAG,EAAEI,CAAC,EAAEW,CAAC,EAAEoC,CAAC,EAAEC,CAAC,EAAEV,CAAC,EAAE;EACzC,IAAIiB,aAAa,GAAG,CAAC,GAAGjB,CAAC;EACzB,IAAIkB,qBAAqB,GAAGD,aAAa,GAAGA,aAAa;EACzD,IAAIN,YAAY,GAAGX,CAAC,GAAGA,CAAC;EACxB,IAAIY,OAAO,GAAGM,qBAAqB,GAAGD,aAAa;EACnD,IAAIJ,OAAO,GAAG,CAAC,GAAGb,CAAC,GAAGkB,qBAAqB;EAC3C,IAAIJ,OAAO,GAAG,CAAC,GAAGH,YAAY,GAAGM,aAAa;EAC9C,IAAIF,OAAO,GAAGJ,YAAY,GAAGX,CAAC;EAC9B1C,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,GAAGkD,OAAO,GAAGvC,CAAC,CAAC,CAAC,CAAC,GAAGwC,OAAO,GAAGJ,CAAC,CAAC,CAAC,CAAC,GAAGK,OAAO,GAAGJ,CAAC,CAAC,CAAC,CAAC,GAAGK,OAAO;EAC1EzD,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,GAAGkD,OAAO,GAAGvC,CAAC,CAAC,CAAC,CAAC,GAAGwC,OAAO,GAAGJ,CAAC,CAAC,CAAC,CAAC,GAAGK,OAAO,GAAGJ,CAAC,CAAC,CAAC,CAAC,GAAGK,OAAO;EAC1EzD,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC,GAAGkD,OAAO,GAAGvC,CAAC,CAAC,CAAC,CAAC,GAAGwC,OAAO,GAAGJ,CAAC,CAAC,CAAC,CAAC,GAAGK,OAAO,GAAGJ,CAAC,CAAC,CAAC,CAAC,GAAGK,OAAO;EAC1E,OAAOzD,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS6D,MAAMA,CAAC7D,GAAG,EAAEwB,KAAK,EAAE;EACjCA,KAAK,GAAGA,KAAK,KAAKsC,SAAS,GAAG,GAAG,GAAGtC,KAAK;EACzC,IAAIuC,CAAC,GAAGjE,QAAQ,CAACkE,MAAM,CAAC,CAAC,GAAG,GAAG,GAAGvD,IAAI,CAACwD,EAAE;EACzC,IAAIzD,CAAC,GAAGV,QAAQ,CAACkE,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;EACrC,IAAIE,MAAM,GAAGzD,IAAI,CAACC,IAAI,CAAC,GAAG,GAAGF,CAAC,GAAGA,CAAC,CAAC,GAAGgB,KAAK;EAC3CxB,GAAG,CAAC,CAAC,CAAC,GAAGS,IAAI,CAAC0D,GAAG,CAACJ,CAAC,CAAC,GAAGG,MAAM;EAC7BlE,GAAG,CAAC,CAAC,CAAC,GAAGS,IAAI,CAACsC,GAAG,CAACgB,CAAC,CAAC,GAAGG,MAAM;EAC7BlE,GAAG,CAAC,CAAC,CAAC,GAAGQ,CAAC,GAAGgB,KAAK;EAClB,OAAOxB,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASoE,aAAaA,CAACpE,GAAG,EAAEI,CAAC,EAAEiE,CAAC,EAAE;EACvC,IAAI/D,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC;IACVG,CAAC,GAAGH,CAAC,CAAC,CAAC,CAAC;IACRI,CAAC,GAAGJ,CAAC,CAAC,CAAC,CAAC;EACV,IAAIkE,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,GAAG/D,CAAC,GAAG+D,CAAC,CAAC,CAAC,CAAC,GAAG9D,CAAC,GAAG8D,CAAC,CAAC,EAAE,CAAC,GAAG7D,CAAC,GAAG6D,CAAC,CAAC,EAAE,CAAC;EAC/CC,CAAC,GAAGA,CAAC,IAAI,GAAG;EACZtE,GAAG,CAAC,CAAC,CAAC,GAAG,CAACqE,CAAC,CAAC,CAAC,CAAC,GAAG/D,CAAC,GAAG+D,CAAC,CAAC,CAAC,CAAC,GAAG9D,CAAC,GAAG8D,CAAC,CAAC,CAAC,CAAC,GAAG7D,CAAC,GAAG6D,CAAC,CAAC,EAAE,CAAC,IAAIC,CAAC;EACrDtE,GAAG,CAAC,CAAC,CAAC,GAAG,CAACqE,CAAC,CAAC,CAAC,CAAC,GAAG/D,CAAC,GAAG+D,CAAC,CAAC,CAAC,CAAC,GAAG9D,CAAC,GAAG8D,CAAC,CAAC,CAAC,CAAC,GAAG7D,CAAC,GAAG6D,CAAC,CAAC,EAAE,CAAC,IAAIC,CAAC;EACrDtE,GAAG,CAAC,CAAC,CAAC,GAAG,CAACqE,CAAC,CAAC,CAAC,CAAC,GAAG/D,CAAC,GAAG+D,CAAC,CAAC,CAAC,CAAC,GAAG9D,CAAC,GAAG8D,CAAC,CAAC,EAAE,CAAC,GAAG7D,CAAC,GAAG6D,CAAC,CAAC,EAAE,CAAC,IAAIC,CAAC;EACtD,OAAOtE,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASuE,aAAaA,CAACvE,GAAG,EAAEI,CAAC,EAAEiE,CAAC,EAAE;EACvC,IAAI/D,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC;IACVG,CAAC,GAAGH,CAAC,CAAC,CAAC,CAAC;IACRI,CAAC,GAAGJ,CAAC,CAAC,CAAC,CAAC;EACVJ,GAAG,CAAC,CAAC,CAAC,GAAGM,CAAC,GAAG+D,CAAC,CAAC,CAAC,CAAC,GAAG9D,CAAC,GAAG8D,CAAC,CAAC,CAAC,CAAC,GAAG7D,CAAC,GAAG6D,CAAC,CAAC,CAAC,CAAC;EACvCrE,GAAG,CAAC,CAAC,CAAC,GAAGM,CAAC,GAAG+D,CAAC,CAAC,CAAC,CAAC,GAAG9D,CAAC,GAAG8D,CAAC,CAAC,CAAC,CAAC,GAAG7D,CAAC,GAAG6D,CAAC,CAAC,CAAC,CAAC;EACvCrE,GAAG,CAAC,CAAC,CAAC,GAAGM,CAAC,GAAG+D,CAAC,CAAC,CAAC,CAAC,GAAG9D,CAAC,GAAG8D,CAAC,CAAC,CAAC,CAAC,GAAG7D,CAAC,GAAG6D,CAAC,CAAC,CAAC,CAAC;EACvC,OAAOrE,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASwE,aAAaA,CAACxE,GAAG,EAAEI,CAAC,EAAEqE,CAAC,EAAE;EACvC;EACA;;EAEA,IAAIC,EAAE,GAAGD,CAAC,CAAC,CAAC,CAAC;IACXE,EAAE,GAAGF,CAAC,CAAC,CAAC,CAAC;IACTG,EAAE,GAAGH,CAAC,CAAC,CAAC,CAAC;IACTI,EAAE,GAAGJ,CAAC,CAAC,CAAC,CAAC;EACX,IAAIK,EAAE,GAAG1E,CAAC,CAAC,CAAC,CAAC;IACX2E,EAAE,GAAG3E,CAAC,CAAC,CAAC,CAAC;IACT4E,EAAE,GAAG5E,CAAC,CAAC,CAAC,CAAC;;EAEX;EACA,IAAI6E,EAAE,GAAGN,EAAE,GAAGK,EAAE,GAAGJ,EAAE,GAAGG,EAAE;EAC1B,IAAIG,EAAE,GAAGN,EAAE,GAAGE,EAAE,GAAGJ,EAAE,GAAGM,EAAE;EAC1B,IAAIG,EAAE,GAAGT,EAAE,GAAGK,EAAE,GAAGJ,EAAE,GAAGG,EAAE;;EAE1B;EACAG,EAAE,GAAGA,EAAE,GAAGA,EAAE;EACZC,EAAE,GAAGA,EAAE,GAAGA,EAAE;EACZC,EAAE,GAAGA,EAAE,GAAGA,EAAE;;EAEZ;EACAnF,GAAG,CAAC,CAAC,CAAC,GAAG8E,EAAE,GAAGD,EAAE,GAAGI,EAAE,GAAGN,EAAE,GAAGQ,EAAE,GAAGP,EAAE,GAAGM,EAAE;EACzClF,GAAG,CAAC,CAAC,CAAC,GAAG+E,EAAE,GAAGF,EAAE,GAAGK,EAAE,GAAGN,EAAE,GAAGK,EAAE,GAAGP,EAAE,GAAGS,EAAE;EACzCnF,GAAG,CAAC,CAAC,CAAC,GAAGgF,EAAE,GAAGH,EAAE,GAAGM,EAAE,GAAGT,EAAE,GAAGQ,EAAE,GAAGP,EAAE,GAAGM,EAAE;EACzC,OAAOjF,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASoF,OAAOA,CAACpF,GAAG,EAAEI,CAAC,EAAEW,CAAC,EAAEsE,GAAG,EAAE;EACtC,IAAIC,CAAC,GAAG,EAAE;IACRvB,CAAC,GAAG,EAAE;EACR;EACAuB,CAAC,CAAC,CAAC,CAAC,GAAGlF,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC;EAClBuE,CAAC,CAAC,CAAC,CAAC,GAAGlF,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC;EAClBuE,CAAC,CAAC,CAAC,CAAC,GAAGlF,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC;;EAElB;EACAgD,CAAC,CAAC,CAAC,CAAC,GAAGuB,CAAC,CAAC,CAAC,CAAC;EACXvB,CAAC,CAAC,CAAC,CAAC,GAAGuB,CAAC,CAAC,CAAC,CAAC,GAAG7E,IAAI,CAAC0D,GAAG,CAACkB,GAAG,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAG7E,IAAI,CAACsC,GAAG,CAACsC,GAAG,CAAC;EAClDtB,CAAC,CAAC,CAAC,CAAC,GAAGuB,CAAC,CAAC,CAAC,CAAC,GAAG7E,IAAI,CAACsC,GAAG,CAACsC,GAAG,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAG7E,IAAI,CAAC0D,GAAG,CAACkB,GAAG,CAAC;;EAElD;EACArF,GAAG,CAAC,CAAC,CAAC,GAAG+D,CAAC,CAAC,CAAC,CAAC,GAAGhD,CAAC,CAAC,CAAC,CAAC;EACpBf,GAAG,CAAC,CAAC,CAAC,GAAG+D,CAAC,CAAC,CAAC,CAAC,GAAGhD,CAAC,CAAC,CAAC,CAAC;EACpBf,GAAG,CAAC,CAAC,CAAC,GAAG+D,CAAC,CAAC,CAAC,CAAC,GAAGhD,CAAC,CAAC,CAAC,CAAC;EACpB,OAAOf,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASuF,OAAOA,CAACvF,GAAG,EAAEI,CAAC,EAAEW,CAAC,EAAEsE,GAAG,EAAE;EACtC,IAAIC,CAAC,GAAG,EAAE;IACRvB,CAAC,GAAG,EAAE;EACR;EACAuB,CAAC,CAAC,CAAC,CAAC,GAAGlF,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC;EAClBuE,CAAC,CAAC,CAAC,CAAC,GAAGlF,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC;EAClBuE,CAAC,CAAC,CAAC,CAAC,GAAGlF,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC;;EAElB;EACAgD,CAAC,CAAC,CAAC,CAAC,GAAGuB,CAAC,CAAC,CAAC,CAAC,GAAG7E,IAAI,CAACsC,GAAG,CAACsC,GAAG,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAG7E,IAAI,CAAC0D,GAAG,CAACkB,GAAG,CAAC;EAClDtB,CAAC,CAAC,CAAC,CAAC,GAAGuB,CAAC,CAAC,CAAC,CAAC;EACXvB,CAAC,CAAC,CAAC,CAAC,GAAGuB,CAAC,CAAC,CAAC,CAAC,GAAG7E,IAAI,CAAC0D,GAAG,CAACkB,GAAG,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAG7E,IAAI,CAACsC,GAAG,CAACsC,GAAG,CAAC;;EAElD;EACArF,GAAG,CAAC,CAAC,CAAC,GAAG+D,CAAC,CAAC,CAAC,CAAC,GAAGhD,CAAC,CAAC,CAAC,CAAC;EACpBf,GAAG,CAAC,CAAC,CAAC,GAAG+D,CAAC,CAAC,CAAC,CAAC,GAAGhD,CAAC,CAAC,CAAC,CAAC;EACpBf,GAAG,CAAC,CAAC,CAAC,GAAG+D,CAAC,CAAC,CAAC,CAAC,GAAGhD,CAAC,CAAC,CAAC,CAAC;EACpB,OAAOf,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASwF,OAAOA,CAACxF,GAAG,EAAEI,CAAC,EAAEW,CAAC,EAAEsE,GAAG,EAAE;EACtC,IAAIC,CAAC,GAAG,EAAE;IACRvB,CAAC,GAAG,EAAE;EACR;EACAuB,CAAC,CAAC,CAAC,CAAC,GAAGlF,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC;EAClBuE,CAAC,CAAC,CAAC,CAAC,GAAGlF,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC;EAClBuE,CAAC,CAAC,CAAC,CAAC,GAAGlF,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC,CAAC,CAAC,CAAC;;EAElB;EACAgD,CAAC,CAAC,CAAC,CAAC,GAAGuB,CAAC,CAAC,CAAC,CAAC,GAAG7E,IAAI,CAAC0D,GAAG,CAACkB,GAAG,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAG7E,IAAI,CAACsC,GAAG,CAACsC,GAAG,CAAC;EAClDtB,CAAC,CAAC,CAAC,CAAC,GAAGuB,CAAC,CAAC,CAAC,CAAC,GAAG7E,IAAI,CAACsC,GAAG,CAACsC,GAAG,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAG7E,IAAI,CAAC0D,GAAG,CAACkB,GAAG,CAAC;EAClDtB,CAAC,CAAC,CAAC,CAAC,GAAGuB,CAAC,CAAC,CAAC,CAAC;;EAEX;EACAtF,GAAG,CAAC,CAAC,CAAC,GAAG+D,CAAC,CAAC,CAAC,CAAC,GAAGhD,CAAC,CAAC,CAAC,CAAC;EACpBf,GAAG,CAAC,CAAC,CAAC,GAAG+D,CAAC,CAAC,CAAC,CAAC,GAAGhD,CAAC,CAAC,CAAC,CAAC;EACpBf,GAAG,CAAC,CAAC,CAAC,GAAG+D,CAAC,CAAC,CAAC,CAAC,GAAGhD,CAAC,CAAC,CAAC,CAAC;EACpB,OAAOf,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS4C,KAAKA,CAACxC,CAAC,EAAEW,CAAC,EAAE;EAC1B,IAAIoB,EAAE,GAAG/B,CAAC,CAAC,CAAC,CAAC;IACXgC,EAAE,GAAGhC,CAAC,CAAC,CAAC,CAAC;IACTiC,EAAE,GAAGjC,CAAC,CAAC,CAAC,CAAC;IACTkC,EAAE,GAAGvB,CAAC,CAAC,CAAC,CAAC;IACTwB,EAAE,GAAGxB,CAAC,CAAC,CAAC,CAAC;IACTyB,EAAE,GAAGzB,CAAC,CAAC,CAAC,CAAC;IACT0E,GAAG,GAAGhF,IAAI,CAACC,IAAI,CAAC,CAACyB,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,KAAKC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC,CAAC;IAC9EkD,MAAM,GAAGD,GAAG,IAAIxD,GAAG,CAAC7B,CAAC,EAAEW,CAAC,CAAC,GAAG0E,GAAG;EACjC,OAAOhF,IAAI,CAACoC,IAAI,CAACpC,IAAI,CAACY,GAAG,CAACZ,IAAI,CAACa,GAAG,CAACoE,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACrD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,IAAIA,CAAC3F,GAAG,EAAE;EACxBA,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACZA,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACZA,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;EACZ,OAAOA,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS4F,GAAGA,CAACxF,CAAC,EAAE;EACrB,OAAO,OAAO,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;AACzD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASyF,WAAWA,CAACzF,CAAC,EAAEW,CAAC,EAAE;EAChC,OAAOX,CAAC,CAAC,CAAC,CAAC,KAAKW,CAAC,CAAC,CAAC,CAAC,IAAIX,CAAC,CAAC,CAAC,CAAC,KAAKW,CAAC,CAAC,CAAC,CAAC,IAAIX,CAAC,CAAC,CAAC,CAAC,KAAKW,CAAC,CAAC,CAAC,CAAC;AACxD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS+E,MAAMA,CAAC1F,CAAC,EAAEW,CAAC,EAAE;EAC3B,IAAIgF,EAAE,GAAG3F,CAAC,CAAC,CAAC,CAAC;IACX4F,EAAE,GAAG5F,CAAC,CAAC,CAAC,CAAC;IACT6F,EAAE,GAAG7F,CAAC,CAAC,CAAC,CAAC;EACX,IAAI8F,EAAE,GAAGnF,CAAC,CAAC,CAAC,CAAC;IACXoF,EAAE,GAAGpF,CAAC,CAAC,CAAC,CAAC;IACTqF,EAAE,GAAGrF,CAAC,CAAC,CAAC,CAAC;EACX,OAAON,IAAI,CAAC4F,GAAG,CAACN,EAAE,GAAGG,EAAE,CAAC,IAAIpG,QAAQ,CAACwG,OAAO,GAAG7F,IAAI,CAACa,GAAG,CAAC,GAAG,EAAEb,IAAI,CAAC4F,GAAG,CAACN,EAAE,CAAC,EAAEtF,IAAI,CAAC4F,GAAG,CAACH,EAAE,CAAC,CAAC,IAAIzF,IAAI,CAAC4F,GAAG,CAACL,EAAE,GAAGG,EAAE,CAAC,IAAIrG,QAAQ,CAACwG,OAAO,GAAG7F,IAAI,CAACa,GAAG,CAAC,GAAG,EAAEb,IAAI,CAAC4F,GAAG,CAACL,EAAE,CAAC,EAAEvF,IAAI,CAAC4F,GAAG,CAACF,EAAE,CAAC,CAAC,IAAI1F,IAAI,CAAC4F,GAAG,CAACJ,EAAE,GAAGG,EAAE,CAAC,IAAItG,QAAQ,CAACwG,OAAO,GAAG7F,IAAI,CAACa,GAAG,CAAC,GAAG,EAAEb,IAAI,CAAC4F,GAAG,CAACJ,EAAE,CAAC,EAAExF,IAAI,CAAC4F,GAAG,CAACD,EAAE,CAAC,CAAC;AACpQ;;AAEA;AACA;AACA;AACA;AACA,OAAO,IAAIG,GAAG,GAAGvF,QAAQ;;AAEzB;AACA;AACA;AACA;AACA,OAAO,IAAIwF,GAAG,GAAGvF,QAAQ;;AAEzB;AACA;AACA;AACA;AACA,OAAO,IAAIwF,GAAG,GAAGvF,MAAM;;AAEvB;AACA;AACA;AACA;AACA,OAAO,IAAIwF,IAAI,GAAGhF,QAAQ;;AAE1B;AACA;AACA;AACA;AACA,OAAO,IAAIiF,OAAO,GAAGhF,eAAe;;AAEpC;AACA;AACA;AACA;AACA,OAAO,IAAIK,GAAG,GAAG3B,MAAM;;AAEvB;AACA;AACA;AACA;AACA,OAAO,IAAIuG,MAAM,GAAGhF,aAAa;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIiF,OAAO,GAAG,YAAY;EAC/B,IAAIC,GAAG,GAAG/G,MAAM,CAAC,CAAC;EAClB,OAAO,UAAUK,CAAC,EAAE2G,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,EAAE,EAAEC,GAAG,EAAE;IAClD,IAAIC,CAAC,EAAEC,CAAC;IACR,IAAI,CAACN,MAAM,EAAE;MACXA,MAAM,GAAG,CAAC;IACZ;IACA,IAAI,CAACC,MAAM,EAAE;MACXA,MAAM,GAAG,CAAC;IACZ;IACA,IAAIC,KAAK,EAAE;MACTI,CAAC,GAAG5G,IAAI,CAACY,GAAG,CAAC4F,KAAK,GAAGF,MAAM,GAAGC,MAAM,EAAE5G,CAAC,CAACC,MAAM,CAAC;IACjD,CAAC,MAAM;MACLgH,CAAC,GAAGjH,CAAC,CAACC,MAAM;IACd;IACA,KAAK+G,CAAC,GAAGJ,MAAM,EAAEI,CAAC,GAAGC,CAAC,EAAED,CAAC,IAAIL,MAAM,EAAE;MACnCD,GAAG,CAAC,CAAC,CAAC,GAAG1G,CAAC,CAACgH,CAAC,CAAC;MACbN,GAAG,CAAC,CAAC,CAAC,GAAG1G,CAAC,CAACgH,CAAC,GAAG,CAAC,CAAC;MACjBN,GAAG,CAAC,CAAC,CAAC,GAAG1G,CAAC,CAACgH,CAAC,GAAG,CAAC,CAAC;MACjBF,EAAE,CAACJ,GAAG,EAAEA,GAAG,EAAEK,GAAG,CAAC;MACjB/G,CAAC,CAACgH,CAAC,CAAC,GAAGN,GAAG,CAAC,CAAC,CAAC;MACb1G,CAAC,CAACgH,CAAC,GAAG,CAAC,CAAC,GAAGN,GAAG,CAAC,CAAC,CAAC;MACjB1G,CAAC,CAACgH,CAAC,GAAG,CAAC,CAAC,GAAGN,GAAG,CAAC,CAAC,CAAC;IACnB;IACA,OAAO1G,CAAC;EACV,CAAC;AACH,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}