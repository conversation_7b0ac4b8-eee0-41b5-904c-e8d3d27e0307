{"ast": null, "code": "import { makeChartComp } from '../../../util/makeChartComp';\nvar TinyProgressChart = makeChartComp('TinyProgress');\nexport default TinyProgressChart;", "map": {"version": 3, "names": ["makeChartComp", "TinyProgressChart"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/@ant-design/plots/es/components/tiny/progress/index.js"], "sourcesContent": ["import { makeChartComp } from '../../../util/makeChartComp';\nvar TinyProgressChart = makeChartComp('TinyProgress');\nexport default TinyProgressChart;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,6BAA6B;AAC3D,IAAIC,iBAAiB,GAAGD,aAAa,CAAC,cAAc,CAAC;AACrD,eAAeC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}