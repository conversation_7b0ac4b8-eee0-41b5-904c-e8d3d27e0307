# 西安银马实业数字化管理系统 API 文档

## 概述

本文档描述了西安银马实业数字化管理系统的后端API接口，包括认证、BOM管理、物料管理、生产管理等模块的接口规范。

### 基础信息

- **基础URL**: `http://localhost:8080/api`
- **API版本**: v1.0
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: JWT Token

### 通用响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 认证模块

### 用户登录

**接口地址**: `POST /auth/login`

**请求参数**:
```json
{
  "username": "admin",
  "password": "123456"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "userInfo": {
      "id": 1,
      "username": "admin",
      "realName": "管理员",
      "email": "<EMAIL>",
      "roles": ["ADMIN"]
    }
  }
}
```

### 用户注销

**接口地址**: `POST /auth/logout`

**请求头**:
```
Authorization: Bearer {token}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "注销成功"
}
```

### 获取用户信息

**接口地址**: `GET /auth/userinfo`

**请求头**:
```
Authorization: Bearer {token}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "username": "admin",
    "realName": "管理员",
    "email": "<EMAIL>",
    "roles": ["ADMIN"],
    "permissions": ["bom:read", "bom:write", "material:read"]
  }
}
```

## BOM管理模块

### 获取BOM列表

**接口地址**: `GET /bom/page`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | Integer | 否 | 当前页码，默认1 |
| size | Integer | 否 | 每页大小，默认10 |
| bomCode | String | 否 | BOM编码 |
| bomName | String | 否 | BOM名称 |
| bomType | String | 否 | BOM类型：EBOM/PBOM/MBOM |
| versionStatus | String | 否 | 版本状态：DRAFT/ACTIVE/OBSOLETE/FROZEN |

**响应数据**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": 1,
        "bomCode": "BOM001",
        "bomName": "主机BOM",
        "bomType": "EBOM",
        "version": "1.0",
        "versionStatus": "ACTIVE",
        "materialCount": 25,
        "description": "主机产品BOM",
        "createBy": "admin",
        "createTime": "2024-01-01T10:00:00Z",
        "updateBy": "admin",
        "updateTime": "2024-01-01T10:00:00Z"
      }
    ],
    "total": 100,
    "current": 1,
    "size": 10
  }
}
```

### 创建BOM

**接口地址**: `POST /bom`

**请求参数**:
```json
{
  "bomCode": "BOM002",
  "bomName": "副机BOM",
  "bomType": "EBOM",
  "description": "副机产品BOM",
  "materials": [
    {
      "materialId": 1,
      "quantity": 2,
      "unit": "个",
      "remark": "主要组件"
    }
  ]
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 2,
    "bomCode": "BOM002",
    "bomName": "副机BOM",
    "bomType": "EBOM",
    "version": "1.0",
    "versionStatus": "DRAFT"
  }
}
```

### 更新BOM

**接口地址**: `PUT /bom/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | BOM ID |

**请求参数**:
```json
{
  "bomName": "副机BOM-修改",
  "description": "副机产品BOM-已修改"
}
```

### 删除BOM

**接口地址**: `DELETE /bom/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | BOM ID |

### 批量删除BOM

**接口地址**: `DELETE /bom/batch`

**请求参数**:
```json
{
  "ids": [1, 2, 3]
}
```

### 复制BOM

**接口地址**: `POST /bom/{id}/copy`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 源BOM ID |

### 获取BOM统计

**接口地址**: `GET /bom/dashboard`

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "totalCount": 150,
    "activeCount": 80,
    "draftCount": 50,
    "obsoleteCount": 20
  }
}
```

### 导出BOM

**接口地址**: `GET /bom/export`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | String | 否 | BOM ID列表，逗号分隔 |
| format | String | 否 | 导出格式：excel/pdf，默认excel |

### 导入BOM

**接口地址**: `POST /bom/import`

**请求参数**: 文件上传（multipart/form-data）
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| file | File | 是 | Excel文件 |

## 物料管理模块

### 获取物料列表

**接口地址**: `GET /material/page`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | Integer | 否 | 当前页码，默认1 |
| size | Integer | 否 | 每页大小，默认10 |
| materialCode | String | 否 | 物料编码 |
| materialName | String | 否 | 物料名称 |
| categoryId | Long | 否 | 分类ID |

**响应数据**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": 1,
        "materialCode": "MAT001",
        "materialName": "钢板",
        "specification": "10mm*1000mm*2000mm",
        "unit": "张",
        "standardPrice": 1500.00,
        "categoryId": 1,
        "categoryName": "原材料",
        "supplier": "钢铁公司",
        "status": "ACTIVE",
        "createTime": "2024-01-01T10:00:00Z"
      }
    ],
    "total": 200,
    "current": 1,
    "size": 10
  }
}
```

### 创建物料

**接口地址**: `POST /material`

**请求参数**:
```json
{
  "materialCode": "MAT002",
  "materialName": "铝板",
  "specification": "5mm*800mm*1600mm",
  "unit": "张",
  "standardPrice": 800.00,
  "categoryId": 1,
  "supplier": "铝业公司",
  "description": "高质量铝板"
}
```

### 更新物料

**接口地址**: `PUT /material/{id}`

### 删除物料

**接口地址**: `DELETE /material/{id}`

### 获取物料统计

**接口地址**: `GET /material/statistics`

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 500,
    "active": 450,
    "inactive": 50,
    "categories": [
      {
        "categoryName": "原材料",
        "count": 200
      },
      {
        "categoryName": "半成品",
        "count": 150
      }
    ]
  }
}
```

## 生产管理模块

### 获取生产订单列表

**接口地址**: `GET /production/order/page`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | Integer | 否 | 当前页码，默认1 |
| size | Integer | 否 | 每页大小，默认10 |
| orderNo | String | 否 | 订单号 |
| productName | String | 否 | 产品名称 |
| status | String | 否 | 订单状态 |

**响应数据**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": 1,
        "orderNo": "PO202401001",
        "productName": "主机设备",
        "quantity": 10,
        "unit": "台",
        "planStartDate": "2024-01-15",
        "planEndDate": "2024-02-15",
        "status": "IN_PROGRESS",
        "progress": 65.5,
        "createTime": "2024-01-01T10:00:00Z"
      }
    ],
    "total": 50,
    "current": 1,
    "size": 10
  }
}
```

### 创建生产订单

**接口地址**: `POST /production/order`

**请求参数**:
```json
{
  "orderNo": "PO202401002",
  "productName": "副机设备",
  "bomId": 1,
  "quantity": 5,
  "unit": "台",
  "planStartDate": "2024-01-20",
  "planEndDate": "2024-02-20",
  "priority": "HIGH",
  "remark": "紧急订单"
}
```

### 更新生产订单状态

**接口地址**: `PUT /production/order/{id}/status`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 订单ID |

**请求参数**:
```json
{
  "status": "COMPLETED",
  "remark": "生产完成"
}
```

## 项目交付模块

### 获取项目列表

**接口地址**: `GET /project/page`

**响应数据**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": 1,
        "projectNo": "PROJ202401001",
        "projectName": "某工厂自动化改造项目",
        "customerName": "某制造公司",
        "contractAmount": 5000000.00,
        "startDate": "2024-01-01",
        "endDate": "2024-06-30",
        "status": "IN_PROGRESS",
        "progress": 45.8,
        "manager": "张经理",
        "createTime": "2024-01-01T10:00:00Z"
      }
    ],
    "total": 30,
    "current": 1,
    "size": 10
  }
}
```

### 创建项目

**接口地址**: `POST /project`

**请求参数**:
```json
{
  "projectNo": "PROJ202401002",
  "projectName": "智能仓储系统项目",
  "customerName": "物流公司",
  "contractAmount": 3000000.00,
  "startDate": "2024-02-01",
  "endDate": "2024-08-31",
  "manager": "李经理",
  "description": "智能仓储管理系统建设"
}
```

## 设备管理模块

### 获取设备列表

**接口地址**: `GET /equipment/page`

**响应数据**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": 1,
        "equipmentCode": "EQ001",
        "equipmentName": "数控机床",
        "model": "CNC-2000",
        "manufacturer": "机床厂",
        "purchaseDate": "2023-01-01",
        "status": "RUNNING",
        "location": "车间A-01",
        "operator": "操作员1",
        "maintenanceDate": "2024-01-01",
        "createTime": "2023-01-01T10:00:00Z"
      }
    ],
    "total": 80,
    "current": 1,
    "size": 10
  }
}
```

## 错误处理

### 错误响应格式

```json
{
  "code": 400,
  "message": "请求参数错误",
  "data": null,
  "errors": [
    {
      "field": "bomCode",
      "message": "BOM编码不能为空"
    }
  ],
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 1001 | 用户名或密码错误 | 检查登录凭据 |
| 1002 | Token已过期 | 重新登录获取新Token |
| 1003 | 权限不足 | 联系管理员分配权限 |
| 2001 | BOM编码已存在 | 使用不同的BOM编码 |
| 2002 | BOM不存在 | 检查BOM ID是否正确 |
| 3001 | 物料编码已存在 | 使用不同的物料编码 |
| 3002 | 物料不存在 | 检查物料ID是否正确 |

## 接口测试

### 使用Postman测试

1. 导入API集合文件：`postman_collection.json`
2. 设置环境变量：
   - `base_url`: http://localhost:8080/api
   - `token`: 登录后获取的JWT Token

### 使用curl测试

```bash
# 登录获取Token
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"123456"}'

# 获取BOM列表
curl -X GET http://localhost:8080/api/bom/page \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json"

# 创建BOM
curl -X POST http://localhost:8080/api/bom \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{"bomCode":"BOM003","bomName":"测试BOM","bomType":"EBOM"}'
```

## 版本更新记录

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现基础的认证、BOM管理、物料管理功能
- 支持RESTful API规范
- 集成JWT认证机制

### 后续版本规划

- v1.1.0: 增加工艺路线管理
- v1.2.0: 增加库存管理功能
- v1.3.0: 增加报表统计功能
- v2.0.0: 支持微服务架构

## 联系方式

- **技术支持**: <EMAIL>
- **API问题**: <EMAIL>
- **文档更新**: <EMAIL>

---

*本文档最后更新时间: 2024-01-01*