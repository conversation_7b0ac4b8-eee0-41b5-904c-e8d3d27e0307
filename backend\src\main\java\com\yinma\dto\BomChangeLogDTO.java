package com.yinma.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * BOM变更日志数据传输对象
 * 用于前后端数据交互
 * 
 * <AUTHOR> System
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class BomChangeLogDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 变更日志主键ID
     */
    private Long id;

    /**
     * BOM主表ID
     */
    @NotNull(message = "BOM主表ID不能为空")
    private Long bomId;

    /**
     * BOM编码
     */
    private String bomCode;

    /**
     * BOM名称
     */
    private String bomName;

    /**
     * 变更类型
     */
    @NotBlank(message = "变更类型不能为空")
    private String changeType;

    /**
     * 变更操作
     */
    @NotBlank(message = "变更操作不能为空")
    private String changeOperation;

    /**
     * 变更对象类型
     */
    @NotBlank(message = "变更对象类型不能为空")
    private String changeObjectType;

    /**
     * 变更对象ID
     */
    private Long changeObjectId;

    /**
     * 变更对象编码
     */
    private String changeObjectCode;

    /**
     * 变更对象名称
     */
    private String changeObjectName;

    /**
     * 变更前数据（JSON格式）
     */
    private String beforeData;

    /**
     * 变更后数据（JSON格式）
     */
    private String afterData;

    /**
     * 变更字段
     */
    private String changedFields;

    /**
     * 变更原因
     */
    @NotBlank(message = "变更原因不能为空")
    @Size(max = 500, message = "变更原因长度不能超过500个字符")
    private String changeReason;

    /**
     * 变更描述
     */
    @Size(max = 1000, message = "变更描述长度不能超过1000个字符")
    private String changeDescription;

    /**
     * 影响分析
     */
    private String impactAnalysis;

    /**
     * 成本影响金额
     */
    private BigDecimal costImpact;

    /**
     * 变更申请人
     */
    private String changeApplicant;

    /**
     * 变更申请时间
     */
    private LocalDateTime changeApplyTime;

    /**
     * 审批状态
     */
    private String approvalStatus;

    /**
     * 审批人
     */
    private String approvedBy;

    /**
     * 审批时间
     */
    private LocalDateTime approvedTime;

    /**
     * 审批意见
     */
    private String approvalComment;

    /**
     * 执行状态
     */
    private String executeStatus;

    /**
     * 执行人
     */
    private String executedBy;

    /**
     * 执行时间
     */
    private LocalDateTime executedTime;

    /**
     * 执行结果
     */
    private String executeResult;

    /**
     * 回滚状态
     */
    private String rollbackStatus;

    /**
     * 回滚人
     */
    private String rollbackBy;

    /**
     * 回滚时间
     */
    private LocalDateTime rollbackTime;

    /**
     * 回滚原因
     */
    private String rollbackReason;

    /**
     * 关联变更单号
     */
    private String relatedChangeNo;

    /**
     * 紧急程度
     */
    private String urgencyLevel;

    /**
     * 计划生效时间
     */
    private LocalDateTime plannedEffectiveTime;

    /**
     * 实际生效时间
     */
    private LocalDateTime actualEffectiveTime;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 关联BOM信息（查询时填充）
     */
    private BomDTO bomInfo;

    /**
     * 影响的BOM列表（查询时填充）
     */
    private List<AffectedBomDTO> affectedBoms;

    /**
     * 变更明细列表（查询时填充）
     */
    private List<ChangeDetailDTO> changeDetails;

    /**
     * 审批流程记录（查询时填充）
     */
    private List<ApprovalRecordDTO> approvalRecords;

    /**
     * 附件列表（查询时填充）
     */
    private List<AttachmentDTO> attachments;

    /**
     * 受影响的BOM信息DTO
     */
    @Data
    @Accessors(chain = true)
    public static class AffectedBomDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * BOM ID
         */
        private Long bomId;

        /**
         * BOM编码
         */
        private String bomCode;

        /**
         * BOM名称
         */
        private String bomName;

        /**
         * BOM类型
         */
        private String bomType;

        /**
         * 产品编码
         */
        private String productCode;

        /**
         * 产品名称
         */
        private String productName;

        /**
         * 影响类型：DIRECT-直接影响，INDIRECT-间接影响
         */
        private String impactType;

        /**
         * 影响层级
         */
        private Integer impactLevel;

        /**
         * 影响描述
         */
        private String impactDescription;

        /**
         * 成本影响
         */
        private BigDecimal costImpact;

        /**
         * 是否需要同步更新
         */
        private Boolean needSync;

        /**
         * 同步状态
         */
        private String syncStatus;
    }

    /**
     * 变更明细DTO
     */
    @Data
    @Accessors(chain = true)
    public static class ChangeDetailDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 变更字段名
         */
        private String fieldName;

        /**
         * 变更字段显示名
         */
        private String fieldDisplayName;

        /**
         * 变更前值
         */
        private String beforeValue;

        /**
         * 变更后值
         */
        private String afterValue;

        /**
         * 变更前显示值
         */
        private String beforeDisplayValue;

        /**
         * 变更后显示值
         */
        private String afterDisplayValue;

        /**
         * 数据类型
         */
        private String dataType;

        /**
         * 变更影响等级
         */
        private String impactLevel;

        /**
         * 变更说明
         */
        private String changeDescription;
    }

    /**
     * 审批记录DTO
     */
    @Data
    @Accessors(chain = true)
    public static class ApprovalRecordDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 审批步骤
         */
        private Integer approvalStep;

        /**
         * 审批节点名称
         */
        private String approvalNodeName;

        /**
         * 审批人
         */
        private String approver;

        /**
         * 审批人姓名
         */
        private String approverName;

        /**
         * 审批结果：APPROVED-通过，REJECTED-拒绝，PENDING-待审批
         */
        private String approvalResult;

        /**
         * 审批时间
         */
        private LocalDateTime approvalTime;

        /**
         * 审批意见
         */
        private String approvalComment;

        /**
         * 是否当前节点
         */
        private Boolean isCurrent;
    }

    /**
     * 附件DTO
     */
    @Data
    @Accessors(chain = true)
    public static class AttachmentDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 附件ID
         */
        private Long attachmentId;

        /**
         * 文件名
         */
        private String fileName;

        /**
         * 文件大小
         */
        private Long fileSize;

        /**
         * 文件类型
         */
        private String fileType;

        /**
         * 文件路径
         */
        private String filePath;

        /**
         * 上传人
         */
        private String uploadBy;

        /**
         * 上传时间
         */
        private LocalDateTime uploadTime;

        /**
         * 文件描述
         */
        private String fileDescription;
    }

    /**
     * BOM变更日志查询条件DTO
     */
    @Data
    @Accessors(chain = true)
    public static class BomChangeLogQueryDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * BOM ID
         */
        private Long bomId;

        /**
         * BOM编码（模糊查询）
         */
        private String bomCode;

        /**
         * BOM名称（模糊查询）
         */
        private String bomName;

        /**
         * 变更类型
         */
        private String changeType;

        /**
         * 变更操作
         */
        private String changeOperation;

        /**
         * 变更对象类型
         */
        private String changeObjectType;

        /**
         * 变更对象编码（模糊查询）
         */
        private String changeObjectCode;

        /**
         * 变更申请人
         */
        private String changeApplicant;

        /**
         * 审批状态
         */
        private String approvalStatus;

        /**
         * 执行状态
         */
        private String executeStatus;

        /**
         * 紧急程度
         */
        private String urgencyLevel;

        /**
         * 变更申请时间开始
         */
        private LocalDateTime changeApplyTimeStart;

        /**
         * 变更申请时间结束
         */
        private LocalDateTime changeApplyTimeEnd;

        /**
         * 审批时间开始
         */
        private LocalDateTime approvedTimeStart;

        /**
         * 审批时间结束
         */
        private LocalDateTime approvedTimeEnd;

        /**
         * 执行时间开始
         */
        private LocalDateTime executedTimeStart;

        /**
         * 执行时间结束
         */
        private LocalDateTime executedTimeEnd;

        /**
         * 关联变更单号
         */
        private String relatedChangeNo;

        /**
         * 页码
         */
        @Min(value = 1, message = "页码必须大于0")
        private Integer pageNum = 1;

        /**
         * 页大小
         */
        @Min(value = 1, message = "页大小必须大于0")
        @Max(value = 100, message = "页大小不能超过100")
        private Integer pageSize = 10;

        /**
         * 排序字段
         */
        private String orderBy = "change_apply_time";

        /**
         * 排序方向
         */
        private String orderDirection = "DESC";
    }

    /**
     * 变更申请DTO
     */
    @Data
    @Accessors(chain = true)
    public static class ChangeRequestDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * BOM ID
         */
        @NotNull(message = "BOM ID不能为空")
        private Long bomId;

        /**
         * 变更类型
         */
        @NotBlank(message = "变更类型不能为空")
        private String changeType;

        /**
         * 变更原因
         */
        @NotBlank(message = "变更原因不能为空")
        private String changeReason;

        /**
         * 变更描述
         */
        private String changeDescription;

        /**
         * 紧急程度
         */
        private String urgencyLevel = "NORMAL";

        /**
         * 计划生效时间
         */
        private LocalDateTime plannedEffectiveTime;

        /**
         * 变更内容列表
         */
        @NotEmpty(message = "变更内容不能为空")
        private List<ChangeContentDTO> changeContents;

        /**
         * 附件列表
         */
        private List<String> attachmentPaths;

        /**
         * 备注
         */
        private String remark;
    }

    /**
     * 变更内容DTO
     */
    @Data
    @Accessors(chain = true)
    public static class ChangeContentDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 变更操作：ADD-新增，UPDATE-修改，DELETE-删除
         */
        @NotBlank(message = "变更操作不能为空")
        private String changeOperation;

        /**
         * 变更对象类型：BOM-BOM主表，DETAIL-BOM明细
         */
        @NotBlank(message = "变更对象类型不能为空")
        private String changeObjectType;

        /**
         * 变更对象ID
         */
        private Long changeObjectId;

        /**
         * 变更前数据
         */
        private String beforeData;

        /**
         * 变更后数据
         */
        private String afterData;

        /**
         * 变更字段
         */
        private String changedFields;

        /**
         * 变更说明
         */
        private String changeDescription;
    }

    /**
     * 变更影响分析结果DTO
     */
    @Data
    @Accessors(chain = true)
    public static class ChangeImpactAnalysisDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 影响的BOM数量
         */
        private Integer affectedBomCount;

        /**
         * 影响的物料数量
         */
        private Integer affectedMaterialCount;

        /**
         * 总成本影响
         */
        private BigDecimal totalCostImpact;

        /**
         * 影响的在制品数量
         */
        private Integer affectedWipCount;

        /**
         * 影响的库存数量
         */
        private BigDecimal affectedStockQty;

        /**
         * 影响的订单数量
         */
        private Integer affectedOrderCount;

        /**
         * 风险等级：LOW-低，MEDIUM-中，HIGH-高
         */
        private String riskLevel;

        /**
         * 影响分析详情
         */
        private String impactDetails;

        /**
         * 建议措施
         */
        private String recommendations;

        /**
         * 受影响的BOM列表
         */
        private List<AffectedBomDTO> affectedBoms;
    }
}