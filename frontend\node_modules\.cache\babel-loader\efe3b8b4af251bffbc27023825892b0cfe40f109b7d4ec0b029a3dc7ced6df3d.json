{"ast": null, "code": "import { curveLinearClosed, curveLinear } from '@antv/vendor/d3-shape';\nimport { isPolar } from '../../utils/coordinate';\nimport { Curve } from './curve';\nexport const Area = (options, context) => {\n  const {\n    coordinate\n  } = context;\n  return (...params) => {\n    const curve = isPolar(coordinate) ? curveLinearClosed : curveLinear;\n    return Curve(Object.assign({\n      curve: curve\n    }, options), context)(...params);\n  };\n};\nArea.props = Object.assign(Object.assign({}, Curve.props), {\n  defaultMarker: 'square'\n});", "map": {"version": 3, "names": ["curveLinearClosed", "curveLinear", "isPolar", "Curve", "Area", "options", "context", "coordinate", "params", "curve", "Object", "assign", "props", "defaultMarker"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\g2\\src\\shape\\area\\area.ts"], "sourcesContent": ["import { curveLinearClosed, curveLinear } from '@antv/vendor/d3-shape';\nimport { isPolar } from '../../utils/coordinate';\nimport { ShapeComponent as SC } from '../../runtime';\nimport { Curve } from './curve';\n\nexport type AreaOptions = Record<string, any>;\n\nexport const Area: SC<AreaOptions> = (options, context) => {\n  const { coordinate } = context;\n  return (...params) => {\n    const curve = isPolar(coordinate) ? curveLinearClosed : curveLinear;\n    return Curve({ curve: curve, ...options }, context)(...params);\n  };\n};\n\nArea.props = {\n  ...Curve.props,\n  defaultMarker: 'square',\n};\n"], "mappings": "AAAA,SAASA,iBAAiB,EAAEC,WAAW,QAAQ,uBAAuB;AACtE,SAASC,OAAO,QAAQ,wBAAwB;AAEhD,SAASC,KAAK,QAAQ,SAAS;AAI/B,OAAO,MAAMC,IAAI,GAAoBA,CAACC,OAAO,EAAEC,OAAO,KAAI;EACxD,MAAM;IAAEC;EAAU,CAAE,GAAGD,OAAO;EAC9B,OAAO,CAAC,GAAGE,MAAM,KAAI;IACnB,MAAMC,KAAK,GAAGP,OAAO,CAACK,UAAU,CAAC,GAAGP,iBAAiB,GAAGC,WAAW;IACnE,OAAOE,KAAK,CAAAO,MAAA,CAAAC,MAAA;MAAGF,KAAK,EAAEA;IAAK,GAAKJ,OAAO,GAAIC,OAAO,CAAC,CAAC,GAAGE,MAAM,CAAC;EAChE,CAAC;AACH,CAAC;AAEDJ,IAAI,CAACQ,KAAK,GAAAF,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACLR,KAAK,CAACS,KAAK;EACdC,aAAa,EAAE;AAAQ,EACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}