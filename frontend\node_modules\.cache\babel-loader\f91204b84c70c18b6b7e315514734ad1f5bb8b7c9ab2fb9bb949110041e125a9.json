{"ast": null, "code": "export default function cancelAnimationFrame(handler) {\n  var method = window.cancelAnimationFrame ||\n  // @ts-ignore\n  window.webkitCancelAnimationFrame ||\n  // @ts-ignore\n  window.mozCancelAnimationFrame ||\n  // @ts-ignore\n  window.msCancelAnimationFrame || clearTimeout;\n  method(handler);\n}", "map": {"version": 3, "names": ["cancelAnimationFrame", "handler", "method", "window", "webkitCancelAnimationFrame", "mozCancelAnimationFrame", "msCancelAnimationFrame", "clearTimeout"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\util\\src\\lodash\\clear-animation-frame.ts"], "sourcesContent": ["export default function cancelAnimationFrame(handler: number) {\n  const method =\n    window.cancelAnimationFrame ||\n    // @ts-ignore\n    window.webkitCancelAnimationFrame ||\n    // @ts-ignore\n    window.mozCancelAnimationFrame ||\n    // @ts-ignore\n    window.msCancelAnimationFrame ||\n    clearTimeout;\n\n  method(handler);\n}\n"], "mappings": "AAAA,eAAc,SAAUA,oBAAoBA,CAACC,OAAe;EAC1D,IAAMC,MAAM,GACVC,MAAM,CAACH,oBAAoB;EAC3B;EACAG,MAAM,CAACC,0BAA0B;EACjC;EACAD,MAAM,CAACE,uBAAuB;EAC9B;EACAF,MAAM,CAACG,sBAAsB,IAC7BC,YAAY;EAEdL,MAAM,CAACD,OAAO,CAAC;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}