{"ast": null, "code": "import { Arc as ArcLayout } from '../data/utils/arc';\n/**\n * For arc diagram(edge with weight) or chord diagram(with weight)\n */\nexport const Arc = options => {\n  return data => {\n    return ArcLayout(options)(data);\n  };\n};\nArc.props = {};", "map": {"version": 3, "names": ["Arc", "ArcLayout", "options", "data", "props"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\g2\\src\\data\\arc.ts"], "sourcesContent": ["import { DataComponent as DC } from '../runtime';\nimport { Arc as ArcLayout } from '../data/utils/arc';\n\nexport type ArcOptions = Omit<Record<string, any>, 'type'>;\n\n/**\n * For arc diagram(edge with weight) or chord diagram(with weight)\n */\nexport const Arc: DC<ArcOptions> = (options) => {\n  return (data) => {\n    return ArcLayout(options)(data);\n  };\n};\n\nArc.props = {};\n"], "mappings": "AACA,SAASA,GAAG,IAAIC,SAAS,QAAQ,mBAAmB;AAIpD;;;AAGA,OAAO,MAAMD,GAAG,GAAoBE,OAAO,IAAI;EAC7C,OAAQC,IAAI,IAAI;IACd,OAAOF,SAAS,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC;EACjC,CAAC;AACH,CAAC;AAEDH,GAAG,CAACI,KAAK,GAAG,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}