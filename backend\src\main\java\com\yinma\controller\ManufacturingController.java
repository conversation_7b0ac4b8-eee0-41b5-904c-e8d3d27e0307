package com.yinma.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinma.common.Result;
import com.yinma.entity.Manufacturing;
import com.yinma.dto.ManufacturingDTO;
import com.yinma.vo.ManufacturingVO;
import com.yinma.service.ManufacturingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 设备制造管理控制器
 * 银马实业设备制造管理系统
 * 
 * <AUTHOR>
 * @since 2024-05-20
 */
@Slf4j
@RestController
@RequestMapping("/api/manufacturing")
@RequiredArgsConstructor
@Api(tags = "设备制造管理")
public class ManufacturingController {

    private final ManufacturingService manufacturingService;

    @GetMapping("/page")
    @ApiOperation("分页查询制造订单")
    public Result<IPage<ManufacturingVO>> getManufacturingPage(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Long current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Long size,
            @ApiParam("订单编号") @RequestParam(required = false) String orderCode,
            @ApiParam("产品名称") @RequestParam(required = false) String productName,
            @ApiParam("产品系列") @RequestParam(required = false) String productSeries,
            @ApiParam("制造状态") @RequestParam(required = false) String status,
            @ApiParam("客户名称") @RequestParam(required = false) String customerName,
            @ApiParam("计划开始日期") @RequestParam(required = false) String planStartDate,
            @ApiParam("计划完成日期") @RequestParam(required = false) String planEndDate) {
        
        Page<Manufacturing> page = new Page<>(current, size);
        IPage<ManufacturingVO> result = manufacturingService.getManufacturingPage(
                page, orderCode, productName, productSeries, status, customerName, planStartDate, planEndDate);
        
        return Result.success(result);
    }

    @GetMapping("/{id}")
    @ApiOperation("根据ID查询制造订单详情")
    public Result<ManufacturingVO> getManufacturingById(
            @ApiParam("制造订单ID") @PathVariable Long id) {
        
        ManufacturingVO manufacturing = manufacturingService.getManufacturingDetailById(id);
        return Result.success(manufacturing);
    }

    @PostMapping
    @ApiOperation("创建制造订单")
    public Result<Manufacturing> createManufacturing(
            @ApiParam("制造订单信息") @Valid @RequestBody ManufacturingDTO manufacturingDTO) {
        
        Manufacturing manufacturing = manufacturingService.createManufacturing(manufacturingDTO);
        return Result.success(manufacturing);
    }

    @PutMapping("/{id}")
    @ApiOperation("更新制造订单")
    public Result<Manufacturing> updateManufacturing(
            @ApiParam("制造订单ID") @PathVariable Long id,
            @ApiParam("制造订单信息") @Valid @RequestBody ManufacturingDTO manufacturingDTO) {
        
        Manufacturing manufacturing = manufacturingService.updateManufacturing(id, manufacturingDTO);
        return Result.success(manufacturing);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除制造订单")
    public Result<Boolean> deleteManufacturing(
            @ApiParam("制造订单ID") @PathVariable Long id) {
        
        boolean result = manufacturingService.deleteManufacturing(id);
        return Result.success(result);
    }

    @DeleteMapping("/batch")
    @ApiOperation("批量删除制造订单")
    public Result<Boolean> batchDeleteManufacturing(
            @ApiParam("制造订单ID列表") @RequestBody List<Long> ids) {
        
        boolean result = manufacturingService.batchDeleteManufacturing(ids);
        return Result.success(result);
    }

    @PutMapping("/{id}/status")
    @ApiOperation("更新制造订单状态")
    public Result<Manufacturing> updateManufacturingStatus(
            @ApiParam("制造订单ID") @PathVariable Long id,
            @ApiParam("新状态") @RequestParam String status,
            @ApiParam("备注") @RequestParam(required = false) String remark) {
        
        Manufacturing manufacturing = manufacturingService.updateManufacturingStatus(id, status, remark);
        return Result.success(manufacturing);
    }

    @PostMapping("/{id}/copy")
    @ApiOperation("复制制造订单")
    public Result<Manufacturing> copyManufacturing(
            @ApiParam("原制造订单ID") @PathVariable Long id,
            @ApiParam("新订单编号") @RequestParam String newOrderCode) {
        
        Manufacturing manufacturing = manufacturingService.copyManufacturing(id, newOrderCode);
        return Result.success(manufacturing);
    }

    @GetMapping("/{id}/bom")
    @ApiOperation("获取制造订单BOM清单")
    public Result<List<Map<String, Object>>> getManufacturingBOM(
            @ApiParam("制造订单ID") @PathVariable Long id) {
        
        List<Map<String, Object>> bom = manufacturingService.getManufacturingBOM(id);
        return Result.success(bom);
    }

    @PutMapping("/{id}/bom")
    @ApiOperation("更新制造订单BOM清单")
    public Result<Void> updateManufacturingBOM(
            @ApiParam("制造订单ID") @PathVariable Long id,
            @ApiParam("BOM清单") @RequestBody List<Map<String, Object>> bomList) {
        
        manufacturingService.updateManufacturingBOM(id, bomList);
        return Result.success();
    }

    @GetMapping("/{id}/process")
    @ApiOperation("获取制造工艺流程")
    public Result<List<Map<String, Object>>> getManufacturingProcess(
            @ApiParam("制造订单ID") @PathVariable Long id) {
        
        List<Map<String, Object>> process = manufacturingService.getManufacturingProcess(id);
        return Result.success(process);
    }

    @PutMapping("/{id}/process")
    @ApiOperation("更新制造工艺流程")
    public Result<Void> updateManufacturingProcess(
            @ApiParam("制造订单ID") @PathVariable Long id,
            @ApiParam("工艺流程") @RequestBody List<Map<String, Object>> processList) {
        
        manufacturingService.updateManufacturingProcess(id, processList);
        return Result.success();
    }

    @GetMapping("/{id}/progress")
    @ApiOperation("获取制造进度")
    public Result<Map<String, Object>> getManufacturingProgress(
            @ApiParam("制造订单ID") @PathVariable Long id) {
        
        Map<String, Object> progress = manufacturingService.getManufacturingProgress(id);
        return Result.success(progress);
    }

    @PutMapping("/{id}/progress")
    @ApiOperation("更新制造进度")
    public Result<Void> updateManufacturingProgress(
            @ApiParam("制造订单ID") @PathVariable Long id,
            @ApiParam("进度信息") @RequestBody Map<String, Object> progressInfo) {
        
        manufacturingService.updateManufacturingProgress(id, progressInfo);
        return Result.success();
    }

    @GetMapping("/{id}/quality")
    @ApiOperation("获取质量检测记录")
    public Result<List<Map<String, Object>>> getManufacturingQuality(
            @ApiParam("制造订单ID") @PathVariable Long id) {
        
        List<Map<String, Object>> quality = manufacturingService.getManufacturingQuality(id);
        return Result.success(quality);
    }

    @PostMapping("/{id}/quality")
    @ApiOperation("添加质量检测记录")
    public Result<Void> addManufacturingQuality(
            @ApiParam("制造订单ID") @PathVariable Long id,
            @ApiParam("质量检测记录") @RequestBody Map<String, Object> qualityRecord) {
        
        manufacturingService.addManufacturingQuality(id, qualityRecord);
        return Result.success();
    }

    @GetMapping("/{id}/materials")
    @ApiOperation("获取物料消耗记录")
    public Result<List<Map<String, Object>>> getManufacturingMaterials(
            @ApiParam("制造订单ID") @PathVariable Long id) {
        
        List<Map<String, Object>> materials = manufacturingService.getManufacturingMaterials(id);
        return Result.success(materials);
    }

    @PostMapping("/{id}/materials")
    @ApiOperation("记录物料消耗")
    public Result<Void> recordManufacturingMaterials(
            @ApiParam("制造订单ID") @PathVariable Long id,
            @ApiParam("物料消耗记录") @RequestBody Map<String, Object> materialRecord) {
        
        manufacturingService.recordManufacturingMaterials(id, materialRecord);
        return Result.success();
    }

    @GetMapping("/{id}/workers")
    @ApiOperation("获取工人工时记录")
    public Result<List<Map<String, Object>>> getManufacturingWorkers(
            @ApiParam("制造订单ID") @PathVariable Long id) {
        
        List<Map<String, Object>> workers = manufacturingService.getManufacturingWorkers(id);
        return Result.success(workers);
    }

    @PostMapping("/{id}/workers")
    @ApiOperation("记录工人工时")
    public Result<Void> recordManufacturingWorkers(
            @ApiParam("制造订单ID") @PathVariable Long id,
            @ApiParam("工时记录") @RequestBody Map<String, Object> workerRecord) {
        
        manufacturingService.recordManufacturingWorkers(id, workerRecord);
        return Result.success();
    }

    @GetMapping("/{id}/equipment")
    @ApiOperation("获取设备使用记录")
    public Result<List<Map<String, Object>>> getManufacturingEquipment(
            @ApiParam("制造订单ID") @PathVariable Long id) {
        
        List<Map<String, Object>> equipment = manufacturingService.getManufacturingEquipment(id);
        return Result.success(equipment);
    }

    @PostMapping("/{id}/equipment")
    @ApiOperation("记录设备使用")
    public Result<Void> recordManufacturingEquipment(
            @ApiParam("制造订单ID") @PathVariable Long id,
            @ApiParam("设备使用记录") @RequestBody Map<String, Object> equipmentRecord) {
        
        manufacturingService.recordManufacturingEquipment(id, equipmentRecord);
        return Result.success();
    }

    @GetMapping("/{id}/cost")
    @ApiOperation("获取制造成本分析")
    public Result<Map<String, Object>> getManufacturingCost(
            @ApiParam("制造订单ID") @PathVariable Long id) {
        
        Map<String, Object> cost = manufacturingService.getManufacturingCost(id);
        return Result.success(cost);
    }

    @PutMapping("/{id}/cost")
    @ApiOperation("更新制造成本")
    public Result<Void> updateManufacturingCost(
            @ApiParam("制造订单ID") @PathVariable Long id,
            @ApiParam("成本信息") @RequestBody Map<String, Object> costInfo) {
        
        manufacturingService.updateManufacturingCost(id, costInfo);
        return Result.success();
    }

    @PostMapping("/{id}/images")
    @ApiOperation("上传制造过程图片")
    public Result<String> uploadManufacturingImage(
            @ApiParam("制造订单ID") @PathVariable Long id,
            @ApiParam("图片文件") @RequestParam("file") MultipartFile file,
            @ApiParam("图片类型") @RequestParam String imageType) {
        
        String imageUrl = manufacturingService.uploadManufacturingImage(id, file, imageType);
        return Result.success(imageUrl);
    }

    @GetMapping("/{id}/images")
    @ApiOperation("获取制造过程图片列表")
    public Result<List<Map<String, Object>>> getManufacturingImages(
            @ApiParam("制造订单ID") @PathVariable Long id) {
        
        List<Map<String, Object>> images = manufacturingService.getManufacturingImages(id);
        return Result.success(images);
    }

    @DeleteMapping("/{id}/images/{imageId}")
    @ApiOperation("删除制造过程图片")
    public Result<Boolean> deleteManufacturingImage(
            @ApiParam("制造订单ID") @PathVariable Long id,
            @ApiParam("图片ID") @PathVariable Long imageId) {
        
        boolean result = manufacturingService.deleteManufacturingImage(id, imageId);
        return Result.success(result);
    }

    @PostMapping("/{id}/documents")
    @ApiOperation("上传制造文档")
    public Result<String> uploadManufacturingDocument(
            @ApiParam("制造订单ID") @PathVariable Long id,
            @ApiParam("文档文件") @RequestParam("file") MultipartFile file,
            @ApiParam("文档类型") @RequestParam String docType) {
        
        String docUrl = manufacturingService.uploadManufacturingDocument(id, file, docType);
        return Result.success(docUrl);
    }

    @GetMapping("/{id}/documents")
    @ApiOperation("获取制造文档列表")
    public Result<List<Map<String, Object>>> getManufacturingDocuments(
            @ApiParam("制造订单ID") @PathVariable Long id) {
        
        List<Map<String, Object>> documents = manufacturingService.getManufacturingDocuments(id);
        return Result.success(documents);
    }

    @DeleteMapping("/{id}/documents/{docId}")
    @ApiOperation("删除制造文档")
    public Result<Boolean> deleteManufacturingDocument(
            @ApiParam("制造订单ID") @PathVariable Long id,
            @ApiParam("文档ID") @PathVariable Long docId) {
        
        boolean result = manufacturingService.deleteManufacturingDocument(id, docId);
        return Result.success(result);
    }

    @GetMapping("/export")
    @ApiOperation("导出制造订单数据")
    public Result<String> exportManufacturing(
            @ApiParam("订单编号") @RequestParam(required = false) String orderCode,
            @ApiParam("产品名称") @RequestParam(required = false) String productName,
            @ApiParam("制造状态") @RequestParam(required = false) String status,
            @ApiParam("导出格式") @RequestParam(defaultValue = "excel") String format) {
        
        String exportUrl = manufacturingService.exportManufacturing(orderCode, productName, status, format);
        return Result.success(exportUrl);
    }

    @PostMapping("/import")
    @ApiOperation("导入制造订单数据")
    public Result<Map<String, Object>> importManufacturing(
            @ApiParam("导入文件") @RequestParam("file") MultipartFile file) {
        
        Map<String, Object> result = manufacturingService.importManufacturing(file);
        return Result.success(result);
    }

    @GetMapping("/statistics")
    @ApiOperation("获取制造统计数据")
    public Result<Map<String, Object>> getManufacturingStatistics() {
        Map<String, Object> statistics = manufacturingService.getManufacturingStatistics();
        return Result.success(statistics);
    }

    @GetMapping("/options")
    @ApiOperation("获取制造订单选项列表")
    public Result<List<Map<String, Object>>> getManufacturingOptions(
            @ApiParam("产品系列") @RequestParam(required = false) String productSeries,
            @ApiParam("制造状态") @RequestParam(required = false) String status) {
        
        List<Map<String, Object>> options = manufacturingService.getManufacturingOptions(productSeries, status);
        return Result.success(options);
    }

    @GetMapping("/validate-order-code")
    @ApiOperation("验证订单编号唯一性")
    public Result<Boolean> validateOrderCode(
            @ApiParam("订单编号") @RequestParam String orderCode,
            @ApiParam("排除的ID") @RequestParam(required = false) Long excludeId) {
        
        boolean isUnique = manufacturingService.validateOrderCode(orderCode, excludeId);
        return Result.success(isUnique);
    }

    @GetMapping("/{id}/timeline")
    @ApiOperation("获取制造时间线")
    public Result<List<Map<String, Object>>> getManufacturingTimeline(
            @ApiParam("制造订单ID") @PathVariable Long id) {
        
        List<Map<String, Object>> timeline = manufacturingService.getManufacturingTimeline(id);
        return Result.success(timeline);
    }

    @PostMapping("/{id}/timeline")
    @ApiOperation("添加制造时间线事件")
    public Result<Void> addManufacturingTimelineEvent(
            @ApiParam("制造订单ID") @PathVariable Long id,
            @ApiParam("时间线事件") @RequestBody Map<String, Object> timelineEvent) {
        
        manufacturingService.addManufacturingTimelineEvent(id, timelineEvent);
        return Result.success();
    }

    @GetMapping("/{id}/alerts")
    @ApiOperation("获取制造预警信息")
    public Result<List<Map<String, Object>>> getManufacturingAlerts(
            @ApiParam("制造订单ID") @PathVariable Long id) {
        
        List<Map<String, Object>> alerts = manufacturingService.getManufacturingAlerts(id);
        return Result.success(alerts);
    }

    @PostMapping("/{id}/alerts")
    @ApiOperation("创建制造预警")
    public Result<Void> createManufacturingAlert(
            @ApiParam("制造订单ID") @PathVariable Long id,
            @ApiParam("预警信息") @RequestBody Map<String, Object> alertInfo) {
        
        manufacturingService.createManufacturingAlert(id, alertInfo);
        return Result.success();
    }

    @PutMapping("/{id}/alerts/{alertId}")
    @ApiOperation("处理制造预警")
    public Result<Void> handleManufacturingAlert(
            @ApiParam("制造订单ID") @PathVariable Long id,
            @ApiParam("预警ID") @PathVariable Long alertId,
            @ApiParam("处理信息") @RequestBody Map<String, Object> handleInfo) {
        
        manufacturingService.handleManufacturingAlert(id, alertId, handleInfo);
        return Result.success();
    }

    @GetMapping("/{id}/performance")
    @ApiOperation("获取制造性能指标")
    public Result<Map<String, Object>> getManufacturingPerformance(
            @ApiParam("制造订单ID") @PathVariable Long id) {
        
        Map<String, Object> performance = manufacturingService.getManufacturingPerformance(id);
        return Result.success(performance);
    }

    @GetMapping("/{id}/efficiency")
    @ApiOperation("获取制造效率分析")
    public Result<Map<String, Object>> getManufacturingEfficiency(
            @ApiParam("制造订单ID") @PathVariable Long id) {
        
        Map<String, Object> efficiency = manufacturingService.getManufacturingEfficiency(id);
        return Result.success(efficiency);
    }

    @GetMapping("/{id}/schedule")
    @ApiOperation("获取制造排程信息")
    public Result<Map<String, Object>> getManufacturingSchedule(
            @ApiParam("制造订单ID") @PathVariable Long id) {
        
        Map<String, Object> schedule = manufacturingService.getManufacturingSchedule(id);
        return Result.success(schedule);
    }

    @PutMapping("/{id}/schedule")
    @ApiOperation("更新制造排程")
    public Result<Void> updateManufacturingSchedule(
            @ApiParam("制造订单ID") @PathVariable Long id,
            @ApiParam("排程信息") @RequestBody Map<String, Object> scheduleInfo) {
        
        manufacturingService.updateManufacturingSchedule(id, scheduleInfo);
        return Result.success();
    }

    @GetMapping("/{id}/resources")
    @ApiOperation("获取制造资源配置")
    public Result<Map<String, Object>> getManufacturingResources(
            @ApiParam("制造订单ID") @PathVariable Long id) {
        
        Map<String, Object> resources = manufacturingService.getManufacturingResources(id);
        return Result.success(resources);
    }

    @PutMapping("/{id}/resources")
    @ApiOperation("更新制造资源配置")
    public Result<Void> updateManufacturingResources(
            @ApiParam("制造订单ID") @PathVariable Long id,
            @ApiParam("资源配置") @RequestBody Map<String, Object> resourcesInfo) {
        
        manufacturingService.updateManufacturingResources(id, resourcesInfo);
        return Result.success();
    }

    @PostMapping("/{id}/start")
    @ApiOperation("启动制造订单")
    public Result<Void> startManufacturing(
            @ApiParam("制造订单ID") @PathVariable Long id,
            @ApiParam("启动信息") @RequestBody Map<String, Object> startInfo) {
        
        manufacturingService.startManufacturing(id, startInfo);
        return Result.success();
    }

    @PostMapping("/{id}/pause")
    @ApiOperation("暂停制造订单")
    public Result<Void> pauseManufacturing(
            @ApiParam("制造订单ID") @PathVariable Long id,
            @ApiParam("暂停原因") @RequestParam String reason) {
        
        manufacturingService.pauseManufacturing(id, reason);
        return Result.success();
    }

    @PostMapping("/{id}/resume")
    @ApiOperation("恢复制造订单")
    public Result<Void> resumeManufacturing(
            @ApiParam("制造订单ID") @PathVariable Long id,
            @ApiParam("恢复信息") @RequestBody Map<String, Object> resumeInfo) {
        
        manufacturingService.resumeManufacturing(id, resumeInfo);
        return Result.success();
    }

    @PostMapping("/{id}/complete")
    @ApiOperation("完成制造订单")
    public Result<Void> completeManufacturing(
            @ApiParam("制造订单ID") @PathVariable Long id,
            @ApiParam("完成信息") @RequestBody Map<String, Object> completeInfo) {
        
        manufacturingService.completeManufacturing(id, completeInfo);
        return Result.success();
    }

    @PostMapping("/{id}/cancel")
    @ApiOperation("取消制造订单")
    public Result<Void> cancelManufacturing(
            @ApiParam("制造订单ID") @PathVariable Long id,
            @ApiParam("取消原因") @RequestParam String reason) {
        
        manufacturingService.cancelManufacturing(id, reason);
        return Result.success();
    }

    @GetMapping("/{id}/qrcode")
    @ApiOperation("生成制造订单二维码")
    public Result<String> generateManufacturingQRCode(
            @ApiParam("制造订单ID") @PathVariable Long id) {
        
        String qrCodeUrl = manufacturingService.generateManufacturingQRCode(id);
        return Result.success(qrCodeUrl);
    }

    @GetMapping("/{id}/report")
    @ApiOperation("生成制造报告")
    public Result<String> generateManufacturingReport(
            @ApiParam("制造订单ID") @PathVariable Long id,
            @ApiParam("报告类型") @RequestParam String reportType) {
        
        String reportUrl = manufacturingService.generateManufacturingReport(id, reportType);
        return Result.success(reportUrl);
    }

    @GetMapping("/dashboard")
    @ApiOperation("获取制造看板数据")
    public Result<Map<String, Object>> getManufacturingDashboard(
            @ApiParam("时间范围") @RequestParam(required = false) String timeRange) {
        
        Map<String, Object> dashboard = manufacturingService.getManufacturingDashboard(timeRange);
        return Result.success(dashboard);
    }

    @GetMapping("/capacity")
    @ApiOperation("获取产能分析")
    public Result<Map<String, Object>> getManufacturingCapacity(
            @ApiParam("开始日期") @RequestParam(required = false) String startDate,
            @ApiParam("结束日期") @RequestParam(required = false) String endDate) {
        
        Map<String, Object> capacity = manufacturingService.getManufacturingCapacity(startDate, endDate);
        return Result.success(capacity);
    }

    @GetMapping("/workload")
    @ApiOperation("获取工作负荷分析")
    public Result<Map<String, Object>> getManufacturingWorkload(
            @ApiParam("部门") @RequestParam(required = false) String department,
            @ApiParam("时间范围") @RequestParam(required = false) String timeRange) {
        
        Map<String, Object> workload = manufacturingService.getManufacturingWorkload(department, timeRange);
        return Result.success(workload);
    }

    @GetMapping("/trends")
    @ApiOperation("获取制造趋势分析")
    public Result<Map<String, Object>> getManufacturingTrends(
            @ApiParam("指标类型") @RequestParam String metricType,
            @ApiParam("时间范围") @RequestParam(required = false) String timeRange) {
        
        Map<String, Object> trends = manufacturingService.getManufacturingTrends(metricType, timeRange);
        return Result.success(trends);
    }
}