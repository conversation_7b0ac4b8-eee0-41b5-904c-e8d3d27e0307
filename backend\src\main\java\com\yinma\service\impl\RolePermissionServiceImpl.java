package com.yinma.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinma.entity.RolePermissionEntity;
import com.yinma.mapper.RolePermissionMapper;
import com.yinma.service.RolePermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 角色权限关联Service实现类
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RolePermissionServiceImpl extends ServiceImpl<RolePermissionMapper, RolePermissionEntity> implements RolePermissionService {

    private final RolePermissionMapper rolePermissionMapper;

    @Override
    public List<Long> selectPermissionIdsByRoleId(Long roleId) {
        return rolePermissionMapper.selectPermissionIdsByRoleId(roleId);
    }

    @Override
    public List<Long> selectRoleIdsByPermissionId(Long permissionId) {
        return rolePermissionMapper.selectRoleIdsByPermissionId(permissionId);
    }

    @Override
    public Boolean checkRolePermissionExists(Long roleId, Long permissionId) {
        return rolePermissionMapper.checkRolePermissionExists(roleId, permissionId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteRolePermissionsByRoleId(Long roleId) {
        int result = rolePermissionMapper.deleteRolePermissionsByRoleId(roleId);
        log.info("删除角色所有权限关联成功, roleId: {}, 删除数量: {}", roleId, result);
        return result >= 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteRolePermissionsByPermissionId(Long permissionId) {
        int result = rolePermissionMapper.deleteRolePermissionsByPermissionId(permissionId);
        log.info("删除权限所有角色关联成功, permissionId: {}, 删除数量: {}", permissionId, result);
        return result >= 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteRolePermission(Long roleId, Long permissionId) {
        int result = rolePermissionMapper.deleteRolePermission(roleId, permissionId);
        log.info("删除角色权限关联成功, roleId: {}, permissionId: {}", roleId, permissionId);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchInsertRolePermissions(Long roleId, List<Long> permissionIds) {
        if (CollectionUtils.isEmpty(permissionIds)) {
            return false;
        }
        
        List<RolePermissionEntity> entities = new ArrayList<>();
        for (Long permissionId : permissionIds) {
            // 检查关联是否已存在
            if (!checkRolePermissionExists(roleId, permissionId)) {
                RolePermissionEntity entity = new RolePermissionEntity();
                entity.setRoleId(roleId);
                entity.setPermissionId(permissionId);
                entity.setCreateTime(LocalDateTime.now());
                entities.add(entity);
            }
        }
        
        if (CollectionUtils.isEmpty(entities)) {
            return true;
        }
        
        int result = rolePermissionMapper.batchInsertRolePermissions(entities);
        log.info("批量插入角色权限关联成功, roleId: {}, permissionIds: {}, 插入数量: {}", roleId, permissionIds, result);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDeleteRolePermissions(Long roleId, List<Long> permissionIds) {
        if (CollectionUtils.isEmpty(permissionIds)) {
            return false;
        }
        
        int result = rolePermissionMapper.batchDeleteRolePermissions(roleId, permissionIds);
        log.info("批量删除角色权限关联成功, roleId: {}, permissionIds: {}, 删除数量: {}", roleId, permissionIds, result);
        return result >= 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchInsertPermissionRoles(Long permissionId, List<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return false;
        }
        
        List<RolePermissionEntity> entities = new ArrayList<>();
        for (Long roleId : roleIds) {
            // 检查关联是否已存在
            if (!checkRolePermissionExists(roleId, permissionId)) {
                RolePermissionEntity entity = new RolePermissionEntity();
                entity.setRoleId(roleId);
                entity.setPermissionId(permissionId);
                entity.setCreateTime(LocalDateTime.now());
                entities.add(entity);
            }
        }
        
        if (CollectionUtils.isEmpty(entities)) {
            return true;
        }
        
        int result = rolePermissionMapper.batchInsertRolePermissions(entities);
        log.info("批量插入权限角色关联成功, permissionId: {}, roleIds: {}, 插入数量: {}", permissionId, roleIds, result);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDeletePermissionRoles(Long permissionId, List<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return false;
        }
        
        int result = rolePermissionMapper.batchDeletePermissionRoles(permissionId, roleIds);
        log.info("批量删除权限角色关联成功, permissionId: {}, roleIds: {}, 删除数量: {}", permissionId, roleIds, result);
        return result >= 0;
    }

    @Override
    public List<Long> selectUserPermissionIds(Long userId) {
        return rolePermissionMapper.selectUserPermissionIds(userId);
    }

    @Override
    public Long selectRolePermissionCount() {
        return rolePermissionMapper.selectRolePermissionCount();
    }

    @Override
    public Long selectRoleCountByPermissionId(Long permissionId) {
        return rolePermissionMapper.selectRoleCountByPermissionId(permissionId);
    }

    @Override
    public Long selectPermissionCountByRoleId(Long roleId) {
        return rolePermissionMapper.selectPermissionCountByRoleId(roleId);
    }

    @Override
    public Long selectRolesWithoutPermissionCount() {
        return rolePermissionMapper.selectRolesWithoutPermissionCount();
    }

    @Override
    public Long selectPermissionsWithoutRoleCount() {
        return rolePermissionMapper.selectPermissionsWithoutRoleCount();
    }
}