{"ast": null, "code": "import { genComponentStyleHook } from '../../theme/internal';\nconst genWaveStyle = token => {\n  const {\n    componentCls,\n    colorPrimary\n  } = token;\n  return {\n    [componentCls]: {\n      position: 'absolute',\n      background: 'transparent',\n      pointerEvents: 'none',\n      boxSizing: 'border-box',\n      color: `var(--wave-color, ${colorPrimary})`,\n      boxShadow: `0 0 0 0 currentcolor`,\n      opacity: 0.2,\n      // =================== Motion ===================\n      '&.wave-motion-appear': {\n        transition: [`box-shadow 0.4s ${token.motionEaseOutCirc}`, `opacity 2s ${token.motionEaseOutCirc}`].join(','),\n        '&-active': {\n          boxShadow: `0 0 0 6px currentcolor`,\n          opacity: 0\n        },\n        '&.wave-quick': {\n          transition: [`box-shadow ${token.motionDurationSlow} ${token.motionEaseInOut}`, `opacity ${token.motionDurationSlow} ${token.motionEaseInOut}`].join(',')\n        }\n      }\n    }\n  };\n};\nexport default genComponentStyleHook('Wave', genWaveStyle);", "map": {"version": 3, "names": ["genComponentStyleHook", "genWaveStyle", "token", "componentCls", "colorPrimary", "position", "background", "pointerEvents", "boxSizing", "color", "boxShadow", "opacity", "transition", "motionEaseOutCirc", "join", "motionDurationSlow", "motionEaseInOut"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/antd/es/_util/wave/style.js"], "sourcesContent": ["import { genComponentStyleHook } from '../../theme/internal';\nconst genWaveStyle = token => {\n  const {\n    componentCls,\n    colorPrimary\n  } = token;\n  return {\n    [componentCls]: {\n      position: 'absolute',\n      background: 'transparent',\n      pointerEvents: 'none',\n      boxSizing: 'border-box',\n      color: `var(--wave-color, ${colorPrimary})`,\n      boxShadow: `0 0 0 0 currentcolor`,\n      opacity: 0.2,\n      // =================== Motion ===================\n      '&.wave-motion-appear': {\n        transition: [`box-shadow 0.4s ${token.motionEaseOutCirc}`, `opacity 2s ${token.motionEaseOutCirc}`].join(','),\n        '&-active': {\n          boxShadow: `0 0 0 6px currentcolor`,\n          opacity: 0\n        },\n        '&.wave-quick': {\n          transition: [`box-shadow ${token.motionDurationSlow} ${token.motionEaseInOut}`, `opacity ${token.motionDurationSlow} ${token.motionEaseInOut}`].join(',')\n        }\n      }\n    }\n  };\n};\nexport default genComponentStyleHook('Wave', genWaveStyle);"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,sBAAsB;AAC5D,MAAMC,YAAY,GAAGC,KAAK,IAAI;EAC5B,MAAM;IACJC,YAAY;IACZC;EACF,CAAC,GAAGF,KAAK;EACT,OAAO;IACL,CAACC,YAAY,GAAG;MACdE,QAAQ,EAAE,UAAU;MACpBC,UAAU,EAAE,aAAa;MACzBC,aAAa,EAAE,MAAM;MACrBC,SAAS,EAAE,YAAY;MACvBC,KAAK,EAAE,qBAAqBL,YAAY,GAAG;MAC3CM,SAAS,EAAE,sBAAsB;MACjCC,OAAO,EAAE,GAAG;MACZ;MACA,sBAAsB,EAAE;QACtBC,UAAU,EAAE,CAAC,mBAAmBV,KAAK,CAACW,iBAAiB,EAAE,EAAE,cAAcX,KAAK,CAACW,iBAAiB,EAAE,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;QAC7G,UAAU,EAAE;UACVJ,SAAS,EAAE,wBAAwB;UACnCC,OAAO,EAAE;QACX,CAAC;QACD,cAAc,EAAE;UACdC,UAAU,EAAE,CAAC,cAAcV,KAAK,CAACa,kBAAkB,IAAIb,KAAK,CAACc,eAAe,EAAE,EAAE,WAAWd,KAAK,CAACa,kBAAkB,IAAIb,KAAK,CAACc,eAAe,EAAE,CAAC,CAACF,IAAI,CAAC,GAAG;QAC1J;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAed,qBAAqB,CAAC,MAAM,EAAEC,YAAY,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}