{"ast": null, "code": "import { string2rbg } from './color';\n/**\n * 返回一个线性插值器，接受数字\n * @param a 任意值\n * @param b 任意值\n * @returns 线性插值器\n */\nexport const createInterpolateNumber = (a, b) => {\n  return t => a * (1 - t) + b * t;\n};\nexport const createInterpolateColor = (a, b) => {\n  const c1 = string2rbg(a);\n  const c2 = string2rbg(b);\n  if (c1 === null || c2 === null) return c1 ? () => a : () => b;\n  return t => {\n    const values = new Array(4);\n    for (let i = 0; i < 4; i += 1) {\n      const from = c1[i];\n      const to = c2[i];\n      values[i] = from * (1 - t) + to * t;\n    }\n    const [r, g, b, a] = values;\n    return `rgba(${Math.round(r)}, ${Math.round(g)}, ${Math.round(b)}, ${a})`;\n  };\n};\n/**\n * 返回一个线性插值器，接受数字和颜色\n * @param a 任意值\n * @param b 任意值\n * @returns 线性插值器\n */\nexport const createInterpolateValue = (a, b) => {\n  if (typeof a === 'number' && typeof b === 'number') return createInterpolateNumber(a, b);\n  if (typeof a === 'string' && typeof b === 'string') return createInterpolateColor(a, b);\n  return () => a;\n};\n/**\n * 返回一个 round 线性差值器，对输出值进行四舍五入\n * @param a 任意值\n * @param b 任意值\n * @returns 线性插值器\n */\nexport const createInterpolateRound = (a, b) => {\n  const interpolateNumber = createInterpolateNumber(a, b);\n  return t => Math.round(interpolateNumber(t));\n};", "map": {"version": 3, "names": ["string2rbg", "createInterpolateNumber", "a", "b", "t", "createInterpolateColor", "c1", "c2", "values", "Array", "i", "from", "to", "r", "g", "Math", "round", "createInterpolateValue", "createInterpolateRound", "interpolateNumber"], "sources": ["utils/interpolate.ts"], "sourcesContent": [null], "mappings": "AACA,SAASA,UAAU,QAAQ,SAAS;AAEpC;;;;;;AAMA,OAAO,MAAMC,uBAAuB,GAAwBA,CAACC,CAAC,EAAEC,CAAC,KAAI;EACnE,OAAQC,CAAC,IAAKF,CAAC,IAAI,CAAC,GAAGE,CAAC,CAAC,GAAGD,CAAC,GAAGC,CAAC;AACnC,CAAC;AAED,OAAO,MAAMC,sBAAsB,GAAwBA,CAACH,CAAC,EAAEC,CAAC,KAAI;EAClE,MAAMG,EAAE,GAAGN,UAAU,CAACE,CAAC,CAAC;EACxB,MAAMK,EAAE,GAAGP,UAAU,CAACG,CAAC,CAAC;EACxB,IAAIG,EAAE,KAAK,IAAI,IAAIC,EAAE,KAAK,IAAI,EAAE,OAAOD,EAAE,GAAG,MAAMJ,CAAC,GAAG,MAAMC,CAAC;EAC7D,OAAQC,CAAC,IAAI;IACX,MAAMI,MAAM,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC;IAC3B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;MAC7B,MAAMC,IAAI,GAAGL,EAAE,CAACI,CAAC,CAAC;MAClB,MAAME,EAAE,GAAGL,EAAE,CAACG,CAAC,CAAC;MAChBF,MAAM,CAACE,CAAC,CAAC,GAAGC,IAAI,IAAI,CAAC,GAAGP,CAAC,CAAC,GAAGQ,EAAE,GAAGR,CAAC;;IAErC,MAAM,CAACS,CAAC,EAAEC,CAAC,EAAEX,CAAC,EAAED,CAAC,CAAC,GAAGM,MAAkB;IACvC,OAAO,QAAQO,IAAI,CAACC,KAAK,CAACH,CAAC,CAAC,KAAKE,IAAI,CAACC,KAAK,CAACF,CAAC,CAAC,KAAKC,IAAI,CAACC,KAAK,CAACb,CAAC,CAAC,KAAKD,CAAC,GAAG;EAC3E,CAAC;AACH,CAAC;AAED;;;;;;AAMA,OAAO,MAAMe,sBAAsB,GAAiCA,CAACf,CAAC,EAAEC,CAAC,KAAI;EAC3E,IAAI,OAAOD,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE,OAAOF,uBAAuB,CAACC,CAAC,EAAEC,CAAC,CAAC;EACxF,IAAI,OAAOD,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE,OAAOE,sBAAsB,CAACH,CAAC,EAAEC,CAAC,CAAC;EACvF,OAAO,MAAMD,CAAC;AAChB,CAAC;AAED;;;;;;AAMA,OAAO,MAAMgB,sBAAsB,GAAwBA,CAAChB,CAAC,EAAEC,CAAC,KAAI;EAClE,MAAMgB,iBAAiB,GAAGlB,uBAAuB,CAACC,CAAC,EAAEC,CAAC,CAAC;EACvD,OAAQC,CAAC,IAAKW,IAAI,CAACC,KAAK,CAACG,iBAAiB,CAACf,CAAC,CAAC,CAAC;AAChD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}