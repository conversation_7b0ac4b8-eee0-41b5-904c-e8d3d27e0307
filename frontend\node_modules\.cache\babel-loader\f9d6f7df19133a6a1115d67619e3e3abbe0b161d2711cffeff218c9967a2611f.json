{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport * as React from 'react';\nimport copy from 'copy-to-clipboard';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport toList from '../../_util/toList';\nconst useCopyClick = ({\n  copyConfig,\n  children\n}) => {\n  const [copied, setCopied] = React.useState(false);\n  const [copyLoading, setCopyLoading] = React.useState(false);\n  const copyIdRef = React.useRef(null);\n  const cleanCopyId = () => {\n    if (copyIdRef.current) {\n      clearTimeout(copyIdRef.current);\n    }\n  };\n  const copyOptions = {};\n  if (copyConfig.format) {\n    copyOptions.format = copyConfig.format;\n  }\n  React.useEffect(() => cleanCopyId, []);\n  // Keep copy action up to date\n  const onClick = useEvent(e => __awaiter(void 0, void 0, void 0, function* () {\n    var _a;\n    e === null || e === void 0 ? void 0 : e.preventDefault();\n    e === null || e === void 0 ? void 0 : e.stopPropagation();\n    setCopyLoading(true);\n    try {\n      const text = typeof copyConfig.text === 'function' ? yield copyConfig.text() : copyConfig.text;\n      copy(text || toList(children, true).join('') || '', copyOptions);\n      setCopyLoading(false);\n      setCopied(true);\n      // Trigger tips update\n      cleanCopyId();\n      copyIdRef.current = setTimeout(() => {\n        setCopied(false);\n      }, 3000);\n      (_a = copyConfig.onCopy) === null || _a === void 0 ? void 0 : _a.call(copyConfig, e);\n    } catch (error) {\n      setCopyLoading(false);\n      throw error;\n    }\n  }));\n  return {\n    copied,\n    copyLoading,\n    onClick\n  };\n};\nexport default useCopyClick;", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "React", "copy", "useEvent", "toList", "useCopyClick", "copyConfig", "children", "copied", "setCopied", "useState", "copyLoading", "setCopyLoading", "copyIdRef", "useRef", "cleanCopyId", "current", "clearTimeout", "copyOptions", "format", "useEffect", "onClick", "_a", "preventDefault", "stopPropagation", "text", "join", "setTimeout", "onCopy", "call", "error"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/antd/es/typography/hooks/useCopyClick.js"], "sourcesContent": ["var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport * as React from 'react';\nimport copy from 'copy-to-clipboard';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport toList from '../../_util/toList';\nconst useCopyClick = ({\n  copyConfig,\n  children\n}) => {\n  const [copied, setCopied] = React.useState(false);\n  const [copyLoading, setCopyLoading] = React.useState(false);\n  const copyIdRef = React.useRef(null);\n  const cleanCopyId = () => {\n    if (copyIdRef.current) {\n      clearTimeout(copyIdRef.current);\n    }\n  };\n  const copyOptions = {};\n  if (copyConfig.format) {\n    copyOptions.format = copyConfig.format;\n  }\n  React.useEffect(() => cleanCopyId, []);\n  // Keep copy action up to date\n  const onClick = useEvent(e => __awaiter(void 0, void 0, void 0, function* () {\n    var _a;\n    e === null || e === void 0 ? void 0 : e.preventDefault();\n    e === null || e === void 0 ? void 0 : e.stopPropagation();\n    setCopyLoading(true);\n    try {\n      const text = typeof copyConfig.text === 'function' ? yield copyConfig.text() : copyConfig.text;\n      copy(text || toList(children, true).join('') || '', copyOptions);\n      setCopyLoading(false);\n      setCopied(true);\n      // Trigger tips update\n      cleanCopyId();\n      copyIdRef.current = setTimeout(() => {\n        setCopied(false);\n      }, 3000);\n      (_a = copyConfig.onCopy) === null || _a === void 0 ? void 0 : _a.call(copyConfig, e);\n    } catch (error) {\n      setCopyLoading(false);\n      throw error;\n    }\n  }));\n  return {\n    copied,\n    copyLoading,\n    onClick\n  };\n};\nexport default useCopyClick;"], "mappings": "AAAA,IAAIA,SAAS,GAAG,IAAI,IAAI,IAAI,CAACA,SAAS,IAAI,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IACpB,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAC3DA,OAAO,CAACD,KAAK,CAAC;IAChB,CAAC,CAAC;EACJ;EACA,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACzD,SAASC,SAASA,CAACJ,KAAK,EAAE;MACxB,IAAI;QACFK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAC7B,CAAC,CAAC,OAAOO,CAAC,EAAE;QACVJ,MAAM,CAACI,CAAC,CAAC;MACX;IACF;IACA,SAASC,QAAQA,CAACR,KAAK,EAAE;MACvB,IAAI;QACFK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MACjC,CAAC,CAAC,OAAOO,CAAC,EAAE;QACVJ,MAAM,CAACI,CAAC,CAAC;MACX;IACF;IACA,SAASF,IAAIA,CAACI,MAAM,EAAE;MACpBA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IACrF;IACAH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACvE,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,KAAKO,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,MAAM,MAAM,oBAAoB;AACvC,MAAMC,YAAY,GAAGA,CAAC;EACpBC,UAAU;EACVC;AACF,CAAC,KAAK;EACJ,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGR,KAAK,CAACS,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGX,KAAK,CAACS,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAMG,SAAS,GAAGZ,KAAK,CAACa,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIF,SAAS,CAACG,OAAO,EAAE;MACrBC,YAAY,CAACJ,SAAS,CAACG,OAAO,CAAC;IACjC;EACF,CAAC;EACD,MAAME,WAAW,GAAG,CAAC,CAAC;EACtB,IAAIZ,UAAU,CAACa,MAAM,EAAE;IACrBD,WAAW,CAACC,MAAM,GAAGb,UAAU,CAACa,MAAM;EACxC;EACAlB,KAAK,CAACmB,SAAS,CAAC,MAAML,WAAW,EAAE,EAAE,CAAC;EACtC;EACA,MAAMM,OAAO,GAAGlB,QAAQ,CAACR,CAAC,IAAIb,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;IAC3E,IAAIwC,EAAE;IACN3B,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAAC4B,cAAc,CAAC,CAAC;IACxD5B,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAAC6B,eAAe,CAAC,CAAC;IACzDZ,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACF,MAAMa,IAAI,GAAG,OAAOnB,UAAU,CAACmB,IAAI,KAAK,UAAU,GAAG,MAAMnB,UAAU,CAACmB,IAAI,CAAC,CAAC,GAAGnB,UAAU,CAACmB,IAAI;MAC9FvB,IAAI,CAACuB,IAAI,IAAIrB,MAAM,CAACG,QAAQ,EAAE,IAAI,CAAC,CAACmB,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,EAAER,WAAW,CAAC;MAChEN,cAAc,CAAC,KAAK,CAAC;MACrBH,SAAS,CAAC,IAAI,CAAC;MACf;MACAM,WAAW,CAAC,CAAC;MACbF,SAAS,CAACG,OAAO,GAAGW,UAAU,CAAC,MAAM;QACnClB,SAAS,CAAC,KAAK,CAAC;MAClB,CAAC,EAAE,IAAI,CAAC;MACR,CAACa,EAAE,GAAGhB,UAAU,CAACsB,MAAM,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACO,IAAI,CAACvB,UAAU,EAAEX,CAAC,CAAC;IACtF,CAAC,CAAC,OAAOmC,KAAK,EAAE;MACdlB,cAAc,CAAC,KAAK,CAAC;MACrB,MAAMkB,KAAK;IACb;EACF,CAAC,CAAC,CAAC;EACH,OAAO;IACLtB,MAAM;IACNG,WAAW;IACXU;EACF,CAAC;AACH,CAAC;AACD,eAAehB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}