/**
 * 系统监控中间件
 * 实现性能监控、健康检查、指标收集等功能
 */

const os = require('os');
const fs = require('fs');
const path = require('path');
const EventEmitter = require('events');
const monitoringConfig = require('../config/monitoring');
const { createLogger } = require('../config/logging');

class SystemMonitor extends EventEmitter {
  constructor() {
    super();
    this.logger = createLogger('monitoring');
    this.metrics = new Map();
    this.alerts = new Map();
    this.isRunning = false;
    this.collectInterval = null;
    this.healthCheckInterval = null;
  }

  // 启动监控
  start() {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;
    this.logger.info('System monitoring started');

    // 启动指标收集
    if (monitoringConfig.monitoring.enabled) {
      this.startMetricsCollection();
    }

    // 启动健康检查
    if (monitoringConfig.healthCheck.interval > 0) {
      this.startHealthCheck();
    }

    this.emit('started');
  }

  // 停止监控
  stop() {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    
    if (this.collectInterval) {
      clearInterval(this.collectInterval);
      this.collectInterval = null;
    }

    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    this.logger.info('System monitoring stopped');
    this.emit('stopped');
  }

  // 启动指标收集
  startMetricsCollection() {
    const interval = monitoringConfig.monitoring.collectInterval * 1000;
    
    this.collectInterval = setInterval(() => {
      this.collectMetrics();
    }, interval);

    // 立即收集一次
    this.collectMetrics();
  }

  // 收集系统指标
  async collectMetrics() {
    try {
      const timestamp = Date.now();
      const metrics = {};

      // 收集系统指标
      if (monitoringConfig.monitoring.metrics.system.cpu) {
        metrics.cpu = await this.getCpuUsage();
      }

      if (monitoringConfig.monitoring.metrics.system.memory) {
        metrics.memory = this.getMemoryUsage();
      }

      if (monitoringConfig.monitoring.metrics.system.disk) {
        metrics.disk = await this.getDiskUsage();
      }

      if (monitoringConfig.monitoring.metrics.system.network) {
        metrics.network = this.getNetworkUsage();
      }

      // 收集应用指标
      if (monitoringConfig.monitoring.metrics.application.responseTime) {
        metrics.responseTime = this.getAverageResponseTime();
      }

      if (monitoringConfig.monitoring.metrics.application.throughput) {
        metrics.throughput = this.getThroughput();
      }

      if (monitoringConfig.monitoring.metrics.application.errorRate) {
        metrics.errorRate = this.getErrorRate();
      }

      if (monitoringConfig.monitoring.metrics.application.activeUsers) {
        metrics.activeUsers = this.getActiveUsers();
      }

      // 存储指标
      this.storeMetrics(timestamp, metrics);

      // 检查告警
      this.checkAlerts(metrics);

      this.emit('metricsCollected', { timestamp, metrics });
    } catch (error) {
      this.logger.error('Failed to collect metrics', { error: error.message });
    }
  }

  // 获取CPU使用率
  async getCpuUsage() {
    return new Promise((resolve) => {
      const startMeasure = this.cpuAverage();
      
      setTimeout(() => {
        const endMeasure = this.cpuAverage();
        const idleDifference = endMeasure.idle - startMeasure.idle;
        const totalDifference = endMeasure.total - startMeasure.total;
        const percentageCPU = 100 - ~~(100 * idleDifference / totalDifference);
        resolve(percentageCPU);
      }, 1000);
    });
  }

  // CPU平均值计算
  cpuAverage() {
    const cpus = os.cpus();
    let user = 0, nice = 0, sys = 0, idle = 0, irq = 0;
    
    for (let cpu of cpus) {
      user += cpu.times.user;
      nice += cpu.times.nice;
      sys += cpu.times.sys;
      idle += cpu.times.idle;
      irq += cpu.times.irq;
    }
    
    const total = user + nice + sys + idle + irq;
    return { idle, total };
  }

  // 获取内存使用率
  getMemoryUsage() {
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    const usagePercentage = (usedMemory / totalMemory) * 100;

    return {
      total: totalMemory,
      used: usedMemory,
      free: freeMemory,
      percentage: usagePercentage
    };
  }

  // 获取磁盘使用率
  async getDiskUsage() {
    try {
      const stats = await fs.promises.statfs || fs.promises.stat;
      const diskPath = process.cwd();
      
      // 简化的磁盘使用率计算
      const diskStats = await fs.promises.stat(diskPath);
      
      return {
        path: diskPath,
        percentage: 0 // 需要根据实际情况实现
      };
    } catch (error) {
      return { percentage: 0, error: error.message };
    }
  }

  // 获取网络使用情况
  getNetworkUsage() {
    const networkInterfaces = os.networkInterfaces();
    const interfaces = [];

    for (const [name, nets] of Object.entries(networkInterfaces)) {
      for (const net of nets) {
        if (net.family === 'IPv4' && !net.internal) {
          interfaces.push({
            name,
            address: net.address,
            mac: net.mac
          });
        }
      }
    }

    return { interfaces };
  }

  // 获取平均响应时间
  getAverageResponseTime() {
    const responseTimes = this.metrics.get('responseTimes') || [];
    if (responseTimes.length === 0) return 0;
    
    const sum = responseTimes.reduce((a, b) => a + b, 0);
    return sum / responseTimes.length;
  }

  // 获取吞吐量
  getThroughput() {
    const requests = this.metrics.get('requests') || [];
    const now = Date.now();
    const oneMinuteAgo = now - 60000;
    
    const recentRequests = requests.filter(timestamp => timestamp > oneMinuteAgo);
    return recentRequests.length;
  }

  // 获取错误率
  getErrorRate() {
    const totalRequests = this.metrics.get('totalRequests') || 0;
    const errorRequests = this.metrics.get('errorRequests') || 0;
    
    if (totalRequests === 0) return 0;
    return (errorRequests / totalRequests) * 100;
  }

  // 获取活跃用户数
  getActiveUsers() {
    const activeUsers = this.metrics.get('activeUsers') || new Set();
    return activeUsers.size;
  }

  // 存储指标
  storeMetrics(timestamp, metrics) {
    const key = `metrics_${timestamp}`;
    this.metrics.set(key, metrics);

    // 清理过期数据
    const retentionTime = monitoringConfig.monitoring.retentionDays * 24 * 60 * 60 * 1000;
    const cutoffTime = timestamp - retentionTime;
    
    for (const [key, value] of this.metrics.entries()) {
      if (key.startsWith('metrics_')) {
        const metricTime = parseInt(key.split('_')[1]);
        if (metricTime < cutoffTime) {
          this.metrics.delete(key);
        }
      }
    }
  }

  // 检查告警
  checkAlerts(metrics) {
    const rules = monitoringConfig.alerting.rules;
    
    for (const [ruleName, rule] of Object.entries(rules)) {
      this.checkAlert(ruleName, rule, metrics);
    }
  }

  // 检查单个告警规则
  checkAlert(ruleName, rule, metrics) {
    let value;
    
    switch (ruleName) {
      case 'cpuUsage':
        value = metrics.cpu;
        break;
      case 'memoryUsage':
        value = metrics.memory?.percentage;
        break;
      case 'diskUsage':
        value = metrics.disk?.percentage;
        break;
      case 'responseTime':
        value = metrics.responseTime;
        break;
      case 'errorRate':
        value = metrics.errorRate;
        break;
      default:
        return;
    }

    if (value === undefined) return;

    const alertKey = `alert_${ruleName}`;
    const existingAlert = this.alerts.get(alertKey);
    
    if (value >= rule.threshold) {
      if (!existingAlert) {
        // 新告警
        const alert = {
          rule: ruleName,
          value,
          threshold: rule.threshold,
          severity: rule.severity,
          startTime: Date.now(),
          notified: false
        };
        
        this.alerts.set(alertKey, alert);
        this.logger.warn(`Alert triggered: ${ruleName}`, alert);
      } else {
        // 更新现有告警
        existingAlert.value = value;
        const duration = Date.now() - existingAlert.startTime;
        
        if (duration >= rule.duration * 1000 && !existingAlert.notified) {
          this.sendAlert(existingAlert);
          existingAlert.notified = true;
        }
      }
    } else {
      if (existingAlert) {
        // 告警恢复
        this.logger.info(`Alert recovered: ${ruleName}`, {
          rule: ruleName,
          value,
          duration: Date.now() - existingAlert.startTime
        });
        
        this.alerts.delete(alertKey);
      }
    }
  }

  // 发送告警
  async sendAlert(alert) {
    try {
      this.logger.warn('Sending alert notification', alert);
      
      // 发送邮件告警
      if (monitoringConfig.alerting.notifications.email.enabled) {
        await this.sendEmailAlert(alert);
      }
      
      // 发送Webhook告警
      if (monitoringConfig.alerting.notifications.webhook.enabled) {
        await this.sendWebhookAlert(alert);
      }
      
      // 发送短信告警
      if (monitoringConfig.alerting.notifications.sms.enabled) {
        await this.sendSmsAlert(alert);
      }
      
      this.emit('alertSent', alert);
    } catch (error) {
      this.logger.error('Failed to send alert', { alert, error: error.message });
    }
  }

  // 发送邮件告警
  async sendEmailAlert(alert) {
    // 邮件发送实现
    this.logger.info('Email alert sent', { rule: alert.rule });
  }

  // 发送Webhook告警
  async sendWebhookAlert(alert) {
    // Webhook发送实现
    this.logger.info('Webhook alert sent', { rule: alert.rule });
  }

  // 发送短信告警
  async sendSmsAlert(alert) {
    // 短信发送实现
    this.logger.info('SMS alert sent', { rule: alert.rule });
  }

  // 启动健康检查
  startHealthCheck() {
    const interval = monitoringConfig.healthCheck.interval * 1000;
    
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, interval);

    // 立即执行一次
    this.performHealthCheck();
  }

  // 执行健康检查
  async performHealthCheck() {
    try {
      const checks = monitoringConfig.healthCheck.checks;
      const results = {};
      
      for (const [checkName, checkConfig] of Object.entries(checks)) {
        if (checkConfig.enabled) {
          results[checkName] = await this.runHealthCheck(checkName, checkConfig);
        }
      }
      
      this.emit('healthCheckCompleted', results);
      this.logger.debug('Health check completed', results);
    } catch (error) {
      this.logger.error('Health check failed', { error: error.message });
    }
  }

  // 运行单个健康检查
  async runHealthCheck(checkName, checkConfig) {
    const startTime = Date.now();
    
    try {
      let result;
      
      switch (checkName) {
        case 'database':
          result = await this.checkDatabase(checkConfig);
          break;
        case 'redis':
          result = await this.checkRedis(checkConfig);
          break;
        case 'fileSystem':
          result = await this.checkFileSystem(checkConfig);
          break;
        case 'externalApi':
          result = await this.checkExternalApi(checkConfig);
          break;
        default:
          result = { status: 'unknown', message: 'Unknown check type' };
      }
      
      const duration = Date.now() - startTime;
      return { ...result, duration, timestamp: startTime };
    } catch (error) {
      const duration = Date.now() - startTime;
      return {
        status: 'error',
        message: error.message,
        duration,
        timestamp: startTime
      };
    }
  }

  // 数据库健康检查
  async checkDatabase(config) {
    // 数据库连接检查实现
    return { status: 'healthy', message: 'Database connection OK' };
  }

  // Redis健康检查
  async checkRedis(config) {
    // Redis连接检查实现
    return { status: 'healthy', message: 'Redis connection OK' };
  }

  // 文件系统健康检查
  async checkFileSystem(config) {
    try {
      await fs.promises.access(config.path, fs.constants.F_OK);
      return { status: 'healthy', message: 'File system accessible' };
    } catch (error) {
      return { status: 'unhealthy', message: 'File system not accessible' };
    }
  }

  // 外部API健康检查
  async checkExternalApi(config) {
    // 外部API检查实现
    return { status: 'healthy', message: 'External API accessible' };
  }

  // 记录请求指标
  recordRequest(req, res, responseTime) {
    const now = Date.now();
    
    // 记录响应时间
    const responseTimes = this.metrics.get('responseTimes') || [];
    responseTimes.push(responseTime);
    if (responseTimes.length > 1000) {
      responseTimes.shift();
    }
    this.metrics.set('responseTimes', responseTimes);
    
    // 记录请求时间戳
    const requests = this.metrics.get('requests') || [];
    requests.push(now);
    if (requests.length > 10000) {
      requests.shift();
    }
    this.metrics.set('requests', requests);
    
    // 记录总请求数
    const totalRequests = this.metrics.get('totalRequests') || 0;
    this.metrics.set('totalRequests', totalRequests + 1);
    
    // 记录错误请求数
    if (res.statusCode >= 400) {
      const errorRequests = this.metrics.get('errorRequests') || 0;
      this.metrics.set('errorRequests', errorRequests + 1);
    }
    
    // 记录活跃用户
    if (req.user?.id) {
      const activeUsers = this.metrics.get('activeUsers') || new Set();
      activeUsers.add(req.user.id);
      this.metrics.set('activeUsers', activeUsers);
    }
  }

  // 获取监控数据
  getMetrics() {
    const result = {};
    
    for (const [key, value] of this.metrics.entries()) {
      if (key.startsWith('metrics_')) {
        const timestamp = parseInt(key.split('_')[1]);
        result[timestamp] = value;
      }
    }
    
    return result;
  }

  // 获取告警状态
  getAlerts() {
    const result = [];
    
    for (const [key, alert] of this.alerts.entries()) {
      result.push(alert);
    }
    
    return result;
  }
}

// 创建全局监控实例
const systemMonitor = new SystemMonitor();

// 监控中间件
function monitoringMiddleware() {
  return (req, res, next) => {
    const startTime = Date.now();
    
    // 监听响应结束
    res.on('finish', () => {
      const responseTime = Date.now() - startTime;
      systemMonitor.recordRequest(req, res, responseTime);
    });
    
    next();
  };
}

// 健康检查端点
function healthCheckEndpoint() {
  return async (req, res) => {
    try {
      const checks = await systemMonitor.performHealthCheck();
      const isHealthy = Object.values(checks).every(check => check.status === 'healthy');
      
      res.status(isHealthy ? 200 : 503).json({
        status: isHealthy ? 'healthy' : 'unhealthy',
        timestamp: Date.now(),
        checks
      });
    } catch (error) {
      res.status(500).json({
        status: 'error',
        message: error.message,
        timestamp: Date.now()
      });
    }
  };
}

// 指标端点
function metricsEndpoint() {
  return (req, res) => {
    try {
      const metrics = systemMonitor.getMetrics();
      const alerts = systemMonitor.getAlerts();
      
      res.json({
        metrics,
        alerts,
        timestamp: Date.now()
      });
    } catch (error) {
      res.status(500).json({
        error: error.message,
        timestamp: Date.now()
      });
    }
  };
}

module.exports = {
  SystemMonitor,
  systemMonitor,
  monitoringMiddleware,
  healthCheckEndpoint,
  metricsEndpoint
};