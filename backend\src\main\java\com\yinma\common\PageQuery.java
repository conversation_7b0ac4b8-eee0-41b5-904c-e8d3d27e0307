package com.yinma.common;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 分页查询基础类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Accessors(chain = true)
public class PageQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 当前页码
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码最小值为1")
    private Integer current = 1;

    /**
     * 每页大小
     */
    @NotNull(message = "每页大小不能为空")
    @Min(value = 1, message = "每页大小最小值为1")
    @Max(value = 1000, message = "每页大小最大值为1000")
    private Integer size = 10;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序方向：ASC-升序，DESC-降序
     */
    private String orderDirection = "DESC";

    /**
     * 搜索关键词
     */
    private String keyword;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 获取偏移量
     */
    public Integer getOffset() {
        return (current - 1) * size;
    }

    /**
     * 获取排序SQL
     */
    public String getOrderSql() {
        if (orderBy == null || orderBy.trim().isEmpty()) {
            return "";
        }
        return String.format("ORDER BY %s %s", orderBy, orderDirection);
    }

    /**
     * 是否升序
     */
    public boolean isAsc() {
        return "ASC".equalsIgnoreCase(orderDirection);
    }

    /**
     * 是否降序
     */
    public boolean isDesc() {
        return "DESC".equalsIgnoreCase(orderDirection);
    }
}