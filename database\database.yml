# 西安银马实业数字化管理系统数据库配置
# 支持多环境配置：开发、测试、生产

# 开发环境配置
development:
  database: yinma_dev
  host: localhost
  port: 5432
  username: postgres
  password: postgres
  pool_size: 10
  timeout: 5000
  encoding: utf8
  schema: public
  ssl_mode: disable
  connection_params:
    application_name: "YinMa-Dev"
    connect_timeout: 10
    statement_timeout: 30000
    idle_in_transaction_session_timeout: 60000

# 测试环境配置
test:
  database: yinma_test
  host: localhost
  port: 5432
  username: postgres
  password: postgres
  pool_size: 5
  timeout: 5000
  encoding: utf8
  schema: public
  ssl_mode: disable
  connection_params:
    application_name: "YinMa-Test"
    connect_timeout: 10
    statement_timeout: 30000

# 生产环境配置
production:
  database: yinma_prod
  host: ${DB_HOST:localhost}
  port: ${DB_PORT:5432}
  username: ${DB_USERNAME:postgres}
  password: ${DB_PASSWORD:postgres}
  pool_size: ${DB_POOL_SIZE:20}
  timeout: ${DB_TIMEOUT:5000}
  encoding: utf8
  schema: public
  ssl_mode: ${DB_SSL_MODE:require}
  connection_params:
    application_name: "YinMa-Prod"
    connect_timeout: 10
    statement_timeout: 60000
    idle_in_transaction_session_timeout: 120000

# 数据库连接池配置
pool_config:
  # 连接池最小连接数
  minimum_idle: 5
  # 连接池最大连接数
  maximum_pool_size: 20
  # 连接超时时间（毫秒）
  connection_timeout: 30000
  # 空闲连接超时时间（毫秒）
  idle_timeout: 600000
  # 连接最大生命周期（毫秒）
  max_lifetime: 1800000
  # 连接泄漏检测阈值（毫秒）
  leak_detection_threshold: 60000

# 数据库迁移配置
migration:
  # 迁移脚本目录
  locations:
    - classpath:db/migration
    - filesystem:database/migration
  # 基线版本
  baseline_version: 1.0.0
  # 基线描述
  baseline_description: "Initial version"
  # 是否在迁移时验证
  validate_on_migrate: true
  # 是否清理验证错误
  clean_on_validation_error: false
  # 编码
  encoding: UTF-8
  # SQL迁移前缀
  sql_migration_prefix: V
  # SQL迁移分隔符
  sql_migration_separator: __
  # SQL迁移后缀
  sql_migration_suffixes:
    - .sql

# 监控配置
monitoring:
  # 是否启用SQL日志
  enable_sql_logging: true
  # 慢查询阈值（毫秒）
  slow_query_threshold: 2000
  # 是否启用连接池监控
  enable_pool_monitoring: true
  # 监控采样间隔（秒）
  monitoring_interval: 30

# 备份配置
backup:
  # 备份目录
  backup_dir: /data/backup/yinma
  # 备份保留天数
  retention_days: 30
  # 备份时间（cron表达式）
  schedule: "0 2 * * *"
  # 是否压缩备份文件
  compress: true
  # 备份文件命名格式
  filename_format: "yinma_backup_%Y%m%d_%H%M%S.sql"

# 性能优化配置
performance:
  # 是否启用查询缓存
  enable_query_cache: true
  # 查询缓存大小（MB）
  query_cache_size: 256
  # 是否启用预编译语句缓存
  enable_prepared_statement_cache: true
  # 预编译语句缓存大小
  prepared_statement_cache_size: 256
  # 批处理大小
  batch_size: 1000
  # 是否启用二级缓存
  enable_second_level_cache: true