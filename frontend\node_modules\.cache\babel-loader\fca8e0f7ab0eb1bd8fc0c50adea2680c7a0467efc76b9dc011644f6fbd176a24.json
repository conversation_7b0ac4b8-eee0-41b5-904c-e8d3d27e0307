{"ast": null, "code": "import { __read } from \"tslib\";\nfunction inside(x1, x2, y1, y2, xk, yk) {\n  return (x1 === x2 || Math.min(x1, x2) <= xk && xk <= Math.max(x1, x2)) && (y1 === y2 || Math.min(y1, y2) <= yk && yk <= Math.max(y1, y2));\n}\nfunction update(ans, x, y) {\n  var out = ans;\n  if (!ans.length || x < ans[0] || x === ans[0] && y < ans[1]) {\n    out[0] = x;\n    out[1] = y;\n  }\n}\n/**\n * 求两个线段的交点坐标\n * 参考：https://leetcode-cn.com/problems/intersection-lcci/solution/jiao-dian-by-leetcode-solution/\n */\nexport function intersection(_a, _b, _c, _d) {\n  var _e = __read(_a, 2),\n    x1 = _e[0],\n    y1 = _e[1];\n  var _f = __read(_b, 2),\n    x2 = _f[0],\n    y2 = _f[1];\n  var _g = __read(_c, 2),\n    x3 = _g[0],\n    y3 = _g[1];\n  var _h = __read(_d, 2),\n    x4 = _h[0],\n    y4 = _h[1];\n  var ans = [];\n  // 若两直线平行\n  if ((y4 - y3) * (x2 - x1) === (y2 - y1) * (x4 - x3)) {\n    // 若两线段有重合\n    if ((y2 - y1) * (x3 - x1) === (y3 - y1) * (x2 - x1)) {\n      // 分别判断四个点\n      if (inside(x1, x2, y1, y2, x3, y3)) {\n        update(ans, x3, y3);\n      }\n      if (inside(x1, x2, y1, y2, x4, y4)) {\n        update(ans, x4, y4);\n      }\n      if (inside(x3, x4, y3, y4, x1, y1)) {\n        update(ans, x1, y1);\n      }\n      if (inside(x3, x4, y3, y4, x2, y2)) {\n        update(ans, x2, y2);\n      }\n    }\n  } else {\n    // 联立方程得到 t1 和 t2 的值\n    var t1 = (x3 * (y4 - y3) + y1 * (x4 - x3) - y3 * (x4 - x3) - x1 * (y4 - y3)) / ((x2 - x1) * (y4 - y3) - (x4 - x3) * (y2 - y1));\n    var t2 = (x1 * (y2 - y1) + y3 * (x2 - x1) - y1 * (x2 - x1) - x3 * (y2 - y1)) / ((x4 - x3) * (y2 - y1) - (x2 - x1) * (y4 - y3));\n    // 判断 t1 和 t2 是否均在 [0, 1] 之间\n    if (t1 >= 0.0 && t1 <= 1.0 && t2 >= 0.0 && t2 <= 1.0) {\n      ans[0] = x1 + t1 * (x2 - x1);\n      ans[1] = y1 + t1 * (y2 - y1);\n    }\n  }\n  return ans;\n}", "map": {"version": 3, "names": ["inside", "x1", "x2", "y1", "y2", "xk", "yk", "Math", "min", "max", "update", "ans", "x", "y", "out", "length", "intersection", "_a", "_b", "_c", "_d", "_e", "__read", "_f", "_g", "x3", "y3", "_h", "x4", "y4", "t1", "t2"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\component\\src\\util\\geometry\\lines-intersection.ts"], "sourcesContent": ["import type { Point } from '../../types';\n\nfunction inside(x1: number, x2: number, y1: number, y2: number, xk: number, yk: number) {\n  return (\n    (x1 === x2 || (Math.min(x1, x2) <= xk && xk <= Math.max(x1, x2))) &&\n    (y1 === y2 || (Math.min(y1, y2) <= yk && yk <= Math.max(y1, y2)))\n  );\n}\nfunction update(ans: number[], x: number, y: number) {\n  const out = ans;\n  if (!ans.length || x < ans[0] || (x === ans[0] && y < ans[1])) {\n    out[0] = x;\n    out[1] = y;\n  }\n}\n\n/**\n * 求两个线段的交点坐标\n * 参考：https://leetcode-cn.com/problems/intersection-lcci/solution/jiao-dian-by-leetcode-solution/\n */\nexport function intersection([x1, y1]: Point, [x2, y2]: Point, [x3, y3]: Point, [x4, y4]: Point) {\n  const ans: number[] = [];\n  // 若两直线平行\n  if ((y4 - y3) * (x2 - x1) === (y2 - y1) * (x4 - x3)) {\n    // 若两线段有重合\n    if ((y2 - y1) * (x3 - x1) === (y3 - y1) * (x2 - x1)) {\n      // 分别判断四个点\n      if (inside(x1, x2, y1, y2, x3, y3)) {\n        update(ans, x3, y3);\n      }\n      if (inside(x1, x2, y1, y2, x4, y4)) {\n        update(ans, x4, y4);\n      }\n      if (inside(x3, x4, y3, y4, x1, y1)) {\n        update(ans, x1, y1);\n      }\n      if (inside(x3, x4, y3, y4, x2, y2)) {\n        update(ans, x2, y2);\n      }\n    }\n  } else {\n    // 联立方程得到 t1 和 t2 的值\n    const t1 =\n      (x3 * (y4 - y3) + y1 * (x4 - x3) - y3 * (x4 - x3) - x1 * (y4 - y3)) /\n      ((x2 - x1) * (y4 - y3) - (x4 - x3) * (y2 - y1));\n    const t2 =\n      (x1 * (y2 - y1) + y3 * (x2 - x1) - y1 * (x2 - x1) - x3 * (y2 - y1)) /\n      ((x4 - x3) * (y2 - y1) - (x2 - x1) * (y4 - y3));\n    // 判断 t1 和 t2 是否均在 [0, 1] 之间\n    if (t1 >= 0.0 && t1 <= 1.0 && t2 >= 0.0 && t2 <= 1.0) {\n      ans[0] = x1 + t1 * (x2 - x1);\n      ans[1] = y1 + t1 * (y2 - y1);\n    }\n  }\n  return ans;\n}\n"], "mappings": ";AAEA,SAASA,MAAMA,CAACC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU;EACpF,OACE,CAACL,EAAE,KAAKC,EAAE,IAAKK,IAAI,CAACC,GAAG,CAACP,EAAE,EAAEC,EAAE,CAAC,IAAIG,EAAE,IAAIA,EAAE,IAAIE,IAAI,CAACE,GAAG,CAACR,EAAE,EAAEC,EAAE,CAAE,MAC/DC,EAAE,KAAKC,EAAE,IAAKG,IAAI,CAACC,GAAG,CAACL,EAAE,EAAEC,EAAE,CAAC,IAAIE,EAAE,IAAIA,EAAE,IAAIC,IAAI,CAACE,GAAG,CAACN,EAAE,EAAEC,EAAE,CAAE,CAAC;AAErE;AACA,SAASM,MAAMA,CAACC,GAAa,EAAEC,CAAS,EAAEC,CAAS;EACjD,IAAMC,GAAG,GAAGH,GAAG;EACf,IAAI,CAACA,GAAG,CAACI,MAAM,IAAIH,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,IAAKC,CAAC,KAAKD,GAAG,CAAC,CAAC,CAAC,IAAIE,CAAC,GAAGF,GAAG,CAAC,CAAC,CAAE,EAAE;IAC7DG,GAAG,CAAC,CAAC,CAAC,GAAGF,CAAC;IACVE,GAAG,CAAC,CAAC,CAAC,GAAGD,CAAC;EACZ;AACF;AAEA;;;;AAIA,OAAM,SAAUG,YAAYA,CAACC,EAAe,EAAEC,EAAe,EAAEC,EAAe,EAAEC,EAAe;MAAlEC,EAAA,GAAAC,MAAA,CAAAL,EAAA,IAAe;IAAdhB,EAAE,GAAAoB,EAAA;IAAElB,EAAE,GAAAkB,EAAA;MAAUE,EAAA,GAAAD,MAAA,CAAAJ,EAAA,IAAe;IAAdhB,EAAE,GAAAqB,EAAA;IAAEnB,EAAE,GAAAmB,EAAA;MAAUC,EAAA,GAAAF,MAAA,CAAAH,EAAA,IAAe;IAAdM,EAAE,GAAAD,EAAA;IAAEE,EAAE,GAAAF,EAAA;MAAUG,EAAA,GAAAL,MAAA,CAAAF,EAAA,IAAe;IAAdQ,EAAE,GAAAD,EAAA;IAAEE,EAAE,GAAAF,EAAA;EACrF,IAAMhB,GAAG,GAAa,EAAE;EACxB;EACA,IAAI,CAACkB,EAAE,GAAGH,EAAE,KAAKxB,EAAE,GAAGD,EAAE,CAAC,KAAK,CAACG,EAAE,GAAGD,EAAE,KAAKyB,EAAE,GAAGH,EAAE,CAAC,EAAE;IACnD;IACA,IAAI,CAACrB,EAAE,GAAGD,EAAE,KAAKsB,EAAE,GAAGxB,EAAE,CAAC,KAAK,CAACyB,EAAE,GAAGvB,EAAE,KAAKD,EAAE,GAAGD,EAAE,CAAC,EAAE;MACnD;MACA,IAAID,MAAM,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEqB,EAAE,EAAEC,EAAE,CAAC,EAAE;QAClChB,MAAM,CAACC,GAAG,EAAEc,EAAE,EAAEC,EAAE,CAAC;MACrB;MACA,IAAI1B,MAAM,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEwB,EAAE,EAAEC,EAAE,CAAC,EAAE;QAClCnB,MAAM,CAACC,GAAG,EAAEiB,EAAE,EAAEC,EAAE,CAAC;MACrB;MACA,IAAI7B,MAAM,CAACyB,EAAE,EAAEG,EAAE,EAAEF,EAAE,EAAEG,EAAE,EAAE5B,EAAE,EAAEE,EAAE,CAAC,EAAE;QAClCO,MAAM,CAACC,GAAG,EAAEV,EAAE,EAAEE,EAAE,CAAC;MACrB;MACA,IAAIH,MAAM,CAACyB,EAAE,EAAEG,EAAE,EAAEF,EAAE,EAAEG,EAAE,EAAE3B,EAAE,EAAEE,EAAE,CAAC,EAAE;QAClCM,MAAM,CAACC,GAAG,EAAET,EAAE,EAAEE,EAAE,CAAC;MACrB;IACF;EACF,CAAC,MAAM;IACL;IACA,IAAM0B,EAAE,GACN,CAACL,EAAE,IAAII,EAAE,GAAGH,EAAE,CAAC,GAAGvB,EAAE,IAAIyB,EAAE,GAAGH,EAAE,CAAC,GAAGC,EAAE,IAAIE,EAAE,GAAGH,EAAE,CAAC,GAAGxB,EAAE,IAAI4B,EAAE,GAAGH,EAAE,CAAC,KACjE,CAACxB,EAAE,GAAGD,EAAE,KAAK4B,EAAE,GAAGH,EAAE,CAAC,GAAG,CAACE,EAAE,GAAGH,EAAE,KAAKrB,EAAE,GAAGD,EAAE,CAAC,CAAC;IACjD,IAAM4B,EAAE,GACN,CAAC9B,EAAE,IAAIG,EAAE,GAAGD,EAAE,CAAC,GAAGuB,EAAE,IAAIxB,EAAE,GAAGD,EAAE,CAAC,GAAGE,EAAE,IAAID,EAAE,GAAGD,EAAE,CAAC,GAAGwB,EAAE,IAAIrB,EAAE,GAAGD,EAAE,CAAC,KACjE,CAACyB,EAAE,GAAGH,EAAE,KAAKrB,EAAE,GAAGD,EAAE,CAAC,GAAG,CAACD,EAAE,GAAGD,EAAE,KAAK4B,EAAE,GAAGH,EAAE,CAAC,CAAC;IACjD;IACA,IAAII,EAAE,IAAI,GAAG,IAAIA,EAAE,IAAI,GAAG,IAAIC,EAAE,IAAI,GAAG,IAAIA,EAAE,IAAI,GAAG,EAAE;MACpDpB,GAAG,CAAC,CAAC,CAAC,GAAGV,EAAE,GAAG6B,EAAE,IAAI5B,EAAE,GAAGD,EAAE,CAAC;MAC5BU,GAAG,CAAC,CAAC,CAAC,GAAGR,EAAE,GAAG2B,EAAE,IAAI1B,EAAE,GAAGD,EAAE,CAAC;IAC9B;EACF;EACA,OAAOQ,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}