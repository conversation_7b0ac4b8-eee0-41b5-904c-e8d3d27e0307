package com.yinma.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yinma.entity.RolePermissionEntity;

import java.util.List;

/**
 * 角色权限关联Service接口
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
public interface RolePermissionService extends IService<RolePermissionEntity> {

    /**
     * 查询角色的权限ID列表
     * 
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    List<Long> selectPermissionIdsByRoleId(Long roleId);

    /**
     * 查询权限的角色ID列表
     * 
     * @param permissionId 权限ID
     * @return 角色ID列表
     */
    List<Long> selectRoleIdsByPermissionId(Long permissionId);

    /**
     * 检查角色权限关联是否存在
     * 
     * @param roleId 角色ID
     * @param permissionId 权限ID
     * @return 是否存在
     */
    Boolean checkRolePermissionExists(Long roleId, Long permissionId);

    /**
     * 删除角色的所有权限关联
     * 
     * @param roleId 角色ID
     * @return 是否成功
     */
    Boolean deleteRolePermissionsByRoleId(Long roleId);

    /**
     * 删除权限的所有角色关联
     * 
     * @param permissionId 权限ID
     * @return 是否成功
     */
    Boolean deleteRolePermissionsByPermissionId(Long permissionId);

    /**
     * 删除角色权限关联
     * 
     * @param roleId 角色ID
     * @param permissionId 权限ID
     * @return 是否成功
     */
    Boolean deleteRolePermission(Long roleId, Long permissionId);

    /**
     * 批量插入角色权限关联
     * 
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @return 是否成功
     */
    Boolean batchInsertRolePermissions(Long roleId, List<Long> permissionIds);

    /**
     * 批量删除角色权限关联
     * 
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @return 是否成功
     */
    Boolean batchDeleteRolePermissions(Long roleId, List<Long> permissionIds);

    /**
     * 批量插入权限角色关联
     * 
     * @param permissionId 权限ID
     * @param roleIds 角色ID列表
     * @return 是否成功
     */
    Boolean batchInsertPermissionRoles(Long permissionId, List<Long> roleIds);

    /**
     * 批量删除权限角色关联
     * 
     * @param permissionId 权限ID
     * @param roleIds 角色ID列表
     * @return 是否成功
     */
    Boolean batchDeletePermissionRoles(Long permissionId, List<Long> roleIds);

    /**
     * 查询用户的权限ID列表（通过角色）
     * 
     * @param userId 用户ID
     * @return 权限ID列表
     */
    List<Long> selectUserPermissionIds(Long userId);

    /**
     * 查询角色权限关联统计信息
     * 
     * @return 统计信息
     */
    Long selectRolePermissionCount();

    /**
     * 查询拥有指定权限的角色数量
     * 
     * @param permissionId 权限ID
     * @return 角色数量
     */
    Long selectRoleCountByPermissionId(Long permissionId);

    /**
     * 查询角色拥有的权限数量
     * 
     * @param roleId 角色ID
     * @return 权限数量
     */
    Long selectPermissionCountByRoleId(Long roleId);

    /**
     * 查询没有分配权限的角色数量
     * 
     * @return 角色数量
     */
    Long selectRolesWithoutPermissionCount();

    /**
     * 查询没有分配角色的权限数量
     * 
     * @return 权限数量
     */
    Long selectPermissionsWithoutRoleCount();
}