{"ast": null, "code": "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { path as d3path } from '@antv/vendor/d3-path';\nimport { applyStyle } from '../utils';\nimport { select } from '../../utils/selection';\n/**\n * Draw density shape.\n */\nexport const Density = (options, context) => {\n  const {\n    document\n  } = context;\n  return (points, value, defaults) => {\n    const {\n      transform\n    } = value;\n    const {\n        color: defaultColor\n      } = defaults,\n      rest = __rest(defaults, [\"color\"]);\n    const {\n      color = defaultColor\n    } = value;\n    const [first, ...p] = points;\n    // todo smooth, hollow\n    const path = d3path();\n    path.moveTo(...first);\n    p.forEach(([x, y]) => {\n      path.lineTo(x, y);\n    });\n    path.closePath();\n    return select(document.createElement('path', {})).call(applyStyle, rest).style('d', path.toString()).style('stroke', color || defaultColor) // Always has stroke color.\n    .style('fill', color || defaultColor).style('fillOpacity', 0.4).style('transform', transform).call(applyStyle, options).node();\n  };\n};\nDensity.props = {\n  defaultMarker: 'square',\n  defaultEnterAnimation: 'fadeIn',\n  defaultUpdateAnimation: 'morphing',\n  defaultExitAnimation: 'fadeOut'\n};", "map": {"version": 3, "names": ["path", "d3path", "applyStyle", "select", "Density", "options", "context", "document", "points", "value", "defaults", "transform", "color", "defaultColor", "rest", "__rest", "first", "p", "moveTo", "for<PERSON>ach", "x", "y", "lineTo", "closePath", "createElement", "call", "style", "toString", "node", "props", "defaultMarker", "defaultEnterAnimation", "defaultUpdateAnimation", "defaultExitAnimation"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\g2\\src\\shape\\density\\density.ts"], "sourcesContent": ["import { path as d3path } from '@antv/vendor/d3-path';\nimport { applyStyle } from '../utils';\nimport { select } from '../../utils/selection';\nimport { ShapeComponent as SC } from '../../runtime';\n\nexport type DensityOptions = {\n  colorAttribute: 'fill' | 'stroke';\n};\n\n/**\n * Draw density shape.\n */\nexport const Density: SC<DensityOptions> = (options, context) => {\n  const { document } = context;\n  return (points, value, defaults) => {\n    const { transform } = value;\n    const { color: defaultColor, ...rest } = defaults;\n    const { color = defaultColor } = value;\n    const [first, ...p] = points;\n\n    // todo smooth, hollow\n    const path = d3path();\n    path.moveTo(...first);\n    p.forEach(([x, y]) => {\n      path.lineTo(x, y);\n    });\n    path.closePath();\n\n    return select(document.createElement('path', {}))\n      .call(applyStyle, rest)\n      .style('d', path.toString())\n      .style('stroke', color || defaultColor) // Always has stroke color.\n      .style('fill', color || defaultColor)\n      .style('fillOpacity', 0.4)\n      .style('transform', transform)\n      .call(applyStyle, options)\n      .node();\n  };\n};\n\nDensity.props = {\n  defaultMarker: 'square',\n  defaultEnterAnimation: 'fadeIn',\n  defaultUpdateAnimation: 'morphing',\n  defaultExitAnimation: 'fadeOut',\n};\n"], "mappings": ";;;;;;;;AAAA,SAASA,IAAI,IAAIC,MAAM,QAAQ,sBAAsB;AACrD,SAASC,UAAU,QAAQ,UAAU;AACrC,SAASC,MAAM,QAAQ,uBAAuB;AAO9C;;;AAGA,OAAO,MAAMC,OAAO,GAAuBA,CAACC,OAAO,EAAEC,OAAO,KAAI;EAC9D,MAAM;IAAEC;EAAQ,CAAE,GAAGD,OAAO;EAC5B,OAAO,CAACE,MAAM,EAAEC,KAAK,EAAEC,QAAQ,KAAI;IACjC,MAAM;MAAEC;IAAS,CAAE,GAAGF,KAAK;IAC3B,MAAM;QAAEG,KAAK,EAAEC;MAAY,IAAcH,QAAQ;MAAjBI,IAAI,GAAAC,MAAA,CAAKL,QAAQ,EAA3C,SAAgC,CAAW;IACjD,MAAM;MAAEE,KAAK,GAAGC;IAAY,CAAE,GAAGJ,KAAK;IACtC,MAAM,CAACO,KAAK,EAAE,GAAGC,CAAC,CAAC,GAAGT,MAAM;IAE5B;IACA,MAAMR,IAAI,GAAGC,MAAM,EAAE;IACrBD,IAAI,CAACkB,MAAM,CAAC,GAAGF,KAAK,CAAC;IACrBC,CAAC,CAACE,OAAO,CAAC,CAAC,CAACC,CAAC,EAAEC,CAAC,CAAC,KAAI;MACnBrB,IAAI,CAACsB,MAAM,CAACF,CAAC,EAAEC,CAAC,CAAC;IACnB,CAAC,CAAC;IACFrB,IAAI,CAACuB,SAAS,EAAE;IAEhB,OAAOpB,MAAM,CAACI,QAAQ,CAACiB,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAC9CC,IAAI,CAACvB,UAAU,EAAEY,IAAI,CAAC,CACtBY,KAAK,CAAC,GAAG,EAAE1B,IAAI,CAAC2B,QAAQ,EAAE,CAAC,CAC3BD,KAAK,CAAC,QAAQ,EAAEd,KAAK,IAAIC,YAAY,CAAC,CAAC;IAAA,CACvCa,KAAK,CAAC,MAAM,EAAEd,KAAK,IAAIC,YAAY,CAAC,CACpCa,KAAK,CAAC,aAAa,EAAE,GAAG,CAAC,CACzBA,KAAK,CAAC,WAAW,EAAEf,SAAS,CAAC,CAC7Bc,IAAI,CAACvB,UAAU,EAAEG,OAAO,CAAC,CACzBuB,IAAI,EAAE;EACX,CAAC;AACH,CAAC;AAEDxB,OAAO,CAACyB,KAAK,GAAG;EACdC,aAAa,EAAE,QAAQ;EACvBC,qBAAqB,EAAE,QAAQ;EAC/BC,sBAAsB,EAAE,UAAU;EAClCC,oBAAoB,EAAE;CACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}