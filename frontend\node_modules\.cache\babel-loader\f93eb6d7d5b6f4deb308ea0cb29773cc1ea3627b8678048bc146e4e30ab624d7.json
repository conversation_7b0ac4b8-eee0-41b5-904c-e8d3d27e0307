{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport QuestionCircleOutlined from \"@ant-design/icons/es/icons/QuestionCircleOutlined\";\nimport classNames from 'classnames';\nimport convertToTooltipProps from '../_util/convertToTooltipProps';\nimport Col from '../grid/col';\nimport { useLocale } from '../locale';\nimport defaultLocale from '../locale/en_US';\nimport Tooltip from '../tooltip';\nimport { FormContext } from './context';\nconst FormItemLabel = ({\n  prefixCls,\n  label,\n  htmlFor,\n  labelCol,\n  labelAlign,\n  colon,\n  required,\n  requiredMark,\n  tooltip,\n  vertical\n}) => {\n  var _a;\n  const [formLocale] = useLocale('Form');\n  const {\n    labelAlign: contextLabelAlign,\n    labelCol: contextLabelCol,\n    labelWrap,\n    colon: contextColon\n  } = React.useContext(FormContext);\n  if (!label) {\n    return null;\n  }\n  const mergedLabelCol = labelCol || contextLabelCol || {};\n  const mergedLabelAlign = labelAlign || contextLabelAlign;\n  const labelClsBasic = `${prefixCls}-item-label`;\n  const labelColClassName = classNames(labelClsBasic, mergedLabelAlign === 'left' && `${labelClsBasic}-left`, mergedLabelCol.className, {\n    [`${labelClsBasic}-wrap`]: !!labelWrap\n  });\n  let labelChildren = label;\n  // Keep label is original where there should have no colon\n  const computedColon = colon === true || contextColon !== false && colon !== false;\n  const haveColon = computedColon && !vertical;\n  // Remove duplicated user input colon\n  if (haveColon && typeof label === 'string' && label.trim()) {\n    labelChildren = label.replace(/[:|：]\\s*$/, '');\n  }\n  // Tooltip\n  const tooltipProps = convertToTooltipProps(tooltip);\n  if (tooltipProps) {\n    const {\n        icon = /*#__PURE__*/React.createElement(QuestionCircleOutlined, null)\n      } = tooltipProps,\n      restTooltipProps = __rest(tooltipProps, [\"icon\"]);\n    const tooltipNode = /*#__PURE__*/React.createElement(Tooltip, Object.assign({}, restTooltipProps), /*#__PURE__*/React.cloneElement(icon, {\n      className: `${prefixCls}-item-tooltip`,\n      title: '',\n      onClick: e => {\n        // Prevent label behavior in tooltip icon\n        // https://github.com/ant-design/ant-design/issues/46154\n        e.preventDefault();\n      },\n      tabIndex: null\n    }));\n    labelChildren = /*#__PURE__*/React.createElement(React.Fragment, null, labelChildren, tooltipNode);\n  }\n  // Required Mark\n  const isOptionalMark = requiredMark === 'optional';\n  const isRenderMark = typeof requiredMark === 'function';\n  const hideRequiredMark = requiredMark === false;\n  if (isRenderMark) {\n    labelChildren = requiredMark(labelChildren, {\n      required: !!required\n    });\n  } else if (isOptionalMark && !required) {\n    labelChildren = /*#__PURE__*/React.createElement(React.Fragment, null, labelChildren, /*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-item-optional`,\n      title: \"\"\n    }, (formLocale === null || formLocale === void 0 ? void 0 : formLocale.optional) || ((_a = defaultLocale.Form) === null || _a === void 0 ? void 0 : _a.optional)));\n  }\n  // https://github.com/ant-design/ant-design/pull/52950#discussion_r1980880316\n  let markType;\n  if (hideRequiredMark) {\n    markType = 'hidden';\n  } else if (isOptionalMark || isRenderMark) {\n    markType = 'optional';\n  }\n  const labelClassName = classNames({\n    [`${prefixCls}-item-required`]: required,\n    [`${prefixCls}-item-required-mark-${markType}`]: markType,\n    [`${prefixCls}-item-no-colon`]: !computedColon\n  });\n  return /*#__PURE__*/React.createElement(Col, Object.assign({}, mergedLabelCol, {\n    className: labelColClassName\n  }), /*#__PURE__*/React.createElement(\"label\", {\n    htmlFor: htmlFor,\n    className: labelClassName,\n    title: typeof label === 'string' ? label : ''\n  }, labelChildren));\n};\nexport default FormItemLabel;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "QuestionCircleOutlined", "classNames", "convertToTooltipProps", "Col", "useLocale", "defaultLocale", "<PERSON><PERSON><PERSON>", "FormContext", "FormItemLabel", "prefixCls", "label", "htmlFor", "labelCol", "labelAlign", "colon", "required", "requiredMark", "tooltip", "vertical", "_a", "formLocale", "contextLabelAlign", "contextLabelCol", "labelWrap", "contextColon", "useContext", "mergedLabelCol", "mergedLabelAlign", "labelClsBasic", "labelColClassName", "className", "labelChildren", "computedColon", "haveColon", "trim", "replace", "tooltipProps", "icon", "createElement", "restTooltipProps", "tooltipNode", "assign", "cloneElement", "title", "onClick", "preventDefault", "tabIndex", "Fragment", "isOptionalMark", "isRenderMark", "hideRequiredMark", "optional", "Form", "markType", "labelClassName"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/antd/es/form/FormItemLabel.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport QuestionCircleOutlined from \"@ant-design/icons/es/icons/QuestionCircleOutlined\";\nimport classNames from 'classnames';\nimport convertToTooltipProps from '../_util/convertToTooltipProps';\nimport Col from '../grid/col';\nimport { useLocale } from '../locale';\nimport defaultLocale from '../locale/en_US';\nimport Tooltip from '../tooltip';\nimport { FormContext } from './context';\nconst FormItemLabel = ({\n  prefixCls,\n  label,\n  htmlFor,\n  labelCol,\n  labelAlign,\n  colon,\n  required,\n  requiredMark,\n  tooltip,\n  vertical\n}) => {\n  var _a;\n  const [formLocale] = useLocale('Form');\n  const {\n    labelAlign: contextLabelAlign,\n    labelCol: contextLabelCol,\n    labelWrap,\n    colon: contextColon\n  } = React.useContext(FormContext);\n  if (!label) {\n    return null;\n  }\n  const mergedLabelCol = labelCol || contextLabelCol || {};\n  const mergedLabelAlign = labelAlign || contextLabelAlign;\n  const labelClsBasic = `${prefixCls}-item-label`;\n  const labelColClassName = classNames(labelClsBasic, mergedLabelAlign === 'left' && `${labelClsBasic}-left`, mergedLabelCol.className, {\n    [`${labelClsBasic}-wrap`]: !!labelWrap\n  });\n  let labelChildren = label;\n  // Keep label is original where there should have no colon\n  const computedColon = colon === true || contextColon !== false && colon !== false;\n  const haveColon = computedColon && !vertical;\n  // Remove duplicated user input colon\n  if (haveColon && typeof label === 'string' && label.trim()) {\n    labelChildren = label.replace(/[:|：]\\s*$/, '');\n  }\n  // Tooltip\n  const tooltipProps = convertToTooltipProps(tooltip);\n  if (tooltipProps) {\n    const {\n        icon = /*#__PURE__*/React.createElement(QuestionCircleOutlined, null)\n      } = tooltipProps,\n      restTooltipProps = __rest(tooltipProps, [\"icon\"]);\n    const tooltipNode = /*#__PURE__*/React.createElement(Tooltip, Object.assign({}, restTooltipProps), /*#__PURE__*/React.cloneElement(icon, {\n      className: `${prefixCls}-item-tooltip`,\n      title: '',\n      onClick: e => {\n        // Prevent label behavior in tooltip icon\n        // https://github.com/ant-design/ant-design/issues/46154\n        e.preventDefault();\n      },\n      tabIndex: null\n    }));\n    labelChildren = /*#__PURE__*/React.createElement(React.Fragment, null, labelChildren, tooltipNode);\n  }\n  // Required Mark\n  const isOptionalMark = requiredMark === 'optional';\n  const isRenderMark = typeof requiredMark === 'function';\n  const hideRequiredMark = requiredMark === false;\n  if (isRenderMark) {\n    labelChildren = requiredMark(labelChildren, {\n      required: !!required\n    });\n  } else if (isOptionalMark && !required) {\n    labelChildren = /*#__PURE__*/React.createElement(React.Fragment, null, labelChildren, /*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-item-optional`,\n      title: \"\"\n    }, (formLocale === null || formLocale === void 0 ? void 0 : formLocale.optional) || ((_a = defaultLocale.Form) === null || _a === void 0 ? void 0 : _a.optional)));\n  }\n  // https://github.com/ant-design/ant-design/pull/52950#discussion_r1980880316\n  let markType;\n  if (hideRequiredMark) {\n    markType = 'hidden';\n  } else if (isOptionalMark || isRenderMark) {\n    markType = 'optional';\n  }\n  const labelClassName = classNames({\n    [`${prefixCls}-item-required`]: required,\n    [`${prefixCls}-item-required-mark-${markType}`]: markType,\n    [`${prefixCls}-item-no-colon`]: !computedColon\n  });\n  return /*#__PURE__*/React.createElement(Col, Object.assign({}, mergedLabelCol, {\n    className: labelColClassName\n  }), /*#__PURE__*/React.createElement(\"label\", {\n    htmlFor: htmlFor,\n    className: labelClassName,\n    title: typeof label === 'string' ? label : ''\n  }, labelChildren));\n};\nexport default FormItemLabel;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,sBAAsB,MAAM,mDAAmD;AACtF,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,qBAAqB,MAAM,gCAAgC;AAClE,OAAOC,GAAG,MAAM,aAAa;AAC7B,SAASC,SAAS,QAAQ,WAAW;AACrC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,OAAO,MAAM,YAAY;AAChC,SAASC,WAAW,QAAQ,WAAW;AACvC,MAAMC,aAAa,GAAGA,CAAC;EACrBC,SAAS;EACTC,KAAK;EACLC,OAAO;EACPC,QAAQ;EACRC,UAAU;EACVC,KAAK;EACLC,QAAQ;EACRC,YAAY;EACZC,OAAO;EACPC;AACF,CAAC,KAAK;EACJ,IAAIC,EAAE;EACN,MAAM,CAACC,UAAU,CAAC,GAAGhB,SAAS,CAAC,MAAM,CAAC;EACtC,MAAM;IACJS,UAAU,EAAEQ,iBAAiB;IAC7BT,QAAQ,EAAEU,eAAe;IACzBC,SAAS;IACTT,KAAK,EAAEU;EACT,CAAC,GAAGzB,KAAK,CAAC0B,UAAU,CAAClB,WAAW,CAAC;EACjC,IAAI,CAACG,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EACA,MAAMgB,cAAc,GAAGd,QAAQ,IAAIU,eAAe,IAAI,CAAC,CAAC;EACxD,MAAMK,gBAAgB,GAAGd,UAAU,IAAIQ,iBAAiB;EACxD,MAAMO,aAAa,GAAG,GAAGnB,SAAS,aAAa;EAC/C,MAAMoB,iBAAiB,GAAG5B,UAAU,CAAC2B,aAAa,EAAED,gBAAgB,KAAK,MAAM,IAAI,GAAGC,aAAa,OAAO,EAAEF,cAAc,CAACI,SAAS,EAAE;IACpI,CAAC,GAAGF,aAAa,OAAO,GAAG,CAAC,CAACL;EAC/B,CAAC,CAAC;EACF,IAAIQ,aAAa,GAAGrB,KAAK;EACzB;EACA,MAAMsB,aAAa,GAAGlB,KAAK,KAAK,IAAI,IAAIU,YAAY,KAAK,KAAK,IAAIV,KAAK,KAAK,KAAK;EACjF,MAAMmB,SAAS,GAAGD,aAAa,IAAI,CAACd,QAAQ;EAC5C;EACA,IAAIe,SAAS,IAAI,OAAOvB,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACwB,IAAI,CAAC,CAAC,EAAE;IAC1DH,aAAa,GAAGrB,KAAK,CAACyB,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;EAChD;EACA;EACA,MAAMC,YAAY,GAAGlC,qBAAqB,CAACe,OAAO,CAAC;EACnD,IAAImB,YAAY,EAAE;IAChB,MAAM;QACFC,IAAI,GAAG,aAAatC,KAAK,CAACuC,aAAa,CAACtC,sBAAsB,EAAE,IAAI;MACtE,CAAC,GAAGoC,YAAY;MAChBG,gBAAgB,GAAGtD,MAAM,CAACmD,YAAY,EAAE,CAAC,MAAM,CAAC,CAAC;IACnD,MAAMI,WAAW,GAAG,aAAazC,KAAK,CAACuC,aAAa,CAAChC,OAAO,EAAEhB,MAAM,CAACmD,MAAM,CAAC,CAAC,CAAC,EAAEF,gBAAgB,CAAC,EAAE,aAAaxC,KAAK,CAAC2C,YAAY,CAACL,IAAI,EAAE;MACvIP,SAAS,EAAE,GAAGrB,SAAS,eAAe;MACtCkC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAEzD,CAAC,IAAI;QACZ;QACA;QACAA,CAAC,CAAC0D,cAAc,CAAC,CAAC;MACpB,CAAC;MACDC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAAC;IACHf,aAAa,GAAG,aAAahC,KAAK,CAACuC,aAAa,CAACvC,KAAK,CAACgD,QAAQ,EAAE,IAAI,EAAEhB,aAAa,EAAES,WAAW,CAAC;EACpG;EACA;EACA,MAAMQ,cAAc,GAAGhC,YAAY,KAAK,UAAU;EAClD,MAAMiC,YAAY,GAAG,OAAOjC,YAAY,KAAK,UAAU;EACvD,MAAMkC,gBAAgB,GAAGlC,YAAY,KAAK,KAAK;EAC/C,IAAIiC,YAAY,EAAE;IAChBlB,aAAa,GAAGf,YAAY,CAACe,aAAa,EAAE;MAC1ChB,QAAQ,EAAE,CAAC,CAACA;IACd,CAAC,CAAC;EACJ,CAAC,MAAM,IAAIiC,cAAc,IAAI,CAACjC,QAAQ,EAAE;IACtCgB,aAAa,GAAG,aAAahC,KAAK,CAACuC,aAAa,CAACvC,KAAK,CAACgD,QAAQ,EAAE,IAAI,EAAEhB,aAAa,EAAE,aAAahC,KAAK,CAACuC,aAAa,CAAC,MAAM,EAAE;MAC7HR,SAAS,EAAE,GAAGrB,SAAS,gBAAgB;MACvCkC,KAAK,EAAE;IACT,CAAC,EAAE,CAACvB,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC+B,QAAQ,MAAM,CAAChC,EAAE,GAAGd,aAAa,CAAC+C,IAAI,MAAM,IAAI,IAAIjC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgC,QAAQ,CAAC,CAAC,CAAC;EACpK;EACA;EACA,IAAIE,QAAQ;EACZ,IAAIH,gBAAgB,EAAE;IACpBG,QAAQ,GAAG,QAAQ;EACrB,CAAC,MAAM,IAAIL,cAAc,IAAIC,YAAY,EAAE;IACzCI,QAAQ,GAAG,UAAU;EACvB;EACA,MAAMC,cAAc,GAAGrD,UAAU,CAAC;IAChC,CAAC,GAAGQ,SAAS,gBAAgB,GAAGM,QAAQ;IACxC,CAAC,GAAGN,SAAS,uBAAuB4C,QAAQ,EAAE,GAAGA,QAAQ;IACzD,CAAC,GAAG5C,SAAS,gBAAgB,GAAG,CAACuB;EACnC,CAAC,CAAC;EACF,OAAO,aAAajC,KAAK,CAACuC,aAAa,CAACnC,GAAG,EAAEb,MAAM,CAACmD,MAAM,CAAC,CAAC,CAAC,EAAEf,cAAc,EAAE;IAC7EI,SAAS,EAAED;EACb,CAAC,CAAC,EAAE,aAAa9B,KAAK,CAACuC,aAAa,CAAC,OAAO,EAAE;IAC5C3B,OAAO,EAAEA,OAAO;IAChBmB,SAAS,EAAEwB,cAAc;IACzBX,KAAK,EAAE,OAAOjC,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG;EAC7C,CAAC,EAAEqB,aAAa,CAAC,CAAC;AACpB,CAAC;AACD,eAAevB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}