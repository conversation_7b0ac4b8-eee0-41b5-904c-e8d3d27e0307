package com.yinma.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinma.entity.Maintenance;
import com.yinma.entity.MaintenancePlan;
import com.yinma.entity.MaintenanceWorkItem;
import com.yinma.entity.MaintenancePart;
import com.yinma.dto.MaintenanceDTO;
import com.yinma.dto.MaintenanceStatisticsDTO;
import com.yinma.vo.MaintenanceVO;
import com.yinma.mapper.MaintenanceMapper;
import com.yinma.service.MaintenanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 维护管理服务实现类
 * 银马实业设备维护保养管理系统
 * 
 * <AUTHOR>
 * @since 2024-05-20
 */
@Slf4j
@Service
public class MaintenanceServiceImpl extends ServiceImpl<MaintenanceMapper, Maintenance> implements MaintenanceService {

    @Override
    public IPage<MaintenanceVO> getMaintenancePage(Page<Maintenance> page, String workOrderNo, 
                                                 String equipmentName, Long equipmentId, String customer,
                                                 String maintenanceType, String status, String priority,
                                                 Long technicianId, LocalDate startDate, LocalDate endDate) {
        QueryWrapper<Maintenance> queryWrapper = new QueryWrapper<>();
        
        if (workOrderNo != null && !workOrderNo.trim().isEmpty()) {
            queryWrapper.like("work_order_no", workOrderNo);
        }
        if (equipmentName != null && !equipmentName.trim().isEmpty()) {
            queryWrapper.like("equipment_name", equipmentName);
        }
        if (equipmentId != null) {
            queryWrapper.eq("equipment_id", equipmentId);
        }
        if (customer != null && !customer.trim().isEmpty()) {
            queryWrapper.like("customer", customer);
        }
        if (maintenanceType != null && !maintenanceType.trim().isEmpty()) {
            queryWrapper.eq("maintenance_type", maintenanceType);
        }
        if (status != null && !status.trim().isEmpty()) {
            queryWrapper.eq("status", status);
        }
        if (priority != null && !priority.trim().isEmpty()) {
            queryWrapper.eq("priority", priority);
        }
        if (technicianId != null) {
            queryWrapper.eq("technician_id", technicianId);
        }
        if (startDate != null) {
            queryWrapper.ge("planned_date", startDate);
        }
        if (endDate != null) {
            queryWrapper.le("planned_date", endDate);
        }
        
        queryWrapper.orderByDesc("created_time");
        
        IPage<Maintenance> maintenancePage = this.page(page, queryWrapper);
        
        // 转换为VO
        IPage<MaintenanceVO> voPage = maintenancePage.convert(maintenance -> {
            MaintenanceVO vo = new MaintenanceVO();
            BeanUtils.copyProperties(maintenance, vo);
            return vo;
        });
        
        return voPage;
    }

    @Override
    public MaintenanceVO getMaintenanceDetailById(Long id) {
        Maintenance maintenance = this.getById(id);
        if (maintenance == null) {
            return null;
        }
        
        MaintenanceVO vo = new MaintenanceVO();
        BeanUtils.copyProperties(maintenance, vo);
        
        // 获取工作项
        vo.setWorkItems(getMaintenanceWorkItems(id));
        
        // 获取使用配件
        vo.setParts(getMaintenanceParts(id));
        
        return vo;
    }

    @Override
    @Transactional
    public Maintenance createMaintenance(MaintenanceDTO maintenanceDTO) {
        Maintenance maintenance = new Maintenance();
        BeanUtils.copyProperties(maintenanceDTO, maintenance);
        
        // 生成工单号
        maintenance.setWorkOrderNo(generateWorkOrderNo());
        maintenance.setStatus("待分配");
        maintenance.setCreatedTime(LocalDateTime.now());
        maintenance.setUpdatedTime(LocalDateTime.now());
        
        this.save(maintenance);
        
        log.info("创建维护工单: {}", maintenance.getWorkOrderNo());
        return maintenance;
    }

    @Override
    @Transactional
    public Maintenance updateMaintenance(Long id, MaintenanceDTO maintenanceDTO) {
        Maintenance maintenance = this.getById(id);
        if (maintenance == null) {
            throw new RuntimeException("维护记录不存在");
        }
        
        BeanUtils.copyProperties(maintenanceDTO, maintenance);
        maintenance.setUpdatedTime(LocalDateTime.now());
        
        this.updateById(maintenance);
        
        log.info("更新维护工单: {}", maintenance.getWorkOrderNo());
        return maintenance;
    }

    @Override
    @Transactional
    public Maintenance updateMaintenanceStatus(Long id, String status, String remark) {
        Maintenance maintenance = this.getById(id);
        if (maintenance == null) {
            throw new RuntimeException("维护记录不存在");
        }
        
        maintenance.setStatus(status);
        if (remark != null) {
            maintenance.setRemark(remark);
        }
        maintenance.setUpdatedTime(LocalDateTime.now());
        
        // 根据状态更新时间字段
        switch (status) {
            case "进行中":
                maintenance.setActualStartTime(LocalDateTime.now());
                break;
            case "已完成":
                maintenance.setActualEndTime(LocalDateTime.now());
                break;
        }
        
        this.updateById(maintenance);
        
        log.info("更新维护状态: {} -> {}", maintenance.getWorkOrderNo(), status);
        return maintenance;
    }

    @Override
    @Transactional
    public Maintenance startMaintenance(Long id) {
        return updateMaintenanceStatus(id, "进行中", "开始维护工作");
    }

    @Override
    @Transactional
    public Maintenance completeMaintenance(Long id, Map<String, Object> completeInfo) {
        Maintenance maintenance = this.getById(id);
        if (maintenance == null) {
            throw new RuntimeException("维护记录不存在");
        }
        
        maintenance.setStatus("已完成");
        maintenance.setActualEndTime(LocalDateTime.now());
        
        if (completeInfo.containsKey("result")) {
            maintenance.setResult(completeInfo.get("result").toString());
        }
        if (completeInfo.containsKey("remark")) {
            maintenance.setRemark(completeInfo.get("remark").toString());
        }
        if (completeInfo.containsKey("totalCost")) {
            maintenance.setTotalCost(Double.valueOf(completeInfo.get("totalCost").toString()));
        }
        
        maintenance.setUpdatedTime(LocalDateTime.now());
        this.updateById(maintenance);
        
        log.info("完成维护工作: {}", maintenance.getWorkOrderNo());
        return maintenance;
    }

    @Override
    public List<MaintenanceWorkItem> getMaintenanceWorkItems(Long maintenanceId) {
        // 模拟数据，实际应从数据库查询
        List<MaintenanceWorkItem> workItems = new ArrayList<>();
        
        MaintenanceWorkItem item1 = new MaintenanceWorkItem();
        item1.setId(1L);
        item1.setMaintenanceId(maintenanceId);
        item1.setItemName("设备清洁");
        item1.setDescription("清洁设备表面和内部组件");
        item1.setStatus("已完成");
        item1.setDuration(2.0);
        workItems.add(item1);
        
        MaintenanceWorkItem item2 = new MaintenanceWorkItem();
        item2.setId(2L);
        item2.setMaintenanceId(maintenanceId);
        item2.setItemName("润滑保养");
        item2.setDescription("对关键部位进行润滑");
        item2.setStatus("进行中");
        item2.setDuration(1.5);
        workItems.add(item2);
        
        return workItems;
    }

    @Override
    @Transactional
    public MaintenanceWorkItem updateWorkItemStatus(Long itemId, String status, Double duration, String remark) {
        // 实际应更新数据库
        MaintenanceWorkItem workItem = new MaintenanceWorkItem();
        workItem.setId(itemId);
        workItem.setStatus(status);
        workItem.setDuration(duration);
        workItem.setRemark(remark);
        workItem.setUpdatedTime(LocalDateTime.now());
        
        log.info("更新工作项状态: {} -> {}", itemId, status);
        return workItem;
    }

    @Override
    public List<MaintenancePart> getMaintenanceParts(Long maintenanceId) {
        // 模拟数据，实际应从数据库查询
        List<MaintenancePart> parts = new ArrayList<>();
        
        MaintenancePart part1 = new MaintenancePart();
        part1.setId(1L);
        part1.setMaintenanceId(maintenanceId);
        part1.setPartName("液压油");
        part1.setPartCode("HYD-001");
        part1.setQuantity(20.0);
        part1.setUnit("升");
        part1.setUnitPrice(15.0);
        part1.setTotalPrice(300.0);
        parts.add(part1);
        
        return parts;
    }

    @Override
    @Transactional
    public MaintenancePart addMaintenancePart(MaintenancePart part) {
        part.setCreatedTime(LocalDateTime.now());
        // 实际应保存到数据库
        log.info("添加维护配件: {}", part.getPartName());
        return part;
    }

    @Override
    public List<MaintenancePlan> getMaintenancePlans(Long equipmentId, String customer, Boolean overdue, Integer dueDays) {
        // 模拟数据，实际应从数据库查询
        List<MaintenancePlan> plans = new ArrayList<>();
        
        MaintenancePlan plan1 = new MaintenancePlan();
        plan1.setId(1L);
        plan1.setEquipmentId(equipmentId);
        plan1.setPlanName("季度保养");
        plan1.setMaintenanceType("预防性维护");
        plan1.setPlannedDate(LocalDate.now().plusDays(7));
        plan1.setStatus("待执行");
        plans.add(plan1);
        
        return plans;
    }

    @Override
    @Transactional
    public MaintenancePlan createMaintenancePlan(MaintenancePlan plan) {
        plan.setCreatedTime(LocalDateTime.now());
        plan.setStatus("待执行");
        // 实际应保存到数据库
        log.info("创建维护计划: {}", plan.getPlanName());
        return plan;
    }

    @Override
    @Transactional
    public Maintenance createMaintenanceFromPlan(Long planId, Long technicianId, LocalDate plannedDate) {
        // 根据计划创建维护工单
        MaintenanceDTO maintenanceDTO = new MaintenanceDTO();
        maintenanceDTO.setPlanId(planId);
        maintenanceDTO.setTechnicianId(technicianId);
        maintenanceDTO.setPlannedDate(plannedDate);
        maintenanceDTO.setMaintenanceType("预防性维护");
        maintenanceDTO.setPriority("中");
        
        return createMaintenance(maintenanceDTO);
    }

    @Override
    @Transactional
    public MaintenancePlan postponeMaintenancePlan(Long planId, LocalDate newDate, String reason) {
        // 实际应从数据库查询并更新
        MaintenancePlan plan = new MaintenancePlan();
        plan.setId(planId);
        plan.setPlannedDate(newDate);
        plan.setRemark(reason);
        plan.setUpdatedTime(LocalDateTime.now());
        
        log.info("延期维护计划: {} -> {}", planId, newDate);
        return plan;
    }

    @Override
    public MaintenanceStatisticsDTO getMaintenanceStatistics(LocalDate startDate, LocalDate endDate, 
                                                            Long equipmentId, String customer) {
        MaintenanceStatisticsDTO statistics = new MaintenanceStatisticsDTO();
        
        // 模拟统计数据
        statistics.setTotalMaintenance(156);
        statistics.setCompletedMaintenance(142);
        statistics.setPendingMaintenance(14);
        statistics.setOverdueMaintenance(3);
        statistics.setAverageCompletionTime(4.2);
        statistics.setCustomerSatisfaction(4.6);
        statistics.setTotalCost(125600.0);
        statistics.setPreventiveMaintenance(89);
        statistics.setCorrectiveMaintenance(53);
        statistics.setEmergencyMaintenance(14);
        
        return statistics;
    }

    @Override
    public Map<String, Object> getMaintenanceTrends(String type, LocalDate startDate, LocalDate endDate) {
        Map<String, Object> trends = new HashMap<>();
        
        // 模拟趋势数据
        List<Map<String, Object>> data = new ArrayList<>();
        for (int i = 0; i < 12; i++) {
            Map<String, Object> item = new HashMap<>();
            item.put("period", startDate.plusMonths(i).toString());
            item.put("count", 10 + (int)(Math.random() * 20));
            item.put("cost", 8000 + (int)(Math.random() * 5000));
            data.add(item);
        }
        
        trends.put("data", data);
        trends.put("type", type);
        
        return trends;
    }

    @Override
    public String uploadMaintenanceAttachment(Long maintenanceId, MultipartFile file) {
        // 实际应实现文件上传逻辑
        String fileName = file.getOriginalFilename();
        String fileUrl = "/uploads/maintenance/" + maintenanceId + "/" + fileName;
        
        log.info("上传维护附件: {}", fileName);
        return fileUrl;
    }

    @Override
    public String exportMaintenanceReport(LocalDate startDate, LocalDate endDate, 
                                        Long equipmentId, String customer, String format) {
        // 实际应实现报告导出逻辑
        String reportUrl = "/exports/maintenance_report_" + System.currentTimeMillis() + "." + format;
        
        log.info("导出维护报告: {}", reportUrl);
        return reportUrl;
    }

    @Override
    public List<Map<String, Object>> getTechnicianWorkload(LocalDate startDate, LocalDate endDate) {
        // 模拟技师工作负载数据
        List<Map<String, Object>> workload = new ArrayList<>();
        
        String[] technicians = {"张师傅", "李师傅", "王师傅", "赵师傅"};
        for (String technician : technicians) {
            Map<String, Object> item = new HashMap<>();
            item.put("technicianName", technician);
            item.put("totalTasks", 15 + (int)(Math.random() * 10));
            item.put("completedTasks", 12 + (int)(Math.random() * 8));
            item.put("workingHours", 120 + (int)(Math.random() * 40));
            item.put("efficiency", 85 + (int)(Math.random() * 15));
            workload.add(item);
        }
        
        return workload;
    }

    @Override
    public List<MaintenanceVO> getEquipmentMaintenanceHistory(Long equipmentId, Page<Maintenance> page) {
        QueryWrapper<Maintenance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("equipment_id", equipmentId);
        queryWrapper.orderByDesc("created_time");
        
        List<Maintenance> maintenanceList = this.list(queryWrapper);
        
        return maintenanceList.stream().map(maintenance -> {
            MaintenanceVO vo = new MaintenanceVO();
            BeanUtils.copyProperties(maintenance, vo);
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public Maintenance rateMaintenance(Long id, Integer rating, String feedback) {
        Maintenance maintenance = this.getById(id);
        if (maintenance == null) {
            throw new RuntimeException("维护记录不存在");
        }
        
        maintenance.setCustomerRating(rating);
        maintenance.setCustomerFeedback(feedback);
        maintenance.setUpdatedTime(LocalDateTime.now());
        
        this.updateById(maintenance);
        
        log.info("客户评价维护服务: {} - 评分: {}", maintenance.getWorkOrderNo(), rating);
        return maintenance;
    }

    @Override
    public String generateWorkOrderNo() {
        String prefix = "WO";
        String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String sequence = String.format("%04d", (int)(Math.random() * 9999) + 1);
        return prefix + date + sequence;
    }

    @Override
    public boolean checkEquipmentMaintenanceNeeded(Long equipmentId) {
        // 实际应检查设备运行时间、故障记录等
        return Math.random() > 0.7; // 模拟30%的概率需要维护
    }

    @Override
    @Transactional
    public int autoCreatePreventiveMaintenancePlans(Long equipmentId) {
        // 实际应根据设备类型和维护规则自动创建计划
        int planCount = 3; // 模拟创建3个计划
        
        for (int i = 0; i < planCount; i++) {
            MaintenancePlan plan = new MaintenancePlan();
            plan.setEquipmentId(equipmentId);
            plan.setPlanName("自动生成计划" + (i + 1));
            plan.setMaintenanceType("预防性维护");
            plan.setPlannedDate(LocalDate.now().plusDays(30 * (i + 1)));
            createMaintenancePlan(plan);
        }
        
        log.info("为设备{}自动创建{}个预防性维护计划", equipmentId, planCount);
        return planCount;
    }

    @Override
    public boolean sendMaintenanceReminder(Long maintenanceId, String reminderType) {
        // 实际应实现消息推送逻辑
        log.info("发送维护提醒: {} - 类型: {}", maintenanceId, reminderType);
        return true;
    }

    @Override
    public Map<String, Object> getMaintenanceCostAnalysis(LocalDate startDate, LocalDate endDate, Long equipmentId) {
        Map<String, Object> analysis = new HashMap<>();
        
        // 模拟成本分析数据
        analysis.put("totalCost", 125600.0);
        analysis.put("laborCost", 75600.0);
        analysis.put("partsCost", 45000.0);
        analysis.put("otherCost", 5000.0);
        analysis.put("averageCostPerMaintenance", 805.1);
        analysis.put("costTrend", "上升");
        
        return analysis;
    }

    @Override
    public Map<String, Object> getMaintenanceEfficiencyAnalysis(LocalDate startDate, LocalDate endDate, Long technicianId) {
        Map<String, Object> analysis = new HashMap<>();
        
        // 模拟效率分析数据
        analysis.put("averageCompletionTime", 4.2);
        analysis.put("onTimeCompletionRate", 92.5);
        analysis.put("firstTimeFixRate", 87.3);
        analysis.put("customerSatisfaction", 4.6);
        analysis.put("productivityScore", 88.5);
        
        return analysis;
    }

    @Override
    public Map<String, Object> predictEquipmentFailure(Long equipmentId) {
        Map<String, Object> prediction = new HashMap<>();
        
        // 模拟故障预测数据
        prediction.put("riskLevel", "中等");
        prediction.put("failureProbability", 0.25);
        prediction.put("predictedFailureDate", LocalDate.now().plusDays(45));
        prediction.put("recommendedActions", Arrays.asList("增加检查频率", "更换易损件", "调整运行参数"));
        
        return prediction;
    }

    @Override
    public Map<String, Object> optimizeMaintenanceSchedule(Long equipmentId) {
        Map<String, Object> optimization = new HashMap<>();
        
        // 模拟优化建议
        optimization.put("currentSchedule", "每月一次");
        optimization.put("recommendedSchedule", "每6周一次");
        optimization.put("expectedSavings", 15000.0);
        optimization.put("riskReduction", 0.15);
        optimization.put("reasoning", "基于历史数据分析，延长维护间隔可降低成本且不增加故障风险");
        
        return optimization;
    }

    @Override
    public List<Map<String, Object>> getMaintenanceKnowledgeBase(String equipmentModel, String problemType) {
        List<Map<String, Object>> knowledge = new ArrayList<>();
        
        // 模拟知识库数据
        Map<String, Object> item1 = new HashMap<>();
        item1.put("title", "液压系统压力不足解决方案");
        item1.put("content", "检查液压油位、清洁滤芯、检查密封件");
        item1.put("difficulty", "中等");
        item1.put("estimatedTime", 2.5);
        knowledge.add(item1);
        
        return knowledge;
    }

    @Override
    @Transactional
    public boolean recordMaintenanceExperience(Long maintenanceId, Map<String, Object> experience) {
        // 实际应保存到知识库
        log.info("记录维护经验: {}", maintenanceId);
        return true;
    }

    @Override
    public Map<String, Object> generateMaintenanceReport(Long maintenanceId) {
        Map<String, Object> report = new HashMap<>();
        
        Maintenance maintenance = this.getById(maintenanceId);
        if (maintenance != null) {
            report.put("workOrderNo", maintenance.getWorkOrderNo());
            report.put("equipmentName", maintenance.getEquipmentName());
            report.put("maintenanceType", maintenance.getMaintenanceType());
            report.put("status", maintenance.getStatus());
            report.put("result", maintenance.getResult());
            report.put("totalCost", maintenance.getTotalCost());
            report.put("workItems", getMaintenanceWorkItems(maintenanceId));
            report.put("parts", getMaintenanceParts(maintenanceId));
        }
        
        return report;
    }

    @Override
    public Map<String, Object> syncRemoteEquipmentStatus(Long equipmentId) {
        Map<String, Object> status = new HashMap<>();
        
        // 模拟远程设备状态同步
        status.put("equipmentId", equipmentId);
        status.put("status", "运行中");
        status.put("temperature", 65.5);
        status.put("pressure", 8.2);
        status.put("vibration", 2.1);
        status.put("lastSyncTime", LocalDateTime.now());
        
        log.info("同步设备{}远程状态", equipmentId);
        return status;
    }

    @Override
    public Map<String, Object> remoteDiagnoseEquipment(Long equipmentId, String diagnosticType) {
        Map<String, Object> diagnosis = new HashMap<>();
        
        // 模拟远程诊断结果
        diagnosis.put("equipmentId", equipmentId);
        diagnosis.put("diagnosticType", diagnosticType);
        diagnosis.put("result", "正常");
        diagnosis.put("issues", Arrays.asList());
        diagnosis.put("recommendations", Arrays.asList("继续正常运行"));
        diagnosis.put("diagnosticTime", LocalDateTime.now());
        
        log.info("远程诊断设备{}: {}", equipmentId, diagnosticType);
        return diagnosis;
    }

    @Override
    public Map<String, Object> getMaintenanceQualityAssessment(Long technicianId, LocalDate startDate, LocalDate endDate) {
        Map<String, Object> assessment = new HashMap<>();
        
        // 模拟质量评估数据
        assessment.put("technicianId", technicianId);
        assessment.put("period", startDate + " 至 " + endDate);
        assessment.put("qualityScore", 88.5);
        assessment.put("customerSatisfaction", 4.6);
        assessment.put("firstTimeFixRate", 87.3);
        assessment.put("reworkRate", 5.2);
        assessment.put("safetyScore", 95.0);
        
        return assessment;
    }

    @Override
    public Map<String, Object> calculateEquipmentReliabilityMetrics(Long equipmentId, LocalDate startDate, LocalDate endDate) {
        Map<String, Object> metrics = new HashMap<>();
        
        // 模拟可靠性指标
        metrics.put("equipmentId", equipmentId);
        metrics.put("mtbf", 720.5); // 平均故障间隔时间(小时)
        metrics.put("mttr", 4.2);   // 平均修复时间(小时)
        metrics.put("availability", 98.5); // 可用性(%)
        metrics.put("reliability", 96.8);  // 可靠性(%)
        metrics.put("failureRate", 0.0014); // 故障率
        
        return metrics;
    }

    @Override
    public Map<String, Object> getSparePartsDemandForecast(Long equipmentId, Integer forecastPeriod) {
        Map<String, Object> forecast = new HashMap<>();
        
        // 模拟备件需求预测
        List<Map<String, Object>> parts = new ArrayList<>();
        
        Map<String, Object> part1 = new HashMap<>();
        part1.put("partName", "液压油");
        part1.put("currentStock", 50);
        part1.put("forecastDemand", 25);
        part1.put("recommendedOrder", 30);
        parts.add(part1);
        
        forecast.put("equipmentId", equipmentId);
        forecast.put("forecastPeriod", forecastPeriod + "个月");
        forecast.put("parts", parts);
        
        return forecast;
    }

    @Override
    public Map<String, Object> optimizeTechnicianScheduling(LocalDate date, String region) {
        Map<String, Object> scheduling = new HashMap<>();
        
        // 模拟调度优化方案
        List<Map<String, Object>> assignments = new ArrayList<>();
        
        Map<String, Object> assignment1 = new HashMap<>();
        assignment1.put("technicianName", "张师傅");
        assignment1.put("tasks", Arrays.asList("设备A维护", "设备B检查"));
        assignment1.put("estimatedTime", 6.5);
        assignment1.put("travelTime", 1.0);
        assignments.add(assignment1);
        
        scheduling.put("date", date);
        scheduling.put("region", region);
        scheduling.put("assignments", assignments);
        scheduling.put("efficiency", 92.5);
        
        return scheduling;
    }
}