{"ast": null, "code": "/**\n * 将多个单个参数的函数合成为一个函数，执行顺序为从右到左\n * @param fn 第一个函数\n * @param rest 剩余函数\n * @returns 复合后的函数\n */\nexport function compose(fn, ...rest) {\n  return rest.reduce((pre, cur) => x => pre(cur(x)), fn);\n}", "map": {"version": 3, "names": ["compose", "fn", "rest", "reduce", "pre", "cur", "x"], "sources": ["utils/compose.ts"], "sourcesContent": [null], "mappings": "AAAA;;;;;;AAMA,OAAM,SAAUA,OAAOA,CAAIC,EAAe,EAAE,GAAGC,IAAqB;EAClE,OAAOA,IAAI,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAMC,CAAC,IAAKF,GAAG,CAACC,GAAG,CAACC,CAAC,CAAC,CAAC,EAAEL,EAAE,CAAC;AAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}