{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar SPLIT = '__@field_split__';\n\n/**\n * Convert name path into string to fast the fetch speed of Map.\n */\nfunction normalize(namePath) {\n  return namePath.map(function (cell) {\n    return \"\".concat(_typeof(cell), \":\").concat(cell);\n  })\n  // Magic split\n  .join(SPLIT);\n}\n\n/**\n * NameMap like a `Map` but accepts `string[]` as key.\n */\nvar NameMap = /*#__PURE__*/function () {\n  function NameMap() {\n    _classCallCheck(this, NameMap);\n    _defineProperty(this, \"kvs\", new Map());\n  }\n  _createClass(NameMap, [{\n    key: \"set\",\n    value: function set(key, value) {\n      this.kvs.set(normalize(key), value);\n    }\n  }, {\n    key: \"get\",\n    value: function get(key) {\n      return this.kvs.get(normalize(key));\n    }\n  }, {\n    key: \"update\",\n    value: function update(key, updater) {\n      var origin = this.get(key);\n      var next = updater(origin);\n      if (!next) {\n        this.delete(key);\n      } else {\n        this.set(key, next);\n      }\n    }\n  }, {\n    key: \"delete\",\n    value: function _delete(key) {\n      this.kvs.delete(normalize(key));\n    }\n\n    // Since we only use this in test, let simply realize this\n  }, {\n    key: \"map\",\n    value: function map(callback) {\n      return _toConsumableArray(this.kvs.entries()).map(function (_ref) {\n        var _ref2 = _slicedToArray(_ref, 2),\n          key = _ref2[0],\n          value = _ref2[1];\n        var cells = key.split(SPLIT);\n        return callback({\n          key: cells.map(function (cell) {\n            var _cell$match = cell.match(/^([^:]*):(.*)$/),\n              _cell$match2 = _slicedToArray(_cell$match, 3),\n              type = _cell$match2[1],\n              unit = _cell$match2[2];\n            return type === 'number' ? Number(unit) : unit;\n          }),\n          value: value\n        });\n      });\n    }\n  }, {\n    key: \"toJSON\",\n    value: function toJSON() {\n      var json = {};\n      this.map(function (_ref3) {\n        var key = _ref3.key,\n          value = _ref3.value;\n        json[key.join('.')] = value;\n        return null;\n      });\n      return json;\n    }\n  }]);\n  return NameMap;\n}();\nexport default NameMap;", "map": {"version": 3, "names": ["_slicedToArray", "_toConsumableArray", "_classCallCheck", "_createClass", "_defineProperty", "_typeof", "SPLIT", "normalize", "namePath", "map", "cell", "concat", "join", "NameMap", "Map", "key", "value", "set", "kvs", "get", "update", "updater", "origin", "next", "delete", "_delete", "callback", "entries", "_ref", "_ref2", "cells", "split", "_cell$match", "match", "_cell$match2", "type", "unit", "Number", "toJSON", "json", "_ref3"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/rc-field-form/es/utils/NameMap.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar SPLIT = '__@field_split__';\n\n/**\n * Convert name path into string to fast the fetch speed of Map.\n */\nfunction normalize(namePath) {\n  return namePath.map(function (cell) {\n    return \"\".concat(_typeof(cell), \":\").concat(cell);\n  })\n  // Magic split\n  .join(SPLIT);\n}\n\n/**\n * NameMap like a `Map` but accepts `string[]` as key.\n */\nvar NameMap = /*#__PURE__*/function () {\n  function NameMap() {\n    _classCallCheck(this, NameMap);\n    _defineProperty(this, \"kvs\", new Map());\n  }\n  _createClass(NameMap, [{\n    key: \"set\",\n    value: function set(key, value) {\n      this.kvs.set(normalize(key), value);\n    }\n  }, {\n    key: \"get\",\n    value: function get(key) {\n      return this.kvs.get(normalize(key));\n    }\n  }, {\n    key: \"update\",\n    value: function update(key, updater) {\n      var origin = this.get(key);\n      var next = updater(origin);\n      if (!next) {\n        this.delete(key);\n      } else {\n        this.set(key, next);\n      }\n    }\n  }, {\n    key: \"delete\",\n    value: function _delete(key) {\n      this.kvs.delete(normalize(key));\n    }\n\n    // Since we only use this in test, let simply realize this\n  }, {\n    key: \"map\",\n    value: function map(callback) {\n      return _toConsumableArray(this.kvs.entries()).map(function (_ref) {\n        var _ref2 = _slicedToArray(_ref, 2),\n          key = _ref2[0],\n          value = _ref2[1];\n        var cells = key.split(SPLIT);\n        return callback({\n          key: cells.map(function (cell) {\n            var _cell$match = cell.match(/^([^:]*):(.*)$/),\n              _cell$match2 = _slicedToArray(_cell$match, 3),\n              type = _cell$match2[1],\n              unit = _cell$match2[2];\n            return type === 'number' ? Number(unit) : unit;\n          }),\n          value: value\n        });\n      });\n    }\n  }, {\n    key: \"toJSON\",\n    value: function toJSON() {\n      var json = {};\n      this.map(function (_ref3) {\n        var key = _ref3.key,\n          value = _ref3.value;\n        json[key.join('.')] = value;\n        return null;\n      });\n      return json;\n    }\n  }]);\n  return NameMap;\n}();\nexport default NameMap;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,IAAIC,KAAK,GAAG,kBAAkB;;AAE9B;AACA;AACA;AACA,SAASC,SAASA,CAACC,QAAQ,EAAE;EAC3B,OAAOA,QAAQ,CAACC,GAAG,CAAC,UAAUC,IAAI,EAAE;IAClC,OAAO,EAAE,CAACC,MAAM,CAACN,OAAO,CAACK,IAAI,CAAC,EAAE,GAAG,CAAC,CAACC,MAAM,CAACD,IAAI,CAAC;EACnD,CAAC;EACD;EAAA,CACCE,IAAI,CAACN,KAAK,CAAC;AACd;;AAEA;AACA;AACA;AACA,IAAIO,OAAO,GAAG,aAAa,YAAY;EACrC,SAASA,OAAOA,CAAA,EAAG;IACjBX,eAAe,CAAC,IAAI,EAAEW,OAAO,CAAC;IAC9BT,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,IAAIU,GAAG,CAAC,CAAC,CAAC;EACzC;EACAX,YAAY,CAACU,OAAO,EAAE,CAAC;IACrBE,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASC,GAAGA,CAACF,GAAG,EAAEC,KAAK,EAAE;MAC9B,IAAI,CAACE,GAAG,CAACD,GAAG,CAACV,SAAS,CAACQ,GAAG,CAAC,EAAEC,KAAK,CAAC;IACrC;EACF,CAAC,EAAE;IACDD,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASG,GAAGA,CAACJ,GAAG,EAAE;MACvB,OAAO,IAAI,CAACG,GAAG,CAACC,GAAG,CAACZ,SAAS,CAACQ,GAAG,CAAC,CAAC;IACrC;EACF,CAAC,EAAE;IACDA,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASI,MAAMA,CAACL,GAAG,EAAEM,OAAO,EAAE;MACnC,IAAIC,MAAM,GAAG,IAAI,CAACH,GAAG,CAACJ,GAAG,CAAC;MAC1B,IAAIQ,IAAI,GAAGF,OAAO,CAACC,MAAM,CAAC;MAC1B,IAAI,CAACC,IAAI,EAAE;QACT,IAAI,CAACC,MAAM,CAACT,GAAG,CAAC;MAClB,CAAC,MAAM;QACL,IAAI,CAACE,GAAG,CAACF,GAAG,EAAEQ,IAAI,CAAC;MACrB;IACF;EACF,CAAC,EAAE;IACDR,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASS,OAAOA,CAACV,GAAG,EAAE;MAC3B,IAAI,CAACG,GAAG,CAACM,MAAM,CAACjB,SAAS,CAACQ,GAAG,CAAC,CAAC;IACjC;;IAEA;EACF,CAAC,EAAE;IACDA,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASP,GAAGA,CAACiB,QAAQ,EAAE;MAC5B,OAAOzB,kBAAkB,CAAC,IAAI,CAACiB,GAAG,CAACS,OAAO,CAAC,CAAC,CAAC,CAAClB,GAAG,CAAC,UAAUmB,IAAI,EAAE;QAChE,IAAIC,KAAK,GAAG7B,cAAc,CAAC4B,IAAI,EAAE,CAAC,CAAC;UACjCb,GAAG,GAAGc,KAAK,CAAC,CAAC,CAAC;UACdb,KAAK,GAAGa,KAAK,CAAC,CAAC,CAAC;QAClB,IAAIC,KAAK,GAAGf,GAAG,CAACgB,KAAK,CAACzB,KAAK,CAAC;QAC5B,OAAOoB,QAAQ,CAAC;UACdX,GAAG,EAAEe,KAAK,CAACrB,GAAG,CAAC,UAAUC,IAAI,EAAE;YAC7B,IAAIsB,WAAW,GAAGtB,IAAI,CAACuB,KAAK,CAAC,gBAAgB,CAAC;cAC5CC,YAAY,GAAGlC,cAAc,CAACgC,WAAW,EAAE,CAAC,CAAC;cAC7CG,IAAI,GAAGD,YAAY,CAAC,CAAC,CAAC;cACtBE,IAAI,GAAGF,YAAY,CAAC,CAAC,CAAC;YACxB,OAAOC,IAAI,KAAK,QAAQ,GAAGE,MAAM,CAACD,IAAI,CAAC,GAAGA,IAAI;UAChD,CAAC,CAAC;UACFpB,KAAK,EAAEA;QACT,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDD,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASsB,MAAMA,CAAA,EAAG;MACvB,IAAIC,IAAI,GAAG,CAAC,CAAC;MACb,IAAI,CAAC9B,GAAG,CAAC,UAAU+B,KAAK,EAAE;QACxB,IAAIzB,GAAG,GAAGyB,KAAK,CAACzB,GAAG;UACjBC,KAAK,GAAGwB,KAAK,CAACxB,KAAK;QACrBuB,IAAI,CAACxB,GAAG,CAACH,IAAI,CAAC,GAAG,CAAC,CAAC,GAAGI,KAAK;QAC3B,OAAO,IAAI;MACb,CAAC,CAAC;MACF,OAAOuB,IAAI;IACb;EACF,CAAC,CAAC,CAAC;EACH,OAAO1B,OAAO;AAChB,CAAC,CAAC,CAAC;AACH,eAAeA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}