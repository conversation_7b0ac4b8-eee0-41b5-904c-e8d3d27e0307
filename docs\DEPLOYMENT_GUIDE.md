# 西安银马实业数字化管理系统 - 部署指南

## 概述

本文档提供了西安银马实业数字化管理系统的完整部署指南，包括开发环境、测试环境和生产环境的部署方案。

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx/CDN     │    │   Load Balancer │    │   Monitoring    │
│   (可选)        │    │   (可选)        │    │   (可选)        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   Prometheus    │
│   (React)       │    │   (Node.js)     │    │   + Grafana     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 ▼
                    ┌─────────────────┐
                    │   Database      │
                    │   PostgreSQL    │
                    │   + Redis       │
                    └─────────────────┘
```

## 环境要求

### 基础要求
- **操作系统**: Linux (推荐 Ubuntu 20.04+), Windows 10+, macOS 10.15+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **内存**: 最低 4GB，推荐 8GB+
- **存储**: 最低 20GB 可用空间
- **网络**: 稳定的互联网连接

### 端口要求
- **80**: 前端应用 (HTTP)
- **443**: 前端应用 (HTTPS，可选)
- **8080**: 后端API
- **5432**: PostgreSQL (内部)
- **6379**: Redis (内部)
- **9090**: Prometheus (可选)
- **3001**: Grafana (可选)

## 快速部署

### 1. 克隆项目

```bash
git clone <repository-url>
cd Link-YinMa
```

### 2. 配置环境变量

```bash
# 复制环境配置模板
cp .env.example .env          # 开发环境
cp .env.prod.example .env.prod # 生产环境

# 编辑配置文件
vim .env.prod
```

### 3. 部署服务

#### Linux/macOS
```bash
# 标准部署
./deploy-prod.sh

# 启用监控
./deploy-prod.sh --monitoring

# 启用Nginx和监控
./deploy-prod.sh --nginx --monitoring
```

#### Windows
```powershell
# 标准部署
.\deploy-prod.ps1

# 启用监控
.\deploy-prod.ps1 -Monitoring

# 启用Nginx和监控
.\deploy-prod.ps1 -Nginx -Monitoring
```

## 详细部署步骤

### 1. 环境准备

#### 1.1 安装Docker

**Ubuntu/Debian:**
```bash
# 更新包索引
sudo apt update

# 安装依赖
sudo apt install apt-transport-https ca-certificates curl gnupg lsb-release

# 添加Docker官方GPG密钥
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# 添加Docker仓库
echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# 安装Docker
sudo apt update
sudo apt install docker-ce docker-ce-cli containerd.io

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

**Windows:**
1. 下载并安装 [Docker Desktop for Windows](https://www.docker.com/products/docker-desktop)
2. 启动Docker Desktop
3. 确保WSL2后端已启用

#### 1.2 配置系统

```bash
# 创建应用用户
sudo useradd -m -s /bin/bash yinma
sudo usermod -aG docker yinma

# 创建应用目录
sudo mkdir -p /opt/yinma
sudo chown yinma:yinma /opt/yinma

# 切换到应用用户
sudo su - yinma
cd /opt/yinma
```

### 2. 项目配置

#### 2.1 环境变量配置

编辑 `.env.prod` 文件：

```bash
# 基础配置
NODE_ENV=production
PORT=8080
HOST=0.0.0.0

# 数据库配置
DB_HOST=postgres
DB_PORT=5432
DB_NAME=yinma_db
DB_USER=yinma_user
DB_PASSWORD=your_secure_password_here

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password_here

# JWT配置
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=24h

# 文件上传配置
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx

# 邮件配置
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password
```

#### 2.2 SSL证书配置（可选）

```bash
# 创建SSL目录
mkdir -p ssl

# 使用Let's Encrypt获取证书
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com

# 复制证书到项目目录
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem ssl/
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem ssl/
sudo chown yinma:yinma ssl/*
```

### 3. 数据库初始化

#### 3.1 启动数据库服务

```bash
# 仅启动数据库服务
docker-compose -f docker-compose.prod.yml up -d postgres redis

# 等待服务启动
sleep 30
```

#### 3.2 初始化数据库

```bash
# 连接到数据库容器
docker-compose -f docker-compose.prod.yml exec postgres psql -U yinma_user -d yinma_db

# 执行初始化脚本（如果有）
\i /docker-entrypoint-initdb.d/init.sql
```

### 4. 应用部署

#### 4.1 构建镜像

```bash
# 构建所有镜像
docker-compose -f docker-compose.prod.yml build

# 或分别构建
docker-compose -f docker-compose.prod.yml build backend
docker-compose -f docker-compose.prod.yml build frontend
```

#### 4.2 启动服务

```bash
# 启动所有服务
docker-compose -f docker-compose.prod.yml up -d

# 启用监控服务
docker-compose -f docker-compose.prod.yml --profile monitoring up -d

# 启用Nginx
docker-compose -f docker-compose.prod.yml --profile nginx up -d
```

#### 4.3 验证部署

```bash
# 检查服务状态
docker-compose -f docker-compose.prod.yml ps

# 检查服务日志
docker-compose -f docker-compose.prod.yml logs

# 健康检查
curl http://localhost:8080/health
curl http://localhost:80
```

## 监控配置

### 1. Prometheus配置

创建 `monitoring/prometheus.yml`：

```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'yinma-backend'
    static_configs:
      - targets: ['backend:8080']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'yinma-postgres'
    static_configs:
      - targets: ['postgres:5432']

  - job_name: 'yinma-redis'
    static_configs:
      - targets: ['redis:6379']

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

### 2. Grafana配置

访问 `http://localhost:3001`，使用默认账号 `admin/admin123` 登录。

导入预配置的仪表板：
- Node.js应用监控
- PostgreSQL监控
- Redis监控
- 系统资源监控

## 备份策略

### 1. 数据库备份

```bash
# 创建备份脚本
cat > backup-db.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/opt/yinma/backup"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份PostgreSQL
docker-compose -f docker-compose.prod.yml exec -T postgres pg_dump -U yinma_user yinma_db > $BACKUP_DIR/db_$TIMESTAMP.sql

# 压缩备份文件
gzip $BACKUP_DIR/db_$TIMESTAMP.sql

# 删除7天前的备份
find $BACKUP_DIR -name "db_*.sql.gz" -mtime +7 -delete

echo "Database backup completed: $BACKUP_DIR/db_$TIMESTAMP.sql.gz"
EOF

chmod +x backup-db.sh
```

### 2. 文件备份

```bash
# 创建文件备份脚本
cat > backup-files.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/opt/yinma/backup"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# 备份上传文件
tar -czf $BACKUP_DIR/uploads_$TIMESTAMP.tar.gz uploads/

# 备份配置文件
tar -czf $BACKUP_DIR/config_$TIMESTAMP.tar.gz .env.prod docker-compose.prod.yml

# 删除7天前的备份
find $BACKUP_DIR -name "uploads_*.tar.gz" -mtime +7 -delete
find $BACKUP_DIR -name "config_*.tar.gz" -mtime +7 -delete

echo "Files backup completed"
EOF

chmod +x backup-files.sh
```

### 3. 自动备份

```bash
# 添加到crontab
crontab -e

# 每天凌晨2点备份数据库
0 2 * * * /opt/yinma/backup-db.sh

# 每天凌晨3点备份文件
0 3 * * * /opt/yinma/backup-files.sh
```

## 运维管理

### 1. 日常维护命令

```bash
# 查看服务状态
docker-compose -f docker-compose.prod.yml ps

# 查看服务日志
docker-compose -f docker-compose.prod.yml logs -f

# 重启服务
docker-compose -f docker-compose.prod.yml restart

# 更新服务
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d

# 清理未使用的镜像
docker image prune -f

# 查看资源使用情况
docker stats
```

### 2. 性能优化

#### 2.1 数据库优化

```sql
-- 创建索引
CREATE INDEX CONCURRENTLY idx_users_email ON users(email);
CREATE INDEX CONCURRENTLY idx_orders_created_at ON orders(created_at);

-- 分析表统计信息
ANALYZE;

-- 清理无用数据
VACUUM ANALYZE;
```

#### 2.2 应用优化

```bash
# 调整Docker资源限制
# 在docker-compose.prod.yml中添加：
deploy:
  resources:
    limits:
      cpus: '2.0'
      memory: 2G
    reservations:
      cpus: '1.0'
      memory: 1G
```

### 3. 故障排查

#### 3.1 常见问题

**服务无法启动：**
```bash
# 检查端口占用
netstat -tlnp | grep :8080

# 检查磁盘空间
df -h

# 检查内存使用
free -h

# 查看详细错误日志
docker-compose -f docker-compose.prod.yml logs backend
```

**数据库连接失败：**
```bash
# 检查数据库服务状态
docker-compose -f docker-compose.prod.yml exec postgres pg_isready

# 检查网络连接
docker-compose -f docker-compose.prod.yml exec backend ping postgres

# 验证数据库配置
docker-compose -f docker-compose.prod.yml exec postgres psql -U yinma_user -d yinma_db -c "SELECT 1;"
```

**性能问题：**
```bash
# 监控资源使用
docker stats --no-stream

# 分析慢查询
docker-compose -f docker-compose.prod.yml exec postgres psql -U yinma_user -d yinma_db -c "SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;"
```

#### 3.2 日志分析

```bash
# 实时查看应用日志
docker-compose -f docker-compose.prod.yml logs -f backend

# 查看错误日志
docker-compose -f docker-compose.prod.yml logs backend | grep ERROR

# 分析访问日志
docker-compose -f docker-compose.prod.yml logs nginx | grep -E "(4[0-9]{2}|5[0-9]{2})"
```

## 安全配置

### 1. 防火墙配置

```bash
# Ubuntu UFW
sudo ufw enable
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw deny 8080/tcp  # 仅内部访问
sudo ufw deny 5432/tcp  # 仅内部访问
sudo ufw deny 6379/tcp  # 仅内部访问
```

### 2. SSL/TLS配置

更新Nginx配置以启用HTTPS：

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /etc/ssl/certs/fullchain.pem;
    ssl_certificate_key /etc/ssl/private/privkey.pem;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    location / {
        proxy_pass http://frontend:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 3. 定期更新

```bash
# 创建更新脚本
cat > update-system.sh << 'EOF'
#!/bin/bash

# 更新系统包
sudo apt update && sudo apt upgrade -y

# 更新Docker镜像
docker-compose -f docker-compose.prod.yml pull

# 重启服务
docker-compose -f docker-compose.prod.yml up -d

# 清理旧镜像
docker image prune -f

echo "System update completed"
EOF

chmod +x update-system.sh
```

## 扩展部署

### 1. 负载均衡

使用Nginx作为负载均衡器：

```nginx
upstream backend {
    server backend1:8080;
    server backend2:8080;
    server backend3:8080;
}

server {
    listen 80;
    
    location /api {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 2. 数据库集群

配置PostgreSQL主从复制：

```yaml
# docker-compose.cluster.yml
services:
  postgres-master:
    image: postgres:15
    environment:
      POSTGRES_REPLICATION_MODE: master
      POSTGRES_REPLICATION_USER: replicator
      POSTGRES_REPLICATION_PASSWORD: replicator_password
  
  postgres-slave:
    image: postgres:15
    environment:
      POSTGRES_REPLICATION_MODE: slave
      POSTGRES_MASTER_HOST: postgres-master
      POSTGRES_REPLICATION_USER: replicator
      POSTGRES_REPLICATION_PASSWORD: replicator_password
```

### 3. 容器编排

使用Kubernetes部署：

```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: yinma-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: yinma-backend
  template:
    metadata:
      labels:
        app: yinma-backend
    spec:
      containers:
      - name: backend
        image: yinma/backend:latest
        ports:
        - containerPort: 8080
        env:
        - name: DB_HOST
          value: postgres-service
```

## 联系支持

如果在部署过程中遇到问题，请联系技术支持：

- **邮箱**: <EMAIL>
- **电话**: 029-12345678
- **文档**: [在线文档](https://docs.yinma.com)
- **问题反馈**: [GitHub Issues](https://github.com/yinma/issues)

---

**版本**: v1.0.0  
**更新时间**: 2024年12月  
**维护者**: 西安银马实业技术团队