{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport Portal from '@rc-component/portal';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport * as React from 'react';\nimport { RefContext } from \"./context\";\nimport DrawerPopup from \"./DrawerPopup\";\nimport { warnCheck } from \"./util\";\nvar Drawer = function Drawer(props) {\n  var _props$open = props.open,\n    open = _props$open === void 0 ? false : _props$open,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-drawer' : _props$prefixCls,\n    _props$placement = props.placement,\n    placement = _props$placement === void 0 ? 'right' : _props$placement,\n    _props$autoFocus = props.autoFocus,\n    autoFocus = _props$autoFocus === void 0 ? true : _props$autoFocus,\n    _props$keyboard = props.keyboard,\n    keyboard = _props$keyboard === void 0 ? true : _props$keyboard,\n    _props$width = props.width,\n    width = _props$width === void 0 ? 378 : _props$width,\n    _props$mask = props.mask,\n    mask = _props$mask === void 0 ? true : _props$mask,\n    _props$maskClosable = props.maskClosable,\n    maskClosable = _props$maskClosable === void 0 ? true : _props$maskClosable,\n    getContainer = props.getContainer,\n    forceRender = props.forceRender,\n    afterOpenChange = props.afterOpenChange,\n    destroyOnClose = props.destroyOnClose,\n    onMouseEnter = props.onMouseEnter,\n    onMouseOver = props.onMouseOver,\n    onMouseLeave = props.onMouseLeave,\n    onClick = props.onClick,\n    onKeyDown = props.onKeyDown,\n    onKeyUp = props.onKeyUp,\n    panelRef = props.panelRef;\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    animatedVisible = _React$useState2[0],\n    setAnimatedVisible = _React$useState2[1];\n\n  // ============================= Warn =============================\n  if (process.env.NODE_ENV !== 'production') {\n    warnCheck(props);\n  }\n\n  // ============================= Open =============================\n  var _React$useState3 = React.useState(false),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    mounted = _React$useState4[0],\n    setMounted = _React$useState4[1];\n  useLayoutEffect(function () {\n    setMounted(true);\n  }, []);\n  var mergedOpen = mounted ? open : false;\n\n  // ============================ Focus =============================\n  var popupRef = React.useRef();\n  var lastActiveRef = React.useRef();\n  useLayoutEffect(function () {\n    if (mergedOpen) {\n      lastActiveRef.current = document.activeElement;\n    }\n  }, [mergedOpen]);\n\n  // ============================= Open =============================\n  var internalAfterOpenChange = function internalAfterOpenChange(nextVisible) {\n    var _popupRef$current;\n    setAnimatedVisible(nextVisible);\n    afterOpenChange === null || afterOpenChange === void 0 || afterOpenChange(nextVisible);\n    if (!nextVisible && lastActiveRef.current && !((_popupRef$current = popupRef.current) !== null && _popupRef$current !== void 0 && _popupRef$current.contains(lastActiveRef.current))) {\n      var _lastActiveRef$curren;\n      (_lastActiveRef$curren = lastActiveRef.current) === null || _lastActiveRef$curren === void 0 || _lastActiveRef$curren.focus({\n        preventScroll: true\n      });\n    }\n  };\n\n  // =========================== Context ============================\n  var refContext = React.useMemo(function () {\n    return {\n      panel: panelRef\n    };\n  }, [panelRef]);\n\n  // ============================ Render ============================\n  if (!forceRender && !animatedVisible && !mergedOpen && destroyOnClose) {\n    return null;\n  }\n  var eventHandlers = {\n    onMouseEnter: onMouseEnter,\n    onMouseOver: onMouseOver,\n    onMouseLeave: onMouseLeave,\n    onClick: onClick,\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp\n  };\n  var drawerPopupProps = _objectSpread(_objectSpread({}, props), {}, {\n    open: mergedOpen,\n    prefixCls: prefixCls,\n    placement: placement,\n    autoFocus: autoFocus,\n    keyboard: keyboard,\n    width: width,\n    mask: mask,\n    maskClosable: maskClosable,\n    inline: getContainer === false,\n    afterOpenChange: internalAfterOpenChange,\n    ref: popupRef\n  }, eventHandlers);\n  return /*#__PURE__*/React.createElement(RefContext.Provider, {\n    value: refContext\n  }, /*#__PURE__*/React.createElement(Portal, {\n    open: mergedOpen || forceRender || animatedVisible,\n    autoDestroy: false,\n    getContainer: getContainer,\n    autoLock: mask && (mergedOpen || animatedVisible)\n  }, /*#__PURE__*/React.createElement(DrawerPopup, drawerPopupProps)));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Drawer.displayName = 'Drawer';\n}\nexport default Drawer;", "map": {"version": 3, "names": ["_objectSpread", "_slicedToArray", "Portal", "useLayoutEffect", "React", "RefContext", "Drawer<PERSON><PERSON><PERSON>", "warn<PERSON>heck", "Drawer", "props", "_props$open", "open", "_props$prefixCls", "prefixCls", "_props$placement", "placement", "_props$autoFocus", "autoFocus", "_props$keyboard", "keyboard", "_props$width", "width", "_props$mask", "mask", "_props$maskClosable", "maskClosable", "getContainer", "forceRender", "afterOpenChange", "destroyOnClose", "onMouseEnter", "onMouseOver", "onMouseLeave", "onClick", "onKeyDown", "onKeyUp", "panelRef", "_React$useState", "useState", "_React$useState2", "animatedVisible", "setAnimatedVisible", "process", "env", "NODE_ENV", "_React$useState3", "_React$useState4", "mounted", "setMounted", "mergedOpen", "popupRef", "useRef", "lastActiveRef", "current", "document", "activeElement", "internalAfterOpenChange", "nextVisible", "_popupRef$current", "contains", "_lastActiveRef$curren", "focus", "preventScroll", "refContext", "useMemo", "panel", "eventHandlers", "drawerPopupProps", "inline", "ref", "createElement", "Provider", "value", "autoDestroy", "autoLock", "displayName"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/rc-drawer/es/Drawer.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport Portal from '@rc-component/portal';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport * as React from 'react';\nimport { RefContext } from \"./context\";\nimport DrawerPopup from \"./DrawerPopup\";\nimport { warnCheck } from \"./util\";\nvar Drawer = function Drawer(props) {\n  var _props$open = props.open,\n    open = _props$open === void 0 ? false : _props$open,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-drawer' : _props$prefixCls,\n    _props$placement = props.placement,\n    placement = _props$placement === void 0 ? 'right' : _props$placement,\n    _props$autoFocus = props.autoFocus,\n    autoFocus = _props$autoFocus === void 0 ? true : _props$autoFocus,\n    _props$keyboard = props.keyboard,\n    keyboard = _props$keyboard === void 0 ? true : _props$keyboard,\n    _props$width = props.width,\n    width = _props$width === void 0 ? 378 : _props$width,\n    _props$mask = props.mask,\n    mask = _props$mask === void 0 ? true : _props$mask,\n    _props$maskClosable = props.maskClosable,\n    maskClosable = _props$maskClosable === void 0 ? true : _props$maskClosable,\n    getContainer = props.getContainer,\n    forceRender = props.forceRender,\n    afterOpenChange = props.afterOpenChange,\n    destroyOnClose = props.destroyOnClose,\n    onMouseEnter = props.onMouseEnter,\n    onMouseOver = props.onMouseOver,\n    onMouseLeave = props.onMouseLeave,\n    onClick = props.onClick,\n    onKeyDown = props.onKeyDown,\n    onKeyUp = props.onKeyUp,\n    panelRef = props.panelRef;\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    animatedVisible = _React$useState2[0],\n    setAnimatedVisible = _React$useState2[1];\n\n  // ============================= Warn =============================\n  if (process.env.NODE_ENV !== 'production') {\n    warnCheck(props);\n  }\n\n  // ============================= Open =============================\n  var _React$useState3 = React.useState(false),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    mounted = _React$useState4[0],\n    setMounted = _React$useState4[1];\n  useLayoutEffect(function () {\n    setMounted(true);\n  }, []);\n  var mergedOpen = mounted ? open : false;\n\n  // ============================ Focus =============================\n  var popupRef = React.useRef();\n  var lastActiveRef = React.useRef();\n  useLayoutEffect(function () {\n    if (mergedOpen) {\n      lastActiveRef.current = document.activeElement;\n    }\n  }, [mergedOpen]);\n\n  // ============================= Open =============================\n  var internalAfterOpenChange = function internalAfterOpenChange(nextVisible) {\n    var _popupRef$current;\n    setAnimatedVisible(nextVisible);\n    afterOpenChange === null || afterOpenChange === void 0 || afterOpenChange(nextVisible);\n    if (!nextVisible && lastActiveRef.current && !((_popupRef$current = popupRef.current) !== null && _popupRef$current !== void 0 && _popupRef$current.contains(lastActiveRef.current))) {\n      var _lastActiveRef$curren;\n      (_lastActiveRef$curren = lastActiveRef.current) === null || _lastActiveRef$curren === void 0 || _lastActiveRef$curren.focus({\n        preventScroll: true\n      });\n    }\n  };\n\n  // =========================== Context ============================\n  var refContext = React.useMemo(function () {\n    return {\n      panel: panelRef\n    };\n  }, [panelRef]);\n\n  // ============================ Render ============================\n  if (!forceRender && !animatedVisible && !mergedOpen && destroyOnClose) {\n    return null;\n  }\n  var eventHandlers = {\n    onMouseEnter: onMouseEnter,\n    onMouseOver: onMouseOver,\n    onMouseLeave: onMouseLeave,\n    onClick: onClick,\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp\n  };\n  var drawerPopupProps = _objectSpread(_objectSpread({}, props), {}, {\n    open: mergedOpen,\n    prefixCls: prefixCls,\n    placement: placement,\n    autoFocus: autoFocus,\n    keyboard: keyboard,\n    width: width,\n    mask: mask,\n    maskClosable: maskClosable,\n    inline: getContainer === false,\n    afterOpenChange: internalAfterOpenChange,\n    ref: popupRef\n  }, eventHandlers);\n  return /*#__PURE__*/React.createElement(RefContext.Provider, {\n    value: refContext\n  }, /*#__PURE__*/React.createElement(Portal, {\n    open: mergedOpen || forceRender || animatedVisible,\n    autoDestroy: false,\n    getContainer: getContainer,\n    autoLock: mask && (mergedOpen || animatedVisible)\n  }, /*#__PURE__*/React.createElement(DrawerPopup, drawerPopupProps)));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Drawer.displayName = 'Drawer';\n}\nexport default Drawer;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,WAAW;AACtC,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,SAAS,QAAQ,QAAQ;AAClC,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,KAAK,EAAE;EAClC,IAAIC,WAAW,GAAGD,KAAK,CAACE,IAAI;IAC1BA,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,WAAW;IACnDE,gBAAgB,GAAGH,KAAK,CAACI,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,WAAW,GAAGA,gBAAgB;IACxEE,gBAAgB,GAAGL,KAAK,CAACM,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,OAAO,GAAGA,gBAAgB;IACpEE,gBAAgB,GAAGP,KAAK,CAACQ,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,gBAAgB;IACjEE,eAAe,GAAGT,KAAK,CAACU,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,eAAe;IAC9DE,YAAY,GAAGX,KAAK,CAACY,KAAK;IAC1BA,KAAK,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,YAAY;IACpDE,WAAW,GAAGb,KAAK,CAACc,IAAI;IACxBA,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,WAAW;IAClDE,mBAAmB,GAAGf,KAAK,CAACgB,YAAY;IACxCA,YAAY,GAAGD,mBAAmB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,mBAAmB;IAC1EE,YAAY,GAAGjB,KAAK,CAACiB,YAAY;IACjCC,WAAW,GAAGlB,KAAK,CAACkB,WAAW;IAC/BC,eAAe,GAAGnB,KAAK,CAACmB,eAAe;IACvCC,cAAc,GAAGpB,KAAK,CAACoB,cAAc;IACrCC,YAAY,GAAGrB,KAAK,CAACqB,YAAY;IACjCC,WAAW,GAAGtB,KAAK,CAACsB,WAAW;IAC/BC,YAAY,GAAGvB,KAAK,CAACuB,YAAY;IACjCC,OAAO,GAAGxB,KAAK,CAACwB,OAAO;IACvBC,SAAS,GAAGzB,KAAK,CAACyB,SAAS;IAC3BC,OAAO,GAAG1B,KAAK,CAAC0B,OAAO;IACvBC,QAAQ,GAAG3B,KAAK,CAAC2B,QAAQ;EAC3B,IAAIC,eAAe,GAAGjC,KAAK,CAACkC,QAAQ,CAAC,KAAK,CAAC;IACzCC,gBAAgB,GAAGtC,cAAc,CAACoC,eAAe,EAAE,CAAC,CAAC;IACrDG,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACrCE,kBAAkB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;;EAE1C;EACA,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCrC,SAAS,CAACE,KAAK,CAAC;EAClB;;EAEA;EACA,IAAIoC,gBAAgB,GAAGzC,KAAK,CAACkC,QAAQ,CAAC,KAAK,CAAC;IAC1CQ,gBAAgB,GAAG7C,cAAc,CAAC4C,gBAAgB,EAAE,CAAC,CAAC;IACtDE,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7BE,UAAU,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAClC3C,eAAe,CAAC,YAAY;IAC1B6C,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EACN,IAAIC,UAAU,GAAGF,OAAO,GAAGpC,IAAI,GAAG,KAAK;;EAEvC;EACA,IAAIuC,QAAQ,GAAG9C,KAAK,CAAC+C,MAAM,CAAC,CAAC;EAC7B,IAAIC,aAAa,GAAGhD,KAAK,CAAC+C,MAAM,CAAC,CAAC;EAClChD,eAAe,CAAC,YAAY;IAC1B,IAAI8C,UAAU,EAAE;MACdG,aAAa,CAACC,OAAO,GAAGC,QAAQ,CAACC,aAAa;IAChD;EACF,CAAC,EAAE,CAACN,UAAU,CAAC,CAAC;;EAEhB;EACA,IAAIO,uBAAuB,GAAG,SAASA,uBAAuBA,CAACC,WAAW,EAAE;IAC1E,IAAIC,iBAAiB;IACrBjB,kBAAkB,CAACgB,WAAW,CAAC;IAC/B7B,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,IAAIA,eAAe,CAAC6B,WAAW,CAAC;IACtF,IAAI,CAACA,WAAW,IAAIL,aAAa,CAACC,OAAO,IAAI,EAAE,CAACK,iBAAiB,GAAGR,QAAQ,CAACG,OAAO,MAAM,IAAI,IAAIK,iBAAiB,KAAK,KAAK,CAAC,IAAIA,iBAAiB,CAACC,QAAQ,CAACP,aAAa,CAACC,OAAO,CAAC,CAAC,EAAE;MACpL,IAAIO,qBAAqB;MACzB,CAACA,qBAAqB,GAAGR,aAAa,CAACC,OAAO,MAAM,IAAI,IAAIO,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACC,KAAK,CAAC;QAC1HC,aAAa,EAAE;MACjB,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,IAAIC,UAAU,GAAG3D,KAAK,CAAC4D,OAAO,CAAC,YAAY;IACzC,OAAO;MACLC,KAAK,EAAE7B;IACT,CAAC;EACH,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;;EAEd;EACA,IAAI,CAACT,WAAW,IAAI,CAACa,eAAe,IAAI,CAACS,UAAU,IAAIpB,cAAc,EAAE;IACrE,OAAO,IAAI;EACb;EACA,IAAIqC,aAAa,GAAG;IAClBpC,YAAY,EAAEA,YAAY;IAC1BC,WAAW,EAAEA,WAAW;IACxBC,YAAY,EAAEA,YAAY;IAC1BC,OAAO,EAAEA,OAAO;IAChBC,SAAS,EAAEA,SAAS;IACpBC,OAAO,EAAEA;EACX,CAAC;EACD,IAAIgC,gBAAgB,GAAGnE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAES,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IACjEE,IAAI,EAAEsC,UAAU;IAChBpC,SAAS,EAAEA,SAAS;IACpBE,SAAS,EAAEA,SAAS;IACpBE,SAAS,EAAEA,SAAS;IACpBE,QAAQ,EAAEA,QAAQ;IAClBE,KAAK,EAAEA,KAAK;IACZE,IAAI,EAAEA,IAAI;IACVE,YAAY,EAAEA,YAAY;IAC1B2C,MAAM,EAAE1C,YAAY,KAAK,KAAK;IAC9BE,eAAe,EAAE4B,uBAAuB;IACxCa,GAAG,EAAEnB;EACP,CAAC,EAAEgB,aAAa,CAAC;EACjB,OAAO,aAAa9D,KAAK,CAACkE,aAAa,CAACjE,UAAU,CAACkE,QAAQ,EAAE;IAC3DC,KAAK,EAAET;EACT,CAAC,EAAE,aAAa3D,KAAK,CAACkE,aAAa,CAACpE,MAAM,EAAE;IAC1CS,IAAI,EAAEsC,UAAU,IAAItB,WAAW,IAAIa,eAAe;IAClDiC,WAAW,EAAE,KAAK;IAClB/C,YAAY,EAAEA,YAAY;IAC1BgD,QAAQ,EAAEnD,IAAI,KAAK0B,UAAU,IAAIT,eAAe;EAClD,CAAC,EAAE,aAAapC,KAAK,CAACkE,aAAa,CAAChE,WAAW,EAAE6D,gBAAgB,CAAC,CAAC,CAAC;AACtE,CAAC;AACD,IAAIzB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCpC,MAAM,CAACmE,WAAW,GAAG,QAAQ;AAC/B;AACA,eAAenE,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}