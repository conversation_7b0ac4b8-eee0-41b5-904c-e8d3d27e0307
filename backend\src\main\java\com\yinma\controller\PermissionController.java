package com.yinma.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yinma.common.result.Result;
import com.yinma.dto.PermissionDTO;
import com.yinma.service.PermissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 权限管理控制器
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/permission")
@RequiredArgsConstructor
@Validated
@Tag(name = "权限管理", description = "权限信息的增删改查操作")
public class PermissionController {

    private final PermissionService permissionService;

    @GetMapping("/page")
    @Operation(summary = "分页查询权限列表", description = "根据条件分页查询权限信息")
    @PreAuthorize("hasAuthority('permission:query')")
    public Result<IPage<PermissionDTO>> selectPermissionPage(@Valid PermissionDTO.PermissionQueryDTO queryDTO) {
        try {
            IPage<PermissionDTO> page = permissionService.selectPermissionPage(queryDTO);
            return Result.success(page);
        } catch (Exception e) {
            log.error("分页查询权限列表失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/{permissionId}")
    @Operation(summary = "查询权限详情", description = "根据权限ID查询权限详细信息")
    @PreAuthorize("hasAuthority('permission:query')")
    public Result<PermissionDTO> selectPermissionById(
            @Parameter(description = "权限ID", required = true)
            @PathVariable @NotNull Long permissionId) {
        try {
            PermissionDTO permission = permissionService.selectPermissionById(permissionId);
            if (permission == null) {
                return Result.error("权限不存在");
            }
            return Result.success(permission);
        } catch (Exception e) {
            log.error("查询权限详情失败, permissionId: {}", permissionId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/code/{permissionCode}")
    @Operation(summary = "根据编码查询权限", description = "根据权限编码查询权限信息")
    @PreAuthorize("hasAuthority('permission:query')")
    public Result<PermissionDTO> selectPermissionByCode(
            @Parameter(description = "权限编码", required = true)
            @PathVariable @NotEmpty String permissionCode) {
        try {
            PermissionDTO permission = permissionService.selectPermissionByCode(permissionCode);
            if (permission == null) {
                return Result.error("权限不存在");
            }
            return Result.success(permission);
        } catch (Exception e) {
            log.error("根据编码查询权限失败, permissionCode: {}", permissionCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @PostMapping
    @Operation(summary = "创建权限", description = "创建新的权限信息")
    @PreAuthorize("hasAuthority('permission:create')")
    public Result<Long> createPermission(@Valid @RequestBody PermissionDTO.PermissionCreateDTO createDTO) {
        try {
            Long permissionId = permissionService.createPermission(createDTO);
            return Result.success(permissionId, "权限创建成功");
        } catch (Exception e) {
            log.error("创建权限失败", e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    @PutMapping
    @Operation(summary = "更新权限", description = "更新权限信息")
    @PreAuthorize("hasAuthority('permission:update')")
    public Result<Boolean> updatePermission(@Valid @RequestBody PermissionDTO.PermissionUpdateDTO updateDTO) {
        try {
            Boolean result = permissionService.updatePermission(updateDTO);
            return Result.success(result, "权限更新成功");
        } catch (Exception e) {
            log.error("更新权限失败", e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/{permissionId}")
    @Operation(summary = "删除权限", description = "根据权限ID删除权限")
    @PreAuthorize("hasAuthority('permission:delete')")
    public Result<Boolean> deletePermission(
            @Parameter(description = "权限ID", required = true)
            @PathVariable @NotNull Long permissionId) {
        try {
            Boolean result = permissionService.deletePermission(permissionId);
            return Result.success(result, "权限删除成功");
        } catch (Exception e) {
            log.error("删除权限失败, permissionId: {}", permissionId, e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/batch")
    @Operation(summary = "批量删除权限", description = "根据权限ID列表批量删除权限")
    @PreAuthorize("hasAuthority('permission:delete')")
    public Result<Boolean> batchDeletePermissions(
            @Parameter(description = "权限ID列表", required = true)
            @RequestBody @NotEmpty List<Long> permissionIds) {
        try {
            Boolean result = permissionService.batchDeletePermissions(permissionIds);
            return Result.success(result, "批量删除成功");
        } catch (Exception e) {
            log.error("批量删除权限失败, permissionIds: {}", permissionIds, e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }

    @PutMapping("/{permissionId}/enable")
    @Operation(summary = "启用权限", description = "启用指定权限")
    @PreAuthorize("hasAuthority('permission:update')")
    public Result<Boolean> enablePermission(
            @Parameter(description = "权限ID", required = true)
            @PathVariable @NotNull Long permissionId) {
        try {
            Boolean result = permissionService.enablePermission(permissionId);
            return Result.success(result, "权限启用成功");
        } catch (Exception e) {
            log.error("启用权限失败, permissionId: {}", permissionId, e);
            return Result.error("启用失败: " + e.getMessage());
        }
    }

    @PutMapping("/{permissionId}/disable")
    @Operation(summary = "禁用权限", description = "禁用指定权限")
    @PreAuthorize("hasAuthority('permission:update')")
    public Result<Boolean> disablePermission(
            @Parameter(description = "权限ID", required = true)
            @PathVariable @NotNull Long permissionId) {
        try {
            Boolean result = permissionService.disablePermission(permissionId);
            return Result.success(result, "权限禁用成功");
        } catch (Exception e) {
            log.error("禁用权限失败, permissionId: {}", permissionId, e);
            return Result.error("禁用失败: " + e.getMessage());
        }
    }

    @PutMapping("/batch/status")
    @Operation(summary = "批量更新权限状态", description = "批量更新权限状态")
    @PreAuthorize("hasAuthority('permission:update')")
    public Result<Boolean> batchUpdatePermissionStatus(
            @Valid @RequestBody PermissionDTO.BatchStatusUpdateDTO updateDTO) {
        try {
            Boolean result = permissionService.batchUpdatePermissionStatus(updateDTO.getPermissionIds(), updateDTO.getStatus());
            return Result.success(result, "批量更新状态成功");
        } catch (Exception e) {
            log.error("批量更新权限状态失败", e);
            return Result.error("批量更新失败: " + e.getMessage());
        }
    }

    @GetMapping("/check-code")
    @Operation(summary = "检查权限编码是否存在", description = "检查权限编码是否已被使用")
    public Result<Boolean> checkPermissionCodeExists(
            @Parameter(description = "权限编码", required = true)
            @RequestParam @NotEmpty String permissionCode,
            @Parameter(description = "排除的权限ID")
            @RequestParam(required = false) Long excludePermissionId) {
        try {
            Boolean exists = permissionService.checkPermissionCodeExists(permissionCode, excludePermissionId);
            return Result.success(exists);
        } catch (Exception e) {
            log.error("检查权限编码失败, permissionCode: {}", permissionCode, e);
            return Result.error("检查失败: " + e.getMessage());
        }
    }

    @GetMapping("/check-name")
    @Operation(summary = "检查权限名称是否存在", description = "检查权限名称是否已被使用")
    public Result<Boolean> checkPermissionNameExists(
            @Parameter(description = "权限名称", required = true)
            @RequestParam @NotEmpty String permissionName,
            @Parameter(description = "排除的权限ID")
            @RequestParam(required = false) Long excludePermissionId) {
        try {
            Boolean exists = permissionService.checkPermissionNameExists(permissionName, excludePermissionId);
            return Result.success(exists);
        } catch (Exception e) {
            log.error("检查权限名称失败, permissionName: {}", permissionName, e);
            return Result.error("检查失败: " + e.getMessage());
        }
    }

    @GetMapping("/tree")
    @Operation(summary = "查询权限树", description = "查询权限树形结构")
    @PreAuthorize("hasAuthority('permission:query')")
    public Result<List<PermissionDTO.PermissionTreeDTO>> selectPermissionTree(
            @Parameter(description = "是否只查询启用的权限")
            @RequestParam(defaultValue = "false") Boolean enabledOnly) {
        try {
            List<PermissionDTO.PermissionTreeDTO> tree = permissionService.selectPermissionTree(enabledOnly);
            return Result.success(tree);
        } catch (Exception e) {
            log.error("查询权限树失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/parent/{parentId}")
    @Operation(summary = "查询子权限", description = "根据父权限ID查询子权限列表")
    @PreAuthorize("hasAuthority('permission:query')")
    public Result<List<PermissionDTO>> selectChildPermissions(
            @Parameter(description = "父权限ID", required = true)
            @PathVariable @NotNull Long parentId) {
        try {
            List<PermissionDTO> permissions = permissionService.selectChildPermissions(parentId);
            return Result.success(permissions);
        } catch (Exception e) {
            log.error("查询子权限失败, parentId: {}", parentId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/user/{userId}")
    @Operation(summary = "查询用户权限", description = "查询用户拥有的权限列表")
    @PreAuthorize("hasAuthority('permission:query')")
    public Result<List<PermissionDTO>> selectUserPermissions(
            @Parameter(description = "用户ID", required = true)
            @PathVariable @NotNull Long userId) {
        try {
            List<PermissionDTO> permissions = permissionService.selectUserPermissions(userId);
            return Result.success(permissions);
        } catch (Exception e) {
            log.error("查询用户权限失败, userId: {}", userId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @PutMapping("/{permissionId}/move")
    @Operation(summary = "移动权限", description = "移动权限到新的父级下")
    @PreAuthorize("hasAuthority('permission:update')")
    public Result<Boolean> movePermission(
            @Parameter(description = "权限ID", required = true)
            @PathVariable @NotNull Long permissionId,
            @Valid @RequestBody PermissionDTO.PermissionMoveDTO moveDTO) {
        try {
            Boolean result = permissionService.movePermission(permissionId, moveDTO.getNewParentId(), moveDTO.getNewSortOrder());
            return Result.success(result, "权限移动成功");
        } catch (Exception e) {
            log.error("移动权限失败, permissionId: {}, newParentId: {}", permissionId, moveDTO.getNewParentId(), e);
            return Result.error("移动失败: " + e.getMessage());
        }
    }

    @PutMapping("/sort")
    @Operation(summary = "权限排序", description = "更新权限排序")
    @PreAuthorize("hasAuthority('permission:update')")
    public Result<Boolean> sortPermissions(
            @Parameter(description = "权限排序列表", required = true)
            @RequestBody @NotEmpty List<PermissionDTO.PermissionSortDTO> sortList) {
        try {
            Boolean result = permissionService.sortPermissions(sortList);
            return Result.success(result, "权限排序成功");
        } catch (Exception e) {
            log.error("权限排序失败", e);
            return Result.error("排序失败: " + e.getMessage());
        }
    }

    @GetMapping("/statistics")
    @Operation(summary = "查询权限统计", description = "查询权限统计信息")
    @PreAuthorize("hasAuthority('permission:statistics')")
    public Result<PermissionDTO.PermissionStatisticsDTO> selectPermissionStatistics() {
        try {
            PermissionDTO.PermissionStatisticsDTO statistics = permissionService.selectPermissionStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("查询权限统计失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/export")
    @Operation(summary = "导出权限数据", description = "导出权限数据")
    @PreAuthorize("hasAuthority('permission:export')")
    public Result<List<PermissionDTO>> exportPermissions(@Valid PermissionDTO.PermissionQueryDTO queryDTO) {
        try {
            List<PermissionDTO> permissions = permissionService.exportPermissions(queryDTO);
            return Result.success(permissions);
        } catch (Exception e) {
            log.error("导出权限数据失败", e);
            return Result.error("导出失败: " + e.getMessage());
        }
    }

    @PostMapping("/import")
    @Operation(summary = "导入权限数据", description = "导入权限数据")
    @PreAuthorize("hasAuthority('permission:import')")
    public Result<PermissionDTO.ImportResultDTO> importPermissions(
            @Parameter(description = "权限数据列表", required = true)
            @RequestBody @NotEmpty List<PermissionDTO.PermissionImportDTO> permissions) {
        try {
            PermissionDTO.ImportResultDTO result = permissionService.importPermissions(permissions);
            return Result.success(result, "导入完成");
        } catch (Exception e) {
            log.error("导入权限数据失败", e);
            return Result.error("导入失败: " + e.getMessage());
        }
    }

    @PostMapping("/copy")
    @Operation(summary = "复制权限", description = "复制权限及其子权限")
    @PreAuthorize("hasAuthority('permission:create')")
    public Result<Long> copyPermission(
            @Parameter(description = "源权限ID", required = true)
            @RequestParam @NotNull Long sourcePermissionId,
            @Parameter(description = "新权限名称", required = true)
            @RequestParam @NotEmpty String newPermissionName,
            @Parameter(description = "新权限编码", required = true)
            @RequestParam @NotEmpty String newPermissionCode,
            @Parameter(description = "目标父权限ID")
            @RequestParam(required = false) Long targetParentId) {
        try {
            Long newPermissionId = permissionService.copyPermission(sourcePermissionId, newPermissionName, newPermissionCode, targetParentId);
            return Result.success(newPermissionId, "权限复制成功");
        } catch (Exception e) {
            log.error("复制权限失败, sourcePermissionId: {}, newPermissionName: {}, newPermissionCode: {}", 
                    sourcePermissionId, newPermissionName, newPermissionCode, e);
            return Result.error("复制失败: " + e.getMessage());
        }
    }

    @PostMapping("/sync-cache")
    @Operation(summary = "同步权限缓存", description = "同步权限缓存")
    @PreAuthorize("hasAuthority('permission:sync-cache')")
    public Result<Boolean> syncPermissionCache() {
        try {
            Boolean result = permissionService.syncPermissionCache();
            return Result.success(result, "缓存同步成功");
        } catch (Exception e) {
            log.error("同步权限缓存失败", e);
            return Result.error("同步失败: " + e.getMessage());
        }
    }

    @PostMapping("/refresh-menu")
    @Operation(summary = "刷新菜单权限", description = "刷新菜单权限缓存")
    @PreAuthorize("hasAuthority('permission:refresh-menu')")
    public Result<Boolean> refreshMenuPermissions() {
        try {
            Boolean result = permissionService.refreshMenuPermissions();
            return Result.success(result, "菜单权限刷新成功");
        } catch (Exception e) {
            log.error("刷新菜单权限失败", e);
            return Result.error("刷新失败: " + e.getMessage());
        }
    }
}