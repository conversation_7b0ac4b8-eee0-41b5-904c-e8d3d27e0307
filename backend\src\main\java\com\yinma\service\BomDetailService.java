package com.yinma.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yinma.dto.BomDetailDTO;
import com.yinma.entity.BomDetailEntity;

import java.util.List;
import java.util.Map;

/**
 * BOM明细管理Service接口
 * 提供BOM明细相关的业务逻辑处理
 * 
 * <AUTHOR> System
 * @since 2024-01-15
 */
public interface BomDetailService extends IService<BomDetailEntity> {

    /**
     * 查询BOM明细列表
     * 
     * @param bomId BOM主表ID
     * @param includeChildren 是否包含子级
     * @param level 指定层级（可选）
     * @return BOM明细列表
     */
    List<BomDetailDTO> getBomDetailList(Long bomId, Boolean includeChildren, Integer level);

    /**
     * 查询BOM明细树形结构
     * 
     * @param bomId BOM主表ID
     * @param maxLevel 最大层级
     * @param expandAll 是否展开所有
     * @return BOM明细树
     */
    List<BomDetailDTO> getBomDetailTree(Long bomId, Integer maxLevel, Boolean expandAll);

    /**
     * 根据ID查询BOM明细详情
     * 
     * @param detailId 明细ID
     * @param includeChildren 是否包含子级
     * @return BOM明细详情
     */
    BomDetailDTO getBomDetailById(Long detailId, Boolean includeChildren);

    /**
     * 查询指定层级的BOM明细
     * 
     * @param bomId BOM主表ID
     * @param level 层级
     * @return 指定层级明细列表
     */
    List<BomDetailDTO> getBomDetailByLevel(Long bomId, Integer level);

    /**
     * 查询子级BOM明细
     * 
     * @param parentDetailId 父级明细ID
     * @return 子级明细列表
     */
    List<BomDetailDTO> getChildDetails(Long parentDetailId);

    /**
     * 创建BOM明细
     * 
     * @param detailDTO BOM明细数据
     * @return 创建结果
     */
    BomDetailDTO createBomDetail(BomDetailDTO detailDTO);

    /**
     * 批量创建BOM明细
     * 
     * @param detailDTOList BOM明细数据列表
     * @return 创建结果
     */
    List<BomDetailDTO> batchCreateBomDetails(List<BomDetailDTO> detailDTOList);

    /**
     * 更新BOM明细
     * 
     * @param detailDTO BOM明细数据
     * @return 更新结果
     */
    BomDetailDTO updateBomDetail(BomDetailDTO detailDTO);

    /**
     * 批量更新BOM明细
     * 
     * @param detailDTOList BOM明细数据列表
     * @return 更新结果
     */
    List<BomDetailDTO> batchUpdateBomDetails(List<BomDetailDTO> detailDTOList);

    /**
     * 删除BOM明细
     * 
     * @param detailId 明细ID
     * @param deleteBy 删除人
     * @return 删除结果
     */
    Boolean deleteBomDetail(Long detailId, String deleteBy);

    /**
     * 批量删除BOM明细
     * 
     * @param detailIds 明细ID列表
     * @param deleteBy 删除人
     * @return 删除结果
     */
    Boolean batchDeleteBomDetails(List<Long> detailIds, String deleteBy);

    /**
     * 根据BOM ID删除所有明细
     * 
     * @param bomId BOM主表ID
     * @param deleteBy 删除人
     * @return 删除结果
     */
    Boolean deleteDetailsByBomId(Long bomId, String deleteBy);

    /**
     * 复制BOM明细到新BOM
     * 
     * @param sourceBomId 源BOM ID
     * @param targetBomId 目标BOM ID
     * @param copyBy 复制人
     * @return 复制结果
     */
    Boolean copyBomDetails(Long sourceBomId, Long targetBomId, String copyBy);

    /**
     * 移动BOM明细
     * 
     * @param detailId 明细ID
     * @param newParentDetailId 新父级明细ID
     * @param newSortOrder 新排序号
     * @param moveBy 移动人
     * @return 移动结果
     */
    Boolean moveBomDetail(Long detailId, Long newParentDetailId, Integer newSortOrder, String moveBy);

    /**
     * 调整BOM明细排序
     * 
     * @param bomId BOM主表ID
     * @param detailOrders 明细排序列表
     * @param adjustBy 调整人
     * @return 调整结果
     */
    Boolean adjustDetailOrder(Long bomId, List<Map<String, Object>> detailOrders, String adjustBy);

    /**
     * 更新BOM明细层级信息
     * 
     * @param bomId BOM主表ID
     * @return 更新结果
     */
    Boolean updateDetailLevels(Long bomId);

    /**
     * 计算物料需求汇总
     * 
     * @param bomId BOM主表ID
     * @param requiredQty 需求数量
     * @param expandSubBom 是否展开子BOM
     * @return 物料需求汇总
     */
    List<Map<String, Object>> calculateMaterialRequirements(Long bomId, Double requiredQty, Boolean expandSubBom);

    /**
     * 查询BOM成本构成分析
     * 
     * @param bomId BOM主表ID
     * @param costType 成本类型（STANDARD/LATEST/AVERAGE）
     * @return 成本构成分析
     */
    List<Map<String, Object>> analyzeBomCostStructure(Long bomId, String costType);

    /**
     * 查询关键物料列表
     * 
     * @param bomId BOM主表ID
     * @param criticalType 关键类型（COST/LEAD_TIME/SUPPLY_RISK）
     * @return 关键物料列表
     */
    List<BomDetailDTO> getCriticalMaterials(Long bomId, String criticalType);

    /**
     * 查询可替代物料列表
     * 
     * @param bomId BOM主表ID
     * @param materialCode 物料编码（可选）
     * @return 可替代物料列表
     */
    List<BomDetailDTO> getSubstitutableMaterials(Long bomId, String materialCode);

    /**
     * 物料替代分析
     * 
     * @param detailId 明细ID
     * @param substituteOptions 替代选项
     * @return 替代分析结果
     */
    Map<String, Object> analyzeMaterialSubstitute(Long detailId, List<String> substituteOptions);

    /**
     * 查询物料在BOM中的使用情况
     * 
     * @param materialCode 物料编码
     * @param includeInactive 是否包含非活动BOM
     * @return 使用情况列表
     */
    List<Map<String, Object>> getMaterialUsageInBom(String materialCode, Boolean includeInactive);

    /**
     * BOM明细有效性检查
     * 
     * @param bomId BOM主表ID
     * @return 检查结果
     */
    Map<String, Object> validateBomDetails(Long bomId);

    /**
     * 检查BOM明细完整性
     * 
     * @param bomId BOM主表ID
     * @return 检查结果
     */
    Map<String, Object> checkDetailCompleteness(Long bomId);

    /**
     * 检查BOM明细一致性
     * 
     * @param bomId BOM主表ID
     * @return 检查结果
     */
    Map<String, Object> checkDetailConsistency(Long bomId);

    /**
     * BOM明细变更影响分析
     * 
     * @param detailId 明细ID
     * @param changeType 变更类型
     * @param changeValue 变更值
     * @return 影响分析结果
     */
    Map<String, Object> analyzeDetailChangeImpact(Long detailId, String changeType, Object changeValue);

    /**
     * 批量更新明细价格
     * 
     * @param bomId BOM主表ID
     * @param priceSource 价格来源（STANDARD/LATEST/SUPPLIER）
     * @param updateBy 更新人
     * @return 更新结果
     */
    Map<String, Object> batchUpdateDetailPrices(Long bomId, String priceSource, String updateBy);

    /**
     * 计算明细总金额
     * 
     * @param detailId 明细ID
     * @return 计算结果
     */
    Boolean calculateDetailTotalAmount(Long detailId);

    /**
     * 批量计算明细总金额
     * 
     * @param bomId BOM主表ID
     * @return 计算结果
     */
    Boolean batchCalculateDetailTotalAmount(Long bomId);

    /**
     * 查询BOM明细统计信息
     * 
     * @param bomId BOM主表ID
     * @return 统计信息
     */
    Map<String, Object> getBomDetailStatistics(Long bomId);

    /**
     * 导入BOM明细
     * 
     * @param bomId BOM主表ID
     * @param detailDataList 明细数据列表
     * @param importBy 导入人
     * @return 导入结果
     */
    Map<String, Object> importBomDetails(Long bomId, List<Map<String, Object>> detailDataList, String importBy);

    /**
     * 导出BOM明细
     * 
     * @param bomId BOM主表ID
     * @param exportFormat 导出格式（EXCEL/CSV）
     * @param includeChildren 是否包含子级
     * @return 导出文件路径
     */
    String exportBomDetails(Long bomId, String exportFormat, Boolean includeChildren);

    /**
     * 查询明细变更历史
     * 
     * @param detailId 明细ID
     * @param limit 限制数量
     * @return 变更历史
     */
    List<Map<String, Object>> getDetailChangeHistory(Long detailId, Integer limit);

    /**
     * 明细数据同步
     * 
     * @param bomId BOM主表ID
     * @param syncSource 同步源（MATERIAL_MASTER/SUPPLIER_PRICE）
     * @param syncBy 同步人
     * @return 同步结果
     */
    Map<String, Object> syncDetailData(Long bomId, String syncSource, String syncBy);

    /**
     * 检查物料是否在BOM中使用
     * 
     * @param materialCode 物料编码
     * @param excludeBomId 排除的BOM ID
     * @return 使用情况
     */
    Map<String, Object> checkMaterialUsage(String materialCode, Long excludeBomId);

    /**
     * 优化BOM明细结构
     * 
     * @param bomId BOM主表ID
     * @param optimizationType 优化类型（COST/STRUCTURE/SUPPLIER）
     * @return 优化建议
     */
    List<Map<String, Object>> optimizeBomDetailStructure(Long bomId, String optimizationType);

    /**
     * 明细审批
     * 
     * @param detailId 明细ID
     * @param approvalAction 审批动作
     * @param approvalComment 审批意见
     * @param approveBy 审批人
     * @return 审批结果
     */
    Boolean approveDetail(Long detailId, String approvalAction, String approvalComment, String approveBy);

    /**
     * 批量审批明细
     * 
     * @param detailIds 明细ID列表
     * @param approvalAction 审批动作
     * @param approvalComment 审批意见
     * @param approveBy 审批人
     * @return 审批结果
     */
    Map<String, Object> batchApproveDetails(List<Long> detailIds, String approvalAction, String approvalComment, String approveBy);
}