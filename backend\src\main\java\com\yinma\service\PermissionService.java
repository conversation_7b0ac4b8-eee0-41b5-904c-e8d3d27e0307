package com.yinma.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yinma.dto.PermissionDTO;
import com.yinma.entity.PermissionEntity;

import java.util.List;

/**
 * 权限信息Service接口
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
public interface PermissionService extends IService<PermissionEntity> {

    /**
     * 分页查询权限列表
     * 
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    IPage<PermissionDTO> selectPermissionPage(PermissionDTO.PermissionQueryDTO queryDTO);

    /**
     * 根据权限ID查询权限详情
     * 
     * @param permissionId 权限ID
     * @return 权限详情
     */
    PermissionDTO selectPermissionById(Long permissionId);

    /**
     * 根据权限编码查询权限
     * 
     * @param permissionCode 权限编码
     * @return 权限信息
     */
    PermissionEntity selectPermissionByCode(String permissionCode);

    /**
     * 创建权限
     * 
     * @param createDTO 创建权限DTO
     * @return 权限ID
     */
    Long createPermission(PermissionDTO.PermissionCreateDTO createDTO);

    /**
     * 更新权限信息
     * 
     * @param updateDTO 更新权限DTO
     * @return 是否成功
     */
    Boolean updatePermission(PermissionDTO.PermissionUpdateDTO updateDTO);

    /**
     * 删除权限
     * 
     * @param permissionId 权限ID
     * @return 是否成功
     */
    Boolean deletePermission(Long permissionId);

    /**
     * 批量删除权限
     * 
     * @param permissionIds 权限ID列表
     * @return 是否成功
     */
    Boolean batchDeletePermissions(List<Long> permissionIds);

    /**
     * 启用权限
     * 
     * @param permissionId 权限ID
     * @return 是否成功
     */
    Boolean enablePermission(Long permissionId);

    /**
     * 禁用权限
     * 
     * @param permissionId 权限ID
     * @return 是否成功
     */
    Boolean disablePermission(Long permissionId);

    /**
     * 批量更新权限状态
     * 
     * @param permissionIds 权限ID列表
     * @param status 状态
     * @return 是否成功
     */
    Boolean batchUpdatePermissionStatus(List<Long> permissionIds, Integer status);

    /**
     * 检查权限编码是否存在
     * 
     * @param permissionCode 权限编码
     * @return 是否存在
     */
    Boolean checkPermissionCodeExists(String permissionCode);

    /**
     * 检查权限名称是否存在
     * 
     * @param permissionName 权限名称
     * @return 是否存在
     */
    Boolean checkPermissionNameExists(String permissionName);

    /**
     * 查询权限树
     * 
     * @return 权限树
     */
    List<PermissionDTO.PermissionTreeDTO> selectPermissionTree();

    /**
     * 查询用户权限树
     * 
     * @param userId 用户ID
     * @return 权限树
     */
    List<PermissionDTO.PermissionTreeDTO> selectUserPermissionTree(Long userId);

    /**
     * 查询角色权限树
     * 
     * @param roleId 角色ID
     * @return 权限树
     */
    List<PermissionDTO.PermissionTreeDTO> selectRolePermissionTree(Long roleId);

    /**
     * 查询子权限列表
     * 
     * @param parentId 父权限ID
     * @return 子权限列表
     */
    List<PermissionDTO> selectChildPermissions(Long parentId);

    /**
     * 查询所有子权限ID（递归）
     * 
     * @param parentId 父权限ID
     * @return 子权限ID列表
     */
    List<Long> selectAllChildPermissionIds(Long parentId);

    /**
     * 查询父权限路径
     * 
     * @param permissionId 权限ID
     * @return 父权限路径
     */
    List<PermissionDTO> selectParentPermissionPath(Long permissionId);

    /**
     * 移动权限
     * 
     * @param moveDTO 移动权限DTO
     * @return 是否成功
     */
    Boolean movePermission(PermissionDTO.PermissionMoveDTO moveDTO);

    /**
     * 更新权限排序
     * 
     * @param permissionId 权限ID
     * @param sortOrder 排序号
     * @return 是否成功
     */
    Boolean updatePermissionSort(Long permissionId, Integer sortOrder);

    /**
     * 批量更新权限排序
     * 
     * @param sortList 排序列表
     * @return 是否成功
     */
    Boolean batchUpdatePermissionSort(List<PermissionDTO.PermissionSortDTO> sortList);

    /**
     * 查询菜单权限
     * 
     * @return 菜单权限列表
     */
    List<PermissionDTO> selectMenuPermissions();

    /**
     * 查询按钮权限
     * 
     * @return 按钮权限列表
     */
    List<PermissionDTO> selectButtonPermissions();

    /**
     * 查询数据权限
     * 
     * @return 数据权限列表
     */
    List<PermissionDTO> selectDataPermissions();

    /**
     * 查询API权限
     * 
     * @return API权限列表
     */
    List<PermissionDTO> selectApiPermissions();

    /**
     * 查询用户菜单权限
     * 
     * @param userId 用户ID
     * @return 菜单权限列表
     */
    List<PermissionDTO> selectUserMenuPermissions(Long userId);

    /**
     * 查询用户按钮权限
     * 
     * @param userId 用户ID
     * @return 按钮权限列表
     */
    List<String> selectUserButtonPermissions(Long userId);

    /**
     * 查询用户API权限
     * 
     * @param userId 用户ID
     * @return API权限列表
     */
    List<String> selectUserApiPermissions(Long userId);

    /**
     * 查询权限统计信息
     * 
     * @return 统计信息
     */
    PermissionDTO.PermissionStatisticsDTO selectPermissionStatistics();

    /**
     * 查询权限类型统计
     * 
     * @return 统计信息
     */
    List<PermissionDTO.PermissionTypeStatisticsDTO> selectPermissionTypeStatistics();

    /**
     * 查询权限使用统计
     * 
     * @return 统计信息
     */
    List<PermissionDTO.PermissionUsageStatisticsDTO> selectPermissionUsageStatistics();

    /**
     * 导出权限数据
     * 
     * @param queryDTO 查询条件
     * @return 权限数据
     */
    List<PermissionDTO> exportPermissions(PermissionDTO.PermissionQueryDTO queryDTO);

    /**
     * 导入权限数据
     * 
     * @param permissions 权限数据
     * @return 导入结果
     */
    PermissionDTO.ImportResultDTO importPermissions(List<PermissionDTO.PermissionImportDTO> permissions);

    /**
     * 复制权限
     * 
     * @param sourcePermissionId 源权限ID
     * @param targetPermissionCode 目标权限编码
     * @param targetPermissionName 目标权限名称
     * @return 新权限ID
     */
    Long copyPermission(Long sourcePermissionId, String targetPermissionCode, String targetPermissionName);

    /**
     * 检查权限是否可以删除
     * 
     * @param permissionId 权限ID
     * @return 是否可以删除
     */
    Boolean checkPermissionCanDelete(Long permissionId);

    /**
     * 查询权限的角色列表
     * 
     * @param permissionId 权限ID
     * @return 角色列表
     */
    List<PermissionDTO.PermissionRoleDTO> selectPermissionRoles(Long permissionId);

    /**
     * 查询权限的用户列表
     * 
     * @param permissionId 权限ID
     * @return 用户列表
     */
    List<PermissionDTO.PermissionUserDTO> selectPermissionUsers(Long permissionId);

    /**
     * 同步权限到缓存
     * 
     * @return 是否成功
     */
    Boolean syncPermissionsToCache();

    /**
     * 清除权限缓存
     * 
     * @return 是否成功
     */
    Boolean clearPermissionCache();

    /**
     * 刷新用户权限缓存
     * 
     * @param userId 用户ID
     * @return 是否成功
     */
    Boolean refreshUserPermissionCache(Long userId);

    /**
     * 刷新角色权限缓存
     * 
     * @param roleId 角色ID
     * @return 是否成功
     */
    Boolean refreshRolePermissionCache(Long roleId);

    /**
     * 验证权限路径
     * 
     * @param path 权限路径
     * @return 是否有效
     */
    Boolean validatePermissionPath(String path);

    /**
     * 验证权限组件
     * 
     * @param component 权限组件
     * @return 是否有效
     */
    Boolean validatePermissionComponent(String component);

    /**
     * 生成权限编码
     * 
     * @param permissionName 权限名称
     * @param permissionType 权限类型
     * @return 权限编码
     */
    String generatePermissionCode(String permissionName, String permissionType);

    /**
     * 查询权限依赖关系
     * 
     * @param permissionId 权限ID
     * @return 依赖关系
     */
    List<PermissionDTO.PermissionDependencyDTO> selectPermissionDependencies(Long permissionId);

    /**
     * 设置权限依赖关系
     * 
     * @param permissionId 权限ID
     * @param dependencyIds 依赖权限ID列表
     * @return 是否成功
     */
    Boolean setPermissionDependencies(Long permissionId, List<Long> dependencyIds);

    /**
     * 检查权限循环依赖
     * 
     * @param permissionId 权限ID
     * @param dependencyIds 依赖权限ID列表
     * @return 是否存在循环依赖
     */
    Boolean checkPermissionCircularDependency(Long permissionId, List<Long> dependencyIds);
}