{"ast": null, "code": "import { Category } from './category';\nimport { Continuous } from './continuous';\nexport { Category, Continuous };", "map": {"version": 3, "names": ["Category", "Continuous"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\component\\src\\ui\\legend\\index.ts"], "sourcesContent": ["import type { CategoryOptions } from './category';\nimport { Category } from './category';\nimport type { ContinuousOptions } from './continuous';\nimport { Continuous } from './continuous';\n\nexport { Category, Continuous };\nexport type { CategoryOptions, ContinuousOptions };\n"], "mappings": "AACA,SAASA,QAAQ,QAAQ,YAAY;AAErC,SAASC,UAAU,QAAQ,cAAc;AAEzC,SAASD,QAAQ,EAAEC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}