{"ast": null, "code": "module.exports = require('../dist/compat/array/sortBy.js').sortBy;", "map": {"version": 3, "names": ["module", "exports", "require", "sortBy"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/es-toolkit/compat/sortBy.js"], "sourcesContent": ["module.exports = require('../dist/compat/array/sortBy.js').sortBy;\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,gCAAgC,CAAC,CAACC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}