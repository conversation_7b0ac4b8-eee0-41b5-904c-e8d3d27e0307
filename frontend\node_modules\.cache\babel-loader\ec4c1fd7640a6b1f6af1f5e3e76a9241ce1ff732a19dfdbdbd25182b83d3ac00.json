{"ast": null, "code": "import { deepMix } from '@antv/util';\nimport { constant, visualColumn } from './utils/helper';\n/**\n * Add 3 constant encode for size channel.\n * This is useful for point geometry.\n */\nexport const MaybeSize = () => {\n  return (I, mark) => {\n    const {\n      encode\n    } = mark;\n    const {\n      size\n    } = encode;\n    if (size !== undefined) return [I, mark];\n    return [I, deepMix({}, mark, {\n      encode: {\n        size: visualColumn(constant(I, 3))\n      }\n    })];\n  };\n};\nMaybeSize.props = {};", "map": {"version": 3, "names": ["deepMix", "constant", "visualColumn", "MaybeSize", "I", "mark", "encode", "size", "undefined", "props"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\g2\\src\\transform\\maybeSize.ts"], "sourcesContent": ["import { deepMix } from '@antv/util';\nimport { TransformComponent as TC } from '../runtime';\nimport { constant, visualColumn } from './utils/helper';\n\nexport type MaybeSizeOptions = Record<string, never>;\n\n/**\n * Add 3 constant encode for size channel.\n * This is useful for point geometry.\n */\nexport const MaybeSize: TC<MaybeSizeOptions> = () => {\n  return (I, mark) => {\n    const { encode } = mark;\n    const { size } = encode;\n    if (size !== undefined) return [I, mark];\n    return [\n      I,\n      deepMix({}, mark, { encode: { size: visualColumn(constant(I, 3)) } }),\n    ];\n  };\n};\n\nMaybeSize.props = {};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AAEpC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,gBAAgB;AAIvD;;;;AAIA,OAAO,MAAMC,SAAS,GAAyBA,CAAA,KAAK;EAClD,OAAO,CAACC,CAAC,EAAEC,IAAI,KAAI;IACjB,MAAM;MAAEC;IAAM,CAAE,GAAGD,IAAI;IACvB,MAAM;MAAEE;IAAI,CAAE,GAAGD,MAAM;IACvB,IAAIC,IAAI,KAAKC,SAAS,EAAE,OAAO,CAACJ,CAAC,EAAEC,IAAI,CAAC;IACxC,OAAO,CACLD,CAAC,EACDJ,OAAO,CAAC,EAAE,EAAEK,IAAI,EAAE;MAAEC,MAAM,EAAE;QAAEC,IAAI,EAAEL,YAAY,CAACD,QAAQ,CAACG,CAAC,EAAE,CAAC,CAAC;MAAC;IAAE,CAAE,CAAC,CACtE;EACH,CAAC;AACH,CAAC;AAEDD,SAAS,CAACM,KAAK,GAAG,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}