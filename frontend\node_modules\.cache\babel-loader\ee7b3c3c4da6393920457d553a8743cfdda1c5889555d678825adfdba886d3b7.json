{"ast": null, "code": "import { d3Ticks } from './d3-ticks';\nimport { pows, logs } from '../utils';\nexport const d3Log = (a, b, n, base = 10) => {\n  const shouldReflect = a < 0;\n  const pow = pows(base, shouldReflect);\n  const log = logs(base, shouldReflect);\n  const r = b < a;\n  const min = r ? b : a;\n  const max = r ? a : b;\n  let i = log(min);\n  let j = log(max);\n  let ticks = [];\n  // 如果 base 是整数\n  if (!(base % 1) && j - i < n) {\n    i = Math.floor(i);\n    j = Math.ceil(j);\n    if (shouldReflect) {\n      for (; i <= j; i += 1) {\n        const p = pow(i);\n        for (let k = base - 1; k >= 1; k -= 1) {\n          const t = p * k;\n          if (t > max) break;\n          if (t >= min) ticks.push(t);\n        }\n      }\n    } else {\n      for (; i <= j; i += 1) {\n        const p = pow(i);\n        for (let k = 1; k < base; k += 1) {\n          const t = p * k;\n          if (t > max) break;\n          if (t >= min) ticks.push(t);\n        }\n      }\n    }\n    if (ticks.length * 2 < n) ticks = d3Ticks(min, max, n);\n  } else {\n    const count = n === -1 ? j - i : Math.min(j - i, n);\n    ticks = d3Ticks(i, j, count).map(pow);\n  }\n  return r ? ticks.reverse() : ticks;\n};", "map": {"version": 3, "names": ["d3Ticks", "pows", "logs", "d3Log", "a", "b", "n", "base", "shouldReflect", "pow", "log", "r", "min", "max", "i", "j", "ticks", "Math", "floor", "ceil", "p", "k", "t", "push", "length", "count", "map", "reverse"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\scale\\src\\tick-methods\\d3-log.ts"], "sourcesContent": ["import { TickMethod } from '../types';\nimport { d3Ticks } from './d3-ticks';\nimport { pows, logs } from '../utils';\n\nexport const d3Log: TickMethod = (a, b, n, base = 10) => {\n  const shouldReflect = a < 0;\n  const pow = pows(base, shouldReflect);\n  const log = logs(base, shouldReflect);\n\n  const r = b < a;\n  const min = r ? b : a;\n  const max = r ? a : b;\n  let i = log(min);\n  let j = log(max);\n  let ticks = [];\n\n  // 如果 base 是整数\n  if (!(base % 1) && j - i < n) {\n    i = Math.floor(i);\n    j = Math.ceil(j);\n    if (shouldReflect) {\n      for (; i <= j; i += 1) {\n        const p = pow(i);\n        for (let k = base - 1; k >= 1; k -= 1) {\n          const t = p * k;\n          if (t > max) break;\n          if (t >= min) ticks.push(t);\n        }\n      }\n    } else {\n      for (; i <= j; i += 1) {\n        const p = pow(i);\n        for (let k = 1; k < base; k += 1) {\n          const t = p * k;\n          if (t > max) break;\n          if (t >= min) ticks.push(t);\n        }\n      }\n    }\n    if (ticks.length * 2 < n) ticks = d3Ticks(min, max, n);\n  } else {\n    const count = n === -1 ? j - i : Math.min(j - i, n);\n    ticks = d3Ticks(i, j, count).map(pow);\n  }\n\n  return r ? ticks.reverse() : ticks;\n};\n"], "mappings": "AACA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,IAAI,EAAEC,IAAI,QAAQ,UAAU;AAErC,OAAO,MAAMC,KAAK,GAAeA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,IAAI,GAAG,EAAE,KAAI;EACtD,MAAMC,aAAa,GAAGJ,CAAC,GAAG,CAAC;EAC3B,MAAMK,GAAG,GAAGR,IAAI,CAACM,IAAI,EAAEC,aAAa,CAAC;EACrC,MAAME,GAAG,GAAGR,IAAI,CAACK,IAAI,EAAEC,aAAa,CAAC;EAErC,MAAMG,CAAC,GAAGN,CAAC,GAAGD,CAAC;EACf,MAAMQ,GAAG,GAAGD,CAAC,GAAGN,CAAC,GAAGD,CAAC;EACrB,MAAMS,GAAG,GAAGF,CAAC,GAAGP,CAAC,GAAGC,CAAC;EACrB,IAAIS,CAAC,GAAGJ,GAAG,CAACE,GAAG,CAAC;EAChB,IAAIG,CAAC,GAAGL,GAAG,CAACG,GAAG,CAAC;EAChB,IAAIG,KAAK,GAAG,EAAE;EAEd;EACA,IAAI,EAAET,IAAI,GAAG,CAAC,CAAC,IAAIQ,CAAC,GAAGD,CAAC,GAAGR,CAAC,EAAE;IAC5BQ,CAAC,GAAGG,IAAI,CAACC,KAAK,CAACJ,CAAC,CAAC;IACjBC,CAAC,GAAGE,IAAI,CAACE,IAAI,CAACJ,CAAC,CAAC;IAChB,IAAIP,aAAa,EAAE;MACjB,OAAOM,CAAC,IAAIC,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE;QACrB,MAAMM,CAAC,GAAGX,GAAG,CAACK,CAAC,CAAC;QAChB,KAAK,IAAIO,CAAC,GAAGd,IAAI,GAAG,CAAC,EAAEc,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;UACrC,MAAMC,CAAC,GAAGF,CAAC,GAAGC,CAAC;UACf,IAAIC,CAAC,GAAGT,GAAG,EAAE;UACb,IAAIS,CAAC,IAAIV,GAAG,EAAEI,KAAK,CAACO,IAAI,CAACD,CAAC,CAAC;;;KAGhC,MAAM;MACL,OAAOR,CAAC,IAAIC,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE;QACrB,MAAMM,CAAC,GAAGX,GAAG,CAACK,CAAC,CAAC;QAChB,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,IAAI,EAAEc,CAAC,IAAI,CAAC,EAAE;UAChC,MAAMC,CAAC,GAAGF,CAAC,GAAGC,CAAC;UACf,IAAIC,CAAC,GAAGT,GAAG,EAAE;UACb,IAAIS,CAAC,IAAIV,GAAG,EAAEI,KAAK,CAACO,IAAI,CAACD,CAAC,CAAC;;;;IAIjC,IAAIN,KAAK,CAACQ,MAAM,GAAG,CAAC,GAAGlB,CAAC,EAAEU,KAAK,GAAGhB,OAAO,CAACY,GAAG,EAAEC,GAAG,EAAEP,CAAC,CAAC;GACvD,MAAM;IACL,MAAMmB,KAAK,GAAGnB,CAAC,KAAK,CAAC,CAAC,GAAGS,CAAC,GAAGD,CAAC,GAAGG,IAAI,CAACL,GAAG,CAACG,CAAC,GAAGD,CAAC,EAAER,CAAC,CAAC;IACnDU,KAAK,GAAGhB,OAAO,CAACc,CAAC,EAAEC,CAAC,EAAEU,KAAK,CAAC,CAACC,GAAG,CAACjB,GAAG,CAAC;;EAGvC,OAAOE,CAAC,GAAGK,KAAK,CAACW,OAAO,EAAE,GAAGX,KAAK;AACpC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}