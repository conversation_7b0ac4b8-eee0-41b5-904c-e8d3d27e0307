{"ast": null, "code": "import { identity, isArray, last } from '@antv/util';\nimport { Continuous } from './continuous';\nimport { createInterpolateValue } from '../utils';\nimport { d3Ticks } from '../tick-methods/d3-ticks';\nimport { d3LinearNice } from '../utils/d3-linear-nice';\n/**\n * Linear 比例尺\n *\n * 构造可创建一个在输入和输出之间具有线性关系的比例尺\n */\nexport class Linear extends Continuous {\n  getDefaultOptions() {\n    return {\n      domain: [0, 1],\n      range: [0, 1],\n      unknown: undefined,\n      nice: false,\n      clamp: false,\n      round: false,\n      interpolate: createInterpolateValue,\n      tickMethod: d3Ticks,\n      tickCount: 5\n    };\n  }\n  removeUnsortedValues(breaksDomain, breaksRange, reverse) {\n    let pre = -Infinity;\n    const deleteIndices = breaksRange.reduce((acc, current, i) => {\n      if (i === 0) return acc;\n      const value = pre > 0 ? pre : current;\n      if (pre > 0 && (reverse ? current > pre : current < pre)) {\n        acc.push(i);\n      } else {\n        const diff = (value - breaksRange[i - 1]) * (reverse ? -1 : 1);\n        if (diff < 0) {\n          if (pre < 0) pre = breaksRange[i - 1];\n          acc.push(i);\n        } else {\n          pre = -Infinity;\n        }\n      }\n      return acc;\n    }, []);\n    deleteIndices.slice().reverse().forEach(index => {\n      breaksDomain.splice(index, 1);\n      breaksRange.splice(index, 1);\n    });\n    return {\n      breaksDomain,\n      breaksRange\n    };\n  }\n  transformDomain(options) {\n    const RANGE_LIMIT = [0.2, 0.8];\n    const DEFAULT_GAP = 0.03;\n    const {\n      domain = [],\n      range = [1, 0],\n      breaks = [],\n      tickCount = 5,\n      nice\n    } = options;\n    const [min, max] = [Math.min(...domain), Math.max(...domain)];\n    let niceDomainMin = min;\n    let niceDomainMax = max;\n    if (nice && breaks.length < 2) {\n      const niceDomain = this.chooseNice()(min, max, tickCount);\n      niceDomainMin = niceDomain[0];\n      niceDomainMax = niceDomain[niceDomain.length - 1];\n    }\n    const domainMin = Math.min(niceDomainMin, min);\n    let domainMax = Math.max(niceDomainMax, max);\n    const sortedBreaks = breaks.filter(({\n      end\n    }) => end < domainMax).sort((a, b) => a.start - b.start);\n    const breaksDomain = d3Ticks(domainMin, domainMax, tickCount, sortedBreaks);\n    if (last(breaksDomain) < domainMax) {\n      const nicest = d3LinearNice(0, domainMax - last(breaksDomain), 3);\n      breaksDomain.push(last(breaksDomain) + last(nicest));\n      domainMax = last(breaksDomain);\n    }\n    const [r0, r1] = [range[0], last(range)];\n    const diffDomain = domainMax - domainMin;\n    const diffRange = Math.abs(r1 - r0);\n    const reverse = r0 > r1;\n    // Calculate the new range based on breaks.\n    const breaksRange = breaksDomain.map(d => {\n      const ratio = (d - domainMin) / diffDomain;\n      return reverse ? r0 - ratio * diffRange : r0 + ratio * diffRange;\n    });\n    // Compress the range scale according to breaks.\n    const [MIN, MAX] = RANGE_LIMIT;\n    sortedBreaks.forEach(({\n      start,\n      end,\n      gap = DEFAULT_GAP,\n      compress = 'middle'\n    }) => {\n      const startIndex = breaksDomain.indexOf(start);\n      const endIndex = breaksDomain.indexOf(end);\n      let value = (breaksRange[startIndex] + breaksRange[endIndex]) / 2;\n      if (compress === 'start') value = breaksRange[startIndex];\n      if (compress === 'end') value = breaksRange[endIndex];\n      const halfSpan = gap * diffRange / 2;\n      // Calculate the new start and end values based on the center and scaled span.\n      let startValue = reverse ? value + halfSpan : value - halfSpan;\n      let endValue = reverse ? value - halfSpan : value + halfSpan;\n      // Ensure the new start and end values are within the defined limits.\n      if (startValue < MIN) {\n        endValue += MIN - startValue;\n        startValue = MIN;\n      }\n      if (endValue > MAX) {\n        startValue -= endValue - MAX;\n        endValue = MAX;\n      }\n      if (startValue > MAX) {\n        endValue -= startValue - MAX;\n        startValue = MAX;\n      }\n      if (endValue < MIN) {\n        startValue += MIN - endValue;\n        endValue = MIN;\n      }\n      breaksRange[startIndex] = startValue;\n      breaksRange[endIndex] = endValue;\n    });\n    return this.removeUnsortedValues(breaksDomain, breaksRange, reverse);\n  }\n  transformBreaks(options) {\n    const {\n      domain,\n      breaks = []\n    } = options;\n    if (!isArray(options.breaks)) return options;\n    const domainMax = Math.max(...domain);\n    const filteredBreaks = breaks.filter(({\n      end\n    }) => end < domainMax);\n    const optWithFilteredBreaks = {\n      ...options,\n      breaks: filteredBreaks\n    };\n    const {\n      breaksDomain,\n      breaksRange\n    } = this.transformDomain(optWithFilteredBreaks);\n    return {\n      ...options,\n      domain: breaksDomain,\n      range: breaksRange,\n      breaks: filteredBreaks,\n      tickMethod: () => [...breaksDomain]\n    };\n  }\n  chooseTransforms() {\n    return [identity, identity];\n  }\n  clone() {\n    return new Linear(this.options);\n  }\n}", "map": {"version": 3, "names": ["identity", "isArray", "last", "Continuous", "createInterpolateValue", "d3Ticks", "d3LinearNice", "Linear", "getDefaultOptions", "domain", "range", "unknown", "undefined", "nice", "clamp", "round", "interpolate", "tickMethod", "tickCount", "removeUnsortedValues", "breaksDomain", "breaksRange", "reverse", "pre", "Infinity", "deleteIndices", "reduce", "acc", "current", "i", "value", "push", "diff", "slice", "for<PERSON>ach", "index", "splice", "transformDomain", "options", "RANGE_LIMIT", "DEFAULT_GAP", "breaks", "min", "max", "Math", "niceDomainMin", "niceDomainMax", "length", "niceDomain", "chooseNice", "domainMin", "domainMax", "sortedBreaks", "filter", "end", "sort", "a", "b", "start", "nicest", "r0", "r1", "diffDomain", "diffRange", "abs", "map", "d", "ratio", "MIN", "MAX", "gap", "compress", "startIndex", "indexOf", "endIndex", "halfSpan", "startValue", "endValue", "transformBreaks", "filteredBreaks", "optWithFilteredBreaks", "chooseTransforms", "clone"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\scale\\src\\scales\\linear.ts"], "sourcesContent": ["import { identity, isArray, last } from '@antv/util';\nimport { Continuous } from './continuous';\nimport { LinearOptions, Transform } from '../types';\nimport { Base } from './base';\nimport { createInterpolateValue } from '../utils';\nimport { d3Ticks } from '../tick-methods/d3-ticks';\nimport { d3LinearNice } from '../utils/d3-linear-nice';\n\n/**\n * Linear 比例尺\n *\n * 构造可创建一个在输入和输出之间具有线性关系的比例尺\n */\nexport class Linear extends Continuous<LinearOptions> {\n  protected getDefaultOptions(): LinearOptions {\n    return {\n      domain: [0, 1],\n      range: [0, 1],\n      unknown: undefined,\n      nice: false,\n      clamp: false,\n      round: false,\n      interpolate: createInterpolateValue,\n      tickMethod: d3Ticks,\n      tickCount: 5,\n    };\n  }\n\n  protected removeUnsortedValues(breaksDomain: number[], breaksRange: number[], reverse: boolean) {\n    let pre = -Infinity;\n    const deleteIndices = breaksRange.reduce((acc, current, i) => {\n      if (i === 0) return acc;\n      const value = pre > 0 ? pre : current;\n      if (pre > 0 && (reverse ? current > pre : current < pre)) {\n        acc.push(i);\n      } else {\n        const diff = (value - breaksRange[i - 1]) * (reverse ? -1 : 1);\n        if (diff < 0) {\n          if (pre < 0) pre = breaksRange[i - 1];\n          acc.push(i);\n        } else {\n          pre = -Infinity;\n        }\n      }\n      return acc;\n    }, [] as number[]);\n\n    deleteIndices\n      .slice()\n      .reverse()\n      .forEach((index) => {\n        breaksDomain.splice(index, 1);\n        breaksRange.splice(index, 1);\n      });\n\n    return { breaksDomain, breaksRange };\n  }\n\n  protected transformDomain(options: LinearOptions): { breaksDomain: number[]; breaksRange: number[] } {\n    const RANGE_LIMIT = [0.2, 0.8];\n    const DEFAULT_GAP = 0.03;\n    const { domain = [], range = [1, 0], breaks = [], tickCount = 5, nice } = options;\n    const [min, max] = [Math.min(...domain), Math.max(...domain)];\n    let niceDomainMin = min;\n    let niceDomainMax = max;\n    if (nice && breaks.length < 2) {\n      const niceDomain = this.chooseNice()(min, max, tickCount) as number[];\n      niceDomainMin = niceDomain[0];\n      niceDomainMax = niceDomain[niceDomain.length - 1];\n    }\n    const domainMin = Math.min(niceDomainMin, min);\n    let domainMax = Math.max(niceDomainMax, max);\n    const sortedBreaks = breaks.filter(({ end }) => end < domainMax).sort((a, b) => a.start - b.start);\n    const breaksDomain = d3Ticks(domainMin, domainMax, tickCount, sortedBreaks);\n    if (last(breaksDomain) < domainMax) {\n      const nicest = d3LinearNice(0, domainMax - last(breaksDomain), 3);\n      breaksDomain.push(last(breaksDomain) + last(nicest));\n      domainMax = last(breaksDomain);\n    }\n    const [r0, r1] = [range[0], last(range)] as number[];\n    const diffDomain = domainMax - domainMin;\n    const diffRange = Math.abs(r1 - r0);\n    const reverse = r0 > r1;\n    // Calculate the new range based on breaks.\n    const breaksRange = breaksDomain.map((d) => {\n      const ratio = (d - domainMin) / diffDomain;\n      return reverse ? r0 - ratio * diffRange : r0 + ratio * diffRange;\n    });\n    // Compress the range scale according to breaks.\n    const [MIN, MAX] = RANGE_LIMIT;\n    sortedBreaks.forEach(({ start, end, gap = DEFAULT_GAP, compress = 'middle' }) => {\n      const startIndex = breaksDomain.indexOf(start);\n      const endIndex = breaksDomain.indexOf(end);\n      let value = (breaksRange[startIndex] + breaksRange[endIndex]) / 2;\n      if (compress === 'start') value = breaksRange[startIndex];\n      if (compress === 'end') value = breaksRange[endIndex];\n      const halfSpan = (gap * diffRange) / 2;\n      // Calculate the new start and end values based on the center and scaled span.\n      let startValue = reverse ? value + halfSpan : value - halfSpan;\n      let endValue = reverse ? value - halfSpan : value + halfSpan;\n\n      // Ensure the new start and end values are within the defined limits.\n      if (startValue < MIN) {\n        endValue += MIN - startValue;\n        startValue = MIN;\n      }\n      if (endValue > MAX) {\n        startValue -= endValue - MAX;\n        endValue = MAX;\n      }\n      if (startValue > MAX) {\n        endValue -= startValue - MAX;\n        startValue = MAX;\n      }\n      if (endValue < MIN) {\n        startValue += MIN - endValue;\n        endValue = MIN;\n      }\n\n      breaksRange[startIndex] = startValue;\n      breaksRange[endIndex] = endValue;\n    });\n\n    return this.removeUnsortedValues(breaksDomain, breaksRange, reverse);\n  }\n\n  protected transformBreaks(options: LinearOptions): LinearOptions {\n    const { domain, breaks = [] } = options;\n    if (!isArray(options.breaks)) return options;\n    const domainMax = Math.max(...(domain as number[]));\n    const filteredBreaks = breaks.filter(({ end }) => end < domainMax);\n    const optWithFilteredBreaks = { ...options, breaks: filteredBreaks };\n    const { breaksDomain, breaksRange } = this.transformDomain(optWithFilteredBreaks);\n    return {\n      ...options,\n      domain: breaksDomain,\n      range: breaksRange,\n      breaks: filteredBreaks,\n      tickMethod: () => [...breaksDomain],\n    };\n  }\n\n  protected chooseTransforms(): Transform[] {\n    return [identity, identity];\n  }\n\n  public clone(): Base<LinearOptions> {\n    return new Linear(this.options);\n  }\n}\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,OAAO,EAAEC,IAAI,QAAQ,YAAY;AACpD,SAASC,UAAU,QAAQ,cAAc;AAGzC,SAASC,sBAAsB,QAAQ,UAAU;AACjD,SAASC,OAAO,QAAQ,0BAA0B;AAClD,SAASC,YAAY,QAAQ,yBAAyB;AAEtD;;;;;AAKA,OAAM,MAAOC,MAAO,SAAQJ,UAAyB;EACzCK,iBAAiBA,CAAA;IACzB,OAAO;MACLC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MACdC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MACbC,OAAO,EAAEC,SAAS;MAClBC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE,KAAK;MACZC,WAAW,EAAEZ,sBAAsB;MACnCa,UAAU,EAAEZ,OAAO;MACnBa,SAAS,EAAE;KACZ;EACH;EAEUC,oBAAoBA,CAACC,YAAsB,EAAEC,WAAqB,EAAEC,OAAgB;IAC5F,IAAIC,GAAG,GAAG,CAACC,QAAQ;IACnB,MAAMC,aAAa,GAAGJ,WAAW,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,EAAEC,CAAC,KAAI;MAC3D,IAAIA,CAAC,KAAK,CAAC,EAAE,OAAOF,GAAG;MACvB,MAAMG,KAAK,GAAGP,GAAG,GAAG,CAAC,GAAGA,GAAG,GAAGK,OAAO;MACrC,IAAIL,GAAG,GAAG,CAAC,KAAKD,OAAO,GAAGM,OAAO,GAAGL,GAAG,GAAGK,OAAO,GAAGL,GAAG,CAAC,EAAE;QACxDI,GAAG,CAACI,IAAI,CAACF,CAAC,CAAC;OACZ,MAAM;QACL,MAAMG,IAAI,GAAG,CAACF,KAAK,GAAGT,WAAW,CAACQ,CAAC,GAAG,CAAC,CAAC,KAAKP,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QAC9D,IAAIU,IAAI,GAAG,CAAC,EAAE;UACZ,IAAIT,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGF,WAAW,CAACQ,CAAC,GAAG,CAAC,CAAC;UACrCF,GAAG,CAACI,IAAI,CAACF,CAAC,CAAC;SACZ,MAAM;UACLN,GAAG,GAAG,CAACC,QAAQ;;;MAGnB,OAAOG,GAAG;IACZ,CAAC,EAAE,EAAc,CAAC;IAElBF,aAAa,CACVQ,KAAK,EAAE,CACPX,OAAO,EAAE,CACTY,OAAO,CAAEC,KAAK,IAAI;MACjBf,YAAY,CAACgB,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;MAC7Bd,WAAW,CAACe,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IAC9B,CAAC,CAAC;IAEJ,OAAO;MAAEf,YAAY;MAAEC;IAAW,CAAE;EACtC;EAEUgB,eAAeA,CAACC,OAAsB;IAC9C,MAAMC,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;IAC9B,MAAMC,WAAW,GAAG,IAAI;IACxB,MAAM;MAAE/B,MAAM,GAAG,EAAE;MAAEC,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;MAAE+B,MAAM,GAAG,EAAE;MAAEvB,SAAS,GAAG,CAAC;MAAEL;IAAI,CAAE,GAAGyB,OAAO;IACjF,MAAM,CAACI,GAAG,EAAEC,GAAG,CAAC,GAAG,CAACC,IAAI,CAACF,GAAG,CAAC,GAAGjC,MAAM,CAAC,EAAEmC,IAAI,CAACD,GAAG,CAAC,GAAGlC,MAAM,CAAC,CAAC;IAC7D,IAAIoC,aAAa,GAAGH,GAAG;IACvB,IAAII,aAAa,GAAGH,GAAG;IACvB,IAAI9B,IAAI,IAAI4B,MAAM,CAACM,MAAM,GAAG,CAAC,EAAE;MAC7B,MAAMC,UAAU,GAAG,IAAI,CAACC,UAAU,EAAE,CAACP,GAAG,EAAEC,GAAG,EAAEzB,SAAS,CAAa;MACrE2B,aAAa,GAAGG,UAAU,CAAC,CAAC,CAAC;MAC7BF,aAAa,GAAGE,UAAU,CAACA,UAAU,CAACD,MAAM,GAAG,CAAC,CAAC;;IAEnD,MAAMG,SAAS,GAAGN,IAAI,CAACF,GAAG,CAACG,aAAa,EAAEH,GAAG,CAAC;IAC9C,IAAIS,SAAS,GAAGP,IAAI,CAACD,GAAG,CAACG,aAAa,EAAEH,GAAG,CAAC;IAC5C,MAAMS,YAAY,GAAGX,MAAM,CAACY,MAAM,CAAC,CAAC;MAAEC;IAAG,CAAE,KAAKA,GAAG,GAAGH,SAAS,CAAC,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,KAAK,GAAGD,CAAC,CAACC,KAAK,CAAC;IAClG,MAAMtC,YAAY,GAAGf,OAAO,CAAC6C,SAAS,EAAEC,SAAS,EAAEjC,SAAS,EAAEkC,YAAY,CAAC;IAC3E,IAAIlD,IAAI,CAACkB,YAAY,CAAC,GAAG+B,SAAS,EAAE;MAClC,MAAMQ,MAAM,GAAGrD,YAAY,CAAC,CAAC,EAAE6C,SAAS,GAAGjD,IAAI,CAACkB,YAAY,CAAC,EAAE,CAAC,CAAC;MACjEA,YAAY,CAACW,IAAI,CAAC7B,IAAI,CAACkB,YAAY,CAAC,GAAGlB,IAAI,CAACyD,MAAM,CAAC,CAAC;MACpDR,SAAS,GAAGjD,IAAI,CAACkB,YAAY,CAAC;;IAEhC,MAAM,CAACwC,EAAE,EAAEC,EAAE,CAAC,GAAG,CAACnD,KAAK,CAAC,CAAC,CAAC,EAAER,IAAI,CAACQ,KAAK,CAAC,CAAa;IACpD,MAAMoD,UAAU,GAAGX,SAAS,GAAGD,SAAS;IACxC,MAAMa,SAAS,GAAGnB,IAAI,CAACoB,GAAG,CAACH,EAAE,GAAGD,EAAE,CAAC;IACnC,MAAMtC,OAAO,GAAGsC,EAAE,GAAGC,EAAE;IACvB;IACA,MAAMxC,WAAW,GAAGD,YAAY,CAAC6C,GAAG,CAAEC,CAAC,IAAI;MACzC,MAAMC,KAAK,GAAG,CAACD,CAAC,GAAGhB,SAAS,IAAIY,UAAU;MAC1C,OAAOxC,OAAO,GAAGsC,EAAE,GAAGO,KAAK,GAAGJ,SAAS,GAAGH,EAAE,GAAGO,KAAK,GAAGJ,SAAS;IAClE,CAAC,CAAC;IACF;IACA,MAAM,CAACK,GAAG,EAAEC,GAAG,CAAC,GAAG9B,WAAW;IAC9Ba,YAAY,CAAClB,OAAO,CAAC,CAAC;MAAEwB,KAAK;MAAEJ,GAAG;MAAEgB,GAAG,GAAG9B,WAAW;MAAE+B,QAAQ,GAAG;IAAQ,CAAE,KAAI;MAC9E,MAAMC,UAAU,GAAGpD,YAAY,CAACqD,OAAO,CAACf,KAAK,CAAC;MAC9C,MAAMgB,QAAQ,GAAGtD,YAAY,CAACqD,OAAO,CAACnB,GAAG,CAAC;MAC1C,IAAIxB,KAAK,GAAG,CAACT,WAAW,CAACmD,UAAU,CAAC,GAAGnD,WAAW,CAACqD,QAAQ,CAAC,IAAI,CAAC;MACjE,IAAIH,QAAQ,KAAK,OAAO,EAAEzC,KAAK,GAAGT,WAAW,CAACmD,UAAU,CAAC;MACzD,IAAID,QAAQ,KAAK,KAAK,EAAEzC,KAAK,GAAGT,WAAW,CAACqD,QAAQ,CAAC;MACrD,MAAMC,QAAQ,GAAIL,GAAG,GAAGP,SAAS,GAAI,CAAC;MACtC;MACA,IAAIa,UAAU,GAAGtD,OAAO,GAAGQ,KAAK,GAAG6C,QAAQ,GAAG7C,KAAK,GAAG6C,QAAQ;MAC9D,IAAIE,QAAQ,GAAGvD,OAAO,GAAGQ,KAAK,GAAG6C,QAAQ,GAAG7C,KAAK,GAAG6C,QAAQ;MAE5D;MACA,IAAIC,UAAU,GAAGR,GAAG,EAAE;QACpBS,QAAQ,IAAIT,GAAG,GAAGQ,UAAU;QAC5BA,UAAU,GAAGR,GAAG;;MAElB,IAAIS,QAAQ,GAAGR,GAAG,EAAE;QAClBO,UAAU,IAAIC,QAAQ,GAAGR,GAAG;QAC5BQ,QAAQ,GAAGR,GAAG;;MAEhB,IAAIO,UAAU,GAAGP,GAAG,EAAE;QACpBQ,QAAQ,IAAID,UAAU,GAAGP,GAAG;QAC5BO,UAAU,GAAGP,GAAG;;MAElB,IAAIQ,QAAQ,GAAGT,GAAG,EAAE;QAClBQ,UAAU,IAAIR,GAAG,GAAGS,QAAQ;QAC5BA,QAAQ,GAAGT,GAAG;;MAGhB/C,WAAW,CAACmD,UAAU,CAAC,GAAGI,UAAU;MACpCvD,WAAW,CAACqD,QAAQ,CAAC,GAAGG,QAAQ;IAClC,CAAC,CAAC;IAEF,OAAO,IAAI,CAAC1D,oBAAoB,CAACC,YAAY,EAAEC,WAAW,EAAEC,OAAO,CAAC;EACtE;EAEUwD,eAAeA,CAACxC,OAAsB;IAC9C,MAAM;MAAE7B,MAAM;MAAEgC,MAAM,GAAG;IAAE,CAAE,GAAGH,OAAO;IACvC,IAAI,CAACrC,OAAO,CAACqC,OAAO,CAACG,MAAM,CAAC,EAAE,OAAOH,OAAO;IAC5C,MAAMa,SAAS,GAAGP,IAAI,CAACD,GAAG,CAAC,GAAIlC,MAAmB,CAAC;IACnD,MAAMsE,cAAc,GAAGtC,MAAM,CAACY,MAAM,CAAC,CAAC;MAAEC;IAAG,CAAE,KAAKA,GAAG,GAAGH,SAAS,CAAC;IAClE,MAAM6B,qBAAqB,GAAG;MAAE,GAAG1C,OAAO;MAAEG,MAAM,EAAEsC;IAAc,CAAE;IACpE,MAAM;MAAE3D,YAAY;MAAEC;IAAW,CAAE,GAAG,IAAI,CAACgB,eAAe,CAAC2C,qBAAqB,CAAC;IACjF,OAAO;MACL,GAAG1C,OAAO;MACV7B,MAAM,EAAEW,YAAY;MACpBV,KAAK,EAAEW,WAAW;MAClBoB,MAAM,EAAEsC,cAAc;MACtB9D,UAAU,EAAEA,CAAA,KAAM,CAAC,GAAGG,YAAY;KACnC;EACH;EAEU6D,gBAAgBA,CAAA;IACxB,OAAO,CAACjF,QAAQ,EAAEA,QAAQ,CAAC;EAC7B;EAEOkF,KAAKA,CAAA;IACV,OAAO,IAAI3E,MAAM,CAAC,IAAI,CAAC+B,OAAO,CAAC;EACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}