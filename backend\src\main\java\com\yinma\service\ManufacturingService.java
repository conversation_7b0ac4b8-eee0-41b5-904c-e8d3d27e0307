package com.yinma.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinma.entity.Manufacturing;
import com.yinma.dto.ManufacturingDTO;
import com.yinma.vo.ManufacturingVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 设备制造管理服务接口
 * 银马实业设备制造管理系统
 * 
 * <AUTHOR>
 * @since 2024-05-20
 */
public interface ManufacturingService {

    /**
     * 分页查询制造订单
     * 
     * @param page 分页参数
     * @param orderCode 订单编号
     * @param productName 产品名称
     * @param productSeries 产品系列
     * @param status 制造状态
     * @param customerName 客户名称
     * @param planStartDate 计划开始日期
     * @param planEndDate 计划完成日期
     * @return 分页结果
     */
    IPage<ManufacturingVO> getManufacturingPage(Page<Manufacturing> page, String orderCode, 
                                               String productName, String productSeries, String status, 
                                               String customerName, String planStartDate, String planEndDate);

    /**
     * 根据ID查询制造订单详情
     * 
     * @param id 制造订单ID
     * @return 制造订单详情
     */
    ManufacturingVO getManufacturingDetailById(Long id);

    /**
     * 创建制造订单
     * 
     * @param manufacturingDTO 制造订单信息
     * @return 创建的制造订单
     */
    Manufacturing createManufacturing(ManufacturingDTO manufacturingDTO);

    /**
     * 更新制造订单
     * 
     * @param id 制造订单ID
     * @param manufacturingDTO 制造订单信息
     * @return 更新后的制造订单
     */
    Manufacturing updateManufacturing(Long id, ManufacturingDTO manufacturingDTO);

    /**
     * 删除制造订单
     * 
     * @param id 制造订单ID
     * @return 删除结果
     */
    boolean deleteManufacturing(Long id);

    /**
     * 批量删除制造订单
     * 
     * @param ids 制造订单ID列表
     * @return 删除结果
     */
    boolean batchDeleteManufacturing(List<Long> ids);

    /**
     * 更新制造订单状态
     * 
     * @param id 制造订单ID
     * @param status 新状态
     * @param remark 备注
     * @return 更新后的制造订单
     */
    Manufacturing updateManufacturingStatus(Long id, String status, String remark);

    /**
     * 复制制造订单
     * 
     * @param id 原制造订单ID
     * @param newOrderCode 新订单编号
     * @return 复制的制造订单
     */
    Manufacturing copyManufacturing(Long id, String newOrderCode);

    /**
     * 获取制造订单BOM清单
     * 
     * @param id 制造订单ID
     * @return BOM清单
     */
    List<Map<String, Object>> getManufacturingBOM(Long id);

    /**
     * 更新制造订单BOM清单
     * 
     * @param id 制造订单ID
     * @param bomList BOM清单
     */
    void updateManufacturingBOM(Long id, List<Map<String, Object>> bomList);

    /**
     * 获取制造工艺流程
     * 
     * @param id 制造订单ID
     * @return 工艺流程
     */
    List<Map<String, Object>> getManufacturingProcess(Long id);

    /**
     * 更新制造工艺流程
     * 
     * @param id 制造订单ID
     * @param processList 工艺流程
     */
    void updateManufacturingProcess(Long id, List<Map<String, Object>> processList);

    /**
     * 获取制造进度
     * 
     * @param id 制造订单ID
     * @return 制造进度
     */
    Map<String, Object> getManufacturingProgress(Long id);

    /**
     * 更新制造进度
     * 
     * @param id 制造订单ID
     * @param progressInfo 进度信息
     */
    void updateManufacturingProgress(Long id, Map<String, Object> progressInfo);

    /**
     * 获取质量检测记录
     * 
     * @param id 制造订单ID
     * @return 质量检测记录
     */
    List<Map<String, Object>> getManufacturingQuality(Long id);

    /**
     * 添加质量检测记录
     * 
     * @param id 制造订单ID
     * @param qualityRecord 质量检测记录
     */
    void addManufacturingQuality(Long id, Map<String, Object> qualityRecord);

    /**
     * 获取物料消耗记录
     * 
     * @param id 制造订单ID
     * @return 物料消耗记录
     */
    List<Map<String, Object>> getManufacturingMaterials(Long id);

    /**
     * 记录物料消耗
     * 
     * @param id 制造订单ID
     * @param materialRecord 物料消耗记录
     */
    void recordManufacturingMaterials(Long id, Map<String, Object> materialRecord);

    /**
     * 获取工人工时记录
     * 
     * @param id 制造订单ID
     * @return 工人工时记录
     */
    List<Map<String, Object>> getManufacturingWorkers(Long id);

    /**
     * 记录工人工时
     * 
     * @param id 制造订单ID
     * @param workerRecord 工时记录
     */
    void recordManufacturingWorkers(Long id, Map<String, Object> workerRecord);

    /**
     * 获取设备使用记录
     * 
     * @param id 制造订单ID
     * @return 设备使用记录
     */
    List<Map<String, Object>> getManufacturingEquipment(Long id);

    /**
     * 记录设备使用
     * 
     * @param id 制造订单ID
     * @param equipmentRecord 设备使用记录
     */
    void recordManufacturingEquipment(Long id, Map<String, Object> equipmentRecord);

    /**
     * 获取制造成本分析
     * 
     * @param id 制造订单ID
     * @return 制造成本分析
     */
    Map<String, Object> getManufacturingCost(Long id);

    /**
     * 更新制造成本
     * 
     * @param id 制造订单ID
     * @param costInfo 成本信息
     */
    void updateManufacturingCost(Long id, Map<String, Object> costInfo);

    /**
     * 上传制造过程图片
     * 
     * @param id 制造订单ID
     * @param file 图片文件
     * @param imageType 图片类型
     * @return 图片URL
     */
    String uploadManufacturingImage(Long id, MultipartFile file, String imageType);

    /**
     * 获取制造过程图片列表
     * 
     * @param id 制造订单ID
     * @return 图片列表
     */
    List<Map<String, Object>> getManufacturingImages(Long id);

    /**
     * 删除制造过程图片
     * 
     * @param id 制造订单ID
     * @param imageId 图片ID
     * @return 删除结果
     */
    boolean deleteManufacturingImage(Long id, Long imageId);

    /**
     * 上传制造文档
     * 
     * @param id 制造订单ID
     * @param file 文档文件
     * @param docType 文档类型
     * @return 文档URL
     */
    String uploadManufacturingDocument(Long id, MultipartFile file, String docType);

    /**
     * 获取制造文档列表
     * 
     * @param id 制造订单ID
     * @return 文档列表
     */
    List<Map<String, Object>> getManufacturingDocuments(Long id);

    /**
     * 删除制造文档
     * 
     * @param id 制造订单ID
     * @param docId 文档ID
     * @return 删除结果
     */
    boolean deleteManufacturingDocument(Long id, Long docId);

    /**
     * 导出制造订单数据
     * 
     * @param orderCode 订单编号
     * @param productName 产品名称
     * @param status 制造状态
     * @param format 导出格式
     * @return 导出文件URL
     */
    String exportManufacturing(String orderCode, String productName, String status, String format);

    /**
     * 导入制造订单数据
     * 
     * @param file 导入文件
     * @return 导入结果
     */
    Map<String, Object> importManufacturing(MultipartFile file);

    /**
     * 获取制造统计数据
     * 
     * @return 统计数据
     */
    Map<String, Object> getManufacturingStatistics();

    /**
     * 获取制造订单选项列表
     * 
     * @param productSeries 产品系列
     * @param status 制造状态
     * @return 选项列表
     */
    List<Map<String, Object>> getManufacturingOptions(String productSeries, String status);

    /**
     * 验证订单编号唯一性
     * 
     * @param orderCode 订单编号
     * @param excludeId 排除的ID
     * @return 是否唯一
     */
    boolean validateOrderCode(String orderCode, Long excludeId);

    /**
     * 获取制造时间线
     * 
     * @param id 制造订单ID
     * @return 时间线
     */
    List<Map<String, Object>> getManufacturingTimeline(Long id);

    /**
     * 添加制造时间线事件
     * 
     * @param id 制造订单ID
     * @param timelineEvent 时间线事件
     */
    void addManufacturingTimelineEvent(Long id, Map<String, Object> timelineEvent);

    /**
     * 获取制造预警信息
     * 
     * @param id 制造订单ID
     * @return 预警信息
     */
    List<Map<String, Object>> getManufacturingAlerts(Long id);

    /**
     * 创建制造预警
     * 
     * @param id 制造订单ID
     * @param alertInfo 预警信息
     */
    void createManufacturingAlert(Long id, Map<String, Object> alertInfo);

    /**
     * 处理制造预警
     * 
     * @param id 制造订单ID
     * @param alertId 预警ID
     * @param handleInfo 处理信息
     */
    void handleManufacturingAlert(Long id, Long alertId, Map<String, Object> handleInfo);

    /**
     * 获取制造性能指标
     * 
     * @param id 制造订单ID
     * @return 性能指标
     */
    Map<String, Object> getManufacturingPerformance(Long id);

    /**
     * 获取制造效率分析
     * 
     * @param id 制造订单ID
     * @return 效率分析
     */
    Map<String, Object> getManufacturingEfficiency(Long id);

    /**
     * 获取制造排程信息
     * 
     * @param id 制造订单ID
     * @return 排程信息
     */
    Map<String, Object> getManufacturingSchedule(Long id);

    /**
     * 更新制造排程
     * 
     * @param id 制造订单ID
     * @param scheduleInfo 排程信息
     */
    void updateManufacturingSchedule(Long id, Map<String, Object> scheduleInfo);

    /**
     * 获取制造资源配置
     * 
     * @param id 制造订单ID
     * @return 资源配置
     */
    Map<String, Object> getManufacturingResources(Long id);

    /**
     * 更新制造资源配置
     * 
     * @param id 制造订单ID
     * @param resourcesInfo 资源配置
     */
    void updateManufacturingResources(Long id, Map<String, Object> resourcesInfo);

    /**
     * 启动制造订单
     * 
     * @param id 制造订单ID
     * @param startInfo 启动信息
     */
    void startManufacturing(Long id, Map<String, Object> startInfo);

    /**
     * 暂停制造订单
     * 
     * @param id 制造订单ID
     * @param reason 暂停原因
     */
    void pauseManufacturing(Long id, String reason);

    /**
     * 恢复制造订单
     * 
     * @param id 制造订单ID
     * @param resumeInfo 恢复信息
     */
    void resumeManufacturing(Long id, Map<String, Object> resumeInfo);

    /**
     * 完成制造订单
     * 
     * @param id 制造订单ID
     * @param completeInfo 完成信息
     */
    void completeManufacturing(Long id, Map<String, Object> completeInfo);

    /**
     * 取消制造订单
     * 
     * @param id 制造订单ID
     * @param reason 取消原因
     */
    void cancelManufacturing(Long id, String reason);

    /**
     * 生成制造订单二维码
     * 
     * @param id 制造订单ID
     * @return 二维码URL
     */
    String generateManufacturingQRCode(Long id);

    /**
     * 生成制造报告
     * 
     * @param id 制造订单ID
     * @param reportType 报告类型
     * @return 报告URL
     */
    String generateManufacturingReport(Long id, String reportType);

    /**
     * 获取制造看板数据
     * 
     * @param timeRange 时间范围
     * @return 看板数据
     */
    Map<String, Object> getManufacturingDashboard(String timeRange);

    /**
     * 获取产能分析
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 产能分析
     */
    Map<String, Object> getManufacturingCapacity(String startDate, String endDate);

    /**
     * 获取工作负荷分析
     * 
     * @param department 部门
     * @param timeRange 时间范围
     * @return 工作负荷分析
     */
    Map<String, Object> getManufacturingWorkload(String department, String timeRange);

    /**
     * 获取制造趋势分析
     * 
     * @param metricType 指标类型
     * @param timeRange 时间范围
     * @return 趋势分析
     */
    Map<String, Object> getManufacturingTrends(String metricType, String timeRange);
}