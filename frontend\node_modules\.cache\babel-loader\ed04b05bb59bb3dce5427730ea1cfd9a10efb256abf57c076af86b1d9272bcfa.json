{"ast": null, "code": "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\nexport var scheme = new Array(3).concat(\"fc8d59ffffbf99d594\", \"d7191cfdae61abdda42b83ba\", \"d7191cfdae61ffffbfabdda42b83ba\", \"d53e4ffc8d59fee08be6f59899d5943288bd\", \"d53e4ffc8d59fee08bffffbfe6f59899d5943288bd\", \"d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd\", \"d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd\", \"9e0142d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd5e4fa2\", \"9e0142d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd5e4fa2\").map(colors);\nexport default ramp(scheme);", "map": {"version": 3, "names": ["colors", "ramp", "scheme", "Array", "concat", "map"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/d3-scale-chromatic/src/diverging/Spectral.js"], "sourcesContent": ["import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"fc8d59ffffbf99d594\",\n  \"d7191cfdae61abdda42b83ba\",\n  \"d7191cfdae61ffffbfabdda42b83ba\",\n  \"d53e4ffc8d59fee08be6f59899d5943288bd\",\n  \"d53e4ffc8d59fee08bffffbfe6f59899d5943288bd\",\n  \"d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd\",\n  \"d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd\",\n  \"9e0142d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd5e4fa2\",\n  \"9e0142d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd5e4fa2\"\n).map(colors);\n\nexport default ramp(scheme);\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAE7B,OAAO,IAAIC,MAAM,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CACrC,oBAAoB,EACpB,0BAA0B,EAC1B,gCAAgC,EAChC,sCAAsC,EACtC,4CAA4C,EAC5C,kDAAkD,EAClD,wDAAwD,EACxD,8DAA8D,EAC9D,oEACF,CAAC,CAACC,GAAG,CAACL,MAAM,CAAC;AAEb,eAAeC,IAAI,CAACC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}