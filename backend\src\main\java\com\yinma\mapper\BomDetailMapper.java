package com.yinma.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yinma.entity.BomDetailEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * BOM明细Mapper接口
 * 提供BOM明细相关的数据库操作
 * 
 * <AUTHOR> System
 * @since 2024-01-15
 */
@Repository
@Mapper
public interface BomDetailMapper extends BaseMapper<BomDetailEntity> {

    /**
     * 查询BOM明细列表（含物料信息）
     * 
     * @param bomId BOM主表ID
     * @param includeChildren 是否包含子级
     * @return BOM明细列表
     */
    @Select({
        "<script>",
        "WITH RECURSIVE bom_detail_tree AS (",
        "  SELECT bd.*, 1 as level, CAST(bd.id AS CHAR(1000)) as path,",
        "         m.material_name, m.material_spec, m.material_type, m.base_unit,",
        "         m.standard_cost, m.latest_purchase_price, m.main_supplier_name,",
        "         m.lead_time, m.safety_stock, m.material_status",
        "  FROM bom_detail bd",
        "  LEFT JOIN material_master m ON bd.material_code = m.material_code AND m.deleted = 0",
        "  WHERE bd.bom_id = #{bomId} AND bd.deleted = 0",
        "  <if test='!includeChildren'>",
        "    AND bd.parent_detail_id IS NULL",
        "  </if>",
        "  UNION ALL",
        "  SELECT bd2.*, bdt.level + 1, CONCAT(bdt.path, '->', bd2.id),",
        "         m2.material_name, m2.material_spec, m2.material_type, m2.base_unit,",
        "         m2.standard_cost, m2.latest_purchase_price, m2.main_supplier_name,",
        "         m2.lead_time, m2.safety_stock, m2.material_status",
        "  FROM bom_detail bd2",
        "  INNER JOIN bom_detail_tree bdt ON bd2.parent_detail_id = bdt.id",
        "  LEFT JOIN material_master m2 ON bd2.material_code = m2.material_code AND m2.deleted = 0",
        "  WHERE bd2.deleted = 0",
        "  <if test='!includeChildren'>",
        "    AND 1 = 0",
        "  </if>",
        ")",
        "SELECT * FROM bom_detail_tree",
        "ORDER BY level, sort_order, id",
        "</script>"
    })
    List<BomDetailEntity> selectBomDetailTree(@Param("bomId") Long bomId, @Param("includeChildren") Boolean includeChildren);

    /**
     * 查询指定层级的BOM明细
     * 
     * @param bomId BOM主表ID
     * @param level 层级
     * @return BOM明细列表
     */
    @Select({
        "SELECT bd.*, ",
        "       m.material_name, m.material_spec, m.material_type, m.base_unit,",
        "       m.standard_cost, m.latest_purchase_price, m.main_supplier_name,",
        "       m.lead_time, m.safety_stock, m.material_status",
        "FROM bom_detail bd",
        "LEFT JOIN material_master m ON bd.material_code = m.material_code AND m.deleted = 0",
        "WHERE bd.bom_id = #{bomId} AND bd.level = #{level} AND bd.deleted = 0",
        "ORDER BY bd.sort_order, bd.id"
    })
    List<BomDetailEntity> selectBomDetailByLevel(@Param("bomId") Long bomId, @Param("level") Integer level);

    /**
     * 查询BOM明细的子级列表
     * 
     * @param parentDetailId 父级明细ID
     * @return 子级明细列表
     */
    @Select({
        "SELECT bd.*, ",
        "       m.material_name, m.material_spec, m.material_type, m.base_unit,",
        "       m.standard_cost, m.latest_purchase_price, m.main_supplier_name,",
        "       m.lead_time, m.safety_stock, m.material_status",
        "FROM bom_detail bd",
        "LEFT JOIN material_master m ON bd.material_code = m.material_code AND m.deleted = 0",
        "WHERE bd.parent_detail_id = #{parentDetailId} AND bd.deleted = 0",
        "ORDER BY bd.sort_order, bd.id"
    })
    List<BomDetailEntity> selectChildDetails(@Param("parentDetailId") Long parentDetailId);

    /**
     * 查询物料在BOM中的使用情况
     * 
     * @param materialCode 物料编码
     * @return 使用情况列表
     */
    @Select({
        "SELECT bd.*, b.bom_code, b.bom_name, b.bom_type, b.product_code,",
        "       m.material_name as product_name",
        "FROM bom_detail bd",
        "INNER JOIN bom_master b ON bd.bom_id = b.id AND b.deleted = 0",
        "LEFT JOIN material_master m ON b.product_code = m.material_code AND m.deleted = 0",
        "WHERE bd.material_code = #{materialCode} AND bd.deleted = 0",
        "ORDER BY b.bom_code, bd.level, bd.sort_order"
    })
    List<BomDetailEntity> selectMaterialUsage(@Param("materialCode") String materialCode);

    /**
     * 查询BOM明细的物料需求汇总
     * 
     * @param bomId BOM主表ID
     * @param expandSubBom 是否展开子BOM
     * @return 物料需求汇总
     */
    @Select({
        "<script>",
        "WITH RECURSIVE material_requirements AS (",
        "  SELECT bd.material_code, bd.actual_qty as required_qty, 1 as level",
        "  FROM bom_detail bd",
        "  WHERE bd.bom_id = #{bomId} AND bd.deleted = 0",
        "  UNION ALL",
        "  SELECT bd2.material_code, mr.required_qty * bd2.actual_qty, mr.level + 1",
        "  FROM material_requirements mr",
        "  INNER JOIN bom_master b ON mr.material_code = b.product_code ",
        "    AND b.is_main_bom = 1 AND b.version_status = 'ACTIVE' AND b.deleted = 0",
        "  INNER JOIN bom_detail bd2 ON b.id = bd2.bom_id AND bd2.deleted = 0",
        "  WHERE mr.level < 10",
        "  <if test='!expandSubBom'>",
        "    AND 1 = 0",
        "  </if>",
        ")",
        "SELECT ",
        "  mr.material_code,",
        "  m.material_name,",
        "  m.material_spec,",
        "  m.material_type,",
        "  m.base_unit,",
        "  SUM(mr.required_qty) as total_required_qty,",
        "  m.latest_purchase_price as unit_price,",
        "  SUM(mr.required_qty) * m.latest_purchase_price as total_amount,",
        "  m.main_supplier_name as supplier,",
        "  m.lead_time,",
        "  COALESCE(s.current_stock, 0) as current_stock,",
        "  GREATEST(0, SUM(mr.required_qty) - COALESCE(s.current_stock, 0)) as shortage_qty",
        "FROM material_requirements mr",
        "LEFT JOIN material_master m ON mr.material_code = m.material_code AND m.deleted = 0",
        "LEFT JOIN (",
        "  SELECT material_code, SUM(current_qty) as current_stock",
        "  FROM inventory_stock WHERE deleted = 0",
        "  GROUP BY material_code",
        ") s ON mr.material_code = s.material_code",
        "WHERE m.material_type IN ('RAW', 'PURCHASE')",
        "GROUP BY mr.material_code, m.material_name, m.material_spec, m.material_type, ",
        "         m.base_unit, m.latest_purchase_price, m.main_supplier_name, m.lead_time, s.current_stock",
        "ORDER BY total_amount DESC",
        "</script>"
    })
    List<Map<String, Object>> selectMaterialRequirements(@Param("bomId") Long bomId, @Param("expandSubBom") Boolean expandSubBom);

    /**
     * 查询BOM明细成本分析
     * 
     * @param bomId BOM主表ID
     * @return 成本分析结果
     */
    @Select({
        "SELECT ",
        "  m.material_type as cost_type,",
        "  CASE m.material_type ",
        "    WHEN 'RAW' THEN '原材料成本'",
        "    WHEN 'PURCHASE' THEN '外购件成本'",
        "    WHEN 'SEMI' THEN '半成品成本'",
        "    ELSE '其他成本'",
        "  END as cost_name,",
        "  SUM(bd.total_amount) as cost_amount,",
        "  COUNT(bd.id) as material_count",
        "FROM bom_detail bd",
        "LEFT JOIN material_master m ON bd.material_code = m.material_code AND m.deleted = 0",
        "WHERE bd.bom_id = #{bomId} AND bd.deleted = 0",
        "GROUP BY m.material_type",
        "ORDER BY cost_amount DESC"
    })
    List<Map<String, Object>> selectBomCostAnalysis(@Param("bomId") Long bomId);

    /**
     * 查询关键物料列表
     * 
     * @param bomId BOM主表ID
     * @return 关键物料列表
     */
    @Select({
        "SELECT bd.*, ",
        "       m.material_name, m.material_spec, m.material_type,",
        "       m.main_supplier_name, m.lead_time",
        "FROM bom_detail bd",
        "LEFT JOIN material_master m ON bd.material_code = m.material_code AND m.deleted = 0",
        "WHERE bd.bom_id = #{bomId} AND bd.is_critical = 1 AND bd.deleted = 0",
        "ORDER BY bd.total_amount DESC, bd.sort_order"
    })
    List<BomDetailEntity> selectCriticalMaterials(@Param("bomId") Long bomId);

    /**
     * 查询可替代物料列表
     * 
     * @param bomId BOM主表ID
     * @return 可替代物料列表
     */
    @Select({
        "SELECT bd.*, ",
        "       m.material_name, m.material_spec, m.material_type",
        "FROM bom_detail bd",
        "LEFT JOIN material_master m ON bd.material_code = m.material_code AND m.deleted = 0",
        "WHERE bd.bom_id = #{bomId} AND bd.is_substitutable = 1 AND bd.deleted = 0",
        "ORDER BY bd.substitute_group, bd.priority"
    })
    List<BomDetailEntity> selectSubstitutableMaterials(@Param("bomId") Long bomId);

    /**
     * 批量插入BOM明细
     * 
     * @param details BOM明细列表
     * @return 插入数量
     */
    @Insert({
        "<script>",
        "INSERT INTO bom_detail (",
        "  bom_id, parent_detail_id, material_code, required_qty, loss_rate, actual_qty,",
        "  unit_price, total_amount, feeding_point, operation_code, operation_name,",
        "  workstation_code, workstation_name, is_critical, is_substitutable, substitute_group,",
        "  priority, level, level_path, sort_order, effective_date, expiry_date,",
        "  supplier_code, supplier_name, lead_time, min_order_qty, package_spec,",
        "  storage_location, inspection_requirement, remark, create_by, create_time",
        ") VALUES ",
        "<foreach collection='details' item='detail' separator=','>",
        "  (",
        "    #{detail.bomId}, #{detail.parentDetailId}, #{detail.materialCode},",
        "    #{detail.requiredQty}, #{detail.lossRate}, #{detail.actualQty},",
        "    #{detail.unitPrice}, #{detail.totalAmount}, #{detail.feedingPoint},",
        "    #{detail.operationCode}, #{detail.operationName}, #{detail.workstationCode},",
        "    #{detail.workstationName}, #{detail.isCritical}, #{detail.isSubstitutable},",
        "    #{detail.substituteGroup}, #{detail.priority}, #{detail.level},",
        "    #{detail.levelPath}, #{detail.sortOrder}, #{detail.effectiveDate},",
        "    #{detail.expiryDate}, #{detail.supplierCode}, #{detail.supplierName},",
        "    #{detail.leadTime}, #{detail.minOrderQty}, #{detail.packageSpec},",
        "    #{detail.storageLocation}, #{detail.inspectionRequirement}, #{detail.remark},",
        "    #{detail.createBy}, #{detail.createTime}",
        "  )",
        "</foreach>",
        "</script>"
    })
    int batchInsertBomDetails(@Param("details") List<BomDetailEntity> details);

    /**
     * 批量更新BOM明细
     * 
     * @param details BOM明细列表
     * @return 更新数量
     */
    @Update({
        "<script>",
        "<foreach collection='details' item='detail' separator=';'>",
        "UPDATE bom_detail SET ",
        "  required_qty = #{detail.requiredQty},",
        "  loss_rate = #{detail.lossRate},",
        "  actual_qty = #{detail.actualQty},",
        "  unit_price = #{detail.unitPrice},",
        "  total_amount = #{detail.totalAmount},",
        "  feeding_point = #{detail.feedingPoint},",
        "  operation_code = #{detail.operationCode},",
        "  operation_name = #{detail.operationName},",
        "  workstation_code = #{detail.workstationCode},",
        "  workstation_name = #{detail.workstationName},",
        "  is_critical = #{detail.isCritical},",
        "  is_substitutable = #{detail.isSubstitutable},",
        "  substitute_group = #{detail.substituteGroup},",
        "  priority = #{detail.priority},",
        "  sort_order = #{detail.sortOrder},",
        "  effective_date = #{detail.effectiveDate},",
        "  expiry_date = #{detail.expiryDate},",
        "  supplier_code = #{detail.supplierCode},",
        "  supplier_name = #{detail.supplierName},",
        "  lead_time = #{detail.leadTime},",
        "  min_order_qty = #{detail.minOrderQty},",
        "  package_spec = #{detail.packageSpec},",
        "  storage_location = #{detail.storageLocation},",
        "  inspection_requirement = #{detail.inspectionRequirement},",
        "  remark = #{detail.remark},",
        "  update_by = #{detail.updateBy},",
        "  update_time = #{detail.updateTime}",
        "WHERE id = #{detail.id} AND deleted = 0",
        "</foreach>",
        "</script>"
    })
    int batchUpdateBomDetails(@Param("details") List<BomDetailEntity> details);

    /**
     * 批量删除BOM明细（逻辑删除）
     * 
     * @param detailIds 明细ID列表
     * @param updateBy 更新人
     * @param updateTime 更新时间
     * @return 删除数量
     */
    @Update({
        "<script>",
        "UPDATE bom_detail SET ",
        "deleted = 1,",
        "update_by = #{updateBy},",
        "update_time = #{updateTime}",
        "WHERE id IN ",
        "<foreach collection='detailIds' item='id' open='(' separator=',' close=')'>",
        "  #{id}",
        "</foreach>",
        "AND deleted = 0",
        "</script>"
    })
    int batchDeleteBomDetails(@Param("detailIds") List<Long> detailIds, 
                             @Param("updateBy") String updateBy, 
                             @Param("updateTime") LocalDateTime updateTime);

    /**
     * 根据BOM ID删除所有明细
     * 
     * @param bomId BOM主表ID
     * @param updateBy 更新人
     * @param updateTime 更新时间
     * @return 删除数量
     */
    @Update("UPDATE bom_detail SET deleted = 1, update_by = #{updateBy}, update_time = #{updateTime} WHERE bom_id = #{bomId} AND deleted = 0")
    int deleteDetailsByBomId(@Param("bomId") Long bomId, 
                            @Param("updateBy") String updateBy, 
                            @Param("updateTime") LocalDateTime updateTime);

    /**
     * 复制BOM明细到新BOM
     * 
     * @param sourceBomId 源BOM ID
     * @param targetBomId 目标BOM ID
     * @param createBy 创建人
     * @param createTime 创建时间
     * @return 复制数量
     */
    @Insert({
        "INSERT INTO bom_detail (",
        "  bom_id, parent_detail_id, material_code, required_qty, loss_rate, actual_qty,",
        "  unit_price, total_amount, feeding_point, operation_code, operation_name,",
        "  workstation_code, workstation_name, is_critical, is_substitutable, substitute_group,",
        "  priority, level, level_path, sort_order, effective_date, expiry_date,",
        "  supplier_code, supplier_name, lead_time, min_order_qty, package_spec,",
        "  storage_location, inspection_requirement, remark, create_by, create_time",
        ")",
        "SELECT ",
        "  #{targetBomId}, parent_detail_id, material_code, required_qty, loss_rate, actual_qty,",
        "  unit_price, total_amount, feeding_point, operation_code, operation_name,",
        "  workstation_code, workstation_name, is_critical, is_substitutable, substitute_group,",
        "  priority, level, level_path, sort_order, effective_date, expiry_date,",
        "  supplier_code, supplier_name, lead_time, min_order_qty, package_spec,",
        "  storage_location, inspection_requirement, remark, #{createBy}, #{createTime}",
        "FROM bom_detail ",
        "WHERE bom_id = #{sourceBomId} AND deleted = 0"
    })
    int copyBomDetails(@Param("sourceBomId") Long sourceBomId, 
                      @Param("targetBomId") Long targetBomId,
                      @Param("createBy") String createBy, 
                      @Param("createTime") LocalDateTime createTime);

    /**
     * 更新BOM明细的层级信息
     * 
     * @param bomId BOM主表ID
     * @return 更新数量
     */
    @Update({
        "<script>",
        "WITH RECURSIVE level_update AS (",
        "  SELECT id, 1 as new_level, CAST(id AS CHAR(1000)) as new_path",
        "  FROM bom_detail ",
        "  WHERE bom_id = #{bomId} AND parent_detail_id IS NULL AND deleted = 0",
        "  UNION ALL",
        "  SELECT bd.id, lu.new_level + 1, CONCAT(lu.new_path, '->', bd.id)",
        "  FROM bom_detail bd",
        "  INNER JOIN level_update lu ON bd.parent_detail_id = lu.id",
        "  WHERE bd.deleted = 0",
        ")",
        "UPDATE bom_detail bd",
        "INNER JOIN level_update lu ON bd.id = lu.id",
        "SET bd.level = lu.new_level, bd.level_path = lu.new_path",
        "WHERE bd.bom_id = #{bomId}",
        "</script>"
    })
    int updateBomDetailLevels(@Param("bomId") Long bomId);

    /**
     * 查询BOM明细统计信息
     * 
     * @param bomId BOM主表ID
     * @return 统计信息
     */
    @Select({
        "SELECT ",
        "  COUNT(*) as total_count,",
        "  COUNT(DISTINCT material_code) as unique_material_count,",
        "  SUM(total_amount) as total_cost,",
        "  MAX(level) as max_level,",
        "  SUM(CASE WHEN is_critical = 1 THEN 1 ELSE 0 END) as critical_count,",
        "  SUM(CASE WHEN is_substitutable = 1 THEN 1 ELSE 0 END) as substitutable_count",
        "FROM bom_detail ",
        "WHERE bom_id = #{bomId} AND deleted = 0"
    })
    Map<String, Object> selectBomDetailStatistics(@Param("bomId") Long bomId);

    /**
     * 检查物料是否在BOM中使用
     * 
     * @param materialCode 物料编码
     * @param excludeBomId 排除的BOM ID
     * @return 使用数量
     */
    @Select({
        "<script>",
        "SELECT COUNT(*) FROM bom_detail bd",
        "INNER JOIN bom_master b ON bd.bom_id = b.id AND b.deleted = 0",
        "WHERE bd.material_code = #{materialCode} AND bd.deleted = 0",
        "<if test='excludeBomId != null'>",
        "  AND bd.bom_id != #{excludeBomId}",
        "</if>",
        "</script>"
    })
    int checkMaterialUsage(@Param("materialCode") String materialCode, @Param("excludeBomId") Long excludeBomId);
}