{"ast": null, "code": "function isNotNil(x) {\n  return x != null;\n}\nexport { isNotNil };", "map": {"version": 3, "names": ["isNotNil", "x"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/es-toolkit/dist/predicate/isNotNil.mjs"], "sourcesContent": ["function isNotNil(x) {\n    return x != null;\n}\n\nexport { isNotNil };\n"], "mappings": "AAAA,SAASA,QAAQA,CAACC,CAAC,EAAE;EACjB,OAAOA,CAAC,IAAI,IAAI;AACpB;AAEA,SAASD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}