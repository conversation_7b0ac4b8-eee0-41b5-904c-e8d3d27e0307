package com.yinma.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinma.common.Result;
import com.yinma.entity.ProductSeries;
import com.yinma.dto.ProductSeriesDTO;
import com.yinma.vo.ProductSeriesVO;
import com.yinma.service.ProductSeriesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 产品系列管理控制器
 * 银马实业设备产品系列管理
 * 
 * <AUTHOR>
 * @since 2024-05-20
 */
@Slf4j
@RestController
@RequestMapping("/api/product-series")
@Api(tags = "产品系列管理")
public class ProductSeriesController {

    @Autowired
    private ProductSeriesService productSeriesService;

    /**
     * 分页查询产品系列
     */
    @GetMapping("/page")
    @ApiOperation("分页查询产品系列")
    public Result<IPage<ProductSeriesVO>> getProductSeriesPage(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("系列名称") @RequestParam(required = false) String seriesName,
            @ApiParam("系列代码") @RequestParam(required = false) String seriesCode,
            @ApiParam("产品类型") @RequestParam(required = false) String productType,
            @ApiParam("技术等级") @RequestParam(required = false) String techLevel,
            @ApiParam("状态") @RequestParam(required = false) String status) {
        
        Page<ProductSeries> page = new Page<>(current, size);
        IPage<ProductSeriesVO> result = productSeriesService.getProductSeriesPage(
                page, seriesName, seriesCode, productType, techLevel, status);
        
        return Result.success(result);
    }

    /**
     * 根据ID查询产品系列详情
     */
    @GetMapping("/{id}")
    @ApiOperation("查询产品系列详情")
    public Result<ProductSeriesVO> getProductSeriesById(
            @ApiParam("产品系列ID") @PathVariable Long id) {
        
        ProductSeriesVO productSeries = productSeriesService.getProductSeriesDetailById(id);
        if (productSeries == null) {
            return Result.error("产品系列不存在");
        }
        
        return Result.success(productSeries);
    }

    /**
     * 创建产品系列
     */
    @PostMapping
    @ApiOperation("创建产品系列")
    public Result<ProductSeries> createProductSeries(
            @ApiParam("产品系列信息") @Valid @RequestBody ProductSeriesDTO productSeriesDTO) {
        
        try {
            ProductSeries productSeries = productSeriesService.createProductSeries(productSeriesDTO);
            return Result.success(productSeries);
        } catch (Exception e) {
            log.error("创建产品系列失败", e);
            return Result.error("创建产品系列失败: " + e.getMessage());
        }
    }

    /**
     * 更新产品系列
     */
    @PutMapping("/{id}")
    @ApiOperation("更新产品系列")
    public Result<ProductSeries> updateProductSeries(
            @ApiParam("产品系列ID") @PathVariable Long id,
            @ApiParam("产品系列信息") @Valid @RequestBody ProductSeriesDTO productSeriesDTO) {
        
        try {
            ProductSeries productSeries = productSeriesService.updateProductSeries(id, productSeriesDTO);
            return Result.success(productSeries);
        } catch (Exception e) {
            log.error("更新产品系列失败", e);
            return Result.error("更新产品系列失败: " + e.getMessage());
        }
    }

    /**
     * 删除产品系列
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除产品系列")
    public Result<Void> deleteProductSeries(
            @ApiParam("产品系列ID") @PathVariable Long id) {
        
        try {
            boolean success = productSeriesService.deleteProductSeries(id);
            if (success) {
                return Result.success();
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除产品系列失败", e);
            return Result.error("删除产品系列失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除产品系列
     */
    @DeleteMapping("/batch")
    @ApiOperation("批量删除产品系列")
    public Result<Void> batchDeleteProductSeries(
            @ApiParam("产品系列ID列表") @RequestBody List<Long> ids) {
        
        try {
            boolean success = productSeriesService.batchDeleteProductSeries(ids);
            if (success) {
                return Result.success();
            } else {
                return Result.error("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除产品系列失败", e);
            return Result.error("批量删除产品系列失败: " + e.getMessage());
        }
    }

    /**
     * 更新产品系列状态
     */
    @PutMapping("/{id}/status")
    @ApiOperation("更新产品系列状态")
    public Result<ProductSeries> updateProductSeriesStatus(
            @ApiParam("产品系列ID") @PathVariable Long id,
            @ApiParam("状态") @RequestParam String status,
            @ApiParam("备注") @RequestParam(required = false) String remark) {
        
        try {
            ProductSeries productSeries = productSeriesService.updateProductSeriesStatus(id, status, remark);
            return Result.success(productSeries);
        } catch (Exception e) {
            log.error("更新产品系列状态失败", e);
            return Result.error("更新状态失败: " + e.getMessage());
        }
    }

    /**
     * 复制产品系列
     */
    @PostMapping("/{id}/copy")
    @ApiOperation("复制产品系列")
    public Result<ProductSeries> copyProductSeries(
            @ApiParam("产品系列ID") @PathVariable Long id,
            @ApiParam("新系列名称") @RequestParam String newSeriesName,
            @ApiParam("新系列代码") @RequestParam String newSeriesCode) {
        
        try {
            ProductSeries productSeries = productSeriesService.copyProductSeries(id, newSeriesName, newSeriesCode);
            return Result.success(productSeries);
        } catch (Exception e) {
            log.error("复制产品系列失败", e);
            return Result.error("复制产品系列失败: " + e.getMessage());
        }
    }

    /**
     * 获取产品系列配置模板
     */
    @GetMapping("/{id}/template")
    @ApiOperation("获取产品系列配置模板")
    public Result<Map<String, Object>> getProductSeriesTemplate(
            @ApiParam("产品系列ID") @PathVariable Long id) {
        
        try {
            Map<String, Object> template = productSeriesService.getProductSeriesTemplate(id);
            return Result.success(template);
        } catch (Exception e) {
            log.error("获取产品系列配置模板失败", e);
            return Result.error("获取配置模板失败: " + e.getMessage());
        }
    }

    /**
     * 保存产品系列配置模板
     */
    @PostMapping("/{id}/template")
    @ApiOperation("保存产品系列配置模板")
    public Result<Void> saveProductSeriesTemplate(
            @ApiParam("产品系列ID") @PathVariable Long id,
            @ApiParam("配置模板") @RequestBody Map<String, Object> template) {
        
        try {
            productSeriesService.saveProductSeriesTemplate(id, template);
            return Result.success();
        } catch (Exception e) {
            log.error("保存产品系列配置模板失败", e);
            return Result.error("保存配置模板失败: " + e.getMessage());
        }
    }

    /**
     * 获取产品系列技术参数
     */
    @GetMapping("/{id}/specifications")
    @ApiOperation("获取产品系列技术参数")
    public Result<Map<String, Object>> getProductSeriesSpecifications(
            @ApiParam("产品系列ID") @PathVariable Long id) {
        
        try {
            Map<String, Object> specifications = productSeriesService.getProductSeriesSpecifications(id);
            return Result.success(specifications);
        } catch (Exception e) {
            log.error("获取产品系列技术参数失败", e);
            return Result.error("获取技术参数失败: " + e.getMessage());
        }
    }

    /**
     * 更新产品系列技术参数
     */
    @PutMapping("/{id}/specifications")
    @ApiOperation("更新产品系列技术参数")
    public Result<Void> updateProductSeriesSpecifications(
            @ApiParam("产品系列ID") @PathVariable Long id,
            @ApiParam("技术参数") @RequestBody Map<String, Object> specifications) {
        
        try {
            productSeriesService.updateProductSeriesSpecifications(id, specifications);
            return Result.success();
        } catch (Exception e) {
            log.error("更新产品系列技术参数失败", e);
            return Result.error("更新技术参数失败: " + e.getMessage());
        }
    }

    /**
     * 获取产品系列BOM清单
     */
    @GetMapping("/{id}/bom")
    @ApiOperation("获取产品系列BOM清单")
    public Result<List<Map<String, Object>>> getProductSeriesBom(
            @ApiParam("产品系列ID") @PathVariable Long id,
            @ApiParam("BOM版本") @RequestParam(required = false) String version) {
        
        try {
            List<Map<String, Object>> bom = productSeriesService.getProductSeriesBom(id, version);
            return Result.success(bom);
        } catch (Exception e) {
            log.error("获取产品系列BOM清单失败", e);
            return Result.error("获取BOM清单失败: " + e.getMessage());
        }
    }

    /**
     * 更新产品系列BOM清单
     */
    @PutMapping("/{id}/bom")
    @ApiOperation("更新产品系列BOM清单")
    public Result<Void> updateProductSeriesBom(
            @ApiParam("产品系列ID") @PathVariable Long id,
            @ApiParam("BOM清单") @RequestBody List<Map<String, Object>> bom,
            @ApiParam("版本号") @RequestParam(required = false) String version) {
        
        try {
            productSeriesService.updateProductSeriesBom(id, bom, version);
            return Result.success();
        } catch (Exception e) {
            log.error("更新产品系列BOM清单失败", e);
            return Result.error("更新BOM清单失败: " + e.getMessage());
        }
    }

    /**
     * 获取产品系列价格配置
     */
    @GetMapping("/{id}/pricing")
    @ApiOperation("获取产品系列价格配置")
    public Result<Map<String, Object>> getProductSeriesPricing(
            @ApiParam("产品系列ID") @PathVariable Long id) {
        
        try {
            Map<String, Object> pricing = productSeriesService.getProductSeriesPricing(id);
            return Result.success(pricing);
        } catch (Exception e) {
            log.error("获取产品系列价格配置失败", e);
            return Result.error("获取价格配置失败: " + e.getMessage());
        }
    }

    /**
     * 更新产品系列价格配置
     */
    @PutMapping("/{id}/pricing")
    @ApiOperation("更新产品系列价格配置")
    public Result<Void> updateProductSeriesPricing(
            @ApiParam("产品系列ID") @PathVariable Long id,
            @ApiParam("价格配置") @RequestBody Map<String, Object> pricing) {
        
        try {
            productSeriesService.updateProductSeriesPricing(id, pricing);
            return Result.success();
        } catch (Exception e) {
            log.error("更新产品系列价格配置失败", e);
            return Result.error("更新价格配置失败: " + e.getMessage());
        }
    }

    /**
     * 上传产品系列图片
     */
    @PostMapping("/{id}/images")
    @ApiOperation("上传产品系列图片")
    public Result<String> uploadProductSeriesImage(
            @ApiParam("产品系列ID") @PathVariable Long id,
            @ApiParam("图片文件") @RequestParam("file") MultipartFile file,
            @ApiParam("图片类型") @RequestParam(required = false) String imageType) {
        
        try {
            String imageUrl = productSeriesService.uploadProductSeriesImage(id, file, imageType);
            return Result.success(imageUrl);
        } catch (Exception e) {
            log.error("上传产品系列图片失败", e);
            return Result.error("上传图片失败: " + e.getMessage());
        }
    }

    /**
     * 获取产品系列图片列表
     */
    @GetMapping("/{id}/images")
    @ApiOperation("获取产品系列图片列表")
    public Result<List<Map<String, Object>>> getProductSeriesImages(
            @ApiParam("产品系列ID") @PathVariable Long id) {
        
        try {
            List<Map<String, Object>> images = productSeriesService.getProductSeriesImages(id);
            return Result.success(images);
        } catch (Exception e) {
            log.error("获取产品系列图片列表失败", e);
            return Result.error("获取图片列表失败: " + e.getMessage());
        }
    }

    /**
     * 删除产品系列图片
     */
    @DeleteMapping("/{id}/images/{imageId}")
    @ApiOperation("删除产品系列图片")
    public Result<Void> deleteProductSeriesImage(
            @ApiParam("产品系列ID") @PathVariable Long id,
            @ApiParam("图片ID") @PathVariable Long imageId) {
        
        try {
            boolean success = productSeriesService.deleteProductSeriesImage(id, imageId);
            if (success) {
                return Result.success();
            } else {
                return Result.error("删除图片失败");
            }
        } catch (Exception e) {
            log.error("删除产品系列图片失败", e);
            return Result.error("删除图片失败: " + e.getMessage());
        }
    }

    /**
     * 导出产品系列数据
     */
    @GetMapping("/export")
    @ApiOperation("导出产品系列数据")
    public Result<String> exportProductSeries(
            @ApiParam("系列名称") @RequestParam(required = false) String seriesName,
            @ApiParam("产品类型") @RequestParam(required = false) String productType,
            @ApiParam("状态") @RequestParam(required = false) String status,
            @ApiParam("导出格式") @RequestParam(defaultValue = "excel") String format) {
        
        try {
            String fileUrl = productSeriesService.exportProductSeries(seriesName, productType, status, format);
            return Result.success(fileUrl);
        } catch (Exception e) {
            log.error("导出产品系列数据失败", e);
            return Result.error("导出数据失败: " + e.getMessage());
        }
    }

    /**
     * 导入产品系列数据
     */
    @PostMapping("/import")
    @ApiOperation("导入产品系列数据")
    public Result<Map<String, Object>> importProductSeries(
            @ApiParam("导入文件") @RequestParam("file") MultipartFile file) {
        
        try {
            Map<String, Object> result = productSeriesService.importProductSeries(file);
            return Result.success(result);
        } catch (Exception e) {
            log.error("导入产品系列数据失败", e);
            return Result.error("导入数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取产品系列统计数据
     */
    @GetMapping("/statistics")
    @ApiOperation("获取产品系列统计数据")
    public Result<Map<String, Object>> getProductSeriesStatistics() {
        
        try {
            Map<String, Object> statistics = productSeriesService.getProductSeriesStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取产品系列统计数据失败", e);
            return Result.error("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取产品系列选项列表
     */
    @GetMapping("/options")
    @ApiOperation("获取产品系列选项列表")
    public Result<List<Map<String, Object>>> getProductSeriesOptions(
            @ApiParam("产品类型") @RequestParam(required = false) String productType,
            @ApiParam("技术等级") @RequestParam(required = false) String techLevel) {
        
        try {
            List<Map<String, Object>> options = productSeriesService.getProductSeriesOptions(productType, techLevel);
            return Result.success(options);
        } catch (Exception e) {
            log.error("获取产品系列选项列表失败", e);
            return Result.error("获取选项列表失败: " + e.getMessage());
        }
    }

    /**
     * 验证产品系列代码唯一性
     */
    @GetMapping("/validate-code")
    @ApiOperation("验证产品系列代码唯一性")
    public Result<Boolean> validateSeriesCode(
            @ApiParam("系列代码") @RequestParam String seriesCode,
            @ApiParam("排除的ID") @RequestParam(required = false) Long excludeId) {
        
        try {
            boolean isUnique = productSeriesService.validateSeriesCode(seriesCode, excludeId);
            return Result.success(isUnique);
        } catch (Exception e) {
            log.error("验证产品系列代码唯一性失败", e);
            return Result.error("验证失败: " + e.getMessage());
        }
    }

    /**
     * 获取产品系列推荐配置
     */
    @GetMapping("/{id}/recommendations")
    @ApiOperation("获取产品系列推荐配置")
    public Result<Map<String, Object>> getProductSeriesRecommendations(
            @ApiParam("产品系列ID") @PathVariable Long id,
            @ApiParam("客户需求") @RequestParam(required = false) String customerRequirements) {
        
        try {
            Map<String, Object> recommendations = productSeriesService.getProductSeriesRecommendations(id, customerRequirements);
            return Result.success(recommendations);
        } catch (Exception e) {
            log.error("获取产品系列推荐配置失败", e);
            return Result.error("获取推荐配置失败: " + e.getMessage());
        }
    }

    /**
     * 生成产品系列配置报告
     */
    @PostMapping("/{id}/report")
    @ApiOperation("生成产品系列配置报告")
    public Result<String> generateProductSeriesReport(
            @ApiParam("产品系列ID") @PathVariable Long id,
            @ApiParam("报告配置") @RequestBody Map<String, Object> reportConfig) {
        
        try {
            String reportUrl = productSeriesService.generateProductSeriesReport(id, reportConfig);
            return Result.success(reportUrl);
        } catch (Exception e) {
            log.error("生成产品系列配置报告失败", e);
            return Result.error("生成报告失败: " + e.getMessage());
        }
    }
}