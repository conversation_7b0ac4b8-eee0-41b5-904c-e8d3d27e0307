-- 西安银马实业数字化管理系统数据库初始化脚本
-- PostgreSQL 13+
-- 创建时间: 2024-01-01
-- 作者: 开发团队

-- 创建数据库（如果不存在）
-- CREATE DATABASE yinma_management_system;

-- 使用数据库
-- \c yinma_management_system;

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ===========================================
-- 系统管理表
-- ===========================================

-- 用户表
CREATE TABLE IF NOT EXISTS sys_user (
    id BIGINT PRIMARY KEY DEFAULT nextval('seq_sys_user'),
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    nickname VARCHAR(50) COMMENT '昵称',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    gender CHAR(1) DEFAULT 'U' COMMENT '性别：M-男，F-女，U-未知',
    avatar VARCHAR(500) COMMENT '头像',
    dept_id BIGINT COMMENT '部门ID',
    dept_name VARCHAR(100) COMMENT '部门名称',
    position VARCHAR(100) COMMENT '职位',
    role_ids VARCHAR(500) COMMENT '角色ID列表（逗号分隔）',
    role_names VARCHAR(500) COMMENT '角色名称列表（逗号分隔）',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-启用，INACTIVE-停用，LOCKED-锁定',
    last_login_time TIMESTAMP COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    login_fail_count INTEGER DEFAULT 0 COMMENT '登录失败次数',
    lock_time TIMESTAMP COMMENT '账户锁定时间',
    password_expire_time TIMESTAMP COMMENT '密码过期时间',
    remark TEXT COMMENT '备注',
    create_by VARCHAR(50) COMMENT '创建人',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(50) COMMENT '更新人',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted INTEGER DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）'
);

-- 创建序列
CREATE SEQUENCE IF NOT EXISTS seq_sys_user START 1000;

-- 用户表索引
CREATE INDEX IF NOT EXISTS idx_sys_user_username ON sys_user(username);
CREATE INDEX IF NOT EXISTS idx_sys_user_email ON sys_user(email);
CREATE INDEX IF NOT EXISTS idx_sys_user_phone ON sys_user(phone);
CREATE INDEX IF NOT EXISTS idx_sys_user_dept_id ON sys_user(dept_id);
CREATE INDEX IF NOT EXISTS idx_sys_user_status ON sys_user(status);
CREATE INDEX IF NOT EXISTS idx_sys_user_deleted ON sys_user(deleted);

-- ===========================================
-- BOM管理表
-- ===========================================

-- BOM主表
CREATE TABLE IF NOT EXISTS bom (
    id BIGINT PRIMARY KEY DEFAULT nextval('seq_bom'),
    bom_code VARCHAR(50) NOT NULL UNIQUE COMMENT 'BOM编码',
    bom_name VARCHAR(200) NOT NULL COMMENT 'BOM名称',
    product_code VARCHAR(50) NOT NULL COMMENT '产品编码',
    product_name VARCHAR(200) NOT NULL COMMENT '产品名称',
    bom_type VARCHAR(20) NOT NULL COMMENT 'BOM类型：EBOM-工程BOM，PBOM-工艺BOM，MBOM-制造BOM',
    version VARCHAR(20) NOT NULL COMMENT 'BOM版本',
    status VARCHAR(20) DEFAULT 'DRAFT' COMMENT '状态：DRAFT-草稿，ACTIVE-生效，INACTIVE-失效',
    effective_date TIMESTAMP COMMENT '生效日期',
    expiry_date TIMESTAMP COMMENT '失效日期',
    base_quantity DECIMAL(18,6) NOT NULL DEFAULT 1 COMMENT '基本数量',
    unit VARCHAR(20) NOT NULL COMMENT '计量单位',
    remark TEXT COMMENT '备注',
    create_by VARCHAR(50) COMMENT '创建人',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(50) COMMENT '更新人',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted INTEGER DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）'
);

-- 创建序列
CREATE SEQUENCE IF NOT EXISTS seq_bom START 1000;

-- BOM主表索引
CREATE INDEX IF NOT EXISTS idx_bom_code ON bom(bom_code);
CREATE INDEX IF NOT EXISTS idx_bom_product_code ON bom(product_code);
CREATE INDEX IF NOT EXISTS idx_bom_type ON bom(bom_type);
CREATE INDEX IF NOT EXISTS idx_bom_status ON bom(status);
CREATE INDEX IF NOT EXISTS idx_bom_version ON bom(version);
CREATE INDEX IF NOT EXISTS idx_bom_deleted ON bom(deleted);

-- BOM明细表
CREATE TABLE IF NOT EXISTS bom_detail (
    id BIGINT PRIMARY KEY DEFAULT nextval('seq_bom_detail'),
    bom_id BIGINT NOT NULL COMMENT 'BOM主表ID',
    parent_material_code VARCHAR(50) NOT NULL COMMENT '父级物料编码',
    child_material_code VARCHAR(50) NOT NULL COMMENT '子级物料编码',
    child_material_name VARCHAR(200) NOT NULL COMMENT '子级物料名称',
    specification VARCHAR(200) COMMENT '物料规格',
    model VARCHAR(100) COMMENT '物料型号',
    quantity DECIMAL(18,6) NOT NULL COMMENT '需求数量',
    unit VARCHAR(20) NOT NULL COMMENT '计量单位',
    loss_rate DECIMAL(5,2) DEFAULT 0 COMMENT '损耗率(%)',
    substitute_flag INTEGER DEFAULT 0 COMMENT '替代料标识：0-主料，1-替代料',
    substitute_group VARCHAR(50) COMMENT '替代料组',
    priority INTEGER DEFAULT 1 COMMENT '优先级',
    level INTEGER DEFAULT 1 COMMENT '层级',
    sequence INTEGER DEFAULT 1 COMMENT '序号',
    effective_date TIMESTAMP COMMENT '生效日期',
    expiry_date TIMESTAMP COMMENT '失效日期',
    remark TEXT COMMENT '备注',
    create_by VARCHAR(50) COMMENT '创建人',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(50) COMMENT '更新人',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted INTEGER DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
    FOREIGN KEY (bom_id) REFERENCES bom(id)
);

-- 创建序列
CREATE SEQUENCE IF NOT EXISTS seq_bom_detail START 1000;

-- BOM明细表索引
CREATE INDEX IF NOT EXISTS idx_bom_detail_bom_id ON bom_detail(bom_id);
CREATE INDEX IF NOT EXISTS idx_bom_detail_parent_code ON bom_detail(parent_material_code);
CREATE INDEX IF NOT EXISTS idx_bom_detail_child_code ON bom_detail(child_material_code);
CREATE INDEX IF NOT EXISTS idx_bom_detail_level ON bom_detail(level);
CREATE INDEX IF NOT EXISTS idx_bom_detail_deleted ON bom_detail(deleted);

-- ===========================================
-- 物料管理表
-- ===========================================

-- 物料主数据表
CREATE TABLE IF NOT EXISTS material (
    id BIGINT PRIMARY KEY DEFAULT nextval('seq_material'),
    material_code VARCHAR(50) NOT NULL UNIQUE COMMENT '物料编码',
    material_name VARCHAR(200) NOT NULL COMMENT '物料名称',
    short_name VARCHAR(100) COMMENT '物料简称',
    category VARCHAR(100) NOT NULL COMMENT '物料分类',
    material_type VARCHAR(20) NOT NULL COMMENT '物料类型：RAW-原材料，SEMI-半成品，FINISHED-成品，SPARE-备件',
    specification VARCHAR(200) COMMENT '规格型号',
    model VARCHAR(100) COMMENT '型号',
    brand VARCHAR(100) COMMENT '品牌',
    base_unit VARCHAR(20) NOT NULL COMMENT '基本计量单位',
    purchase_unit VARCHAR(20) COMMENT '采购计量单位',
    stock_unit VARCHAR(20) COMMENT '库存计量单位',
    sales_unit VARCHAR(20) COMMENT '销售计量单位',
    standard_cost DECIMAL(18,4) COMMENT '标准成本',
    latest_purchase_price DECIMAL(18,4) COMMENT '最新采购价',
    average_purchase_price DECIMAL(18,4) COMMENT '平均采购价',
    sales_price DECIMAL(18,4) COMMENT '销售价格',
    safety_stock DECIMAL(18,6) COMMENT '安全库存',
    min_stock DECIMAL(18,6) COMMENT '最小库存',
    max_stock DECIMAL(18,6) COMMENT '最大库存',
    lead_time INTEGER COMMENT '采购提前期(天)',
    abc_category CHAR(1) COMMENT 'ABC分类：A-重要，B-一般，C-次要',
    main_supplier_code VARCHAR(50) COMMENT '主供应商编码',
    main_supplier_name VARCHAR(200) COMMENT '主供应商名称',
    quality_grade VARCHAR(20) COMMENT '质量等级',
    shelf_life INTEGER COMMENT '保质期(天)',
    storage_condition VARCHAR(200) COMMENT '存储条件',
    batch_management INTEGER DEFAULT 0 COMMENT '是否批次管理：0-否，1-是',
    serial_management INTEGER DEFAULT 0 COMMENT '是否序列号管理：0-否，1-是',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-启用，INACTIVE-停用',
    remark TEXT COMMENT '备注',
    create_by VARCHAR(50) COMMENT '创建人',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(50) COMMENT '更新人',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted INTEGER DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）'
);

-- 创建序列
CREATE SEQUENCE IF NOT EXISTS seq_material START 1000;

-- 物料主数据表索引
CREATE INDEX IF NOT EXISTS idx_material_code ON material(material_code);
CREATE INDEX IF NOT EXISTS idx_material_name ON material(material_name);
CREATE INDEX IF NOT EXISTS idx_material_category ON material(category);
CREATE INDEX IF NOT EXISTS idx_material_type ON material(material_type);
CREATE INDEX IF NOT EXISTS idx_material_abc_category ON material(abc_category);
CREATE INDEX IF NOT EXISTS idx_material_status ON material(status);
CREATE INDEX IF NOT EXISTS idx_material_deleted ON material(deleted);

-- ===========================================
-- BOM变更管理表
-- ===========================================

-- BOM变更日志表
CREATE TABLE IF NOT EXISTS bom_change_log (
    id BIGINT PRIMARY KEY DEFAULT nextval('seq_bom_change_log'),
    change_no VARCHAR(50) NOT NULL UNIQUE COMMENT '变更单号',
    bom_code VARCHAR(50) NOT NULL COMMENT 'BOM编码',
    bom_name VARCHAR(200) NOT NULL COMMENT 'BOM名称',
    change_type VARCHAR(20) NOT NULL COMMENT '变更类型：ADD-新增，UPDATE-修改，DELETE-删除',
    change_object VARCHAR(20) NOT NULL COMMENT '变更对象：BOM-BOM主表，DETAIL-BOM明细',
    change_field VARCHAR(100) COMMENT '变更字段',
    old_value TEXT COMMENT '变更前值',
    new_value TEXT COMMENT '变更后值',
    change_reason VARCHAR(500) NOT NULL COMMENT '变更原因',
    change_description TEXT COMMENT '变更描述',
    impact_analysis TEXT COMMENT '影响分析',
    change_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '变更状态：PENDING-待审核，APPROVED-已批准，REJECTED-已拒绝，IMPLEMENTED-已实施',
    applicant VARCHAR(50) NOT NULL COMMENT '申请人',
    apply_time TIMESTAMP NOT NULL COMMENT '申请时间',
    reviewer VARCHAR(50) COMMENT '审核人',
    review_time TIMESTAMP COMMENT '审核时间',
    review_comment TEXT COMMENT '审核意见',
    implementer VARCHAR(50) COMMENT '实施人',
    implement_time TIMESTAMP COMMENT '实施时间',
    implement_result TEXT COMMENT '实施结果',
    remark TEXT COMMENT '备注',
    create_by VARCHAR(50) COMMENT '创建人',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(50) COMMENT '更新人',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted INTEGER DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）'
);

-- 创建序列
CREATE SEQUENCE IF NOT EXISTS seq_bom_change_log START 1000;

-- BOM变更日志表索引
CREATE INDEX IF NOT EXISTS idx_bom_change_log_change_no ON bom_change_log(change_no);
CREATE INDEX IF NOT EXISTS idx_bom_change_log_bom_code ON bom_change_log(bom_code);
CREATE INDEX IF NOT EXISTS idx_bom_change_log_change_type ON bom_change_log(change_type);
CREATE INDEX IF NOT EXISTS idx_bom_change_log_change_status ON bom_change_log(change_status);
CREATE INDEX IF NOT EXISTS idx_bom_change_log_applicant ON bom_change_log(applicant);
CREATE INDEX IF NOT EXISTS idx_bom_change_log_apply_time ON bom_change_log(apply_time);
CREATE INDEX IF NOT EXISTS idx_bom_change_log_deleted ON bom_change_log(deleted);

-- ===========================================
-- 初始化数据
-- ===========================================

-- 插入默认管理员用户
INSERT INTO sys_user (id, username, password, real_name, email, dept_name, position, role_ids, role_names, status, create_by, update_by)
VALUES (1, 'admin', '$2a$10$7JB720yubVSOfvVWbazBuOWWZHrOBXjbZcNLAcBYQVYdZSfHAJxOa', '系统管理员', '<EMAIL>', '信息技术部', '系统管理员', '1', '超级管理员', 'ACTIVE', 'system', 'system')
ON CONFLICT (username) DO NOTHING;

-- 插入测试用户
INSERT INTO sys_user (id, username, password, real_name, email, dept_name, position, role_ids, role_names, status, create_by, update_by)
VALUES (2, 'test', '$2a$10$7JB720yubVSOfvVWbazBuOWWZHrOBXjbZcNLAcBYQVYdZSfHAJxOa', '测试用户', '<EMAIL>', '技术部', '测试工程师', '2', '普通用户', 'ACTIVE', 'system', 'system')
ON CONFLICT (username) DO NOTHING;

-- 插入示例物料数据
INSERT INTO material (id, material_code, material_name, category, material_type, specification, base_unit, status, create_by, update_by)
VALUES 
(1, 'M001', '钢板Q235', '原材料', 'RAW', '10mm*1000mm*2000mm', 'kg', 'ACTIVE', 'system', 'system'),
(2, 'M002', '螺栓M8*20', '标准件', 'RAW', 'M8*20', 'pcs', 'ACTIVE', 'system', 'system'),
(3, 'M003', '电机YE2-90L-4', '电机', 'RAW', '1.1KW 380V', 'pcs', 'ACTIVE', 'system', 'system'),
(4, 'P001', '搅拌机主体', '半成品', 'SEMI', '型号JS1000', 'pcs', 'ACTIVE', 'system', 'system'),
(5, 'F001', 'JS1000搅拌机', '成品', 'FINISHED', '1立方米搅拌机', 'pcs', 'ACTIVE', 'system', 'system')
ON CONFLICT (material_code) DO NOTHING;

-- 插入示例BOM数据
INSERT INTO bom (id, bom_code, bom_name, product_code, product_name, bom_type, version, status, base_quantity, unit, create_by, update_by)
VALUES 
(1, 'BOM001', 'JS1000搅拌机BOM', 'F001', 'JS1000搅拌机', 'MBOM', 'V1.0', 'ACTIVE', 1, 'pcs', 'system', 'system')
ON CONFLICT (bom_code) DO NOTHING;

-- 插入示例BOM明细数据
INSERT INTO bom_detail (id, bom_id, parent_material_code, child_material_code, child_material_name, quantity, unit, level, sequence, create_by, update_by)
VALUES 
(1, 1, 'F001', 'P001', '搅拌机主体', 1, 'pcs', 1, 1, 'system', 'system'),
(2, 1, 'F001', 'M003', '电机YE2-90L-4', 1, 'pcs', 1, 2, 'system', 'system'),
(3, 1, 'P001', 'M001', '钢板Q235', 50, 'kg', 2, 1, 'system', 'system'),
(4, 1, 'P001', 'M002', '螺栓M8*20', 20, 'pcs', 2, 2, 'system', 'system')
ON CONFLICT (id) DO NOTHING;

-- 提交事务
COMMIT;

-- 输出完成信息
SELECT '数据库初始化完成！' AS message;