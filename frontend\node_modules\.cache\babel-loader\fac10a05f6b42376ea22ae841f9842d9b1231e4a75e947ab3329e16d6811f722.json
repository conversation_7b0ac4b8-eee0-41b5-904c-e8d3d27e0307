{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport InsuranceFilledSvg from \"@ant-design/icons-svg/es/asn/InsuranceFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar InsuranceFilled = function InsuranceFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: InsuranceFilledSvg\n  }));\n};\n\n/**![insurance](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxOS45IDM1OC44aDk3Ljl2NDEuNmgtOTcuOXptMzQ3LTE4OC45TDUyNy4xIDU0LjFDNTIzIDUyLjcgNTE3LjUgNTIgNTEyIDUycy0xMSAuNy0xNS4xIDIuMUwxNTcuMSAxNjkuOWMtOC4zIDIuOC0xNS4xIDEyLjQtMTUuMSAyMS4ydjQ4Mi40YzAgOC44IDUuNyAyMC40IDEyLjYgMjUuOUw0OTkuMyA5NjhjMy41IDIuNyA4IDQuMSAxMi42IDQuMXM5LjItMS40IDEyLjYtNC4xbDM0NC43LTI2OC42YzYuOS01LjQgMTIuNi0xNyAxMi42LTI1LjlWMTkxLjFjLjItOC44LTYuNi0xOC4zLTE0LjktMjEuMnpNNDExLjMgNjU2aC0uMmMwIDQuNC0zLjYgOC04IDhoLTM3LjNjLTQuNCAwLTgtMy42LTgtOFY0NzEuNGMtNy43IDkuMi0xNS40IDE3LjktMjMuMSAyNmE2LjA0IDYuMDQgMCAwMS0xMC4yLTIuNGwtMTMuMi00My41Yy0uNi0yLS4yLTQuMSAxLjItNS42IDM3LTQzLjQgNjQuNy05NS4xIDgyLjItMTUzLjYgMS4xLTMuNSA1LTUuMyA4LjQtMy43bDM4LjYgMTguM2MyLjcgMS4zIDQuMSA0LjQgMy4yIDcuMmE0MjkuMiA0MjkuMiAwIDAxLTMzLjYgNzlWNjU2em0yOTYuNS00OS4ybC0yNi4zIDM1LjNhNS45MiA1LjkyIDAgMDEtOC45LjdjLTMwLjYtMjkuMy01Ni44LTY1LjItNzguMS0xMDYuOVY2NTZjMCA0LjQtMy42IDgtOCA4aC0zNi4yYy00LjQgMC04LTMuNi04LThWNTM2Yy0yMiA0NC43LTQ5IDgwLjgtODAuNiAxMDcuNmE1LjkgNS45IDAgMDEtOC45LTEuNEw0MzAgNjA1LjdhNiA2IDAgMDExLjYtOC4xYzI4LjYtMjAuMyA1MS45LTQ1LjIgNzEtNzZoLTU1LjFjLTQuNCAwLTgtMy42LTgtOFY0NzhjMC00LjQgMy42LTggOC04aDk0Ljl2LTE4LjZoLTY1LjljLTQuNCAwLTgtMy42LTgtOFYzMTZjMC00LjQgMy42LTggOC04aDE4NC43YzQuNCAwIDggMy42IDggOHYxMjcuMmMwIDQuNC0zLjYgOC04IDhoLTY2Ljd2MTguNmg5OC44YzQuNCAwIDggMy42IDggOHYzNS42YzAgNC40LTMuNiA4LTggOGgtNTljMTguMSAyOS4xIDQxLjggNTQuMyA3Mi4zIDc2LjkgMi42IDIuMSAzLjIgNS45IDEuMiA4LjV6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(InsuranceFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'InsuranceFilled';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "InsuranceFilledSvg", "AntdIcon", "InsuranceFilled", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/@ant-design/icons/es/icons/InsuranceFilled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport InsuranceFilledSvg from \"@ant-design/icons-svg/es/asn/InsuranceFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar InsuranceFilled = function InsuranceFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: InsuranceFilledSvg\n  }));\n};\n\n/**![insurance](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxOS45IDM1OC44aDk3Ljl2NDEuNmgtOTcuOXptMzQ3LTE4OC45TDUyNy4xIDU0LjFDNTIzIDUyLjcgNTE3LjUgNTIgNTEyIDUycy0xMSAuNy0xNS4xIDIuMUwxNTcuMSAxNjkuOWMtOC4zIDIuOC0xNS4xIDEyLjQtMTUuMSAyMS4ydjQ4Mi40YzAgOC44IDUuNyAyMC40IDEyLjYgMjUuOUw0OTkuMyA5NjhjMy41IDIuNyA4IDQuMSAxMi42IDQuMXM5LjItMS40IDEyLjYtNC4xbDM0NC43LTI2OC42YzYuOS01LjQgMTIuNi0xNyAxMi42LTI1LjlWMTkxLjFjLjItOC44LTYuNi0xOC4zLTE0LjktMjEuMnpNNDExLjMgNjU2aC0uMmMwIDQuNC0zLjYgOC04IDhoLTM3LjNjLTQuNCAwLTgtMy42LTgtOFY0NzEuNGMtNy43IDkuMi0xNS40IDE3LjktMjMuMSAyNmE2LjA0IDYuMDQgMCAwMS0xMC4yLTIuNGwtMTMuMi00My41Yy0uNi0yLS4yLTQuMSAxLjItNS42IDM3LTQzLjQgNjQuNy05NS4xIDgyLjItMTUzLjYgMS4xLTMuNSA1LTUuMyA4LjQtMy43bDM4LjYgMTguM2MyLjcgMS4zIDQuMSA0LjQgMy4yIDcuMmE0MjkuMiA0MjkuMiAwIDAxLTMzLjYgNzlWNjU2em0yOTYuNS00OS4ybC0yNi4zIDM1LjNhNS45MiA1LjkyIDAgMDEtOC45LjdjLTMwLjYtMjkuMy01Ni44LTY1LjItNzguMS0xMDYuOVY2NTZjMCA0LjQtMy42IDgtOCA4aC0zNi4yYy00LjQgMC04LTMuNi04LThWNTM2Yy0yMiA0NC43LTQ5IDgwLjgtODAuNiAxMDcuNmE1LjkgNS45IDAgMDEtOC45LTEuNEw0MzAgNjA1LjdhNiA2IDAgMDExLjYtOC4xYzI4LjYtMjAuMyA1MS45LTQ1LjIgNzEtNzZoLTU1LjFjLTQuNCAwLTgtMy42LTgtOFY0NzhjMC00LjQgMy42LTggOC04aDk0Ljl2LTE4LjZoLTY1LjljLTQuNCAwLTgtMy42LTgtOFYzMTZjMC00LjQgMy42LTggOC04aDE4NC43YzQuNCAwIDggMy42IDggOHYxMjcuMmMwIDQuNC0zLjYgOC04IDhoLTY2Ljd2MTguNmg5OC44YzQuNCAwIDggMy42IDggOHYzNS42YzAgNC40LTMuNiA4LTggOGgtNTljMTguMSAyOS4xIDQxLjggNTQuMyA3Mi4zIDc2LjkgMi42IDIuMSAzLjIgNS45IDEuMiA4LjV6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(InsuranceFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'InsuranceFilled';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACzD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,eAAe,CAAC;AAC5D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,iBAAiB;AACzC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}