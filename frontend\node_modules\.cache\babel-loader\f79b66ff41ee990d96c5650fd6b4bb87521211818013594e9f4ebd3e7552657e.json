{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { INTERNAL_HOOKS } from 'rc-table';\nimport { convertChildrenToColumns } from \"rc-table/es/hooks/useColumns\";\nimport omit from \"rc-util/es/omit\";\nimport useProxyImperativeHandle from '../_util/hooks/useProxyImperativeHandle';\nimport scrollTo from '../_util/scrollTo';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider/context';\nimport DefaultRenderEmpty from '../config-provider/defaultRenderEmpty';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport useSize from '../config-provider/hooks/useSize';\nimport useBreakpoint from '../grid/hooks/useBreakpoint';\nimport defaultLocale from '../locale/en_US';\nimport Pagination from '../pagination';\nimport Spin from '../spin';\nimport { useToken } from '../theme/internal';\nimport renderExpandIcon from './ExpandIcon';\nimport useContainerWidth from './hooks/useContainerWidth';\nimport useFilter, { getFilterData } from './hooks/useFilter';\nimport useLazyKVMap from './hooks/useLazyKVMap';\nimport usePagination, { DEFAULT_PAGE_SIZE, getPaginationParam } from './hooks/usePagination';\nimport useSelection from './hooks/useSelection';\nimport useSorter, { getSortData } from './hooks/useSorter';\nimport useTitleColumns from './hooks/useTitleColumns';\nimport RcTable from './RcTable';\nimport RcVirtualTable from './RcTable/VirtualTable';\nimport useStyle from './style';\nconst EMPTY_LIST = [];\nconst InternalTable = (props, ref) => {\n  var _a, _b;\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    rootClassName,\n    style,\n    size: customizeSize,\n    bordered,\n    dropdownPrefixCls: customizeDropdownPrefixCls,\n    dataSource,\n    pagination,\n    rowSelection,\n    rowKey = 'key',\n    rowClassName,\n    columns,\n    children,\n    childrenColumnName: legacyChildrenColumnName,\n    onChange,\n    getPopupContainer,\n    loading,\n    expandIcon,\n    expandable,\n    expandedRowRender,\n    expandIconColumnIndex,\n    indentSize,\n    scroll,\n    sortDirections,\n    locale,\n    showSorterTooltip = {\n      target: 'full-header'\n    },\n    virtual\n  } = props;\n  const warning = devUseWarning('Table');\n  if (process.env.NODE_ENV !== 'production') {\n    process.env.NODE_ENV !== \"production\" ? warning(!(typeof rowKey === 'function' && rowKey.length > 1), 'usage', '`index` parameter of `rowKey` function is deprecated. There is no guarantee that it will work as expected.') : void 0;\n  }\n  const baseColumns = React.useMemo(() => columns || convertChildrenToColumns(children), [columns, children]);\n  const needResponsive = React.useMemo(() => baseColumns.some(col => col.responsive), [baseColumns]);\n  const screens = useBreakpoint(needResponsive);\n  const mergedColumns = React.useMemo(() => {\n    const matched = new Set(Object.keys(screens).filter(m => screens[m]));\n    return baseColumns.filter(c => !c.responsive || c.responsive.some(r => matched.has(r)));\n  }, [baseColumns, screens]);\n  const tableProps = omit(props, ['className', 'style', 'columns']);\n  const {\n    locale: contextLocale = defaultLocale,\n    direction,\n    table,\n    renderEmpty,\n    getPrefixCls,\n    getPopupContainer: getContextPopupContainer\n  } = React.useContext(ConfigContext);\n  const mergedSize = useSize(customizeSize);\n  const tableLocale = Object.assign(Object.assign({}, contextLocale.Table), locale);\n  const rawData = dataSource || EMPTY_LIST;\n  const prefixCls = getPrefixCls('table', customizePrefixCls);\n  const dropdownPrefixCls = getPrefixCls('dropdown', customizeDropdownPrefixCls);\n  const [, token] = useToken();\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const mergedExpandable = Object.assign(Object.assign({\n    childrenColumnName: legacyChildrenColumnName,\n    expandIconColumnIndex\n  }, expandable), {\n    expandIcon: (_a = expandable === null || expandable === void 0 ? void 0 : expandable.expandIcon) !== null && _a !== void 0 ? _a : (_b = table === null || table === void 0 ? void 0 : table.expandable) === null || _b === void 0 ? void 0 : _b.expandIcon\n  });\n  const {\n    childrenColumnName = 'children'\n  } = mergedExpandable;\n  const expandType = React.useMemo(() => {\n    if (rawData.some(item => item === null || item === void 0 ? void 0 : item[childrenColumnName])) {\n      return 'nest';\n    }\n    if (expandedRowRender || (expandable === null || expandable === void 0 ? void 0 : expandable.expandedRowRender)) {\n      return 'row';\n    }\n    return null;\n  }, [rawData]);\n  const internalRefs = {\n    body: React.useRef(null)\n  };\n  // ============================ Width =============================\n  const getContainerWidth = useContainerWidth(prefixCls);\n  // ============================= Refs =============================\n  const rootRef = React.useRef(null);\n  const tblRef = React.useRef(null);\n  useProxyImperativeHandle(ref, () => Object.assign(Object.assign({}, tblRef.current), {\n    nativeElement: rootRef.current\n  }));\n  // ============================ RowKey ============================\n  const getRowKey = React.useMemo(() => {\n    if (typeof rowKey === 'function') {\n      return rowKey;\n    }\n    return record => record === null || record === void 0 ? void 0 : record[rowKey];\n  }, [rowKey]);\n  const [getRecordByKey] = useLazyKVMap(rawData, childrenColumnName, getRowKey);\n  // ============================ Events =============================\n  const changeEventInfo = {};\n  const triggerOnChange = (info, action, reset = false) => {\n    var _a, _b, _c, _d;\n    const changeInfo = Object.assign(Object.assign({}, changeEventInfo), info);\n    if (reset) {\n      (_a = changeEventInfo.resetPagination) === null || _a === void 0 ? void 0 : _a.call(changeEventInfo);\n      // Reset event param\n      if ((_b = changeInfo.pagination) === null || _b === void 0 ? void 0 : _b.current) {\n        changeInfo.pagination.current = 1;\n      }\n      // Trigger pagination events\n      if (pagination) {\n        (_c = pagination.onChange) === null || _c === void 0 ? void 0 : _c.call(pagination, 1, (_d = changeInfo.pagination) === null || _d === void 0 ? void 0 : _d.pageSize);\n      }\n    }\n    if (scroll && scroll.scrollToFirstRowOnChange !== false && internalRefs.body.current) {\n      scrollTo(0, {\n        getContainer: () => internalRefs.body.current\n      });\n    }\n    onChange === null || onChange === void 0 ? void 0 : onChange(changeInfo.pagination, changeInfo.filters, changeInfo.sorter, {\n      currentDataSource: getFilterData(getSortData(rawData, changeInfo.sorterStates, childrenColumnName), changeInfo.filterStates, childrenColumnName),\n      action\n    });\n  };\n  /**\n   * Controlled state in `columns` is not a good idea that makes too many code (1000+ line?) to read\n   * state out and then put it back to title render. Move these code into `hooks` but still too\n   * complex. We should provides Table props like `sorter` & `filter` to handle control in next big\n   * version.\n   */\n  // ============================ Sorter =============================\n  const onSorterChange = (sorter, sorterStates) => {\n    triggerOnChange({\n      sorter,\n      sorterStates\n    }, 'sort', false);\n  };\n  const [transformSorterColumns, sortStates, sorterTitleProps, getSorters] = useSorter({\n    prefixCls,\n    mergedColumns,\n    onSorterChange,\n    sortDirections: sortDirections || ['ascend', 'descend'],\n    tableLocale,\n    showSorterTooltip\n  });\n  const sortedData = React.useMemo(() => getSortData(rawData, sortStates, childrenColumnName), [rawData, sortStates]);\n  changeEventInfo.sorter = getSorters();\n  changeEventInfo.sorterStates = sortStates;\n  // ============================ Filter ============================\n  const onFilterChange = (filters, filterStates) => {\n    triggerOnChange({\n      filters,\n      filterStates\n    }, 'filter', true);\n  };\n  const [transformFilterColumns, filterStates, filters] = useFilter({\n    prefixCls,\n    locale: tableLocale,\n    dropdownPrefixCls,\n    mergedColumns,\n    onFilterChange,\n    getPopupContainer: getPopupContainer || getContextPopupContainer,\n    rootClassName: classNames(rootClassName, rootCls)\n  });\n  const mergedData = getFilterData(sortedData, filterStates, childrenColumnName);\n  changeEventInfo.filters = filters;\n  changeEventInfo.filterStates = filterStates;\n  // ============================ Column ============================\n  const columnTitleProps = React.useMemo(() => {\n    const mergedFilters = {};\n    Object.keys(filters).forEach(filterKey => {\n      if (filters[filterKey] !== null) {\n        mergedFilters[filterKey] = filters[filterKey];\n      }\n    });\n    return Object.assign(Object.assign({}, sorterTitleProps), {\n      filters: mergedFilters\n    });\n  }, [sorterTitleProps, filters]);\n  const [transformTitleColumns] = useTitleColumns(columnTitleProps);\n  // ========================== Pagination ==========================\n  const onPaginationChange = (current, pageSize) => {\n    triggerOnChange({\n      pagination: Object.assign(Object.assign({}, changeEventInfo.pagination), {\n        current,\n        pageSize\n      })\n    }, 'paginate');\n  };\n  const [mergedPagination, resetPagination] = usePagination(mergedData.length, onPaginationChange, pagination);\n  changeEventInfo.pagination = pagination === false ? {} : getPaginationParam(mergedPagination, pagination);\n  changeEventInfo.resetPagination = resetPagination;\n  // ============================= Data =============================\n  const pageData = React.useMemo(() => {\n    if (pagination === false || !mergedPagination.pageSize) {\n      return mergedData;\n    }\n    const {\n      current = 1,\n      total,\n      pageSize = DEFAULT_PAGE_SIZE\n    } = mergedPagination;\n    process.env.NODE_ENV !== \"production\" ? warning(current > 0, 'usage', '`current` should be positive number.') : void 0;\n    // Dynamic table data\n    if (mergedData.length < total) {\n      if (mergedData.length > pageSize) {\n        process.env.NODE_ENV !== \"production\" ? warning(false, 'usage', '`dataSource` length is less than `pagination.total` but large than `pagination.pageSize`. Please make sure your config correct data with async mode.') : void 0;\n        return mergedData.slice((current - 1) * pageSize, current * pageSize);\n      }\n      return mergedData;\n    }\n    return mergedData.slice((current - 1) * pageSize, current * pageSize);\n  }, [!!pagination, mergedData, mergedPagination === null || mergedPagination === void 0 ? void 0 : mergedPagination.current, mergedPagination === null || mergedPagination === void 0 ? void 0 : mergedPagination.pageSize, mergedPagination === null || mergedPagination === void 0 ? void 0 : mergedPagination.total]);\n  // ========================== Selections ==========================\n  const [transformSelectionColumns, selectedKeySet] = useSelection({\n    prefixCls,\n    data: mergedData,\n    pageData,\n    getRowKey,\n    getRecordByKey,\n    expandType,\n    childrenColumnName,\n    locale: tableLocale,\n    getPopupContainer: getPopupContainer || getContextPopupContainer\n  }, rowSelection);\n  const internalRowClassName = (record, index, indent) => {\n    let mergedRowClassName;\n    if (typeof rowClassName === 'function') {\n      mergedRowClassName = classNames(rowClassName(record, index, indent));\n    } else {\n      mergedRowClassName = classNames(rowClassName);\n    }\n    return classNames({\n      [`${prefixCls}-row-selected`]: selectedKeySet.has(getRowKey(record, index))\n    }, mergedRowClassName);\n  };\n  // ========================== Expandable ==========================\n  // Pass origin render status into `rc-table`, this can be removed when refactor with `rc-table`\n  mergedExpandable.__PARENT_RENDER_ICON__ = mergedExpandable.expandIcon;\n  // Customize expandable icon\n  mergedExpandable.expandIcon = mergedExpandable.expandIcon || expandIcon || renderExpandIcon(tableLocale);\n  // Adjust expand icon index, no overwrite expandIconColumnIndex if set.\n  if (expandType === 'nest' && mergedExpandable.expandIconColumnIndex === undefined) {\n    mergedExpandable.expandIconColumnIndex = rowSelection ? 1 : 0;\n  } else if (mergedExpandable.expandIconColumnIndex > 0 && rowSelection) {\n    mergedExpandable.expandIconColumnIndex -= 1;\n  }\n  // Indent size\n  if (typeof mergedExpandable.indentSize !== 'number') {\n    mergedExpandable.indentSize = typeof indentSize === 'number' ? indentSize : 15;\n  }\n  // ============================ Render ============================\n  const transformColumns = React.useCallback(innerColumns => transformTitleColumns(transformSelectionColumns(transformFilterColumns(transformSorterColumns(innerColumns)))), [transformSorterColumns, transformFilterColumns, transformSelectionColumns]);\n  let topPaginationNode;\n  let bottomPaginationNode;\n  if (pagination !== false && (mergedPagination === null || mergedPagination === void 0 ? void 0 : mergedPagination.total)) {\n    let paginationSize;\n    if (mergedPagination.size) {\n      paginationSize = mergedPagination.size;\n    } else {\n      paginationSize = mergedSize === 'small' || mergedSize === 'middle' ? 'small' : undefined;\n    }\n    const renderPagination = position => (/*#__PURE__*/React.createElement(Pagination, Object.assign({}, mergedPagination, {\n      className: classNames(`${prefixCls}-pagination ${prefixCls}-pagination-${position}`, mergedPagination.className),\n      size: paginationSize\n    })));\n    const defaultPosition = direction === 'rtl' ? 'left' : 'right';\n    const {\n      position\n    } = mergedPagination;\n    if (position !== null && Array.isArray(position)) {\n      const topPos = position.find(p => p.includes('top'));\n      const bottomPos = position.find(p => p.includes('bottom'));\n      const isDisable = position.every(p => `${p}` === 'none');\n      if (!topPos && !bottomPos && !isDisable) {\n        bottomPaginationNode = renderPagination(defaultPosition);\n      }\n      if (topPos) {\n        topPaginationNode = renderPagination(topPos.toLowerCase().replace('top', ''));\n      }\n      if (bottomPos) {\n        bottomPaginationNode = renderPagination(bottomPos.toLowerCase().replace('bottom', ''));\n      }\n    } else {\n      bottomPaginationNode = renderPagination(defaultPosition);\n    }\n  }\n  // >>>>>>>>> Spinning\n  let spinProps;\n  if (typeof loading === 'boolean') {\n    spinProps = {\n      spinning: loading\n    };\n  } else if (typeof loading === 'object') {\n    spinProps = Object.assign({\n      spinning: true\n    }, loading);\n  }\n  const wrapperClassNames = classNames(cssVarCls, rootCls, `${prefixCls}-wrapper`, table === null || table === void 0 ? void 0 : table.className, {\n    [`${prefixCls}-wrapper-rtl`]: direction === 'rtl'\n  }, className, rootClassName, hashId);\n  const mergedStyle = Object.assign(Object.assign({}, table === null || table === void 0 ? void 0 : table.style), style);\n  // ========== empty ==========\n  const mergedEmptyNode = React.useMemo(() => {\n    // When dataSource is null/undefined (detected by reference equality with EMPTY_LIST),\n    // and the table is in a loading state, we only show the loading spinner without the empty placeholder.\n    // For empty arrays (datasource={[]}), both loading and empty states would normally be shown.\n    // discussion https://github.com/ant-design/ant-design/issues/54601#issuecomment-3158091383\n    if ((spinProps === null || spinProps === void 0 ? void 0 : spinProps.spinning) && rawData === EMPTY_LIST) {\n      return null;\n    }\n    if (typeof (locale === null || locale === void 0 ? void 0 : locale.emptyText) !== 'undefined') {\n      return locale.emptyText;\n    }\n    return (renderEmpty === null || renderEmpty === void 0 ? void 0 : renderEmpty('Table')) || /*#__PURE__*/React.createElement(DefaultRenderEmpty, {\n      componentName: \"Table\"\n    });\n  }, [spinProps === null || spinProps === void 0 ? void 0 : spinProps.spinning, rawData, locale === null || locale === void 0 ? void 0 : locale.emptyText, renderEmpty]);\n  // ========================== Render ==========================\n  const TableComponent = virtual ? RcVirtualTable : RcTable;\n  // >>> Virtual Table props. We set height here since it will affect height collection\n  const virtualProps = {};\n  const listItemHeight = React.useMemo(() => {\n    const {\n      fontSize,\n      lineHeight,\n      lineWidth,\n      padding,\n      paddingXS,\n      paddingSM\n    } = token;\n    const fontHeight = Math.floor(fontSize * lineHeight);\n    switch (mergedSize) {\n      case 'middle':\n        return paddingSM * 2 + fontHeight + lineWidth;\n      case 'small':\n        return paddingXS * 2 + fontHeight + lineWidth;\n      default:\n        return padding * 2 + fontHeight + lineWidth;\n    }\n  }, [token, mergedSize]);\n  if (virtual) {\n    virtualProps.listItemHeight = listItemHeight;\n  }\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    ref: rootRef,\n    className: wrapperClassNames,\n    style: mergedStyle\n  }, /*#__PURE__*/React.createElement(Spin, Object.assign({\n    spinning: false\n  }, spinProps), topPaginationNode, /*#__PURE__*/React.createElement(TableComponent, Object.assign({}, virtualProps, tableProps, {\n    ref: tblRef,\n    columns: mergedColumns,\n    direction: direction,\n    expandable: mergedExpandable,\n    prefixCls: prefixCls,\n    className: classNames({\n      [`${prefixCls}-middle`]: mergedSize === 'middle',\n      [`${prefixCls}-small`]: mergedSize === 'small',\n      [`${prefixCls}-bordered`]: bordered,\n      [`${prefixCls}-empty`]: rawData.length === 0\n    }, cssVarCls, rootCls, hashId),\n    data: pageData,\n    rowKey: getRowKey,\n    rowClassName: internalRowClassName,\n    emptyText: mergedEmptyNode,\n    // Internal\n    internalHooks: INTERNAL_HOOKS,\n    internalRefs: internalRefs,\n    transformColumns: transformColumns,\n    getContainerWidth: getContainerWidth\n  })), bottomPaginationNode)));\n};\nexport default /*#__PURE__*/React.forwardRef(InternalTable);", "map": {"version": 3, "names": ["React", "classNames", "INTERNAL_HOOKS", "convertChildrenToColumns", "omit", "useProxyImperativeHandle", "scrollTo", "devUseW<PERSON>ning", "ConfigContext", "DefaultRenderEmpty", "useCSSVarCls", "useSize", "useBreakpoint", "defaultLocale", "Pagination", "Spin", "useToken", "renderExpandIcon", "useContainerWidth", "useFilter", "getFilterData", "useLazyKVMap", "usePagination", "DEFAULT_PAGE_SIZE", "getPaginationParam", "useSelection", "useSorter", "getSortData", "useTitleColumns", "RcTable", "RcVirtualTable", "useStyle", "EMPTY_LIST", "InternalTable", "props", "ref", "_a", "_b", "prefixCls", "customizePrefixCls", "className", "rootClassName", "style", "size", "customizeSize", "bordered", "dropdownPrefixCls", "customizeDropdownPrefixCls", "dataSource", "pagination", "rowSelection", "<PERSON><PERSON><PERSON>", "rowClassName", "columns", "children", "childrenColumnName", "legacyChildrenColumnName", "onChange", "getPopupContainer", "loading", "expandIcon", "expandable", "expandedRowRender", "expandIconColumnIndex", "indentSize", "scroll", "sortDirections", "locale", "showSorterTooltip", "target", "virtual", "warning", "process", "env", "NODE_ENV", "length", "baseColumns", "useMemo", "needResponsive", "some", "col", "responsive", "screens", "mergedColumns", "matched", "Set", "Object", "keys", "filter", "m", "c", "r", "has", "tableProps", "contextLocale", "direction", "table", "renderEmpty", "getPrefixCls", "getContextPopupContainer", "useContext", "mergedSize", "tableLocale", "assign", "Table", "rawData", "token", "rootCls", "wrapCSSVar", "hashId", "cssVarCls", "mergedExpandable", "expandType", "item", "internalRefs", "body", "useRef", "getContainer<PERSON>idth", "rootRef", "tblRef", "current", "nativeElement", "getRowKey", "record", "getRecordByKey", "changeEventInfo", "triggerOnChange", "info", "action", "reset", "_c", "_d", "changeInfo", "resetPagination", "call", "pageSize", "scrollToFirstRowOnChange", "getContainer", "filters", "sorter", "currentDataSource", "sorterStates", "filterStates", "onSorterChange", "transformSorterColumns", "sortStates", "sorterTitleProps", "getSorters", "sortedData", "onFilterChange", "transformFilterColumns", "mergedData", "columnTitleProps", "mergedFilters", "for<PERSON>ach", "<PERSON><PERSON><PERSON>", "transformTitleColumns", "onPaginationChange", "mergedPagination", "pageData", "total", "slice", "transformSelectionColumns", "selectedKeySet", "data", "internalRowClassName", "index", "indent", "mergedRowClassName", "__PARENT_RENDER_ICON__", "undefined", "transformColumns", "useCallback", "innerColumns", "topPaginationNode", "bottomPaginationNode", "paginationSize", "renderPagination", "position", "createElement", "defaultPosition", "Array", "isArray", "topPos", "find", "p", "includes", "bottomPos", "isDisable", "every", "toLowerCase", "replace", "spinProps", "spinning", "wrapperClassNames", "mergedStyle", "mergedEmptyNode", "emptyText", "componentName", "TableComponent", "virtualProps", "listItemHeight", "fontSize", "lineHeight", "lineWidth", "padding", "paddingXS", "paddingSM", "fontHeight", "Math", "floor", "internalHooks", "forwardRef"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/antd/es/table/InternalTable.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { INTERNAL_HOOKS } from 'rc-table';\nimport { convertChildrenToColumns } from \"rc-table/es/hooks/useColumns\";\nimport omit from \"rc-util/es/omit\";\nimport useProxyImperativeHandle from '../_util/hooks/useProxyImperativeHandle';\nimport scrollTo from '../_util/scrollTo';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider/context';\nimport DefaultRenderEmpty from '../config-provider/defaultRenderEmpty';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport useSize from '../config-provider/hooks/useSize';\nimport useBreakpoint from '../grid/hooks/useBreakpoint';\nimport defaultLocale from '../locale/en_US';\nimport Pagination from '../pagination';\nimport Spin from '../spin';\nimport { useToken } from '../theme/internal';\nimport renderExpandIcon from './ExpandIcon';\nimport useContainerWidth from './hooks/useContainerWidth';\nimport useFilter, { getFilterData } from './hooks/useFilter';\nimport useLazyKVMap from './hooks/useLazyKVMap';\nimport usePagination, { DEFAULT_PAGE_SIZE, getPaginationParam } from './hooks/usePagination';\nimport useSelection from './hooks/useSelection';\nimport useSorter, { getSortData } from './hooks/useSorter';\nimport useTitleColumns from './hooks/useTitleColumns';\nimport RcTable from './RcTable';\nimport RcVirtualTable from './RcTable/VirtualTable';\nimport useStyle from './style';\nconst EMPTY_LIST = [];\nconst InternalTable = (props, ref) => {\n  var _a, _b;\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    rootClassName,\n    style,\n    size: customizeSize,\n    bordered,\n    dropdownPrefixCls: customizeDropdownPrefixCls,\n    dataSource,\n    pagination,\n    rowSelection,\n    rowKey = 'key',\n    rowClassName,\n    columns,\n    children,\n    childrenColumnName: legacyChildrenColumnName,\n    onChange,\n    getPopupContainer,\n    loading,\n    expandIcon,\n    expandable,\n    expandedRowRender,\n    expandIconColumnIndex,\n    indentSize,\n    scroll,\n    sortDirections,\n    locale,\n    showSorterTooltip = {\n      target: 'full-header'\n    },\n    virtual\n  } = props;\n  const warning = devUseWarning('Table');\n  if (process.env.NODE_ENV !== 'production') {\n    process.env.NODE_ENV !== \"production\" ? warning(!(typeof rowKey === 'function' && rowKey.length > 1), 'usage', '`index` parameter of `rowKey` function is deprecated. There is no guarantee that it will work as expected.') : void 0;\n  }\n  const baseColumns = React.useMemo(() => columns || convertChildrenToColumns(children), [columns, children]);\n  const needResponsive = React.useMemo(() => baseColumns.some(col => col.responsive), [baseColumns]);\n  const screens = useBreakpoint(needResponsive);\n  const mergedColumns = React.useMemo(() => {\n    const matched = new Set(Object.keys(screens).filter(m => screens[m]));\n    return baseColumns.filter(c => !c.responsive || c.responsive.some(r => matched.has(r)));\n  }, [baseColumns, screens]);\n  const tableProps = omit(props, ['className', 'style', 'columns']);\n  const {\n    locale: contextLocale = defaultLocale,\n    direction,\n    table,\n    renderEmpty,\n    getPrefixCls,\n    getPopupContainer: getContextPopupContainer\n  } = React.useContext(ConfigContext);\n  const mergedSize = useSize(customizeSize);\n  const tableLocale = Object.assign(Object.assign({}, contextLocale.Table), locale);\n  const rawData = dataSource || EMPTY_LIST;\n  const prefixCls = getPrefixCls('table', customizePrefixCls);\n  const dropdownPrefixCls = getPrefixCls('dropdown', customizeDropdownPrefixCls);\n  const [, token] = useToken();\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const mergedExpandable = Object.assign(Object.assign({\n    childrenColumnName: legacyChildrenColumnName,\n    expandIconColumnIndex\n  }, expandable), {\n    expandIcon: (_a = expandable === null || expandable === void 0 ? void 0 : expandable.expandIcon) !== null && _a !== void 0 ? _a : (_b = table === null || table === void 0 ? void 0 : table.expandable) === null || _b === void 0 ? void 0 : _b.expandIcon\n  });\n  const {\n    childrenColumnName = 'children'\n  } = mergedExpandable;\n  const expandType = React.useMemo(() => {\n    if (rawData.some(item => item === null || item === void 0 ? void 0 : item[childrenColumnName])) {\n      return 'nest';\n    }\n    if (expandedRowRender || (expandable === null || expandable === void 0 ? void 0 : expandable.expandedRowRender)) {\n      return 'row';\n    }\n    return null;\n  }, [rawData]);\n  const internalRefs = {\n    body: React.useRef(null)\n  };\n  // ============================ Width =============================\n  const getContainerWidth = useContainerWidth(prefixCls);\n  // ============================= Refs =============================\n  const rootRef = React.useRef(null);\n  const tblRef = React.useRef(null);\n  useProxyImperativeHandle(ref, () => Object.assign(Object.assign({}, tblRef.current), {\n    nativeElement: rootRef.current\n  }));\n  // ============================ RowKey ============================\n  const getRowKey = React.useMemo(() => {\n    if (typeof rowKey === 'function') {\n      return rowKey;\n    }\n    return record => record === null || record === void 0 ? void 0 : record[rowKey];\n  }, [rowKey]);\n  const [getRecordByKey] = useLazyKVMap(rawData, childrenColumnName, getRowKey);\n  // ============================ Events =============================\n  const changeEventInfo = {};\n  const triggerOnChange = (info, action, reset = false) => {\n    var _a, _b, _c, _d;\n    const changeInfo = Object.assign(Object.assign({}, changeEventInfo), info);\n    if (reset) {\n      (_a = changeEventInfo.resetPagination) === null || _a === void 0 ? void 0 : _a.call(changeEventInfo);\n      // Reset event param\n      if ((_b = changeInfo.pagination) === null || _b === void 0 ? void 0 : _b.current) {\n        changeInfo.pagination.current = 1;\n      }\n      // Trigger pagination events\n      if (pagination) {\n        (_c = pagination.onChange) === null || _c === void 0 ? void 0 : _c.call(pagination, 1, (_d = changeInfo.pagination) === null || _d === void 0 ? void 0 : _d.pageSize);\n      }\n    }\n    if (scroll && scroll.scrollToFirstRowOnChange !== false && internalRefs.body.current) {\n      scrollTo(0, {\n        getContainer: () => internalRefs.body.current\n      });\n    }\n    onChange === null || onChange === void 0 ? void 0 : onChange(changeInfo.pagination, changeInfo.filters, changeInfo.sorter, {\n      currentDataSource: getFilterData(getSortData(rawData, changeInfo.sorterStates, childrenColumnName), changeInfo.filterStates, childrenColumnName),\n      action\n    });\n  };\n  /**\n   * Controlled state in `columns` is not a good idea that makes too many code (1000+ line?) to read\n   * state out and then put it back to title render. Move these code into `hooks` but still too\n   * complex. We should provides Table props like `sorter` & `filter` to handle control in next big\n   * version.\n   */\n  // ============================ Sorter =============================\n  const onSorterChange = (sorter, sorterStates) => {\n    triggerOnChange({\n      sorter,\n      sorterStates\n    }, 'sort', false);\n  };\n  const [transformSorterColumns, sortStates, sorterTitleProps, getSorters] = useSorter({\n    prefixCls,\n    mergedColumns,\n    onSorterChange,\n    sortDirections: sortDirections || ['ascend', 'descend'],\n    tableLocale,\n    showSorterTooltip\n  });\n  const sortedData = React.useMemo(() => getSortData(rawData, sortStates, childrenColumnName), [rawData, sortStates]);\n  changeEventInfo.sorter = getSorters();\n  changeEventInfo.sorterStates = sortStates;\n  // ============================ Filter ============================\n  const onFilterChange = (filters, filterStates) => {\n    triggerOnChange({\n      filters,\n      filterStates\n    }, 'filter', true);\n  };\n  const [transformFilterColumns, filterStates, filters] = useFilter({\n    prefixCls,\n    locale: tableLocale,\n    dropdownPrefixCls,\n    mergedColumns,\n    onFilterChange,\n    getPopupContainer: getPopupContainer || getContextPopupContainer,\n    rootClassName: classNames(rootClassName, rootCls)\n  });\n  const mergedData = getFilterData(sortedData, filterStates, childrenColumnName);\n  changeEventInfo.filters = filters;\n  changeEventInfo.filterStates = filterStates;\n  // ============================ Column ============================\n  const columnTitleProps = React.useMemo(() => {\n    const mergedFilters = {};\n    Object.keys(filters).forEach(filterKey => {\n      if (filters[filterKey] !== null) {\n        mergedFilters[filterKey] = filters[filterKey];\n      }\n    });\n    return Object.assign(Object.assign({}, sorterTitleProps), {\n      filters: mergedFilters\n    });\n  }, [sorterTitleProps, filters]);\n  const [transformTitleColumns] = useTitleColumns(columnTitleProps);\n  // ========================== Pagination ==========================\n  const onPaginationChange = (current, pageSize) => {\n    triggerOnChange({\n      pagination: Object.assign(Object.assign({}, changeEventInfo.pagination), {\n        current,\n        pageSize\n      })\n    }, 'paginate');\n  };\n  const [mergedPagination, resetPagination] = usePagination(mergedData.length, onPaginationChange, pagination);\n  changeEventInfo.pagination = pagination === false ? {} : getPaginationParam(mergedPagination, pagination);\n  changeEventInfo.resetPagination = resetPagination;\n  // ============================= Data =============================\n  const pageData = React.useMemo(() => {\n    if (pagination === false || !mergedPagination.pageSize) {\n      return mergedData;\n    }\n    const {\n      current = 1,\n      total,\n      pageSize = DEFAULT_PAGE_SIZE\n    } = mergedPagination;\n    process.env.NODE_ENV !== \"production\" ? warning(current > 0, 'usage', '`current` should be positive number.') : void 0;\n    // Dynamic table data\n    if (mergedData.length < total) {\n      if (mergedData.length > pageSize) {\n        process.env.NODE_ENV !== \"production\" ? warning(false, 'usage', '`dataSource` length is less than `pagination.total` but large than `pagination.pageSize`. Please make sure your config correct data with async mode.') : void 0;\n        return mergedData.slice((current - 1) * pageSize, current * pageSize);\n      }\n      return mergedData;\n    }\n    return mergedData.slice((current - 1) * pageSize, current * pageSize);\n  }, [!!pagination, mergedData, mergedPagination === null || mergedPagination === void 0 ? void 0 : mergedPagination.current, mergedPagination === null || mergedPagination === void 0 ? void 0 : mergedPagination.pageSize, mergedPagination === null || mergedPagination === void 0 ? void 0 : mergedPagination.total]);\n  // ========================== Selections ==========================\n  const [transformSelectionColumns, selectedKeySet] = useSelection({\n    prefixCls,\n    data: mergedData,\n    pageData,\n    getRowKey,\n    getRecordByKey,\n    expandType,\n    childrenColumnName,\n    locale: tableLocale,\n    getPopupContainer: getPopupContainer || getContextPopupContainer\n  }, rowSelection);\n  const internalRowClassName = (record, index, indent) => {\n    let mergedRowClassName;\n    if (typeof rowClassName === 'function') {\n      mergedRowClassName = classNames(rowClassName(record, index, indent));\n    } else {\n      mergedRowClassName = classNames(rowClassName);\n    }\n    return classNames({\n      [`${prefixCls}-row-selected`]: selectedKeySet.has(getRowKey(record, index))\n    }, mergedRowClassName);\n  };\n  // ========================== Expandable ==========================\n  // Pass origin render status into `rc-table`, this can be removed when refactor with `rc-table`\n  mergedExpandable.__PARENT_RENDER_ICON__ = mergedExpandable.expandIcon;\n  // Customize expandable icon\n  mergedExpandable.expandIcon = mergedExpandable.expandIcon || expandIcon || renderExpandIcon(tableLocale);\n  // Adjust expand icon index, no overwrite expandIconColumnIndex if set.\n  if (expandType === 'nest' && mergedExpandable.expandIconColumnIndex === undefined) {\n    mergedExpandable.expandIconColumnIndex = rowSelection ? 1 : 0;\n  } else if (mergedExpandable.expandIconColumnIndex > 0 && rowSelection) {\n    mergedExpandable.expandIconColumnIndex -= 1;\n  }\n  // Indent size\n  if (typeof mergedExpandable.indentSize !== 'number') {\n    mergedExpandable.indentSize = typeof indentSize === 'number' ? indentSize : 15;\n  }\n  // ============================ Render ============================\n  const transformColumns = React.useCallback(innerColumns => transformTitleColumns(transformSelectionColumns(transformFilterColumns(transformSorterColumns(innerColumns)))), [transformSorterColumns, transformFilterColumns, transformSelectionColumns]);\n  let topPaginationNode;\n  let bottomPaginationNode;\n  if (pagination !== false && (mergedPagination === null || mergedPagination === void 0 ? void 0 : mergedPagination.total)) {\n    let paginationSize;\n    if (mergedPagination.size) {\n      paginationSize = mergedPagination.size;\n    } else {\n      paginationSize = mergedSize === 'small' || mergedSize === 'middle' ? 'small' : undefined;\n    }\n    const renderPagination = position => (/*#__PURE__*/React.createElement(Pagination, Object.assign({}, mergedPagination, {\n      className: classNames(`${prefixCls}-pagination ${prefixCls}-pagination-${position}`, mergedPagination.className),\n      size: paginationSize\n    })));\n    const defaultPosition = direction === 'rtl' ? 'left' : 'right';\n    const {\n      position\n    } = mergedPagination;\n    if (position !== null && Array.isArray(position)) {\n      const topPos = position.find(p => p.includes('top'));\n      const bottomPos = position.find(p => p.includes('bottom'));\n      const isDisable = position.every(p => `${p}` === 'none');\n      if (!topPos && !bottomPos && !isDisable) {\n        bottomPaginationNode = renderPagination(defaultPosition);\n      }\n      if (topPos) {\n        topPaginationNode = renderPagination(topPos.toLowerCase().replace('top', ''));\n      }\n      if (bottomPos) {\n        bottomPaginationNode = renderPagination(bottomPos.toLowerCase().replace('bottom', ''));\n      }\n    } else {\n      bottomPaginationNode = renderPagination(defaultPosition);\n    }\n  }\n  // >>>>>>>>> Spinning\n  let spinProps;\n  if (typeof loading === 'boolean') {\n    spinProps = {\n      spinning: loading\n    };\n  } else if (typeof loading === 'object') {\n    spinProps = Object.assign({\n      spinning: true\n    }, loading);\n  }\n  const wrapperClassNames = classNames(cssVarCls, rootCls, `${prefixCls}-wrapper`, table === null || table === void 0 ? void 0 : table.className, {\n    [`${prefixCls}-wrapper-rtl`]: direction === 'rtl'\n  }, className, rootClassName, hashId);\n  const mergedStyle = Object.assign(Object.assign({}, table === null || table === void 0 ? void 0 : table.style), style);\n  // ========== empty ==========\n  const mergedEmptyNode = React.useMemo(() => {\n    // When dataSource is null/undefined (detected by reference equality with EMPTY_LIST),\n    // and the table is in a loading state, we only show the loading spinner without the empty placeholder.\n    // For empty arrays (datasource={[]}), both loading and empty states would normally be shown.\n    // discussion https://github.com/ant-design/ant-design/issues/54601#issuecomment-3158091383\n    if ((spinProps === null || spinProps === void 0 ? void 0 : spinProps.spinning) && rawData === EMPTY_LIST) {\n      return null;\n    }\n    if (typeof (locale === null || locale === void 0 ? void 0 : locale.emptyText) !== 'undefined') {\n      return locale.emptyText;\n    }\n    return (renderEmpty === null || renderEmpty === void 0 ? void 0 : renderEmpty('Table')) || /*#__PURE__*/React.createElement(DefaultRenderEmpty, {\n      componentName: \"Table\"\n    });\n  }, [spinProps === null || spinProps === void 0 ? void 0 : spinProps.spinning, rawData, locale === null || locale === void 0 ? void 0 : locale.emptyText, renderEmpty]);\n  // ========================== Render ==========================\n  const TableComponent = virtual ? RcVirtualTable : RcTable;\n  // >>> Virtual Table props. We set height here since it will affect height collection\n  const virtualProps = {};\n  const listItemHeight = React.useMemo(() => {\n    const {\n      fontSize,\n      lineHeight,\n      lineWidth,\n      padding,\n      paddingXS,\n      paddingSM\n    } = token;\n    const fontHeight = Math.floor(fontSize * lineHeight);\n    switch (mergedSize) {\n      case 'middle':\n        return paddingSM * 2 + fontHeight + lineWidth;\n      case 'small':\n        return paddingXS * 2 + fontHeight + lineWidth;\n      default:\n        return padding * 2 + fontHeight + lineWidth;\n    }\n  }, [token, mergedSize]);\n  if (virtual) {\n    virtualProps.listItemHeight = listItemHeight;\n  }\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    ref: rootRef,\n    className: wrapperClassNames,\n    style: mergedStyle\n  }, /*#__PURE__*/React.createElement(Spin, Object.assign({\n    spinning: false\n  }, spinProps), topPaginationNode, /*#__PURE__*/React.createElement(TableComponent, Object.assign({}, virtualProps, tableProps, {\n    ref: tblRef,\n    columns: mergedColumns,\n    direction: direction,\n    expandable: mergedExpandable,\n    prefixCls: prefixCls,\n    className: classNames({\n      [`${prefixCls}-middle`]: mergedSize === 'middle',\n      [`${prefixCls}-small`]: mergedSize === 'small',\n      [`${prefixCls}-bordered`]: bordered,\n      [`${prefixCls}-empty`]: rawData.length === 0\n    }, cssVarCls, rootCls, hashId),\n    data: pageData,\n    rowKey: getRowKey,\n    rowClassName: internalRowClassName,\n    emptyText: mergedEmptyNode,\n    // Internal\n    internalHooks: INTERNAL_HOOKS,\n    internalRefs: internalRefs,\n    transformColumns: transformColumns,\n    getContainerWidth: getContainerWidth\n  })), bottomPaginationNode)));\n};\nexport default /*#__PURE__*/React.forwardRef(InternalTable);"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,cAAc,QAAQ,UAAU;AACzC,SAASC,wBAAwB,QAAQ,8BAA8B;AACvE,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,wBAAwB,MAAM,yCAAyC;AAC9E,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,OAAOC,kBAAkB,MAAM,uCAAuC;AACtE,OAAOC,YAAY,MAAM,uCAAuC;AAChE,OAAOC,OAAO,MAAM,kCAAkC;AACtD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAOC,IAAI,MAAM,SAAS;AAC1B,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,OAAOC,gBAAgB,MAAM,cAAc;AAC3C,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,SAAS,IAAIC,aAAa,QAAQ,mBAAmB;AAC5D,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,aAAa,IAAIC,iBAAiB,EAAEC,kBAAkB,QAAQ,uBAAuB;AAC5F,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,SAAS,IAAIC,WAAW,QAAQ,mBAAmB;AAC1D,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,QAAQ,MAAM,SAAS;AAC9B,MAAMC,UAAU,GAAG,EAAE;AACrB,MAAMC,aAAa,GAAGA,CAACC,KAAK,EAAEC,GAAG,KAAK;EACpC,IAAIC,EAAE,EAAEC,EAAE;EACV,MAAM;IACJC,SAAS,EAAEC,kBAAkB;IAC7BC,SAAS;IACTC,aAAa;IACbC,KAAK;IACLC,IAAI,EAAEC,aAAa;IACnBC,QAAQ;IACRC,iBAAiB,EAAEC,0BAA0B;IAC7CC,UAAU;IACVC,UAAU;IACVC,YAAY;IACZC,MAAM,GAAG,KAAK;IACdC,YAAY;IACZC,OAAO;IACPC,QAAQ;IACRC,kBAAkB,EAAEC,wBAAwB;IAC5CC,QAAQ;IACRC,iBAAiB;IACjBC,OAAO;IACPC,UAAU;IACVC,UAAU;IACVC,iBAAiB;IACjBC,qBAAqB;IACrBC,UAAU;IACVC,MAAM;IACNC,cAAc;IACdC,MAAM;IACNC,iBAAiB,GAAG;MAClBC,MAAM,EAAE;IACV,CAAC;IACDC;EACF,CAAC,GAAGpC,KAAK;EACT,MAAMqC,OAAO,GAAGhE,aAAa,CAAC,OAAO,CAAC;EACtC,IAAIiE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGH,OAAO,CAAC,EAAE,OAAOpB,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACwB,MAAM,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,4GAA4G,CAAC,GAAG,KAAK,CAAC;EACvO;EACA,MAAMC,WAAW,GAAG5E,KAAK,CAAC6E,OAAO,CAAC,MAAMxB,OAAO,IAAIlD,wBAAwB,CAACmD,QAAQ,CAAC,EAAE,CAACD,OAAO,EAAEC,QAAQ,CAAC,CAAC;EAC3G,MAAMwB,cAAc,GAAG9E,KAAK,CAAC6E,OAAO,CAAC,MAAMD,WAAW,CAACG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,UAAU,CAAC,EAAE,CAACL,WAAW,CAAC,CAAC;EAClG,MAAMM,OAAO,GAAGtE,aAAa,CAACkE,cAAc,CAAC;EAC7C,MAAMK,aAAa,GAAGnF,KAAK,CAAC6E,OAAO,CAAC,MAAM;IACxC,MAAMO,OAAO,GAAG,IAAIC,GAAG,CAACC,MAAM,CAACC,IAAI,CAACL,OAAO,CAAC,CAACM,MAAM,CAACC,CAAC,IAAIP,OAAO,CAACO,CAAC,CAAC,CAAC,CAAC;IACrE,OAAOb,WAAW,CAACY,MAAM,CAACE,CAAC,IAAI,CAACA,CAAC,CAACT,UAAU,IAAIS,CAAC,CAACT,UAAU,CAACF,IAAI,CAACY,CAAC,IAAIP,OAAO,CAACQ,GAAG,CAACD,CAAC,CAAC,CAAC,CAAC;EACzF,CAAC,EAAE,CAACf,WAAW,EAAEM,OAAO,CAAC,CAAC;EAC1B,MAAMW,UAAU,GAAGzF,IAAI,CAAC8B,KAAK,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;EACjE,MAAM;IACJiC,MAAM,EAAE2B,aAAa,GAAGjF,aAAa;IACrCkF,SAAS;IACTC,KAAK;IACLC,WAAW;IACXC,YAAY;IACZxC,iBAAiB,EAAEyC;EACrB,CAAC,GAAGnG,KAAK,CAACoG,UAAU,CAAC5F,aAAa,CAAC;EACnC,MAAM6F,UAAU,GAAG1F,OAAO,CAACiC,aAAa,CAAC;EACzC,MAAM0D,WAAW,GAAGhB,MAAM,CAACiB,MAAM,CAACjB,MAAM,CAACiB,MAAM,CAAC,CAAC,CAAC,EAAET,aAAa,CAACU,KAAK,CAAC,EAAErC,MAAM,CAAC;EACjF,MAAMsC,OAAO,GAAGzD,UAAU,IAAIhB,UAAU;EACxC,MAAMM,SAAS,GAAG4D,YAAY,CAAC,OAAO,EAAE3D,kBAAkB,CAAC;EAC3D,MAAMO,iBAAiB,GAAGoD,YAAY,CAAC,UAAU,EAAEnD,0BAA0B,CAAC;EAC9E,MAAM,GAAG2D,KAAK,CAAC,GAAG1F,QAAQ,CAAC,CAAC;EAC5B,MAAM2F,OAAO,GAAGjG,YAAY,CAAC4B,SAAS,CAAC;EACvC,MAAM,CAACsE,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAG/E,QAAQ,CAACO,SAAS,EAAEqE,OAAO,CAAC;EACpE,MAAMI,gBAAgB,GAAGzB,MAAM,CAACiB,MAAM,CAACjB,MAAM,CAACiB,MAAM,CAAC;IACnDhD,kBAAkB,EAAEC,wBAAwB;IAC5CO;EACF,CAAC,EAAEF,UAAU,CAAC,EAAE;IACdD,UAAU,EAAE,CAACxB,EAAE,GAAGyB,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACD,UAAU,MAAM,IAAI,IAAIxB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAACC,EAAE,GAAG2D,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACnC,UAAU,MAAM,IAAI,IAAIxB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuB;EAClP,CAAC,CAAC;EACF,MAAM;IACJL,kBAAkB,GAAG;EACvB,CAAC,GAAGwD,gBAAgB;EACpB,MAAMC,UAAU,GAAGhH,KAAK,CAAC6E,OAAO,CAAC,MAAM;IACrC,IAAI4B,OAAO,CAAC1B,IAAI,CAACkC,IAAI,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC1D,kBAAkB,CAAC,CAAC,EAAE;MAC9F,OAAO,MAAM;IACf;IACA,IAAIO,iBAAiB,KAAKD,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACC,iBAAiB,CAAC,EAAE;MAC/G,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC,EAAE,CAAC2C,OAAO,CAAC,CAAC;EACb,MAAMS,YAAY,GAAG;IACnBC,IAAI,EAAEnH,KAAK,CAACoH,MAAM,CAAC,IAAI;EACzB,CAAC;EACD;EACA,MAAMC,iBAAiB,GAAGnG,iBAAiB,CAACoB,SAAS,CAAC;EACtD;EACA,MAAMgF,OAAO,GAAGtH,KAAK,CAACoH,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMG,MAAM,GAAGvH,KAAK,CAACoH,MAAM,CAAC,IAAI,CAAC;EACjC/G,wBAAwB,CAAC8B,GAAG,EAAE,MAAMmD,MAAM,CAACiB,MAAM,CAACjB,MAAM,CAACiB,MAAM,CAAC,CAAC,CAAC,EAAEgB,MAAM,CAACC,OAAO,CAAC,EAAE;IACnFC,aAAa,EAAEH,OAAO,CAACE;EACzB,CAAC,CAAC,CAAC;EACH;EACA,MAAME,SAAS,GAAG1H,KAAK,CAAC6E,OAAO,CAAC,MAAM;IACpC,IAAI,OAAO1B,MAAM,KAAK,UAAU,EAAE;MAChC,OAAOA,MAAM;IACf;IACA,OAAOwE,MAAM,IAAIA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACxE,MAAM,CAAC;EACjF,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACZ,MAAM,CAACyE,cAAc,CAAC,GAAGvG,YAAY,CAACoF,OAAO,EAAElD,kBAAkB,EAAEmE,SAAS,CAAC;EAC7E;EACA,MAAMG,eAAe,GAAG,CAAC,CAAC;EAC1B,MAAMC,eAAe,GAAGA,CAACC,IAAI,EAAEC,MAAM,EAAEC,KAAK,GAAG,KAAK,KAAK;IACvD,IAAI7F,EAAE,EAAEC,EAAE,EAAE6F,EAAE,EAAEC,EAAE;IAClB,MAAMC,UAAU,GAAG9C,MAAM,CAACiB,MAAM,CAACjB,MAAM,CAACiB,MAAM,CAAC,CAAC,CAAC,EAAEsB,eAAe,CAAC,EAAEE,IAAI,CAAC;IAC1E,IAAIE,KAAK,EAAE;MACT,CAAC7F,EAAE,GAAGyF,eAAe,CAACQ,eAAe,MAAM,IAAI,IAAIjG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACkG,IAAI,CAACT,eAAe,CAAC;MACpG;MACA,IAAI,CAACxF,EAAE,GAAG+F,UAAU,CAACnF,UAAU,MAAM,IAAI,IAAIZ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmF,OAAO,EAAE;QAChFY,UAAU,CAACnF,UAAU,CAACuE,OAAO,GAAG,CAAC;MACnC;MACA;MACA,IAAIvE,UAAU,EAAE;QACd,CAACiF,EAAE,GAAGjF,UAAU,CAACQ,QAAQ,MAAM,IAAI,IAAIyE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,IAAI,CAACrF,UAAU,EAAE,CAAC,EAAE,CAACkF,EAAE,GAAGC,UAAU,CAACnF,UAAU,MAAM,IAAI,IAAIkF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,QAAQ,CAAC;MACvK;IACF;IACA,IAAItE,MAAM,IAAIA,MAAM,CAACuE,wBAAwB,KAAK,KAAK,IAAItB,YAAY,CAACC,IAAI,CAACK,OAAO,EAAE;MACpFlH,QAAQ,CAAC,CAAC,EAAE;QACVmI,YAAY,EAAEA,CAAA,KAAMvB,YAAY,CAACC,IAAI,CAACK;MACxC,CAAC,CAAC;IACJ;IACA/D,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC2E,UAAU,CAACnF,UAAU,EAAEmF,UAAU,CAACM,OAAO,EAAEN,UAAU,CAACO,MAAM,EAAE;MACzHC,iBAAiB,EAAExH,aAAa,CAACO,WAAW,CAAC8E,OAAO,EAAE2B,UAAU,CAACS,YAAY,EAAEtF,kBAAkB,CAAC,EAAE6E,UAAU,CAACU,YAAY,EAAEvF,kBAAkB,CAAC;MAChJyE;IACF,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACE;EACA,MAAMe,cAAc,GAAGA,CAACJ,MAAM,EAAEE,YAAY,KAAK;IAC/Cf,eAAe,CAAC;MACda,MAAM;MACNE;IACF,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC;EACnB,CAAC;EACD,MAAM,CAACG,sBAAsB,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,UAAU,CAAC,GAAGzH,SAAS,CAAC;IACnFY,SAAS;IACT6C,aAAa;IACb4D,cAAc;IACd7E,cAAc,EAAEA,cAAc,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC;IACvDoC,WAAW;IACXlC;EACF,CAAC,CAAC;EACF,MAAMgF,UAAU,GAAGpJ,KAAK,CAAC6E,OAAO,CAAC,MAAMlD,WAAW,CAAC8E,OAAO,EAAEwC,UAAU,EAAE1F,kBAAkB,CAAC,EAAE,CAACkD,OAAO,EAAEwC,UAAU,CAAC,CAAC;EACnHpB,eAAe,CAACc,MAAM,GAAGQ,UAAU,CAAC,CAAC;EACrCtB,eAAe,CAACgB,YAAY,GAAGI,UAAU;EACzC;EACA,MAAMI,cAAc,GAAGA,CAACX,OAAO,EAAEI,YAAY,KAAK;IAChDhB,eAAe,CAAC;MACdY,OAAO;MACPI;IACF,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC;EACpB,CAAC;EACD,MAAM,CAACQ,sBAAsB,EAAER,YAAY,EAAEJ,OAAO,CAAC,GAAGvH,SAAS,CAAC;IAChEmB,SAAS;IACT6B,MAAM,EAAEmC,WAAW;IACnBxD,iBAAiB;IACjBqC,aAAa;IACbkE,cAAc;IACd3F,iBAAiB,EAAEA,iBAAiB,IAAIyC,wBAAwB;IAChE1D,aAAa,EAAExC,UAAU,CAACwC,aAAa,EAAEkE,OAAO;EAClD,CAAC,CAAC;EACF,MAAM4C,UAAU,GAAGnI,aAAa,CAACgI,UAAU,EAAEN,YAAY,EAAEvF,kBAAkB,CAAC;EAC9EsE,eAAe,CAACa,OAAO,GAAGA,OAAO;EACjCb,eAAe,CAACiB,YAAY,GAAGA,YAAY;EAC3C;EACA,MAAMU,gBAAgB,GAAGxJ,KAAK,CAAC6E,OAAO,CAAC,MAAM;IAC3C,MAAM4E,aAAa,GAAG,CAAC,CAAC;IACxBnE,MAAM,CAACC,IAAI,CAACmD,OAAO,CAAC,CAACgB,OAAO,CAACC,SAAS,IAAI;MACxC,IAAIjB,OAAO,CAACiB,SAAS,CAAC,KAAK,IAAI,EAAE;QAC/BF,aAAa,CAACE,SAAS,CAAC,GAAGjB,OAAO,CAACiB,SAAS,CAAC;MAC/C;IACF,CAAC,CAAC;IACF,OAAOrE,MAAM,CAACiB,MAAM,CAACjB,MAAM,CAACiB,MAAM,CAAC,CAAC,CAAC,EAAE2C,gBAAgB,CAAC,EAAE;MACxDR,OAAO,EAAEe;IACX,CAAC,CAAC;EACJ,CAAC,EAAE,CAACP,gBAAgB,EAAER,OAAO,CAAC,CAAC;EAC/B,MAAM,CAACkB,qBAAqB,CAAC,GAAGhI,eAAe,CAAC4H,gBAAgB,CAAC;EACjE;EACA,MAAMK,kBAAkB,GAAGA,CAACrC,OAAO,EAAEe,QAAQ,KAAK;IAChDT,eAAe,CAAC;MACd7E,UAAU,EAAEqC,MAAM,CAACiB,MAAM,CAACjB,MAAM,CAACiB,MAAM,CAAC,CAAC,CAAC,EAAEsB,eAAe,CAAC5E,UAAU,CAAC,EAAE;QACvEuE,OAAO;QACPe;MACF,CAAC;IACH,CAAC,EAAE,UAAU,CAAC;EAChB,CAAC;EACD,MAAM,CAACuB,gBAAgB,EAAEzB,eAAe,CAAC,GAAG/G,aAAa,CAACiI,UAAU,CAAC5E,MAAM,EAAEkF,kBAAkB,EAAE5G,UAAU,CAAC;EAC5G4E,eAAe,CAAC5E,UAAU,GAAGA,UAAU,KAAK,KAAK,GAAG,CAAC,CAAC,GAAGzB,kBAAkB,CAACsI,gBAAgB,EAAE7G,UAAU,CAAC;EACzG4E,eAAe,CAACQ,eAAe,GAAGA,eAAe;EACjD;EACA,MAAM0B,QAAQ,GAAG/J,KAAK,CAAC6E,OAAO,CAAC,MAAM;IACnC,IAAI5B,UAAU,KAAK,KAAK,IAAI,CAAC6G,gBAAgB,CAACvB,QAAQ,EAAE;MACtD,OAAOgB,UAAU;IACnB;IACA,MAAM;MACJ/B,OAAO,GAAG,CAAC;MACXwC,KAAK;MACLzB,QAAQ,GAAGhH;IACb,CAAC,GAAGuI,gBAAgB;IACpBtF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGH,OAAO,CAACiD,OAAO,GAAG,CAAC,EAAE,OAAO,EAAE,sCAAsC,CAAC,GAAG,KAAK,CAAC;IACtH;IACA,IAAI+B,UAAU,CAAC5E,MAAM,GAAGqF,KAAK,EAAE;MAC7B,IAAIT,UAAU,CAAC5E,MAAM,GAAG4D,QAAQ,EAAE;QAChC/D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGH,OAAO,CAAC,KAAK,EAAE,OAAO,EAAE,sJAAsJ,CAAC,GAAG,KAAK,CAAC;QAChO,OAAOgF,UAAU,CAACU,KAAK,CAAC,CAACzC,OAAO,GAAG,CAAC,IAAIe,QAAQ,EAAEf,OAAO,GAAGe,QAAQ,CAAC;MACvE;MACA,OAAOgB,UAAU;IACnB;IACA,OAAOA,UAAU,CAACU,KAAK,CAAC,CAACzC,OAAO,GAAG,CAAC,IAAIe,QAAQ,EAAEf,OAAO,GAAGe,QAAQ,CAAC;EACvE,CAAC,EAAE,CAAC,CAAC,CAACtF,UAAU,EAAEsG,UAAU,EAAEO,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACtC,OAAO,EAAEsC,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACvB,QAAQ,EAAEuB,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACE,KAAK,CAAC,CAAC;EACvT;EACA,MAAM,CAACE,yBAAyB,EAAEC,cAAc,CAAC,GAAG1I,YAAY,CAAC;IAC/Da,SAAS;IACT8H,IAAI,EAAEb,UAAU;IAChBQ,QAAQ;IACRrC,SAAS;IACTE,cAAc;IACdZ,UAAU;IACVzD,kBAAkB;IAClBY,MAAM,EAAEmC,WAAW;IACnB5C,iBAAiB,EAAEA,iBAAiB,IAAIyC;EAC1C,CAAC,EAAEjD,YAAY,CAAC;EAChB,MAAMmH,oBAAoB,GAAGA,CAAC1C,MAAM,EAAE2C,KAAK,EAAEC,MAAM,KAAK;IACtD,IAAIC,kBAAkB;IACtB,IAAI,OAAOpH,YAAY,KAAK,UAAU,EAAE;MACtCoH,kBAAkB,GAAGvK,UAAU,CAACmD,YAAY,CAACuE,MAAM,EAAE2C,KAAK,EAAEC,MAAM,CAAC,CAAC;IACtE,CAAC,MAAM;MACLC,kBAAkB,GAAGvK,UAAU,CAACmD,YAAY,CAAC;IAC/C;IACA,OAAOnD,UAAU,CAAC;MAChB,CAAC,GAAGqC,SAAS,eAAe,GAAG6H,cAAc,CAACvE,GAAG,CAAC8B,SAAS,CAACC,MAAM,EAAE2C,KAAK,CAAC;IAC5E,CAAC,EAAEE,kBAAkB,CAAC;EACxB,CAAC;EACD;EACA;EACAzD,gBAAgB,CAAC0D,sBAAsB,GAAG1D,gBAAgB,CAACnD,UAAU;EACrE;EACAmD,gBAAgB,CAACnD,UAAU,GAAGmD,gBAAgB,CAACnD,UAAU,IAAIA,UAAU,IAAI3C,gBAAgB,CAACqF,WAAW,CAAC;EACxG;EACA,IAAIU,UAAU,KAAK,MAAM,IAAID,gBAAgB,CAAChD,qBAAqB,KAAK2G,SAAS,EAAE;IACjF3D,gBAAgB,CAAChD,qBAAqB,GAAGb,YAAY,GAAG,CAAC,GAAG,CAAC;EAC/D,CAAC,MAAM,IAAI6D,gBAAgB,CAAChD,qBAAqB,GAAG,CAAC,IAAIb,YAAY,EAAE;IACrE6D,gBAAgB,CAAChD,qBAAqB,IAAI,CAAC;EAC7C;EACA;EACA,IAAI,OAAOgD,gBAAgB,CAAC/C,UAAU,KAAK,QAAQ,EAAE;IACnD+C,gBAAgB,CAAC/C,UAAU,GAAG,OAAOA,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAG,EAAE;EAChF;EACA;EACA,MAAM2G,gBAAgB,GAAG3K,KAAK,CAAC4K,WAAW,CAACC,YAAY,IAAIjB,qBAAqB,CAACM,yBAAyB,CAACZ,sBAAsB,CAACN,sBAAsB,CAAC6B,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC7B,sBAAsB,EAAEM,sBAAsB,EAAEY,yBAAyB,CAAC,CAAC;EACvP,IAAIY,iBAAiB;EACrB,IAAIC,oBAAoB;EACxB,IAAI9H,UAAU,KAAK,KAAK,KAAK6G,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACE,KAAK,CAAC,EAAE;IACxH,IAAIgB,cAAc;IAClB,IAAIlB,gBAAgB,CAACnH,IAAI,EAAE;MACzBqI,cAAc,GAAGlB,gBAAgB,CAACnH,IAAI;IACxC,CAAC,MAAM;MACLqI,cAAc,GAAG3E,UAAU,KAAK,OAAO,IAAIA,UAAU,KAAK,QAAQ,GAAG,OAAO,GAAGqE,SAAS;IAC1F;IACA,MAAMO,gBAAgB,GAAGC,QAAQ,KAAK,aAAalL,KAAK,CAACmL,aAAa,CAACrK,UAAU,EAAEwE,MAAM,CAACiB,MAAM,CAAC,CAAC,CAAC,EAAEuD,gBAAgB,EAAE;MACrHtH,SAAS,EAAEvC,UAAU,CAAC,GAAGqC,SAAS,eAAeA,SAAS,eAAe4I,QAAQ,EAAE,EAAEpB,gBAAgB,CAACtH,SAAS,CAAC;MAChHG,IAAI,EAAEqI;IACR,CAAC,CAAC,CAAC,CAAC;IACJ,MAAMI,eAAe,GAAGrF,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO;IAC9D,MAAM;MACJmF;IACF,CAAC,GAAGpB,gBAAgB;IACpB,IAAIoB,QAAQ,KAAK,IAAI,IAAIG,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAAC,EAAE;MAChD,MAAMK,MAAM,GAAGL,QAAQ,CAACM,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,CAAC;MACpD,MAAMC,SAAS,GAAGT,QAAQ,CAACM,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC,QAAQ,CAAC,CAAC;MAC1D,MAAME,SAAS,GAAGV,QAAQ,CAACW,KAAK,CAACJ,CAAC,IAAI,GAAGA,CAAC,EAAE,KAAK,MAAM,CAAC;MACxD,IAAI,CAACF,MAAM,IAAI,CAACI,SAAS,IAAI,CAACC,SAAS,EAAE;QACvCb,oBAAoB,GAAGE,gBAAgB,CAACG,eAAe,CAAC;MAC1D;MACA,IAAIG,MAAM,EAAE;QACVT,iBAAiB,GAAGG,gBAAgB,CAACM,MAAM,CAACO,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;MAC/E;MACA,IAAIJ,SAAS,EAAE;QACbZ,oBAAoB,GAAGE,gBAAgB,CAACU,SAAS,CAACG,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;MACxF;IACF,CAAC,MAAM;MACLhB,oBAAoB,GAAGE,gBAAgB,CAACG,eAAe,CAAC;IAC1D;EACF;EACA;EACA,IAAIY,SAAS;EACb,IAAI,OAAOrI,OAAO,KAAK,SAAS,EAAE;IAChCqI,SAAS,GAAG;MACVC,QAAQ,EAAEtI;IACZ,CAAC;EACH,CAAC,MAAM,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IACtCqI,SAAS,GAAG1G,MAAM,CAACiB,MAAM,CAAC;MACxB0F,QAAQ,EAAE;IACZ,CAAC,EAAEtI,OAAO,CAAC;EACb;EACA,MAAMuI,iBAAiB,GAAGjM,UAAU,CAAC6G,SAAS,EAAEH,OAAO,EAAE,GAAGrE,SAAS,UAAU,EAAE0D,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACxD,SAAS,EAAE;IAC9I,CAAC,GAAGF,SAAS,cAAc,GAAGyD,SAAS,KAAK;EAC9C,CAAC,EAAEvD,SAAS,EAAEC,aAAa,EAAEoE,MAAM,CAAC;EACpC,MAAMsF,WAAW,GAAG7G,MAAM,CAACiB,MAAM,CAACjB,MAAM,CAACiB,MAAM,CAAC,CAAC,CAAC,EAAEP,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACtD,KAAK,CAAC,EAAEA,KAAK,CAAC;EACtH;EACA,MAAM0J,eAAe,GAAGpM,KAAK,CAAC6E,OAAO,CAAC,MAAM;IAC1C;IACA;IACA;IACA;IACA,IAAI,CAACmH,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACC,QAAQ,KAAKxF,OAAO,KAAKzE,UAAU,EAAE;MACxG,OAAO,IAAI;IACb;IACA,IAAI,QAAQmC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACkI,SAAS,CAAC,KAAK,WAAW,EAAE;MAC7F,OAAOlI,MAAM,CAACkI,SAAS;IACzB;IACA,OAAO,CAACpG,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC,OAAO,CAAC,KAAK,aAAajG,KAAK,CAACmL,aAAa,CAAC1K,kBAAkB,EAAE;MAC9I6L,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACN,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACC,QAAQ,EAAExF,OAAO,EAAEtC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACkI,SAAS,EAAEpG,WAAW,CAAC,CAAC;EACtK;EACA,MAAMsG,cAAc,GAAGjI,OAAO,GAAGxC,cAAc,GAAGD,OAAO;EACzD;EACA,MAAM2K,YAAY,GAAG,CAAC,CAAC;EACvB,MAAMC,cAAc,GAAGzM,KAAK,CAAC6E,OAAO,CAAC,MAAM;IACzC,MAAM;MACJ6H,QAAQ;MACRC,UAAU;MACVC,SAAS;MACTC,OAAO;MACPC,SAAS;MACTC;IACF,CAAC,GAAGrG,KAAK;IACT,MAAMsG,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACR,QAAQ,GAAGC,UAAU,CAAC;IACpD,QAAQtG,UAAU;MAChB,KAAK,QAAQ;QACX,OAAO0G,SAAS,GAAG,CAAC,GAAGC,UAAU,GAAGJ,SAAS;MAC/C,KAAK,OAAO;QACV,OAAOE,SAAS,GAAG,CAAC,GAAGE,UAAU,GAAGJ,SAAS;MAC/C;QACE,OAAOC,OAAO,GAAG,CAAC,GAAGG,UAAU,GAAGJ,SAAS;IAC/C;EACF,CAAC,EAAE,CAAClG,KAAK,EAAEL,UAAU,CAAC,CAAC;EACvB,IAAI/B,OAAO,EAAE;IACXkI,YAAY,CAACC,cAAc,GAAGA,cAAc;EAC9C;EACA,OAAO7F,UAAU,CAAC,aAAa5G,KAAK,CAACmL,aAAa,CAAC,KAAK,EAAE;IACxDhJ,GAAG,EAAEmF,OAAO;IACZ9E,SAAS,EAAE0J,iBAAiB;IAC5BxJ,KAAK,EAAEyJ;EACT,CAAC,EAAE,aAAanM,KAAK,CAACmL,aAAa,CAACpK,IAAI,EAAEuE,MAAM,CAACiB,MAAM,CAAC;IACtD0F,QAAQ,EAAE;EACZ,CAAC,EAAED,SAAS,CAAC,EAAElB,iBAAiB,EAAE,aAAa9K,KAAK,CAACmL,aAAa,CAACoB,cAAc,EAAEjH,MAAM,CAACiB,MAAM,CAAC,CAAC,CAAC,EAAEiG,YAAY,EAAE3G,UAAU,EAAE;IAC7H1D,GAAG,EAAEoF,MAAM;IACXlE,OAAO,EAAE8B,aAAa;IACtBY,SAAS,EAAEA,SAAS;IACpBlC,UAAU,EAAEkD,gBAAgB;IAC5BzE,SAAS,EAAEA,SAAS;IACpBE,SAAS,EAAEvC,UAAU,CAAC;MACpB,CAAC,GAAGqC,SAAS,SAAS,GAAG+D,UAAU,KAAK,QAAQ;MAChD,CAAC,GAAG/D,SAAS,QAAQ,GAAG+D,UAAU,KAAK,OAAO;MAC9C,CAAC,GAAG/D,SAAS,WAAW,GAAGO,QAAQ;MACnC,CAAC,GAAGP,SAAS,QAAQ,GAAGmE,OAAO,CAAC9B,MAAM,KAAK;IAC7C,CAAC,EAAEmC,SAAS,EAAEH,OAAO,EAAEE,MAAM,CAAC;IAC9BuD,IAAI,EAAEL,QAAQ;IACd5G,MAAM,EAAEuE,SAAS;IACjBtE,YAAY,EAAEiH,oBAAoB;IAClCgC,SAAS,EAAED,eAAe;IAC1B;IACAe,aAAa,EAAEjN,cAAc;IAC7BgH,YAAY,EAAEA,YAAY;IAC1ByD,gBAAgB,EAAEA,gBAAgB;IAClCtD,iBAAiB,EAAEA;EACrB,CAAC,CAAC,CAAC,EAAE0D,oBAAoB,CAAC,CAAC,CAAC;AAC9B,CAAC;AACD,eAAe,aAAa/K,KAAK,CAACoN,UAAU,CAACnL,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}