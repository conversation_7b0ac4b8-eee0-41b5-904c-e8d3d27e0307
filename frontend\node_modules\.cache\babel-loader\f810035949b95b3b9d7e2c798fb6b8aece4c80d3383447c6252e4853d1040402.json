{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport React from 'react';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { supportRef, useComposeRef } from \"rc-util/es/ref\";\nimport findDOMNode from \"rc-util/es/Dom/findDOMNode\";\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport DomWrapper from \"./wrapper\";\nimport useMutateObserver from \"./useMutateObserver\";\nvar MutateObserver = function MutateObserver(props) {\n  var children = props.children,\n    options = props.options,\n    _props$onMutate = props.onMutate,\n    onMutate = _props$onMutate === void 0 ? function () {} : _props$onMutate;\n  var callback = useEvent(onMutate);\n  var wrapperRef = React.useRef(null);\n  var elementRef = React.useRef(null);\n  var canRef = /*#__PURE__*/React.isValidElement(children) && supportRef(children);\n  var mergedRef = useComposeRef(elementRef, canRef ? children.ref : null);\n  var _React$useState = React.useState(null),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    target = _React$useState2[0],\n    setTarget = _React$useState2[1];\n  useMutateObserver(target, callback, options);\n\n  // =========================== Effect ===========================\n  // Bind target\n  useLayoutEffect(function () {\n    setTarget(findDOMNode(elementRef.current) || findDOMNode(wrapperRef.current));\n  });\n\n  // =========================== Render ===========================\n  if (!children) {\n    if (process.env.NODE_ENV !== 'production') {\n      console.error('MutationObserver need children props');\n    }\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(DomWrapper, {\n    ref: wrapperRef\n  }, canRef ? /*#__PURE__*/React.cloneElement(children, {\n    ref: mergedRef\n  }) : children);\n};\nexport default MutateObserver;", "map": {"version": 3, "names": ["_slicedToArray", "React", "useLayoutEffect", "supportRef", "useComposeRef", "findDOMNode", "useEvent", "DomWrapper", "useMutateObserver", "MutateObserver", "props", "children", "options", "_props$onMutate", "onMutate", "callback", "wrapperRef", "useRef", "elementRef", "canRef", "isValidElement", "mergedRef", "ref", "_React$useState", "useState", "_React$useState2", "target", "<PERSON><PERSON><PERSON><PERSON>", "current", "process", "env", "NODE_ENV", "console", "error", "createElement", "cloneElement"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/@rc-component/mutate-observer/es/MutateObserver.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport React from 'react';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { supportRef, useComposeRef } from \"rc-util/es/ref\";\nimport findDOMNode from \"rc-util/es/Dom/findDOMNode\";\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport DomWrapper from \"./wrapper\";\nimport useMutateObserver from \"./useMutateObserver\";\nvar MutateObserver = function MutateObserver(props) {\n  var children = props.children,\n    options = props.options,\n    _props$onMutate = props.onMutate,\n    onMutate = _props$onMutate === void 0 ? function () {} : _props$onMutate;\n  var callback = useEvent(onMutate);\n  var wrapperRef = React.useRef(null);\n  var elementRef = React.useRef(null);\n  var canRef = /*#__PURE__*/React.isValidElement(children) && supportRef(children);\n  var mergedRef = useComposeRef(elementRef, canRef ? children.ref : null);\n  var _React$useState = React.useState(null),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    target = _React$useState2[0],\n    setTarget = _React$useState2[1];\n  useMutateObserver(target, callback, options);\n\n  // =========================== Effect ===========================\n  // Bind target\n  useLayoutEffect(function () {\n    setTarget(findDOMNode(elementRef.current) || findDOMNode(wrapperRef.current));\n  });\n\n  // =========================== Render ===========================\n  if (!children) {\n    if (process.env.NODE_ENV !== 'production') {\n      console.error('MutationObserver need children props');\n    }\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(DomWrapper, {\n    ref: wrapperRef\n  }, canRef ? /*#__PURE__*/React.cloneElement(children, {\n    ref: mergedRef\n  }) : children);\n};\nexport default MutateObserver;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,SAASC,UAAU,EAAEC,aAAa,QAAQ,gBAAgB;AAC1D,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,UAAU,MAAM,WAAW;AAClC,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;EAClD,IAAIC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;IAC3BC,OAAO,GAAGF,KAAK,CAACE,OAAO;IACvBC,eAAe,GAAGH,KAAK,CAACI,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,YAAY,CAAC,CAAC,GAAGA,eAAe;EAC1E,IAAIE,QAAQ,GAAGT,QAAQ,CAACQ,QAAQ,CAAC;EACjC,IAAIE,UAAU,GAAGf,KAAK,CAACgB,MAAM,CAAC,IAAI,CAAC;EACnC,IAAIC,UAAU,GAAGjB,KAAK,CAACgB,MAAM,CAAC,IAAI,CAAC;EACnC,IAAIE,MAAM,GAAG,aAAalB,KAAK,CAACmB,cAAc,CAACT,QAAQ,CAAC,IAAIR,UAAU,CAACQ,QAAQ,CAAC;EAChF,IAAIU,SAAS,GAAGjB,aAAa,CAACc,UAAU,EAAEC,MAAM,GAAGR,QAAQ,CAACW,GAAG,GAAG,IAAI,CAAC;EACvE,IAAIC,eAAe,GAAGtB,KAAK,CAACuB,QAAQ,CAAC,IAAI,CAAC;IACxCC,gBAAgB,GAAGzB,cAAc,CAACuB,eAAe,EAAE,CAAC,CAAC;IACrDG,MAAM,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC5BE,SAAS,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACjCjB,iBAAiB,CAACkB,MAAM,EAAEX,QAAQ,EAAEH,OAAO,CAAC;;EAE5C;EACA;EACAV,eAAe,CAAC,YAAY;IAC1ByB,SAAS,CAACtB,WAAW,CAACa,UAAU,CAACU,OAAO,CAAC,IAAIvB,WAAW,CAACW,UAAU,CAACY,OAAO,CAAC,CAAC;EAC/E,CAAC,CAAC;;EAEF;EACA,IAAI,CAACjB,QAAQ,EAAE;IACb,IAAIkB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCC,OAAO,CAACC,KAAK,CAAC,sCAAsC,CAAC;IACvD;IACA,OAAO,IAAI;EACb;EACA,OAAO,aAAahC,KAAK,CAACiC,aAAa,CAAC3B,UAAU,EAAE;IAClDe,GAAG,EAAEN;EACP,CAAC,EAAEG,MAAM,GAAG,aAAalB,KAAK,CAACkC,YAAY,CAACxB,QAAQ,EAAE;IACpDW,GAAG,EAAED;EACP,CAAC,CAAC,GAAGV,QAAQ,CAAC;AAChB,CAAC;AACD,eAAeF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}