package com.yinma.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * BOM数据传输对象
 * 用于前后端数据交互
 * 
 * <AUTHOR> System
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class BomDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * BOM主键ID
     */
    private Long id;

    /**
     * BOM编码
     */
    @NotBlank(message = "BOM编码不能为空")
    @Size(max = 50, message = "BOM编码长度不能超过50个字符")
    private String bomCode;

    /**
     * BOM名称
     */
    @NotBlank(message = "BOM名称不能为空")
    @Size(max = 200, message = "BOM名称长度不能超过200个字符")
    private String bomName;

    /**
     * BOM类型
     */
    @NotBlank(message = "BOM类型不能为空")
    private String bomType;

    /**
     * 产品编码
     */
    @NotBlank(message = "产品编码不能为空")
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品规格
     */
    private String productSpec;

    /**
     * 版本号
     */
    @NotBlank(message = "版本号不能为空")
    private String version;

    /**
     * 版本状态
     */
    private String versionStatus;

    /**
     * 生效日期
     */
    @NotNull(message = "生效日期不能为空")
    private LocalDateTime effectiveDate;

    /**
     * 失效日期
     */
    private LocalDateTime expiryDate;

    /**
     * 转换来源类型
     */
    private String conversionSource;

    /**
     * 来源BOM ID
     */
    private Long sourceBomId;

    /**
     * 来源BOM编码
     */
    private String sourceBomCode;

    /**
     * 工艺路线编码
     */
    private String routingCode;

    /**
     * 工艺路线名称
     */
    private String routingName;

    /**
     * 标准工时（小时）
     */
    @DecimalMin(value = "0", message = "标准工时不能为负数")
    private BigDecimal standardHours;

    /**
     * 标准成本
     */
    @DecimalMin(value = "0", message = "标准成本不能为负数")
    private BigDecimal standardCost;

    /**
     * 是否主BOM
     */
    private Boolean isMainBom;

    /**
     * 审批状态
     */
    private String approvalStatus;

    /**
     * 审批人
     */
    private String approvedBy;

    /**
     * 审批时间
     */
    private LocalDateTime approvedTime;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * BOM明细列表
     */
    @Valid
    private List<BomDetailDTO> bomDetails;

    /**
     * 子级BOM列表
     */
    private List<BomDTO> childBoms;

    /**
     * 变更日志列表
     */
    private List<BomChangeLogDTO> changeLogs;

    /**
     * 物料需求汇总
     */
    private List<MaterialRequirementDTO> materialRequirements;

    /**
     * 成本构成分析
     */
    private List<CostAnalysisDTO> costAnalysis;

    /**
     * BOM层级（计算字段）
     */
    private Integer bomLevel;

    /**
     * 总物料数量（计算字段）
     */
    private Integer totalMaterialCount;

    /**
     * 总成本（计算字段）
     */
    private BigDecimal totalCost;

    /**
     * 是否有变更（计算字段）
     */
    private Boolean hasChanges;

    /**
     * 最后变更时间（计算字段）
     */
    private LocalDateTime lastChangeTime;

    /**
     * 影响的下级BOM数量（计算字段）
     */
    private Integer affectedBomCount;

    /**
     * 物料需求汇总DTO
     */
    @Data
    @Accessors(chain = true)
    public static class MaterialRequirementDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 物料编码
         */
        private String materialCode;

        /**
         * 物料名称
         */
        private String materialName;

        /**
         * 物料规格
         */
        private String materialSpec;

        /**
         * 物料类型
         */
        private String materialType;

        /**
         * 需求数量
         */
        private BigDecimal requiredQty;

        /**
         * 单位
         */
        private String unit;

        /**
         * 单价
         */
        private BigDecimal unitPrice;

        /**
         * 总金额
         */
        private BigDecimal totalAmount;

        /**
         * 当前库存
         */
        private BigDecimal currentStock;

        /**
         * 缺料数量
         */
        private BigDecimal shortageQty;

        /**
         * 供应商
         */
        private String supplier;

        /**
         * 采购提前期
         */
        private Integer leadTime;
    }

    /**
     * 成本分析DTO
     */
    @Data
    @Accessors(chain = true)
    public static class CostAnalysisDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 成本类型：MATERIAL-物料成本，LABOR-人工成本，OVERHEAD-制造费用
         */
        private String costType;

        /**
         * 成本名称
         */
        private String costName;

        /**
         * 成本金额
         */
        private BigDecimal costAmount;

        /**
         * 成本占比
         */
        private BigDecimal costRatio;

        /**
         * 明细说明
         */
        private String description;
    }

    /**
     * BOM查询条件DTO
     */
    @Data
    @Accessors(chain = true)
    public static class BomQueryDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * BOM编码（模糊查询）
         */
        private String bomCode;

        /**
         * BOM名称（模糊查询）
         */
        private String bomName;

        /**
         * BOM类型
         */
        private String bomType;

        /**
         * 产品编码（模糊查询）
         */
        private String productCode;

        /**
         * 产品名称（模糊查询）
         */
        private String productName;

        /**
         * 版本状态
         */
        private String versionStatus;

        /**
         * 审批状态
         */
        private String approvalStatus;

        /**
         * 是否主BOM
         */
        private Boolean isMainBom;

        /**
         * 生效日期开始
         */
        private LocalDateTime effectiveDateStart;

        /**
         * 生效日期结束
         */
        private LocalDateTime effectiveDateEnd;

        /**
         * 创建人
         */
        private String createBy;

        /**
         * 创建时间开始
         */
        private LocalDateTime createTimeStart;

        /**
         * 创建时间结束
         */
        private LocalDateTime createTimeEnd;

        /**
         * 页码
         */
        @Min(value = 1, message = "页码必须大于0")
        private Integer pageNum = 1;

        /**
         * 页大小
         */
        @Min(value = 1, message = "页大小必须大于0")
        @Max(value = 100, message = "页大小不能超过100")
        private Integer pageSize = 10;

        /**
         * 排序字段
         */
        private String orderBy;

        /**
         * 排序方向：ASC-升序，DESC-降序
         */
        private String orderDirection = "DESC";
    }

    /**
     * BOM转换请求DTO
     */
    @Data
    @Accessors(chain = true)
    public static class BomConversionDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 源BOM ID
         */
        @NotNull(message = "源BOM ID不能为空")
        private Long sourceBomId;

        /**
         * 目标BOM类型
         */
        @NotBlank(message = "目标BOM类型不能为空")
        private String targetBomType;

        /**
         * 新BOM编码
         */
        @NotBlank(message = "新BOM编码不能为空")
        private String newBomCode;

        /**
         * 新BOM名称
         */
        @NotBlank(message = "新BOM名称不能为空")
        private String newBomName;

        /**
         * 新版本号
         */
        @NotBlank(message = "新版本号不能为空")
        private String newVersion;

        /**
         * 生效日期
         */
        @NotNull(message = "生效日期不能为空")
        private LocalDateTime effectiveDate;

        /**
         * 工艺路线编码
         */
        private String routingCode;

        /**
         * 转换规则配置
         */
        private String conversionRules;

        /**
         * 备注
         */
        private String remark;
    }

    /**
     * BOM比较结果DTO
     */
    @Data
    @Accessors(chain = true)
    public static class BomComparisonDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 比较类型：ADDED-新增，DELETED-删除，MODIFIED-修改，UNCHANGED-未变更
         */
        private String compareType;

        /**
         * 物料编码
         */
        private String materialCode;

        /**
         * 物料名称
         */
        private String materialName;

        /**
         * 原数量
         */
        private BigDecimal oldQty;

        /**
         * 新数量
         */
        private BigDecimal newQty;

        /**
         * 数量差异
         */
        private BigDecimal qtyDifference;

        /**
         * 原单价
         */
        private BigDecimal oldUnitPrice;

        /**
         * 新单价
         */
        private BigDecimal newUnitPrice;

        /**
         * 成本影响
         */
        private BigDecimal costImpact;

        /**
         * 变更说明
         */
        private String changeDescription;
    }
}