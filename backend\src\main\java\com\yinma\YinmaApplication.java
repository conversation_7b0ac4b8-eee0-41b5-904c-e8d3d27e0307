package com.yinma;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 西安银马实业数字化管理系统启动类
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@SpringBootApplication
@EnableCaching
@EnableAsync
@EnableScheduling
@EnableTransactionManagement
@MapperScan("com.yinma.mapper")
public class YinmaApplication {

    public static void main(String[] args) {
        SpringApplication.run(YinmaApplication.class, args);
        System.out.println("");
        System.out.println("===========================================");
        System.out.println("    西安银马实业数字化管理系统启动成功！    ");
        System.out.println("    API文档地址: http://localhost:8080/api/doc.html");
        System.out.println("    Druid监控: http://localhost:8080/api/druid");
        System.out.println("===========================================");
        System.out.println("");
    }
}