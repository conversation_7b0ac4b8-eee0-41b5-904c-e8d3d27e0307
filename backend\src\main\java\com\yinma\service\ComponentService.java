package com.yinma.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yinma.entity.Component;
import com.yinma.dto.ComponentDTO;
import com.yinma.vo.ComponentVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 核心部件管理服务接口
 * 银马实业设备核心部件管理系统
 * 
 * <AUTHOR>
 * @since 2024-05-20
 */
public interface ComponentService extends IService<Component> {

    /**
     * 分页查询核心部件
     * 
     * @param page 分页参数
     * @param componentName 部件名称
     * @param componentCode 部件编码
     * @param category 部件分类
     * @param componentType 部件类型
     * @param supplier 供应商
     * @param status 状态
     * @return 分页结果
     */
    IPage<ComponentVO> getComponentPage(Page<Component> page, String componentName, 
                                      String componentCode, String category, 
                                      String componentType, String supplier, String status);

    /**
     * 根据ID查询核心部件详情
     * 
     * @param id 部件ID
     * @return 部件详情
     */
    ComponentVO getComponentDetailById(Long id);

    /**
     * 创建核心部件
     * 
     * @param componentDTO 部件信息
     * @return 创建的部件
     */
    Component createComponent(ComponentDTO componentDTO);

    /**
     * 更新核心部件
     * 
     * @param id 部件ID
     * @param componentDTO 部件信息
     * @return 更新后的部件
     */
    Component updateComponent(Long id, ComponentDTO componentDTO);

    /**
     * 删除核心部件
     * 
     * @param id 部件ID
     * @return 是否删除成功
     */
    boolean deleteComponent(Long id);

    /**
     * 批量删除核心部件
     * 
     * @param ids 部件ID列表
     * @return 是否删除成功
     */
    boolean batchDeleteComponents(List<Long> ids);

    /**
     * 更新核心部件状态
     * 
     * @param id 部件ID
     * @param status 新状态
     * @param remark 备注
     * @return 更新后的部件
     */
    Component updateComponentStatus(Long id, String status, String remark);

    /**
     * 复制核心部件
     * 
     * @param id 原部件ID
     * @param newComponentName 新部件名称
     * @param newComponentCode 新部件编码
     * @return 复制的部件
     */
    Component copyComponent(Long id, String newComponentName, String newComponentCode);

    /**
     * 获取核心部件技术规格
     * 
     * @param id 部件ID
     * @return 技术规格
     */
    Map<String, Object> getComponentSpecifications(Long id);

    /**
     * 更新核心部件技术规格
     * 
     * @param id 部件ID
     * @param specifications 技术规格
     */
    void updateComponentSpecifications(Long id, Map<String, Object> specifications);

    /**
     * 获取核心部件供应商信息
     * 
     * @param id 部件ID
     * @return 供应商信息列表
     */
    List<Map<String, Object>> getComponentSuppliers(Long id);

    /**
     * 添加核心部件供应商
     * 
     * @param id 部件ID
     * @param supplierInfo 供应商信息
     */
    void addComponentSupplier(Long id, Map<String, Object> supplierInfo);

    /**
     * 移除核心部件供应商
     * 
     * @param id 部件ID
     * @param supplierId 供应商ID
     * @return 是否移除成功
     */
    boolean removeComponentSupplier(Long id, Long supplierId);

    /**
     * 获取核心部件价格历史
     * 
     * @param id 部件ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 价格历史
     */
    List<Map<String, Object>> getComponentPriceHistory(Long id, String startDate, String endDate);

    /**
     * 更新核心部件价格
     * 
     * @param id 部件ID
     * @param priceInfo 价格信息
     */
    void updateComponentPrice(Long id, Map<String, Object> priceInfo);

    /**
     * 获取核心部件库存信息
     * 
     * @param id 部件ID
     * @return 库存信息
     */
    Map<String, Object> getComponentInventory(Long id);

    /**
     * 更新核心部件库存
     * 
     * @param id 部件ID
     * @param inventoryInfo 库存信息
     */
    void updateComponentInventory(Long id, Map<String, Object> inventoryInfo);

    /**
     * 获取核心部件质量检测记录
     * 
     * @param id 部件ID
     * @return 质量检测记录
     */
    List<Map<String, Object>> getComponentQualityRecords(Long id);

    /**
     * 添加核心部件质量检测记录
     * 
     * @param id 部件ID
     * @param qualityRecord 质量检测记录
     */
    void addComponentQualityRecord(Long id, Map<String, Object> qualityRecord);

    /**
     * 获取核心部件使用记录
     * 
     * @param id 部件ID
     * @return 使用记录
     */
    List<Map<String, Object>> getComponentUsageRecords(Long id);

    /**
     * 记录核心部件使用情况
     * 
     * @param id 部件ID
     * @param usageRecord 使用记录
     */
    void recordComponentUsage(Long id, Map<String, Object> usageRecord);

    /**
     * 获取核心部件维护计划
     * 
     * @param id 部件ID
     * @return 维护计划
     */
    List<Map<String, Object>> getComponentMaintenancePlan(Long id);

    /**
     * 创建核心部件维护计划
     * 
     * @param id 部件ID
     * @param maintenancePlan 维护计划
     * @return 创建的维护计划
     */
    Map<String, Object> createComponentMaintenancePlan(Long id, Map<String, Object> maintenancePlan);

    /**
     * 上传核心部件图片
     * 
     * @param id 部件ID
     * @param file 图片文件
     * @param imageType 图片类型
     * @return 图片URL
     */
    String uploadComponentImage(Long id, MultipartFile file, String imageType);

    /**
     * 获取核心部件图片列表
     * 
     * @param id 部件ID
     * @return 图片列表
     */
    List<Map<String, Object>> getComponentImages(Long id);

    /**
     * 删除核心部件图片
     * 
     * @param id 部件ID
     * @param imageId 图片ID
     * @return 是否删除成功
     */
    boolean deleteComponentImage(Long id, Long imageId);

    /**
     * 上传核心部件技术文档
     * 
     * @param id 部件ID
     * @param file 文档文件
     * @param docType 文档类型
     * @return 文档URL
     */
    String uploadComponentDocument(Long id, MultipartFile file, String docType);

    /**
     * 获取核心部件技术文档列表
     * 
     * @param id 部件ID
     * @return 文档列表
     */
    List<Map<String, Object>> getComponentDocuments(Long id);

    /**
     * 删除核心部件技术文档
     * 
     * @param id 部件ID
     * @param docId 文档ID
     * @return 是否删除成功
     */
    boolean deleteComponentDocument(Long id, Long docId);

    /**
     * 导出核心部件数据
     * 
     * @param componentName 部件名称
     * @param category 部件分类
     * @param status 状态
     * @param format 导出格式
     * @return 文件URL
     */
    String exportComponents(String componentName, String category, String status, String format);

    /**
     * 导入核心部件数据
     * 
     * @param file 导入文件
     * @return 导入结果
     */
    Map<String, Object> importComponents(MultipartFile file);

    /**
     * 获取核心部件统计数据
     * 
     * @return 统计数据
     */
    Map<String, Object> getComponentStatistics();

    /**
     * 获取核心部件选项列表
     * 
     * @param category 部件分类
     * @param componentType 部件类型
     * @return 选项列表
     */
    List<Map<String, Object>> getComponentOptions(String category, String componentType);

    /**
     * 验证核心部件编码唯一性
     * 
     * @param componentCode 部件编码
     * @param excludeId 排除的ID
     * @return 是否唯一
     */
    boolean validateComponentCode(String componentCode, Long excludeId);

    /**
     * 获取核心部件分类树
     * 
     * @return 分类树
     */
    List<Map<String, Object>> getComponentCategoryTree();

    /**
     * 获取核心部件兼容性信息
     * 
     * @param id 部件ID
     * @return 兼容性信息
     */
    Map<String, Object> getComponentCompatibility(Long id);

    /**
     * 更新核心部件兼容性信息
     * 
     * @param id 部件ID
     * @param compatibility 兼容性信息
     */
    void updateComponentCompatibility(Long id, Map<String, Object> compatibility);

    /**
     * 获取核心部件替代方案
     * 
     * @param id 部件ID
     * @return 替代方案列表
     */
    List<Map<String, Object>> getComponentAlternatives(Long id);

    /**
     * 添加核心部件替代方案
     * 
     * @param id 部件ID
     * @param alternativeId 替代部件ID
     * @param reason 替代原因
     */
    void addComponentAlternative(Long id, Long alternativeId, String reason);

    /**
     * 移除核心部件替代方案
     * 
     * @param id 部件ID
     * @param alternativeId 替代部件ID
     * @return 是否移除成功
     */
    boolean removeComponentAlternative(Long id, Long alternativeId);

    /**
     * 获取核心部件生命周期信息
     * 
     * @param id 部件ID
     * @return 生命周期信息
     */
    Map<String, Object> getComponentLifecycle(Long id);

    /**
     * 更新核心部件生命周期信息
     * 
     * @param id 部件ID
     * @param lifecycle 生命周期信息
     */
    void updateComponentLifecycle(Long id, Map<String, Object> lifecycle);

    /**
     * 生成核心部件二维码
     * 
     * @param id 部件ID
     * @return 二维码URL
     */
    String generateComponentQRCode(Long id);

    /**
     * 获取核心部件性能指标
     * 
     * @param id 部件ID
     * @return 性能指标
     */
    Map<String, Object> getComponentPerformance(Long id);

    /**
     * 更新核心部件性能指标
     * 
     * @param id 部件ID
     * @param performance 性能指标
     */
    void updateComponentPerformance(Long id, Map<String, Object> performance);

    /**
     * 获取核心部件成本分析
     * 
     * @param id 部件ID
     * @return 成本分析
     */
    Map<String, Object> getComponentCostAnalysis(Long id);

    /**
     * 更新核心部件成本信息
     * 
     * @param id 部件ID
     * @param costInfo 成本信息
     */
    void updateComponentCost(Long id, Map<String, Object> costInfo);

    /**
     * 获取核心部件变更历史
     * 
     * @param id 部件ID
     * @return 变更历史
     */
    List<Map<String, Object>> getComponentChangeHistory(Long id);

    /**
     * 记录核心部件变更
     * 
     * @param id 部件ID
     * @param changeType 变更类型
     * @param changeContent 变更内容
     * @param operator 操作人
     */
    void recordComponentChange(Long id, String changeType, String changeContent, String operator);

    /**
     * 获取核心部件关联的产品系列
     * 
     * @param id 部件ID
     * @return 关联的产品系列列表
     */
    List<Map<String, Object>> getComponentRelatedSeries(Long id);

    /**
     * 添加核心部件与产品系列的关联
     * 
     * @param id 部件ID
     * @param seriesId 产品系列ID
     * @param usage 使用说明
     */
    void addComponentSeriesRelation(Long id, Long seriesId, String usage);

    /**
     * 移除核心部件与产品系列的关联
     * 
     * @param id 部件ID
     * @param seriesId 产品系列ID
     * @return 是否移除成功
     */
    boolean removeComponentSeriesRelation(Long id, Long seriesId);

    /**
     * 获取核心部件的标准化信息
     * 
     * @param id 部件ID
     * @return 标准化信息
     */
    Map<String, Object> getComponentStandardization(Long id);

    /**
     * 更新核心部件的标准化信息
     * 
     * @param id 部件ID
     * @param standardization 标准化信息
     */
    void updateComponentStandardization(Long id, Map<String, Object> standardization);

    /**
     * 获取核心部件的认证信息
     * 
     * @param id 部件ID
     * @return 认证信息列表
     */
    List<Map<String, Object>> getComponentCertifications(Long id);

    /**
     * 添加核心部件认证信息
     * 
     * @param id 部件ID
     * @param certification 认证信息
     */
    void addComponentCertification(Long id, Map<String, Object> certification);

    /**
     * 移除核心部件认证信息
     * 
     * @param id 部件ID
     * @param certificationId 认证ID
     * @return 是否移除成功
     */
    boolean removeComponentCertification(Long id, Long certificationId);

    /**
     * 获取核心部件的环保信息
     * 
     * @param id 部件ID
     * @return 环保信息
     */
    Map<String, Object> getComponentEnvironmentalInfo(Long id);

    /**
     * 更新核心部件的环保信息
     * 
     * @param id 部件ID
     * @param environmentalInfo 环保信息
     */
    void updateComponentEnvironmentalInfo(Long id, Map<String, Object> environmentalInfo);

    /**
     * 获取核心部件的安全信息
     * 
     * @param id 部件ID
     * @return 安全信息
     */
    Map<String, Object> getComponentSafetyInfo(Long id);

    /**
     * 更新核心部件的安全信息
     * 
     * @param id 部件ID
     * @param safetyInfo 安全信息
     */
    void updateComponentSafetyInfo(Long id, Map<String, Object> safetyInfo);

    /**
     * 获取核心部件的可靠性数据
     * 
     * @param id 部件ID
     * @return 可靠性数据
     */
    Map<String, Object> getComponentReliabilityData(Long id);

    /**
     * 更新核心部件的可靠性数据
     * 
     * @param id 部件ID
     * @param reliabilityData 可靠性数据
     */
    void updateComponentReliabilityData(Long id, Map<String, Object> reliabilityData);

    /**
     * 获取核心部件的测试报告
     * 
     * @param id 部件ID
     * @return 测试报告列表
     */
    List<Map<String, Object>> getComponentTestReports(Long id);

    /**
     * 上传核心部件测试报告
     * 
     * @param id 部件ID
     * @param file 测试报告文件
     * @param testType 测试类型
     * @return 报告URL
     */
    String uploadComponentTestReport(Long id, MultipartFile file, String testType);

    /**
     * 删除核心部件测试报告
     * 
     * @param id 部件ID
     * @param reportId 报告ID
     * @return 是否删除成功
     */
    boolean deleteComponentTestReport(Long id, Long reportId);

    /**
     * 获取核心部件的工艺要求
     * 
     * @param id 部件ID
     * @return 工艺要求
     */
    Map<String, Object> getComponentProcessRequirements(Long id);

    /**
     * 更新核心部件的工艺要求
     * 
     * @param id 部件ID
     * @param processRequirements 工艺要求
     */
    void updateComponentProcessRequirements(Long id, Map<String, Object> processRequirements);

    /**
     * 获取核心部件的包装要求
     * 
     * @param id 部件ID
     * @return 包装要求
     */
    Map<String, Object> getComponentPackagingRequirements(Long id);

    /**
     * 更新核心部件的包装要求
     * 
     * @param id 部件ID
     * @param packagingRequirements 包装要求
     */
    void updateComponentPackagingRequirements(Long id, Map<String, Object> packagingRequirements);

    /**
     * 获取核心部件的运输要求
     * 
     * @param id 部件ID
     * @return 运输要求
     */
    Map<String, Object> getComponentTransportRequirements(Long id);

    /**
     * 更新核心部件的运输要求
     * 
     * @param id 部件ID
     * @param transportRequirements 运输要求
     */
    void updateComponentTransportRequirements(Long id, Map<String, Object> transportRequirements);

    /**
     * 获取核心部件的存储要求
     * 
     * @param id 部件ID
     * @return 存储要求
     */
    Map<String, Object> getComponentStorageRequirements(Long id);

    /**
     * 更新核心部件的存储要求
     * 
     * @param id 部件ID
     * @param storageRequirements 存储要求
     */
    void updateComponentStorageRequirements(Long id, Map<String, Object> storageRequirements);

    /**
     * 获取核心部件的安装指南
     * 
     * @param id 部件ID
     * @return 安装指南
     */
    Map<String, Object> getComponentInstallationGuide(Long id);

    /**
     * 更新核心部件的安装指南
     * 
     * @param id 部件ID
     * @param installationGuide 安装指南
     */
    void updateComponentInstallationGuide(Long id, Map<String, Object> installationGuide);

    /**
     * 获取核心部件的维护手册
     * 
     * @param id 部件ID
     * @return 维护手册
     */
    Map<String, Object> getComponentMaintenanceManual(Long id);

    /**
     * 更新核心部件的维护手册
     * 
     * @param id 部件ID
     * @param maintenanceManual 维护手册
     */
    void updateComponentMaintenanceManual(Long id, Map<String, Object> maintenanceManual);

    /**
     * 获取核心部件的故障诊断指南
     * 
     * @param id 部件ID
     * @return 故障诊断指南
     */
    Map<String, Object> getComponentTroubleshootingGuide(Long id);

    /**
     * 更新核心部件的故障诊断指南
     * 
     * @param id 部件ID
     * @param troubleshootingGuide 故障诊断指南
     */
    void updateComponentTroubleshootingGuide(Long id, Map<String, Object> troubleshootingGuide);

    /**
     * 获取核心部件的备件清单
     * 
     * @param id 部件ID
     * @return 备件清单
     */
    List<Map<String, Object>> getComponentSparePartsList(Long id);

    /**
     * 更新核心部件的备件清单
     * 
     * @param id 部件ID
     * @param sparePartsList 备件清单
     */
    void updateComponentSparePartsList(Long id, List<Map<String, Object>> sparePartsList);

    /**
     * 获取核心部件的工具清单
     * 
     * @param id 部件ID
     * @return 工具清单
     */
    List<Map<String, Object>> getComponentToolsList(Long id);

    /**
     * 更新核心部件的工具清单
     * 
     * @param id 部件ID
     * @param toolsList 工具清单
     */
    void updateComponentToolsList(Long id, List<Map<String, Object>> toolsList);

    /**
     * 获取核心部件的培训材料
     * 
     * @param id 部件ID
     * @return 培训材料列表
     */
    List<Map<String, Object>> getComponentTrainingMaterials(Long id);

    /**
     * 添加核心部件培训材料
     * 
     * @param id 部件ID
     * @param trainingMaterial 培训材料
     */
    void addComponentTrainingMaterial(Long id, Map<String, Object> trainingMaterial);

    /**
     * 删除核心部件培训材料
     * 
     * @param id 部件ID
     * @param materialId 材料ID
     * @return 是否删除成功
     */
    boolean deleteComponentTrainingMaterial(Long id, Long materialId);

    /**
     * 获取核心部件的数字化模型
     * 
     * @param id 部件ID
     * @return 数字化模型信息
     */
    Map<String, Object> getComponentDigitalModel(Long id);

    /**
     * 上传核心部件数字化模型
     * 
     * @param id 部件ID
     * @param file 模型文件
     * @param modelType 模型类型
     * @return 模型URL
     */
    String uploadComponentDigitalModel(Long id, MultipartFile file, String modelType);

    /**
     * 获取核心部件的AR/VR展示配置
     * 
     * @param id 部件ID
     * @return AR/VR配置
     */
    Map<String, Object> getComponentARVRConfig(Long id);

    /**
     * 更新核心部件的AR/VR展示配置
     * 
     * @param id 部件ID
     * @param arvrConfig AR/VR配置
     */
    void updateComponentARVRConfig(Long id, Map<String, Object> arvrConfig);
}