{"ast": null, "code": "import isObjectLike from './is-object-like';\nimport isType from './is-type';\nvar isPlainObject = function (value) {\n  /**\n   * isObjectLike(new Foo) => false\n   * isObjectLike([1, 2, 3]) => false\n   * isObjectLike({ x: 0, y: 0 }) => true\n   * isObjectLike(Object.create(null)) => true\n   */\n  if (!isObjectLike(value) || !isType(value, 'Object')) {\n    return false;\n  }\n  if (Object.getPrototypeOf(value) === null) {\n    return true;\n  }\n  var proto = value;\n  while (Object.getPrototypeOf(proto) !== null) {\n    proto = Object.getPrototypeOf(proto);\n  }\n  return Object.getPrototypeOf(value) === proto;\n};\nexport default isPlainObject;", "map": {"version": 3, "names": ["isObjectLike", "isType", "isPlainObject", "value", "Object", "getPrototypeOf", "proto"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\util\\src\\lodash\\is-plain-object.ts"], "sourcesContent": ["import isObjectLike from './is-object-like';\nimport isType from './is-type';\n\nconst isPlainObject = function (value: any): value is object {\n  /**\n   * isObjectLike(new Foo) => false\n   * isObjectLike([1, 2, 3]) => false\n   * isObjectLike({ x: 0, y: 0 }) => true\n   * isObjectLike(Object.create(null)) => true\n   */\n  if (!isObjectLike(value) || !isType(value, 'Object')) {\n    return false;\n  }\n  if (Object.getPrototypeOf(value) === null) {\n    return true;\n  }\n  let proto = value;\n  while (Object.getPrototypeOf(proto) !== null) {\n    proto = Object.getPrototypeOf(proto);\n  }\n  return Object.getPrototypeOf(value) === proto;\n};\n\nexport default isPlainObject;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,kBAAkB;AAC3C,OAAOC,MAAM,MAAM,WAAW;AAE9B,IAAMC,aAAa,GAAG,SAAAA,CAAUC,KAAU;EACxC;;;;;;EAMA,IAAI,CAACH,YAAY,CAACG,KAAK,CAAC,IAAI,CAACF,MAAM,CAACE,KAAK,EAAE,QAAQ,CAAC,EAAE;IACpD,OAAO,KAAK;EACd;EACA,IAAIC,MAAM,CAACC,cAAc,CAACF,KAAK,CAAC,KAAK,IAAI,EAAE;IACzC,OAAO,IAAI;EACb;EACA,IAAIG,KAAK,GAAGH,KAAK;EACjB,OAAOC,MAAM,CAACC,cAAc,CAACC,KAAK,CAAC,KAAK,IAAI,EAAE;IAC5CA,KAAK,GAAGF,MAAM,CAACC,cAAc,CAACC,KAAK,CAAC;EACtC;EACA,OAAOF,MAAM,CAACC,cAAc,CAACF,KAAK,CAAC,KAAKG,KAAK;AAC/C,CAAC;AAED,eAAeJ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}