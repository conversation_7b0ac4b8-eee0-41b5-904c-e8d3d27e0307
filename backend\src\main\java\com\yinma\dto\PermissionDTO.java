package com.yinma.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 权限信息DTO
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
@Data
@Accessors(chain = true)
@Schema(description = "权限信息DTO")
public class PermissionDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "权限ID")
    private Long permissionId;

    @Schema(description = "父权限ID")
    private Long parentId;

    @Schema(description = "权限编码")
    @NotBlank(message = "权限编码不能为空")
    @Size(min = 2, max = 100, message = "权限编码长度必须在2-100个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9:_-]+$", message = "权限编码只能包含字母、数字、冒号、下划线和横线")
    private String permissionCode;

    @Schema(description = "权限名称")
    @NotBlank(message = "权限名称不能为空")
    @Size(min = 2, max = 100, message = "权限名称长度必须在2-100个字符之间")
    private String permissionName;

    @Schema(description = "权限类型：1-菜单，2-按钮，3-接口")
    @NotNull(message = "权限类型不能为空")
    @Min(value = 1, message = "权限类型值不正确")
    @Max(value = 3, message = "权限类型值不正确")
    private Integer permissionType;

    @Schema(description = "权限描述")
    @Size(max = 500, message = "权限描述长度不能超过500个字符")
    private String permissionDesc;

    @Schema(description = "菜单路径")
    @Size(max = 200, message = "菜单路径长度不能超过200个字符")
    private String menuPath;

    @Schema(description = "菜单组件")
    @Size(max = 200, message = "菜单组件长度不能超过200个字符")
    private String menuComponent;

    @Schema(description = "菜单图标")
    @Size(max = 100, message = "菜单图标长度不能超过100个字符")
    private String menuIcon;

    @Schema(description = "接口URL")
    @Size(max = 500, message = "接口URL长度不能超过500个字符")
    private String apiUrl;

    @Schema(description = "接口方法：GET,POST,PUT,DELETE等")
    @Size(max = 20, message = "接口方法长度不能超过20个字符")
    private String apiMethod;

    @Schema(description = "权限状态：0-禁用，1-启用")
    private Integer status;

    @Schema(description = "是否系统内置：0-否，1-是")
    private Integer isSystem;

    @Schema(description = "是否显示：0-隐藏，1-显示")
    private Integer isVisible;

    @Schema(description = "排序号")
    @Min(value = 0, message = "排序号不能为负数")
    private Integer sortOrder;

    @Schema(description = "权限级别")
    private Integer permissionLevel;

    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    @Schema(description = "子权限列表")
    private List<PermissionDTO> children;

    @Schema(description = "角色数量")
    private Long roleCount;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 权限查询DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "权限查询DTO")
    public static class PermissionQueryDTO implements Serializable {

        @Schema(description = "页码")
        @Min(value = 1, message = "页码必须大于0")
        private Integer current = 1;

        @Schema(description = "每页大小")
        @Min(value = 1, message = "每页大小必须大于0")
        @Max(value = 100, message = "每页大小不能超过100")
        private Integer size = 10;

        @Schema(description = "父权限ID")
        private Long parentId;

        @Schema(description = "权限编码")
        private String permissionCode;

        @Schema(description = "权限名称")
        private String permissionName;

        @Schema(description = "权限类型：1-菜单，2-按钮，3-接口")
        private Integer permissionType;

        @Schema(description = "权限状态：0-禁用，1-启用")
        private Integer status;

        @Schema(description = "是否系统内置：0-否，1-是")
        private Integer isSystem;

        @Schema(description = "是否显示：0-隐藏，1-显示")
        private Integer isVisible;

        @Schema(description = "创建开始时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createTimeStart;

        @Schema(description = "创建结束时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createTimeEnd;
    }

    /**
     * 权限树DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "权限树DTO")
    public static class PermissionTreeDTO implements Serializable {

        @Schema(description = "权限ID")
        private Long permissionId;

        @Schema(description = "父权限ID")
        private Long parentId;

        @Schema(description = "权限编码")
        private String permissionCode;

        @Schema(description = "权限名称")
        private String permissionName;

        @Schema(description = "权限类型：1-菜单，2-按钮，3-接口")
        private Integer permissionType;

        @Schema(description = "菜单路径")
        private String menuPath;

        @Schema(description = "菜单组件")
        private String menuComponent;

        @Schema(description = "菜单图标")
        private String menuIcon;

        @Schema(description = "权限状态：0-禁用，1-启用")
        private Integer status;

        @Schema(description = "是否显示：0-隐藏，1-显示")
        private Integer isVisible;

        @Schema(description = "排序号")
        private Integer sortOrder;

        @Schema(description = "权限级别")
        private Integer permissionLevel;

        @Schema(description = "子权限列表")
        private List<PermissionTreeDTO> children;
    }

    /**
     * 权限移动DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "权限移动DTO")
    public static class PermissionMoveDTO implements Serializable {

        @Schema(description = "权限ID")
        @NotNull(message = "权限ID不能为空")
        private Long permissionId;

        @Schema(description = "目标父权限ID")
        private Long targetParentId;

        @Schema(description = "目标排序号")
        @Min(value = 0, message = "排序号不能为负数")
        private Integer targetSortOrder;
    }

    /**
     * 权限状态更新DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "权限状态更新DTO")
    public static class PermissionStatusUpdateDTO implements Serializable {

        @Schema(description = "权限ID")
        @NotNull(message = "权限ID不能为空")
        private Long permissionId;

        @Schema(description = "状态：0-禁用，1-启用")
        @NotNull(message = "状态不能为空")
        @Min(value = 0, message = "状态值不正确")
        @Max(value = 1, message = "状态值不正确")
        private Integer status;
    }

    /**
     * 权限复制DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "权限复制DTO")
    public static class PermissionCopyDTO implements Serializable {

        @Schema(description = "源权限ID")
        @NotNull(message = "源权限ID不能为空")
        private Long sourcePermissionId;

        @Schema(description = "目标父权限ID")
        private Long targetParentId;

        @Schema(description = "新权限编码")
        @NotBlank(message = "新权限编码不能为空")
        @Size(min = 2, max = 100, message = "权限编码长度必须在2-100个字符之间")
        @Pattern(regexp = "^[a-zA-Z0-9:_-]+$", message = "权限编码只能包含字母、数字、冒号、下划线和横线")
        private String newPermissionCode;

        @Schema(description = "新权限名称")
        @NotBlank(message = "新权限名称不能为空")
        @Size(min = 2, max = 100, message = "权限名称长度必须在2-100个字符之间")
        private String newPermissionName;

        @Schema(description = "是否复制子权限")
        private Boolean copyChildren = true;
    }

    /**
     * 权限统计DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "权限统计DTO")
    public static class PermissionStatisticsDTO implements Serializable {

        @Schema(description = "总权限数")
        private Long totalPermissions;

        @Schema(description = "菜单权限数")
        private Long menuPermissions;

        @Schema(description = "按钮权限数")
        private Long buttonPermissions;

        @Schema(description = "接口权限数")
        private Long apiPermissions;

        @Schema(description = "启用权限数")
        private Long activePermissions;

        @Schema(description = "禁用权限数")
        private Long inactivePermissions;

        @Schema(description = "系统权限数")
        private Long systemPermissions;

        @Schema(description = "自定义权限数")
        private Long customPermissions;

        @Schema(description = "今日新增权限数")
        private Long todayNewPermissions;

        @Schema(description = "本月新增权限数")
        private Long monthNewPermissions;
    }

    /**
     * 用户权限DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "用户权限DTO")
    public static class UserPermissionDTO implements Serializable {

        @Schema(description = "权限ID")
        private Long permissionId;

        @Schema(description = "权限编码")
        private String permissionCode;

        @Schema(description = "权限名称")
        private String permissionName;

        @Schema(description = "权限类型：1-菜单，2-按钮，3-接口")
        private Integer permissionType;

        @Schema(description = "菜单路径")
        private String menuPath;

        @Schema(description = "菜单组件")
        private String menuComponent;

        @Schema(description = "菜单图标")
        private String menuIcon;

        @Schema(description = "接口URL")
        private String apiUrl;

        @Schema(description = "接口方法")
        private String apiMethod;

        @Schema(description = "是否显示：0-隐藏，1-显示")
        private Integer isVisible;

        @Schema(description = "排序号")
        private Integer sortOrder;

        @Schema(description = "权限来源角色")
        private String sourceRole;
    }

    /**
     * 权限验证DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "权限验证DTO")
    public static class PermissionCheckDTO implements Serializable {

        @Schema(description = "用户ID")
        @NotNull(message = "用户ID不能为空")
        private Long userId;

        @Schema(description = "权限编码")
        @NotBlank(message = "权限编码不能为空")
        private String permissionCode;

        @Schema(description = "接口URL")
        private String apiUrl;

        @Schema(description = "接口方法")
        private String apiMethod;
    }
}