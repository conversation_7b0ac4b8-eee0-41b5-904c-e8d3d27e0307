package com.yinma.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * BOM（物料清单）实体类
 * 支持EBOM（工程BOM）、PBOM（计划BOM）、MBOM（制造BOM）三种类型
 * 
 * <AUTHOR> System
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("bom_master")
public class BomEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * BOM主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * BOM编码（唯一标识）
     */
    @TableField("bom_code")
    private String bomCode;

    /**
     * BOM名称
     */
    @TableField("bom_name")
    private String bomName;

    /**
     * BOM类型：EBOM-工程BOM，PBOM-计划BOM，MBOM-制造BOM
     */
    @TableField("bom_type")
    private String bomType;

    /**
     * 产品编码
     */
    @TableField("product_code")
    private String productCode;

    /**
     * 产品名称
     */
    @TableField("product_name")
    private String productName;

    /**
     * 产品规格型号
     */
    @TableField("product_spec")
    private String productSpec;

    /**
     * BOM版本号
     */
    @TableField("version")
    private String version;

    /**
     * 版本状态：DRAFT-草稿，ACTIVE-生效，INACTIVE-失效
     */
    @TableField("version_status")
    private String versionStatus;

    /**
     * 生效日期
     */
    @TableField("effective_date")
    private LocalDateTime effectiveDate;

    /**
     * 失效日期
     */
    @TableField("expiry_date")
    private LocalDateTime expiryDate;

    /**
     * 父级BOM ID（用于BOM转换关系）
     */
    @TableField("parent_bom_id")
    private Long parentBomId;

    /**
     * 转换来源类型：MANUAL-手工创建，CONVERT-转换生成
     */
    @TableField("source_type")
    private String sourceType;

    /**
     * 转换准确率（%）
     */
    @TableField("conversion_accuracy")
    private BigDecimal conversionAccuracy;

    /**
     * 基本数量（生产该产品的基准数量）
     */
    @TableField("base_quantity")
    private BigDecimal baseQuantity;

    /**
     * 基本单位
     */
    @TableField("base_unit")
    private String baseUnit;

    /**
     * 工艺路线ID
     */
    @TableField("routing_id")
    private Long routingId;

    /**
     * 成本中心
     */
    @TableField("cost_center")
    private String costCenter;

    /**
     * 标准成本
     */
    @TableField("standard_cost")
    private BigDecimal standardCost;

    /**
     * 备注说明
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * BOM明细列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<BomDetailEntity> bomDetails;

    /**
     * 子级BOM列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<BomEntity> childBoms;

    /**
     * 变更历史列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<BomChangeLogEntity> changeLogs;

    /**
     * 是否为叶子节点（非数据库字段）
     */
    @TableField(exist = false)
    private Boolean isLeaf;

    /**
     * BOM层级（非数据库字段）
     */
    @TableField(exist = false)
    private Integer level;

    /**
     * 完整路径（非数据库字段）
     */
    @TableField(exist = false)
    private String fullPath;

    /**
     * 物料需求计算结果（非数据库字段）
     */
    @TableField(exist = false)
    private List<MaterialRequirementEntity> materialRequirements;

    /**
     * BOM类型枚举
     */
    public enum BomType {
        EBOM("EBOM", "工程BOM"),
        PBOM("PBOM", "计划BOM"),
        MBOM("MBOM", "制造BOM");

        private final String code;
        private final String name;

        BomType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * 版本状态枚举
     */
    public enum VersionStatus {
        DRAFT("DRAFT", "草稿"),
        ACTIVE("ACTIVE", "生效"),
        INACTIVE("INACTIVE", "失效");

        private final String code;
        private final String name;

        VersionStatus(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * 转换来源类型枚举
     */
    public enum SourceType {
        MANUAL("MANUAL", "手工创建"),
        CONVERT("CONVERT", "转换生成");

        private final String code;
        private final String name;

        SourceType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }
}