package com.yinma.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinma.dto.BomDTO;
import com.yinma.entity.BomEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * BOM管理Mapper接口
 * 提供BOM相关的数据库操作
 * 
 * <AUTHOR> System
 * @since 2024-01-15
 */
@Repository
@Mapper
public interface BomMapper extends BaseMapper<BomEntity> {

    /**
     * 分页查询BOM列表（含关联信息）
     * 
     * @param page 分页参数
     * @param queryParams 查询条件
     * @return BOM分页列表
     */
    @Select({
        "<script>",
        "SELECT b.*, ",
        "       m.material_name as product_name,",
        "       m.material_spec as product_spec,",
        "       (SELECT COUNT(*) FROM bom_detail bd WHERE bd.bom_id = b.id AND bd.deleted = 0) as detail_count,",
        "       (SELECT SUM(bd.total_amount) FROM bom_detail bd WHERE bd.bom_id = b.id AND bd.deleted = 0) as total_cost",
        "FROM bom_master b",
        "LEFT JOIN material_master m ON b.product_code = m.material_code AND m.deleted = 0",
        "WHERE b.deleted = 0",
        "<if test='queryParams.bomCode != null and queryParams.bomCode != \"\"'>",
        "  AND b.bom_code LIKE CONCAT('%', #{queryParams.bomCode}, '%')",
        "</if>",
        "<if test='queryParams.bomName != null and queryParams.bomName != \"\"'>",
        "  AND b.bom_name LIKE CONCAT('%', #{queryParams.bomName}, '%')",
        "</if>",
        "<if test='queryParams.bomType != null and queryParams.bomType != \"\"'>",
        "  AND b.bom_type = #{queryParams.bomType}",
        "</if>",
        "<if test='queryParams.productCode != null and queryParams.productCode != \"\"'>",
        "  AND b.product_code LIKE CONCAT('%', #{queryParams.productCode}, '%')",
        "</if>",
        "<if test='queryParams.productName != null and queryParams.productName != \"\"'>",
        "  AND m.material_name LIKE CONCAT('%', #{queryParams.productName}, '%')",
        "</if>",
        "<if test='queryParams.versionStatus != null and queryParams.versionStatus != \"\"'>",
        "  AND b.version_status = #{queryParams.versionStatus}",
        "</if>",
        "<if test='queryParams.approvalStatus != null and queryParams.approvalStatus != \"\"'>",
        "  AND b.approval_status = #{queryParams.approvalStatus}",
        "</if>",
        "<if test='queryParams.isMainBom != null'>",
        "  AND b.is_main_bom = #{queryParams.isMainBom}",
        "</if>",
        "<if test='queryParams.effectiveDateStart != null'>",
        "  AND b.effective_date >= #{queryParams.effectiveDateStart}",
        "</if>",
        "<if test='queryParams.effectiveDateEnd != null'>",
        "  AND b.effective_date <= #{queryParams.effectiveDateEnd}",
        "</if>",
        "<if test='queryParams.createBy != null and queryParams.createBy != \"\"'>",
        "  AND b.create_by = #{queryParams.createBy}",
        "</if>",
        "<if test='queryParams.createTimeStart != null'>",
        "  AND b.create_time >= #{queryParams.createTimeStart}",
        "</if>",
        "<if test='queryParams.createTimeEnd != null'>",
        "  AND b.create_time <= #{queryParams.createTimeEnd}",
        "</if>",
        "ORDER BY ",
        "<if test='queryParams.orderBy != null and queryParams.orderBy != \"\"'>",
        "  ${queryParams.orderBy} ${queryParams.orderDirection}",
        "</if>",
        "<if test='queryParams.orderBy == null or queryParams.orderBy == \"\"'>",
        "  b.create_time DESC",
        "</if>",
        "</script>"
    })
    IPage<BomEntity> selectBomPageWithDetails(Page<BomEntity> page, @Param("queryParams") BomDTO.BomQueryDTO queryParams);

    /**
     * 根据产品编码查询主BOM
     * 
     * @param productCode 产品编码
     * @return 主BOM信息
     */
    @Select("SELECT * FROM bom_master WHERE product_code = #{productCode} AND is_main_bom = 1 AND version_status = 'ACTIVE' AND deleted = 0 ORDER BY version DESC LIMIT 1")
    BomEntity selectMainBomByProductCode(@Param("productCode") String productCode);

    /**
     * 根据产品编码和BOM类型查询BOM列表
     * 
     * @param productCode 产品编码
     * @param bomType BOM类型
     * @return BOM列表
     */
    @Select("SELECT * FROM bom_master WHERE product_code = #{productCode} AND bom_type = #{bomType} AND deleted = 0 ORDER BY version DESC")
    List<BomEntity> selectBomsByProductCodeAndType(@Param("productCode") String productCode, @Param("bomType") String bomType);

    /**
     * 查询BOM版本历史
     * 
     * @param productCode 产品编码
     * @param bomType BOM类型
     * @return BOM版本列表
     */
    @Select({
        "SELECT b.*, ",
        "       m.material_name as product_name,",
        "       m.material_spec as product_spec",
        "FROM bom_master b",
        "LEFT JOIN material_master m ON b.product_code = m.material_code AND m.deleted = 0",
        "WHERE b.product_code = #{productCode} AND b.bom_type = #{bomType} AND b.deleted = 0",
        "ORDER BY b.version DESC, b.create_time DESC"
    })
    List<BomEntity> selectBomVersionHistory(@Param("productCode") String productCode, @Param("bomType") String bomType);

    /**
     * 查询可转换的源BOM列表
     * 
     * @param targetBomType 目标BOM类型
     * @return 可转换的BOM列表
     */
    @Select({
        "<script>",
        "SELECT b.*, m.material_name as product_name, m.material_spec as product_spec",
        "FROM bom_master b",
        "LEFT JOIN material_master m ON b.product_code = m.material_code AND m.deleted = 0",
        "WHERE b.deleted = 0 AND b.version_status = 'ACTIVE' AND b.approval_status = 'APPROVED'",
        "<if test='targetBomType == \"PBOM\"'>",
        "  AND b.bom_type = 'EBOM'",
        "</if>",
        "<if test='targetBomType == \"MBOM\"'>",
        "  AND b.bom_type IN ('EBOM', 'PBOM')",
        "</if>",
        "ORDER BY b.product_code, b.version DESC",
        "</script>"
    })
    List<BomEntity> selectConvertibleBoms(@Param("targetBomType") String targetBomType);

    /**
     * 查询BOM的下级使用情况
     * 
     * @param productCode 产品编码
     * @return 使用此产品作为物料的BOM列表
     */
    @Select({
        "SELECT DISTINCT b.*, m.material_name as product_name",
        "FROM bom_master b",
        "INNER JOIN bom_detail bd ON b.id = bd.bom_id AND bd.deleted = 0",
        "LEFT JOIN material_master m ON b.product_code = m.material_code AND m.deleted = 0",
        "WHERE bd.material_code = #{productCode} AND b.deleted = 0",
        "ORDER BY b.bom_code"
    })
    List<BomEntity> selectBomUsageByMaterial(@Param("productCode") String productCode);

    /**
     * 查询BOM成本汇总信息
     * 
     * @param bomId BOM ID
     * @return 成本汇总信息
     */
    @Select({
        "SELECT ",
        "  COUNT(bd.id) as material_count,",
        "  SUM(bd.total_amount) as total_material_cost,",
        "  SUM(CASE WHEN m.material_type = 'RAW' THEN bd.total_amount ELSE 0 END) as raw_material_cost,",
        "  SUM(CASE WHEN m.material_type = 'PURCHASE' THEN bd.total_amount ELSE 0 END) as purchase_cost,",
        "  SUM(CASE WHEN m.material_type = 'SEMI' THEN bd.total_amount ELSE 0 END) as semi_finished_cost",
        "FROM bom_detail bd",
        "LEFT JOIN material_master m ON bd.material_code = m.material_code AND m.deleted = 0",
        "WHERE bd.bom_id = #{bomId} AND bd.deleted = 0"
    })
    Map<String, Object> selectBomCostSummary(@Param("bomId") Long bomId);

    /**
     * 查询BOM物料需求汇总
     * 
     * @param bomId BOM ID
     * @param includeSubBom 是否包含子BOM
     * @return 物料需求汇总
     */
    @Select({
        "<script>",
        "WITH RECURSIVE bom_tree AS (",
        "  SELECT bd.*, 1 as level, CAST(bd.id AS CHAR(1000)) as path",
        "  FROM bom_detail bd",
        "  WHERE bd.bom_id = #{bomId} AND bd.deleted = 0",
        "  UNION ALL",
        "  SELECT bd2.*, bt.level + 1, CONCAT(bt.path, '->', bd2.id)",
        "  FROM bom_detail bd2",
        "  INNER JOIN bom_tree bt ON bd2.bom_id = (",
        "    SELECT b.id FROM bom_master b ",
        "    WHERE b.product_code = bt.material_code AND b.is_main_bom = 1 ",
        "    AND b.version_status = 'ACTIVE' AND b.deleted = 0",
        "  )",
        "  WHERE bd2.deleted = 0",
        "  <if test='!includeSubBom'>",
        "    AND 1 = 0",
        "  </if>",
        ")",
        "SELECT ",
        "  bt.material_code,",
        "  m.material_name,",
        "  m.material_spec,",
        "  m.material_type,",
        "  m.base_unit,",
        "  SUM(bt.actual_qty) as total_required_qty,",
        "  AVG(bt.unit_price) as avg_unit_price,",
        "  SUM(bt.total_amount) as total_amount,",
        "  m.main_supplier_name as supplier,",
        "  m.lead_time",
        "FROM bom_tree bt",
        "LEFT JOIN material_master m ON bt.material_code = m.material_code AND m.deleted = 0",
        "GROUP BY bt.material_code, m.material_name, m.material_spec, m.material_type, m.base_unit, m.main_supplier_name, m.lead_time",
        "ORDER BY total_amount DESC",
        "</script>"
    })
    List<Map<String, Object>> selectBomMaterialRequirements(@Param("bomId") Long bomId, @Param("includeSubBom") Boolean includeSubBom);

    /**
     * 检查BOM编码是否存在
     * 
     * @param bomCode BOM编码
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 存在数量
     */
    @Select({
        "<script>",
        "SELECT COUNT(*) FROM bom_master ",
        "WHERE bom_code = #{bomCode} AND deleted = 0",
        "<if test='excludeId != null'>",
        "  AND id != #{excludeId}",
        "</if>",
        "</script>"
    })
    int checkBomCodeExists(@Param("bomCode") String bomCode, @Param("excludeId") Long excludeId);

    /**
     * 检查产品是否已有主BOM
     * 
     * @param productCode 产品编码
     * @param bomType BOM类型
     * @param excludeId 排除的ID
     * @return 存在数量
     */
    @Select({
        "<script>",
        "SELECT COUNT(*) FROM bom_master ",
        "WHERE product_code = #{productCode} AND bom_type = #{bomType} ",
        "AND is_main_bom = 1 AND version_status = 'ACTIVE' AND deleted = 0",
        "<if test='excludeId != null'>",
        "  AND id != #{excludeId}",
        "</if>",
        "</script>"
    })
    int checkMainBomExists(@Param("productCode") String productCode, @Param("bomType") String bomType, @Param("excludeId") Long excludeId);

    /**
     * 批量更新BOM状态
     * 
     * @param bomIds BOM ID列表
     * @param status 新状态
     * @param updateBy 更新人
     * @param updateTime 更新时间
     * @return 更新数量
     */
    @Update({
        "<script>",
        "UPDATE bom_master SET ",
        "version_status = #{status},",
        "update_by = #{updateBy},",
        "update_time = #{updateTime}",
        "WHERE id IN ",
        "<foreach collection='bomIds' item='id' open='(' separator=',' close=')'>",
        "  #{id}",
        "</foreach>",
        "AND deleted = 0",
        "</script>"
    })
    int batchUpdateBomStatus(@Param("bomIds") List<Long> bomIds, 
                            @Param("status") String status, 
                            @Param("updateBy") String updateBy, 
                            @Param("updateTime") LocalDateTime updateTime);

    /**
     * 批量更新BOM审批状态
     * 
     * @param bomIds BOM ID列表
     * @param approvalStatus 审批状态
     * @param approvedBy 审批人
     * @param approvedTime 审批时间
     * @param updateBy 更新人
     * @param updateTime 更新时间
     * @return 更新数量
     */
    @Update({
        "<script>",
        "UPDATE bom_master SET ",
        "approval_status = #{approvalStatus},",
        "approved_by = #{approvedBy},",
        "approved_time = #{approvedTime},",
        "update_by = #{updateBy},",
        "update_time = #{updateTime}",
        "WHERE id IN ",
        "<foreach collection='bomIds' item='id' open='(' separator=',' close=')'>",
        "  #{id}",
        "</foreach>",
        "AND deleted = 0",
        "</script>"
    })
    int batchUpdateBomApprovalStatus(@Param("bomIds") List<Long> bomIds, 
                                    @Param("approvalStatus") String approvalStatus,
                                    @Param("approvedBy") String approvedBy,
                                    @Param("approvedTime") LocalDateTime approvedTime,
                                    @Param("updateBy") String updateBy, 
                                    @Param("updateTime") LocalDateTime updateTime);

    /**
     * 查询BOM统计信息
     * 
     * @return 统计信息
     */
    @Select({
        "SELECT ",
        "  COUNT(*) as total_count,",
        "  SUM(CASE WHEN bom_type = 'EBOM' THEN 1 ELSE 0 END) as ebom_count,",
        "  SUM(CASE WHEN bom_type = 'PBOM' THEN 1 ELSE 0 END) as pbom_count,",
        "  SUM(CASE WHEN bom_type = 'MBOM' THEN 1 ELSE 0 END) as mbom_count,",
        "  SUM(CASE WHEN version_status = 'ACTIVE' THEN 1 ELSE 0 END) as active_count,",
        "  SUM(CASE WHEN version_status = 'DRAFT' THEN 1 ELSE 0 END) as draft_count,",
        "  SUM(CASE WHEN approval_status = 'PENDING' THEN 1 ELSE 0 END) as pending_approval_count,",
        "  SUM(CASE WHEN approval_status = 'APPROVED' THEN 1 ELSE 0 END) as approved_count",
        "FROM bom_master WHERE deleted = 0"
    })
    Map<String, Object> selectBomStatistics();

    /**
     * 查询最近创建的BOM列表
     * 
     * @param limit 限制数量
     * @return 最近创建的BOM列表
     */
    @Select({
        "SELECT b.*, m.material_name as product_name",
        "FROM bom_master b",
        "LEFT JOIN material_master m ON b.product_code = m.material_code AND m.deleted = 0",
        "WHERE b.deleted = 0",
        "ORDER BY b.create_time DESC",
        "LIMIT #{limit}"
    })
    List<BomEntity> selectRecentBoms(@Param("limit") Integer limit);

    /**
     * 查询待审批的BOM列表
     * 
     * @param limit 限制数量
     * @return 待审批的BOM列表
     */
    @Select({
        "SELECT b.*, m.material_name as product_name",
        "FROM bom_master b",
        "LEFT JOIN material_master m ON b.product_code = m.material_code AND m.deleted = 0",
        "WHERE b.deleted = 0 AND b.approval_status = 'PENDING'",
        "ORDER BY b.create_time ASC",
        "LIMIT #{limit}"
    })
    List<BomEntity> selectPendingApprovalBoms(@Param("limit") Integer limit);
}