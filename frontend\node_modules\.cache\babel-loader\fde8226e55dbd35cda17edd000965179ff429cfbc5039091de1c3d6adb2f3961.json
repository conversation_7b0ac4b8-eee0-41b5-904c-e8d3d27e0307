{"ast": null, "code": "function debounce(func, wait, immediate) {\n  var timeout;\n  return function () {\n    var context = this,\n      args = arguments;\n    var later = function () {\n      timeout = null;\n      if (!immediate) {\n        func.apply(context, args);\n      }\n    };\n    var callNow = immediate && !timeout;\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n    if (callNow) {\n      func.apply(context, args);\n    }\n  };\n}\nexport default debounce;", "map": {"version": 3, "names": ["debounce", "func", "wait", "immediate", "timeout", "context", "args", "arguments", "later", "apply", "callNow", "clearTimeout", "setTimeout"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\util\\src\\lodash\\debounce.ts"], "sourcesContent": ["function debounce(func: Function, wait?: number, immediate?: boolean) {\n  let timeout;\n  return function () {\n    const context = this,\n      args = arguments;\n    const later = function () {\n      timeout = null;\n      if (!immediate) {\n        func.apply(context, args);\n      }\n    };\n    const callNow = immediate && !timeout;\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n    if (callNow) {\n      func.apply(context, args);\n    }\n  };\n}\n\nexport default debounce;\n"], "mappings": "AAAA,SAASA,QAAQA,CAACC,IAAc,EAAEC,IAAa,EAAEC,SAAmB;EAClE,IAAIC,OAAO;EACX,OAAO;IACL,IAAMC,OAAO,GAAG,IAAI;MAClBC,IAAI,GAAGC,SAAS;IAClB,IAAMC,KAAK,GAAG,SAAAA,CAAA;MACZJ,OAAO,GAAG,IAAI;MACd,IAAI,CAACD,SAAS,EAAE;QACdF,IAAI,CAACQ,KAAK,CAACJ,OAAO,EAAEC,IAAI,CAAC;MAC3B;IACF,CAAC;IACD,IAAMI,OAAO,GAAGP,SAAS,IAAI,CAACC,OAAO;IACrCO,YAAY,CAACP,OAAO,CAAC;IACrBA,OAAO,GAAGQ,UAAU,CAACJ,KAAK,EAAEN,IAAI,CAAC;IACjC,IAAIQ,OAAO,EAAE;MACXT,IAAI,CAACQ,KAAK,CAACJ,OAAO,EAAEC,IAAI,CAAC;IAC3B;EACF,CAAC;AACH;AAEA,eAAeN,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}