package com.yinma.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yinma.entity.Maintenance;
import com.yinma.entity.MaintenancePlan;
import com.yinma.entity.MaintenanceWorkItem;
import com.yinma.entity.MaintenancePart;
import com.yinma.dto.MaintenanceDTO;
import com.yinma.dto.MaintenanceStatisticsDTO;
import com.yinma.vo.MaintenanceVO;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 维护管理服务接口
 * 银马实业设备维护保养管理系统
 * 
 * <AUTHOR>
 * @since 2024-05-20
 */
public interface MaintenanceService extends IService<Maintenance> {

    /**
     * 分页查询维护记录
     * 
     * @param page 分页参数
     * @param workOrderNo 工单号
     * @param equipmentName 设备名称
     * @param equipmentId 设备ID
     * @param customer 客户名称
     * @param maintenanceType 维护类型
     * @param status 状态
     * @param priority 优先级
     * @param technicianId 技师ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 分页结果
     */
    IPage<MaintenanceVO> getMaintenancePage(Page<Maintenance> page, String workOrderNo, 
                                          String equipmentName, Long equipmentId, String customer,
                                          String maintenanceType, String status, String priority,
                                          Long technicianId, LocalDate startDate, LocalDate endDate);

    /**
     * 根据ID查询维护记录详情
     * 
     * @param id 维护记录ID
     * @return 维护记录详情
     */
    MaintenanceVO getMaintenanceDetailById(Long id);

    /**
     * 创建维护工单
     * 
     * @param maintenanceDTO 维护工单信息
     * @return 创建的维护记录
     */
    Maintenance createMaintenance(MaintenanceDTO maintenanceDTO);

    /**
     * 更新维护工单
     * 
     * @param id 维护记录ID
     * @param maintenanceDTO 维护工单信息
     * @return 更新后的维护记录
     */
    Maintenance updateMaintenance(Long id, MaintenanceDTO maintenanceDTO);

    /**
     * 更新维护状态
     * 
     * @param id 维护记录ID
     * @param status 新状态
     * @param remark 备注
     * @return 更新后的维护记录
     */
    Maintenance updateMaintenanceStatus(Long id, String status, String remark);

    /**
     * 开始维护工作
     * 
     * @param id 维护记录ID
     * @return 更新后的维护记录
     */
    Maintenance startMaintenance(Long id);

    /**
     * 完成维护工作
     * 
     * @param id 维护记录ID
     * @param completeInfo 完成信息
     * @return 更新后的维护记录
     */
    Maintenance completeMaintenance(Long id, Map<String, Object> completeInfo);

    /**
     * 查询维护工作项
     * 
     * @param maintenanceId 维护记录ID
     * @return 工作项列表
     */
    List<MaintenanceWorkItem> getMaintenanceWorkItems(Long maintenanceId);

    /**
     * 更新工作项状态
     * 
     * @param itemId 工作项ID
     * @param status 状态
     * @param duration 耗时
     * @param remark 备注
     * @return 更新后的工作项
     */
    MaintenanceWorkItem updateWorkItemStatus(Long itemId, String status, Double duration, String remark);

    /**
     * 查询使用配件
     * 
     * @param maintenanceId 维护记录ID
     * @return 配件列表
     */
    List<MaintenancePart> getMaintenanceParts(Long maintenanceId);

    /**
     * 添加使用配件
     * 
     * @param part 配件信息
     * @return 保存的配件记录
     */
    MaintenancePart addMaintenancePart(MaintenancePart part);

    /**
     * 查询维护计划
     * 
     * @param equipmentId 设备ID
     * @param customer 客户名称
     * @param overdue 是否逾期
     * @param dueDays 即将到期天数
     * @return 维护计划列表
     */
    List<MaintenancePlan> getMaintenancePlans(Long equipmentId, String customer, Boolean overdue, Integer dueDays);

    /**
     * 创建维护计划
     * 
     * @param plan 维护计划信息
     * @return 保存的维护计划
     */
    MaintenancePlan createMaintenancePlan(MaintenancePlan plan);

    /**
     * 根据计划创建维护工单
     * 
     * @param planId 计划ID
     * @param technicianId 技师ID
     * @param plannedDate 计划日期
     * @return 创建的维护工单
     */
    Maintenance createMaintenanceFromPlan(Long planId, Long technicianId, LocalDate plannedDate);

    /**
     * 延期维护计划
     * 
     * @param planId 计划ID
     * @param newDate 新的计划日期
     * @param reason 延期原因
     * @return 更新后的维护计划
     */
    MaintenancePlan postponeMaintenancePlan(Long planId, LocalDate newDate, String reason);

    /**
     * 获取维护统计数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param equipmentId 设备ID
     * @param customer 客户名称
     * @return 统计数据
     */
    MaintenanceStatisticsDTO getMaintenanceStatistics(LocalDate startDate, LocalDate endDate, 
                                                     Long equipmentId, String customer);

    /**
     * 获取维护趋势数据
     * 
     * @param type 统计类型(daily/weekly/monthly/yearly)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 趋势数据
     */
    Map<String, Object> getMaintenanceTrends(String type, LocalDate startDate, LocalDate endDate);

    /**
     * 上传维护附件
     * 
     * @param maintenanceId 维护记录ID
     * @param file 附件文件
     * @return 文件URL
     */
    String uploadMaintenanceAttachment(Long maintenanceId, MultipartFile file);

    /**
     * 导出维护报告
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param equipmentId 设备ID
     * @param customer 客户名称
     * @param format 报告格式
     * @return 报告文件URL
     */
    String exportMaintenanceReport(LocalDate startDate, LocalDate endDate, 
                                 Long equipmentId, String customer, String format);

    /**
     * 获取技师工作负载
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 技师工作负载数据
     */
    List<Map<String, Object>> getTechnicianWorkload(LocalDate startDate, LocalDate endDate);

    /**
     * 获取设备维护历史
     * 
     * @param equipmentId 设备ID
     * @param page 分页参数
     * @return 维护历史列表
     */
    List<MaintenanceVO> getEquipmentMaintenanceHistory(Long equipmentId, Page<Maintenance> page);

    /**
     * 客户评价维护服务
     * 
     * @param id 维护记录ID
     * @param rating 评分
     * @param feedback 反馈意见
     * @return 更新后的维护记录
     */
    Maintenance rateMaintenance(Long id, Integer rating, String feedback);

    /**
     * 生成工单号
     * 
     * @return 工单号
     */
    String generateWorkOrderNo();

    /**
     * 检查设备是否需要维护
     * 
     * @param equipmentId 设备ID
     * @return 是否需要维护
     */
    boolean checkEquipmentMaintenanceNeeded(Long equipmentId);

    /**
     * 自动创建预防性维护计划
     * 
     * @param equipmentId 设备ID
     * @return 创建的计划数量
     */
    int autoCreatePreventiveMaintenancePlans(Long equipmentId);

    /**
     * 发送维护提醒
     * 
     * @param maintenanceId 维护记录ID
     * @param reminderType 提醒类型
     * @return 是否发送成功
     */
    boolean sendMaintenanceReminder(Long maintenanceId, String reminderType);

    /**
     * 获取维护成本分析
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param equipmentId 设备ID
     * @return 成本分析数据
     */
    Map<String, Object> getMaintenanceCostAnalysis(LocalDate startDate, LocalDate endDate, Long equipmentId);

    /**
     * 获取维护效率分析
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param technicianId 技师ID
     * @return 效率分析数据
     */
    Map<String, Object> getMaintenanceEfficiencyAnalysis(LocalDate startDate, LocalDate endDate, Long technicianId);

    /**
     * 预测设备故障
     * 
     * @param equipmentId 设备ID
     * @return 故障预测结果
     */
    Map<String, Object> predictEquipmentFailure(Long equipmentId);

    /**
     * 优化维护计划
     * 
     * @param equipmentId 设备ID
     * @return 优化建议
     */
    Map<String, Object> optimizeMaintenanceSchedule(Long equipmentId);

    /**
     * 获取维护知识库
     * 
     * @param equipmentModel 设备型号
     * @param problemType 问题类型
     * @return 知识库条目
     */
    List<Map<String, Object>> getMaintenanceKnowledgeBase(String equipmentModel, String problemType);

    /**
     * 记录维护经验
     * 
     * @param maintenanceId 维护记录ID
     * @param experience 经验内容
     * @return 是否记录成功
     */
    boolean recordMaintenanceExperience(Long maintenanceId, Map<String, Object> experience);

    /**
     * 生成维护报告
     * 
     * @param maintenanceId 维护记录ID
     * @return 报告内容
     */
    Map<String, Object> generateMaintenanceReport(Long maintenanceId);

    /**
     * 同步远程设备状态
     * 
     * @param equipmentId 设备ID
     * @return 同步结果
     */
    Map<String, Object> syncRemoteEquipmentStatus(Long equipmentId);

    /**
     * 远程诊断设备
     * 
     * @param equipmentId 设备ID
     * @param diagnosticType 诊断类型
     * @return 诊断结果
     */
    Map<String, Object> remoteDiagnoseEquipment(Long equipmentId, String diagnosticType);

    /**
     * 获取维护质量评估
     * 
     * @param technicianId 技师ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 质量评估数据
     */
    Map<String, Object> getMaintenanceQualityAssessment(Long technicianId, LocalDate startDate, LocalDate endDate);

    /**
     * 计算设备可靠性指标
     * 
     * @param equipmentId 设备ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 可靠性指标
     */
    Map<String, Object> calculateEquipmentReliabilityMetrics(Long equipmentId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取备件需求预测
     * 
     * @param equipmentId 设备ID
     * @param forecastPeriod 预测周期(月)
     * @return 备件需求预测
     */
    Map<String, Object> getSparePartsDemandForecast(Long equipmentId, Integer forecastPeriod);

    /**
     * 优化技师调度
     * 
     * @param date 调度日期
     * @param region 区域
     * @return 调度优化方案
     */
    Map<String, Object> optimizeTechnicianScheduling(LocalDate date, String region);
}