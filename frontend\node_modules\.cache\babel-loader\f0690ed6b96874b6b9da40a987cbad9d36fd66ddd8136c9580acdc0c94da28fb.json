{"ast": null, "code": "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\nexport var scheme = new Array(3).concat(\"fee0d2fc9272de2d26\", \"fee5d9fcae91fb6a4acb181d\", \"fee5d9fcae91fb6a4ade2d26a50f15\", \"fee5d9fcbba1fc9272fb6a4ade2d26a50f15\", \"fee5d9fcbba1fc9272fb6a4aef3b2ccb181d99000d\", \"fff5f0fee0d2fcbba1fc9272fb6a4aef3b2ccb181d99000d\", \"fff5f0fee0d2fcbba1fc9272fb6a4aef3b2ccb181da50f1567000d\").map(colors);\nexport default ramp(scheme);", "map": {"version": 3, "names": ["colors", "ramp", "scheme", "Array", "concat", "map"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/d3-scale-chromatic/src/sequential-single/Reds.js"], "sourcesContent": ["import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"fee0d2fc9272de2d26\",\n  \"fee5d9fcae91fb6a4acb181d\",\n  \"fee5d9fcae91fb6a4ade2d26a50f15\",\n  \"fee5d9fcbba1fc9272fb6a4ade2d26a50f15\",\n  \"fee5d9fcbba1fc9272fb6a4aef3b2ccb181d99000d\",\n  \"fff5f0fee0d2fcbba1fc9272fb6a4aef3b2ccb181d99000d\",\n  \"fff5f0fee0d2fcbba1fc9272fb6a4aef3b2ccb181da50f1567000d\"\n).map(colors);\n\nexport default ramp(scheme);\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAE7B,OAAO,IAAIC,MAAM,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CACrC,oBAAoB,EACpB,0BAA0B,EAC1B,gCAAgC,EAChC,sCAAsC,EACtC,4CAA4C,EAC5C,kDAAkD,EAClD,wDACF,CAAC,CAACC,GAAG,CAACL,MAAM,CAAC;AAEb,eAAeC,IAAI,CAACC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}