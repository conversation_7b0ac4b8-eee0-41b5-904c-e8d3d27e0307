package com.yinma.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 角色信息DTO
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
@Data
@Accessors(chain = true)
@Schema(description = "角色信息DTO")
public class RoleDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "角色ID")
    private Long roleId;

    @Schema(description = "角色编码")
    @NotBlank(message = "角色编码不能为空")
    @Size(min = 2, max = 50, message = "角色编码长度必须在2-50个字符之间")
    @Pattern(regexp = "^[A-Z_]+$", message = "角色编码只能包含大写字母和下划线")
    private String roleCode;

    @Schema(description = "角色名称")
    @NotBlank(message = "角色名称不能为空")
    @Size(min = 2, max = 100, message = "角色名称长度必须在2-100个字符之间")
    private String roleName;

    @Schema(description = "角色描述")
    @Size(max = 500, message = "角色描述长度不能超过500个字符")
    private String roleDesc;

    @Schema(description = "角色类型：1-系统角色，2-业务角色")
    @NotNull(message = "角色类型不能为空")
    @Min(value = 1, message = "角色类型值不正确")
    @Max(value = 2, message = "角色类型值不正确")
    private Integer roleType;

    @Schema(description = "角色级别")
    @Min(value = 1, message = "角色级别必须大于0")
    private Integer roleLevel;

    @Schema(description = "数据权限范围：1-全部数据，2-部门数据，3-个人数据")
    @NotNull(message = "数据权限范围不能为空")
    @Min(value = 1, message = "数据权限范围值不正确")
    @Max(value = 3, message = "数据权限范围值不正确")
    private Integer dataScope;

    @Schema(description = "角色状态：0-禁用，1-启用")
    private Integer status;

    @Schema(description = "是否系统内置：0-否，1-是")
    private Integer isSystem;

    @Schema(description = "排序号")
    @Min(value = 0, message = "排序号不能为负数")
    private Integer sortOrder;

    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    @Schema(description = "权限列表")
    private List<PermissionDTO> permissions;

    @Schema(description = "用户数量")
    private Long userCount;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 角色查询DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "角色查询DTO")
    public static class RoleQueryDTO implements Serializable {

        @Schema(description = "页码")
        @Min(value = 1, message = "页码必须大于0")
        private Integer current = 1;

        @Schema(description = "每页大小")
        @Min(value = 1, message = "每页大小必须大于0")
        @Max(value = 100, message = "每页大小不能超过100")
        private Integer size = 10;

        @Schema(description = "角色编码")
        private String roleCode;

        @Schema(description = "角色名称")
        private String roleName;

        @Schema(description = "角色类型：1-系统角色，2-业务角色")
        private Integer roleType;

        @Schema(description = "数据权限范围：1-全部数据，2-部门数据，3-个人数据")
        private Integer dataScope;

        @Schema(description = "角色状态：0-禁用，1-启用")
        private Integer status;

        @Schema(description = "是否系统内置：0-否，1-是")
        private Integer isSystem;

        @Schema(description = "创建开始时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createTimeStart;

        @Schema(description = "创建结束时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createTimeEnd;
    }

    /**
     * 角色权限分配DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "角色权限分配DTO")
    public static class RolePermissionAssignDTO implements Serializable {

        @Schema(description = "角色ID")
        @NotNull(message = "角色ID不能为空")
        private Long roleId;

        @Schema(description = "权限ID列表")
        @NotEmpty(message = "权限ID列表不能为空")
        private List<Long> permissionIds;
    }

    /**
     * 角色状态更新DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "角色状态更新DTO")
    public static class RoleStatusUpdateDTO implements Serializable {

        @Schema(description = "角色ID")
        @NotNull(message = "角色ID不能为空")
        private Long roleId;

        @Schema(description = "状态：0-禁用，1-启用")
        @NotNull(message = "状态不能为空")
        @Min(value = 0, message = "状态值不正确")
        @Max(value = 1, message = "状态值不正确")
        private Integer status;
    }

    /**
     * 角色复制DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "角色复制DTO")
    public static class RoleCopyDTO implements Serializable {

        @Schema(description = "源角色ID")
        @NotNull(message = "源角色ID不能为空")
        private Long sourceRoleId;

        @Schema(description = "新角色编码")
        @NotBlank(message = "新角色编码不能为空")
        @Size(min = 2, max = 50, message = "角色编码长度必须在2-50个字符之间")
        @Pattern(regexp = "^[A-Z_]+$", message = "角色编码只能包含大写字母和下划线")
        private String newRoleCode;

        @Schema(description = "新角色名称")
        @NotBlank(message = "新角色名称不能为空")
        @Size(min = 2, max = 100, message = "角色名称长度必须在2-100个字符之间")
        private String newRoleName;

        @Schema(description = "新角色描述")
        @Size(max = 500, message = "角色描述长度不能超过500个字符")
        private String newRoleDesc;

        @Schema(description = "是否复制权限")
        private Boolean copyPermissions = true;
    }

    /**
     * 角色统计DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "角色统计DTO")
    public static class RoleStatisticsDTO implements Serializable {

        @Schema(description = "总角色数")
        private Long totalRoles;

        @Schema(description = "系统角色数")
        private Long systemRoles;

        @Schema(description = "业务角色数")
        private Long businessRoles;

        @Schema(description = "启用角色数")
        private Long activeRoles;

        @Schema(description = "禁用角色数")
        private Long inactiveRoles;

        @Schema(description = "今日新增角色数")
        private Long todayNewRoles;

        @Schema(description = "本月新增角色数")
        private Long monthNewRoles;
    }

    /**
     * 角色用户DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "角色用户DTO")
    public static class RoleUserDTO implements Serializable {

        @Schema(description = "用户ID")
        private Long userId;

        @Schema(description = "用户名")
        private String username;

        @Schema(description = "真实姓名")
        private String realName;

        @Schema(description = "邮箱")
        private String email;

        @Schema(description = "手机号")
        private String phone;

        @Schema(description = "部门名称")
        private String deptName;

        @Schema(description = "职位")
        private String position;

        @Schema(description = "用户状态：0-禁用，1-启用")
        private Integer status;

        @Schema(description = "分配时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime assignTime;
    }

    /**
     * 角色权限树DTO
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "角色权限树DTO")
    public static class RolePermissionTreeDTO implements Serializable {

        @Schema(description = "权限ID")
        private Long permissionId;

        @Schema(description = "父权限ID")
        private Long parentId;

        @Schema(description = "权限编码")
        private String permissionCode;

        @Schema(description = "权限名称")
        private String permissionName;

        @Schema(description = "权限类型：1-菜单，2-按钮，3-接口")
        private Integer permissionType;

        @Schema(description = "是否选中")
        private Boolean checked = false;

        @Schema(description = "是否半选中")
        private Boolean indeterminate = false;

        @Schema(description = "子权限列表")
        private List<RolePermissionTreeDTO> children;
    }
}