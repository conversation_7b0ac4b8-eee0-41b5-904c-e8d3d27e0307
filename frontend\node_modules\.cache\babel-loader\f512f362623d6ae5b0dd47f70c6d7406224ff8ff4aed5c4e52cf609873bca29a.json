{"ast": null, "code": "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { Marker } from '@antv/component';\nimport { createElement } from '../../utils/createElement';\nimport { subObject } from '../../utils/helper';\nimport { select } from '../../utils/selection';\nimport { applyStyle } from '../../shape/utils';\n/**\n * Get the path to draw a built-in badge, which is like a balloon.\n */\nfunction getPath(r) {\n  const offset = r / Math.sqrt(2);\n  const dy = r * Math.sqrt(2);\n  const [p0x, p0y] = [-offset, offset - dy];\n  const [p1x, p1y] = [0, 0];\n  const [p2x, p2y] = [offset, offset - dy];\n  return [['M', p0x, p0y], ['A', r, r, 0, 1, 1, p2x, p2y], ['L', p1x, p1y], ['Z']];\n}\nfunction inferTextPosition(shape) {\n  const {\n    min,\n    max\n  } = shape.getLocalBounds();\n  return [(min[0] + max[0]) * 0.5, (min[1] + max[1]) * 0.5];\n}\nconst BadgeShape = createElement(g => {\n  const _a = g.attributes,\n    {\n      class: className,\n      x: x0,\n      y: y0,\n      transform\n    } = _a,\n    rest = __rest(_a, [\"class\", \"x\", \"y\", \"transform\"]);\n  const markerStyle = subObject(rest, 'marker');\n  const {\n    size = 24\n  } = markerStyle;\n  const symbol = () => getPath(size / 2);\n  const bgShape = select(g).maybeAppend('marker', () => new Marker({})).call(selection => selection.node().update(Object.assign({\n    symbol\n  }, markerStyle))).node();\n  const [x, y] = inferTextPosition(bgShape);\n  select(g).maybeAppend('text', 'text').style('x', x).style('y', y).call(applyStyle, rest);\n});\nexport const Badge = (options, context) => {\n  const style = __rest(options, []);\n  return (points, value, defaults) => {\n    const {\n        color: defaultColor\n      } = defaults,\n      rest = __rest(defaults, [\"color\"]);\n    const {\n      color = defaultColor,\n      text = ''\n    } = value;\n    const textStyle = {\n      text: String(text),\n      stroke: color,\n      fill: color\n    };\n    const [[x0, y0]] = points;\n    return select(new BadgeShape()).call(applyStyle, rest).style('transform', `translate(${x0},${y0})`).call(applyStyle, textStyle).call(applyStyle, style).node();\n  };\n};\nBadge.props = {\n  defaultMarker: 'point',\n  defaultEnterAnimation: 'fadeIn',\n  defaultUpdateAnimation: 'morphing',\n  defaultExitAnimation: 'fadeOut'\n};", "map": {"version": 3, "names": ["<PERSON><PERSON>", "createElement", "subObject", "select", "applyStyle", "<PERSON><PERSON><PERSON>", "r", "offset", "Math", "sqrt", "dy", "p0x", "p0y", "p1x", "p1y", "p2x", "p2y", "inferTextPosition", "shape", "min", "max", "getLocalBounds", "BadgeShape", "g", "_a", "attributes", "class", "className", "x", "x0", "y", "y0", "transform", "rest", "__rest", "markerStyle", "size", "symbol", "bgShape", "maybe<PERSON><PERSON>nd", "call", "selection", "node", "update", "Object", "assign", "style", "Badge", "options", "context", "points", "value", "defaults", "color", "defaultColor", "text", "textStyle", "String", "stroke", "fill", "props", "defaultMarker", "defaultEnterAnimation", "defaultUpdateAnimation", "defaultExitAnimation"], "sources": ["D:\\customerDemo\\Link-YinMa\\frontend\\node_modules\\@antv\\g2\\src\\shape\\text\\badge.ts"], "sourcesContent": ["import { TextStyleProps, DisplayObject } from '@antv/g';\nimport { Marker } from '@antv/component';\nimport { ShapeComponent as SC, WithPrefix } from '../../runtime';\nimport { createElement } from '../../utils/createElement';\nimport { subObject } from '../../utils/helper';\nimport { select } from '../../utils/selection';\nimport { applyStyle } from '../../shape/utils';\n\nexport type BadgeOptions = BadgeShapeStyleProps & Record<string, any>;\n\ntype MarkerStyleProps<P extends string> = WithPrefix<Record<string, any>, P>;\ntype BadgeShapeStyleProps = Partial<TextStyleProps> &\n  MarkerStyleProps<'marker'>;\n\n/**\n * Get the path to draw a built-in badge, which is like a balloon.\n */\nfunction getPath(r: number) {\n  const offset = r / Math.sqrt(2);\n  const dy = r * Math.sqrt(2);\n  const [p0x, p0y] = [-offset, offset - dy];\n  const [p1x, p1y] = [0, 0];\n  const [p2x, p2y] = [offset, offset - dy];\n  return [\n    ['M', p0x, p0y],\n    ['A', r, r, 0, 1, 1, p2x, p2y],\n    ['L', p1x, p1y],\n    ['Z'],\n  ];\n}\n\nfunction inferTextPosition(shape: DisplayObject) {\n  const { min, max } = shape.getLocalBounds();\n  return [(min[0] + max[0]) * 0.5, (min[1] + max[1]) * 0.5];\n}\n\nconst BadgeShape = createElement((g) => {\n  const { class: className, x: x0, y: y0, transform, ...rest } = g.attributes;\n\n  const markerStyle = subObject(rest, 'marker');\n  const { size = 24 } = markerStyle;\n\n  const symbol = () => getPath(size / 2);\n  const bgShape = select(g)\n    .maybeAppend('marker', () => new Marker({}))\n    .call((selection) =>\n      (selection.node() as Marker).update({ symbol, ...markerStyle }),\n    )\n    .node() as DisplayObject;\n\n  const [x, y] = inferTextPosition(bgShape);\n  select(g)\n    .maybeAppend('text', 'text')\n    .style('x', x)\n    .style('y', y)\n    .call(applyStyle, rest);\n});\n\nexport const Badge: SC<BadgeOptions> = (options, context) => {\n  const { ...style } = options;\n  return (points, value, defaults) => {\n    const { color: defaultColor, ...rest } = defaults;\n    const { color = defaultColor, text = '' } = value;\n    const textStyle = {\n      text: String(text),\n      stroke: color,\n      fill: color,\n    };\n    const [[x0, y0]] = points;\n    return select(new BadgeShape())\n      .call(applyStyle, rest)\n      .style('transform', `translate(${x0},${y0})`)\n      .call(applyStyle, textStyle)\n      .call(applyStyle, style)\n      .node();\n  };\n};\n\nBadge.props = {\n  defaultMarker: 'point',\n  defaultEnterAnimation: 'fadeIn',\n  defaultUpdateAnimation: 'morphing',\n  defaultExitAnimation: 'fadeOut',\n};\n"], "mappings": ";;;;;;;;AACA,SAASA,MAAM,QAAQ,iBAAiB;AAExC,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,MAAM,QAAQ,uBAAuB;AAC9C,SAASC,UAAU,QAAQ,mBAAmB;AAQ9C;;;AAGA,SAASC,OAAOA,CAACC,CAAS;EACxB,MAAMC,MAAM,GAAGD,CAAC,GAAGE,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;EAC/B,MAAMC,EAAE,GAAGJ,CAAC,GAAGE,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;EAC3B,MAAM,CAACE,GAAG,EAAEC,GAAG,CAAC,GAAG,CAAC,CAACL,MAAM,EAAEA,MAAM,GAAGG,EAAE,CAAC;EACzC,MAAM,CAACG,GAAG,EAAEC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EACzB,MAAM,CAACC,GAAG,EAAEC,GAAG,CAAC,GAAG,CAACT,MAAM,EAAEA,MAAM,GAAGG,EAAE,CAAC;EACxC,OAAO,CACL,CAAC,GAAG,EAAEC,GAAG,EAAEC,GAAG,CAAC,EACf,CAAC,GAAG,EAAEN,CAAC,EAAEA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAES,GAAG,EAAEC,GAAG,CAAC,EAC9B,CAAC,GAAG,EAAEH,GAAG,EAAEC,GAAG,CAAC,EACf,CAAC,GAAG,CAAC,CACN;AACH;AAEA,SAASG,iBAAiBA,CAACC,KAAoB;EAC7C,MAAM;IAAEC,GAAG;IAAEC;EAAG,CAAE,GAAGF,KAAK,CAACG,cAAc,EAAE;EAC3C,OAAO,CAAC,CAACF,GAAG,CAAC,CAAC,CAAC,GAAGC,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,CAACD,GAAG,CAAC,CAAC,CAAC,GAAGC,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;AAC3D;AAEA,MAAME,UAAU,GAAGrB,aAAa,CAAEsB,CAAC,IAAI;EACrC,MAAMC,EAAA,GAAyDD,CAAC,CAACE,UAAU;IAArE;MAAEC,KAAK,EAAEC,SAAS;MAAEC,CAAC,EAAEC,EAAE;MAAEC,CAAC,EAAEC,EAAE;MAAEC;IAAS,IAAAR,EAA0B;IAArBS,IAAI,GAAAC,MAAA,CAAAV,EAAA,EAApD,gCAAsD,CAAe;EAE3E,MAAMW,WAAW,GAAGjC,SAAS,CAAC+B,IAAI,EAAE,QAAQ,CAAC;EAC7C,MAAM;IAAEG,IAAI,GAAG;EAAE,CAAE,GAAGD,WAAW;EAEjC,MAAME,MAAM,GAAGA,CAAA,KAAMhC,OAAO,CAAC+B,IAAI,GAAG,CAAC,CAAC;EACtC,MAAME,OAAO,GAAGnC,MAAM,CAACoB,CAAC,CAAC,CACtBgB,WAAW,CAAC,QAAQ,EAAE,MAAM,IAAIvC,MAAM,CAAC,EAAE,CAAC,CAAC,CAC3CwC,IAAI,CAAEC,SAAS,IACbA,SAAS,CAACC,IAAI,EAAa,CAACC,MAAM,CAAAC,MAAA,CAAAC,MAAA;IAAGR;EAAM,GAAKF,WAAW,EAAG,CAChE,CACAO,IAAI,EAAmB;EAE1B,MAAM,CAACd,CAAC,EAAEE,CAAC,CAAC,GAAGb,iBAAiB,CAACqB,OAAO,CAAC;EACzCnC,MAAM,CAACoB,CAAC,CAAC,CACNgB,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,CAC3BO,KAAK,CAAC,GAAG,EAAElB,CAAC,CAAC,CACbkB,KAAK,CAAC,GAAG,EAAEhB,CAAC,CAAC,CACbU,IAAI,CAACpC,UAAU,EAAE6B,IAAI,CAAC;AAC3B,CAAC,CAAC;AAEF,OAAO,MAAMc,KAAK,GAAqBA,CAACC,OAAO,EAAEC,OAAO,KAAI;EAC1D,MAAWH,KAAK,GAAAZ,MAAA,CAAKc,OAAO,EAAtB,EAAY,CAAU;EAC5B,OAAO,CAACE,MAAM,EAAEC,KAAK,EAAEC,QAAQ,KAAI;IACjC,MAAM;QAAEC,KAAK,EAAEC;MAAY,IAAcF,QAAQ;MAAjBnB,IAAI,GAAAC,MAAA,CAAKkB,QAAQ,EAA3C,SAAgC,CAAW;IACjD,MAAM;MAAEC,KAAK,GAAGC,YAAY;MAAEC,IAAI,GAAG;IAAE,CAAE,GAAGJ,KAAK;IACjD,MAAMK,SAAS,GAAG;MAChBD,IAAI,EAAEE,MAAM,CAACF,IAAI,CAAC;MAClBG,MAAM,EAAEL,KAAK;MACbM,IAAI,EAAEN;KACP;IACD,MAAM,CAAC,CAACxB,EAAE,EAAEE,EAAE,CAAC,CAAC,GAAGmB,MAAM;IACzB,OAAO/C,MAAM,CAAC,IAAImB,UAAU,EAAE,CAAC,CAC5BkB,IAAI,CAACpC,UAAU,EAAE6B,IAAI,CAAC,CACtBa,KAAK,CAAC,WAAW,EAAE,aAAajB,EAAE,IAAIE,EAAE,GAAG,CAAC,CAC5CS,IAAI,CAACpC,UAAU,EAAEoD,SAAS,CAAC,CAC3BhB,IAAI,CAACpC,UAAU,EAAE0C,KAAK,CAAC,CACvBJ,IAAI,EAAE;EACX,CAAC;AACH,CAAC;AAEDK,KAAK,CAACa,KAAK,GAAG;EACZC,aAAa,EAAE,OAAO;EACtBC,qBAAqB,EAAE,QAAQ;EAC/BC,sBAAsB,EAAE,UAAU;EAClCC,oBAAoB,EAAE;CACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}