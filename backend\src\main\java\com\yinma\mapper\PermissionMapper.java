package com.yinma.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinma.dto.PermissionDTO;
import com.yinma.entity.PermissionEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 权限信息Mapper接口
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
@Repository
@Mapper
public interface PermissionMapper extends BaseMapper<PermissionEntity> {

    /**
     * 分页查询权限列表
     * 
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 权限分页列表
     */
    @Select("<script>" +
            "SELECT p.*, " +
            "(SELECT COUNT(1) FROM sys_role_permission rp WHERE rp.permission_id = p.permission_id) as roleCount " +
            "FROM sys_permission p " +
            "WHERE p.is_deleted = 0 " +
            "<if test='queryDTO.parentId != null'>" +
            "AND p.parent_id = #{queryDTO.parentId} " +
            "</if>" +
            "<if test='queryDTO.permissionCode != null and queryDTO.permissionCode != \"\"'>" +
            "AND p.permission_code LIKE CONCAT('%', #{queryDTO.permissionCode}, '%') " +
            "</if>" +
            "<if test='queryDTO.permissionName != null and queryDTO.permissionName != \"\"'>" +
            "AND p.permission_name LIKE CONCAT('%', #{queryDTO.permissionName}, '%') " +
            "</if>" +
            "<if test='queryDTO.permissionType != null'>" +
            "AND p.permission_type = #{queryDTO.permissionType} " +
            "</if>" +
            "<if test='queryDTO.status != null'>" +
            "AND p.status = #{queryDTO.status} " +
            "</if>" +
            "<if test='queryDTO.isSystem != null'>" +
            "AND p.is_system = #{queryDTO.isSystem} " +
            "</if>" +
            "<if test='queryDTO.isVisible != null'>" +
            "AND p.is_visible = #{queryDTO.isVisible} " +
            "</if>" +
            "<if test='queryDTO.createTimeStart != null'>" +
            "AND p.create_time >= #{queryDTO.createTimeStart} " +
            "</if>" +
            "<if test='queryDTO.createTimeEnd != null'>" +
            "AND p.create_time <= #{queryDTO.createTimeEnd} " +
            "</if>" +
            "ORDER BY p.sort_order ASC, p.create_time DESC" +
            "</script>")
    IPage<PermissionEntity> selectPermissionPage(Page<PermissionEntity> page, @Param("queryDTO") PermissionDTO.PermissionQueryDTO queryDTO);

    /**
     * 根据权限编码查询权限
     * 
     * @param permissionCode 权限编码
     * @return 权限信息
     */
    @Select("SELECT * FROM sys_permission WHERE permission_code = #{permissionCode} AND is_deleted = 0")
    PermissionEntity selectByPermissionCode(@Param("permissionCode") String permissionCode);

    /**
     * 根据用户ID查询权限列表
     * 
     * @param userId 用户ID
     * @return 权限列表
     */
    @Select("SELECT DISTINCT p.* FROM sys_permission p " +
            "INNER JOIN sys_role_permission rp ON p.permission_id = rp.permission_id " +
            "INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND p.status = 1 AND p.is_deleted = 0")
    List<PermissionEntity> selectUserPermissions(@Param("userId") Long userId);

    /**
     * 检查权限编码是否存在
     * 
     * @param permissionCode 权限编码
     * @param excludePermissionId 排除的权限ID
     * @return 是否存在
     */
    @Select("<script>" +
            "SELECT COUNT(1) FROM sys_permission " +
            "WHERE permission_code = #{permissionCode} AND is_deleted = 0 " +
            "<if test='excludePermissionId != null'>" +
            "AND permission_id != #{excludePermissionId} " +
            "</if>" +
            "</script>")
    int checkPermissionCodeExists(@Param("permissionCode") String permissionCode, @Param("excludePermissionId") Long excludePermissionId);

    /**
     * 检查权限名称是否存在
     * 
     * @param permissionName 权限名称
     * @param excludePermissionId 排除的权限ID
     * @return 是否存在
     */
    @Select("<script>" +
            "SELECT COUNT(1) FROM sys_permission " +
            "WHERE permission_name = #{permissionName} AND is_deleted = 0 " +
            "<if test='excludePermissionId != null'>" +
            "AND permission_id != #{excludePermissionId} " +
            "</if>" +
            "</script>")
    int checkPermissionNameExists(@Param("permissionName") String permissionName, @Param("excludePermissionId") Long excludePermissionId);

    /**
     * 查询权限树结构
     * 
     * @return 权限树列表
     */
    @Select("SELECT * FROM sys_permission WHERE status = 1 AND is_deleted = 0 ORDER BY sort_order ASC")
    List<PermissionEntity> selectPermissionTree();

    /**
     * 查询子权限列表
     * 
     * @param parentId 父权限ID
     * @return 子权限列表
     */
    @Select("SELECT * FROM sys_permission WHERE parent_id = #{parentId} AND is_deleted = 0 ORDER BY sort_order ASC")
    List<PermissionEntity> selectByParentId(@Param("parentId") Long parentId);

    /**
     * 查询用户权限列表
     * 
     * @param userId 用户ID
     * @return 权限列表
     */
    @Select("SELECT DISTINCT p.* FROM sys_permission p " +
            "INNER JOIN sys_role_permission rp ON p.permission_id = rp.permission_id " +
            "INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND p.status = 1 AND p.is_deleted = 0 " +
            "ORDER BY p.sort_order ASC")
    List<PermissionEntity> selectByUserId(@Param("userId") Long userId);

    /**
     * 查询用户菜单权限
     * 
     * @param userId 用户ID
     * @return 菜单权限列表
     */
    @Select("SELECT DISTINCT p.* FROM sys_permission p " +
            "INNER JOIN sys_role_permission rp ON p.permission_id = rp.permission_id " +
            "INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND p.permission_type = 1 " +
            "AND p.status = 1 AND p.is_visible = 1 AND p.is_deleted = 0 " +
            "ORDER BY p.sort_order ASC")
    List<PermissionEntity> selectUserMenus(@Param("userId") Long userId);

    /**
     * 查询用户按钮权限
     * 
     * @param userId 用户ID
     * @return 按钮权限列表
     */
    @Select("SELECT DISTINCT p.permission_code FROM sys_permission p " +
            "INNER JOIN sys_role_permission rp ON p.permission_id = rp.permission_id " +
            "INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND p.permission_type = 2 " +
            "AND p.status = 1 AND p.is_deleted = 0")
    List<String> selectUserButtons(@Param("userId") Long userId);

    /**
     * 查询用户接口权限
     * 
     * @param userId 用户ID
     * @return 接口权限列表
     */
    @Select("SELECT DISTINCT p.* FROM sys_permission p " +
            "INNER JOIN sys_role_permission rp ON p.permission_id = rp.permission_id " +
            "INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND p.permission_type = 3 " +
            "AND p.status = 1 AND p.is_deleted = 0")
    List<PermissionEntity> selectUserApis(@Param("userId") Long userId);

    /**
     * 检查用户是否有指定权限
     * 
     * @param userId 用户ID
     * @param permissionCode 权限编码
     * @return 是否有权限
     */
    @Select("SELECT COUNT(1) FROM sys_permission p " +
            "INNER JOIN sys_role_permission rp ON p.permission_id = rp.permission_id " +
            "INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND p.permission_code = #{permissionCode} " +
            "AND p.status = 1 AND p.is_deleted = 0")
    int checkUserPermission(@Param("userId") Long userId, @Param("permissionCode") String permissionCode);

    /**
     * 检查用户是否有接口权限
     * 
     * @param userId 用户ID
     * @param apiUrl 接口URL
     * @param apiMethod 接口方法
     * @return 是否有权限
     */
    @Select("SELECT COUNT(1) FROM sys_permission p " +
            "INNER JOIN sys_role_permission rp ON p.permission_id = rp.permission_id " +
            "INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND p.permission_type = 3 " +
            "AND p.api_url = #{apiUrl} AND p.api_method = #{apiMethod} " +
            "AND p.status = 1 AND p.is_deleted = 0")
    int checkUserApiPermission(@Param("userId") Long userId, @Param("apiUrl") String apiUrl, @Param("apiMethod") String apiMethod);

    /**
     * 更新权限状态
     * 
     * @param permissionId 权限ID
     * @param status 状态
     * @return 更新行数
     */
    @Update("UPDATE sys_permission SET status = #{status} WHERE permission_id = #{permissionId}")
    int updateStatus(@Param("permissionId") Long permissionId, @Param("status") Integer status);

    /**
     * 批量更新权限状态
     * 
     * @param permissionIds 权限ID列表
     * @param status 状态
     * @return 更新行数
     */
    @Update("<script>" +
            "UPDATE sys_permission SET status = #{status} " +
            "WHERE permission_id IN " +
            "<foreach collection='permissionIds' item='permissionId' open='(' separator=',' close=')'>" +
            "#{permissionId}" +
            "</foreach>" +
            "</script>")
    int batchUpdateStatus(@Param("permissionIds") List<Long> permissionIds, @Param("status") Integer status);

    /**
     * 更新权限父级ID
     * 
     * @param permissionId 权限ID
     * @param parentId 父权限ID
     * @return 更新行数
     */
    @Update("UPDATE sys_permission SET parent_id = #{parentId} WHERE permission_id = #{permissionId}")
    int updateParentId(@Param("permissionId") Long permissionId, @Param("parentId") Long parentId);

    /**
     * 查询所有菜单权限
     * 
     * @return 菜单权限列表
     */
    @Select("SELECT * FROM sys_permission WHERE permission_type = 1 AND status = 1 AND is_deleted = 0 ORDER BY sort_order ASC")
    List<PermissionEntity> selectAllMenus();

    /**
     * 查询所有按钮权限
     * 
     * @return 按钮权限列表
     */
    @Select("SELECT * FROM sys_permission WHERE permission_type = 2 AND status = 1 AND is_deleted = 0 ORDER BY sort_order ASC")
    List<PermissionEntity> selectAllButtons();

    /**
     * 查询所有接口权限
     * 
     * @return 接口权限列表
     */
    @Select("SELECT * FROM sys_permission WHERE permission_type = 3 AND status = 1 AND is_deleted = 0 ORDER BY sort_order ASC")
    List<PermissionEntity> selectAllApis();

    /**
     * 查询权限统计信息
     * 
     * @return 统计信息
     */
    @Select("SELECT " +
            "COUNT(1) as totalPermissions, " +
            "SUM(CASE WHEN permission_type = 1 THEN 1 ELSE 0 END) as menuPermissions, " +
            "SUM(CASE WHEN permission_type = 2 THEN 1 ELSE 0 END) as buttonPermissions, " +
            "SUM(CASE WHEN permission_type = 3 THEN 1 ELSE 0 END) as apiPermissions, " +
            "SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as activePermissions, " +
            "SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as inactivePermissions, " +
            "SUM(CASE WHEN is_system = 1 THEN 1 ELSE 0 END) as systemPermissions, " +
            "SUM(CASE WHEN is_system = 0 THEN 1 ELSE 0 END) as customPermissions, " +
            "SUM(CASE WHEN DATE(create_time) = CURDATE() THEN 1 ELSE 0 END) as todayNewPermissions, " +
            "SUM(CASE WHEN YEAR(create_time) = YEAR(NOW()) AND MONTH(create_time) = MONTH(NOW()) THEN 1 ELSE 0 END) as monthNewPermissions " +
            "FROM sys_permission WHERE is_deleted = 0")
    PermissionDTO.PermissionStatisticsDTO selectPermissionStatistics();

    /**
     * 查询权限层级关系
     * 
     * @return 权限层级列表
     */
    @Select("SELECT permission_id, parent_id, permission_code, permission_name, permission_level, sort_order " +
            "FROM sys_permission WHERE status = 1 AND is_deleted = 0 " +
            "ORDER BY permission_level ASC, sort_order ASC")
    List<PermissionEntity> selectPermissionHierarchy();

    /**
     * 查询最大排序号
     * 
     * @param parentId 父权限ID
     * @return 最大排序号
     */
    @Select("SELECT COALESCE(MAX(sort_order), 0) FROM sys_permission " +
            "WHERE parent_id = #{parentId} AND is_deleted = 0")
    Integer selectMaxSortOrder(@Param("parentId") Long parentId);

    /**
     * 更新排序号
     * 
     * @param permissionId 权限ID
     * @param sortOrder 排序号
     * @return 更新行数
     */
    @Update("UPDATE sys_permission SET sort_order = #{sortOrder} WHERE permission_id = #{permissionId}")
    int updateSortOrder(@Param("permissionId") Long permissionId, @Param("sortOrder") Integer sortOrder);

    /**
     * 查询权限的所有子权限ID
     * 
     * @param permissionId 权限ID
     * @return 子权限ID列表
     */
    @Select("WITH RECURSIVE permission_tree AS (" +
            "SELECT permission_id FROM sys_permission WHERE permission_id = #{permissionId} " +
            "UNION ALL " +
            "SELECT p.permission_id FROM sys_permission p " +
            "INNER JOIN permission_tree pt ON p.parent_id = pt.permission_id " +
            "WHERE p.is_deleted = 0" +
            ") SELECT permission_id FROM permission_tree WHERE permission_id != #{permissionId}")
    List<Long> selectChildPermissionIds(@Param("permissionId") Long permissionId);

    /**
     * 查询权限的所有父权限ID
     * 
     * @param permissionId 权限ID
     * @return 父权限ID列表
     */
    @Select("WITH RECURSIVE permission_parent AS (" +
            "SELECT permission_id, parent_id FROM sys_permission WHERE permission_id = #{permissionId} " +
            "UNION ALL " +
            "SELECT p.permission_id, p.parent_id FROM sys_permission p " +
            "INNER JOIN permission_parent pp ON p.permission_id = pp.parent_id " +
            "WHERE p.is_deleted = 0" +
            ") SELECT permission_id FROM permission_parent WHERE permission_id != #{permissionId}")
    List<Long> selectParentPermissionIds(@Param("permissionId") Long permissionId);

    /**
     * 查询角色权限关联数量
     * 
     * @param permissionId 权限ID
     * @return 关联数量
     */
    @Select("SELECT COUNT(1) FROM sys_role_permission WHERE permission_id = #{permissionId}")
    Long selectRolePermissionCount(@Param("permissionId") Long permissionId);

    /**
     * 删除权限的角色关联
     * 
     * @param permissionId 权限ID
     * @return 删除行数
     */
    @Delete("DELETE FROM sys_role_permission WHERE permission_id = #{permissionId}")
    int deletePermissionRoles(@Param("permissionId") Long permissionId);

    /**
     * 批量删除权限的角色关联
     * 
     * @param permissionIds 权限ID列表
     * @return 删除行数
     */
    @Delete("<script>" +
            "DELETE FROM sys_role_permission " +
            "WHERE permission_id IN " +
            "<foreach collection='permissionIds' item='permissionId' open='(' separator=',' close=')'>" +
            "#{permissionId}" +
            "</foreach>" +
            "</script>")
    int batchDeletePermissionRoles(@Param("permissionIds") List<Long> permissionIds);
}