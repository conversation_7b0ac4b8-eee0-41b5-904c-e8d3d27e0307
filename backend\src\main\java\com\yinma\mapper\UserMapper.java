package com.yinma.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yinma.dto.UserDTO;
import com.yinma.entity.UserEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户信息Mapper接口
 * 
 * <AUTHOR> System
 * @since 2024-01-01
 */
@Repository
@Mapper
public interface UserMapper extends BaseMapper<UserEntity> {

    /**
     * 分页查询用户列表
     * 
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 用户分页列表
     */
    @Select("<script>" +
            "SELECT u.*, d.dept_name, p.position_name " +
            "FROM sys_user u " +
            "LEFT JOIN sys_dept d ON u.dept_id = d.dept_id " +
            "LEFT JOIN sys_position p ON u.position_id = p.position_id " +
            "WHERE u.is_deleted = 0 " +
            "<if test='queryDTO.username != null and queryDTO.username != \"\"'>" +
            "AND u.username LIKE CONCAT('%', #{queryDTO.username}, '%') " +
            "</if>" +
            "<if test='queryDTO.realName != null and queryDTO.realName != \"\"'>" +
            "AND u.real_name LIKE CONCAT('%', #{queryDTO.realName}, '%') " +
            "</if>" +
            "<if test='queryDTO.email != null and queryDTO.email != \"\"'>" +
            "AND u.email LIKE CONCAT('%', #{queryDTO.email}, '%') " +
            "</if>" +
            "<if test='queryDTO.phone != null and queryDTO.phone != \"\"'>" +
            "AND u.phone LIKE CONCAT('%', #{queryDTO.phone}, '%') " +
            "</if>" +
            "<if test='queryDTO.deptId != null'>" +
            "AND u.dept_id = #{queryDTO.deptId} " +
            "</if>" +
            "<if test='queryDTO.positionId != null'>" +
            "AND u.position_id = #{queryDTO.positionId} " +
            "</if>" +
            "<if test='queryDTO.status != null'>" +
            "AND u.status = #{queryDTO.status} " +
            "</if>" +
            "<if test='queryDTO.userType != null'>" +
            "AND u.user_type = #{queryDTO.userType} " +
            "</if>" +
            "<if test='queryDTO.createTimeStart != null'>" +
            "AND u.create_time >= #{queryDTO.createTimeStart} " +
            "</if>" +
            "<if test='queryDTO.createTimeEnd != null'>" +
            "AND u.create_time <= #{queryDTO.createTimeEnd} " +
            "</if>" +
            "ORDER BY u.create_time DESC" +
            "</script>")
    IPage<UserEntity> selectUserPage(Page<UserEntity> page, @Param("queryDTO") UserDTO.UserQueryDTO queryDTO);

    /**
     * 根据用户名查询用户
     * 
     * @param username 用户名
     * @return 用户信息
     */
    @Select("SELECT * FROM sys_user WHERE username = #{username} AND is_deleted = 0")
    UserEntity selectByUsername(@Param("username") String username);

    /**
     * 根据邮箱查询用户
     * 
     * @param email 邮箱
     * @return 用户信息
     */
    @Select("SELECT * FROM sys_user WHERE email = #{email} AND is_deleted = 0")
    UserEntity selectByEmail(@Param("email") String email);

    /**
     * 根据手机号查询用户
     * 
     * @param phone 手机号
     * @return 用户信息
     */
    @Select("SELECT * FROM sys_user WHERE phone = #{phone} AND is_deleted = 0")
    UserEntity selectByPhone(@Param("phone") String phone);

    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @param excludeUserId 排除的用户ID
     * @return 是否存在
     */
    @Select("<script>" +
            "SELECT COUNT(1) FROM sys_user " +
            "WHERE username = #{username} AND is_deleted = 0 " +
            "<if test='excludeUserId != null'>" +
            "AND user_id != #{excludeUserId} " +
            "</if>" +
            "</script>")
    int checkUsernameExists(@Param("username") String username, @Param("excludeUserId") Long excludeUserId);

    /**
     * 检查邮箱是否存在
     * 
     * @param email 邮箱
     * @param excludeUserId 排除的用户ID
     * @return 是否存在
     */
    @Select("<script>" +
            "SELECT COUNT(1) FROM sys_user " +
            "WHERE email = #{email} AND is_deleted = 0 " +
            "<if test='excludeUserId != null'>" +
            "AND user_id != #{excludeUserId} " +
            "</if>" +
            "</script>")
    int checkEmailExists(@Param("email") String email, @Param("excludeUserId") Long excludeUserId);

    /**
     * 检查手机号是否存在
     * 
     * @param phone 手机号
     * @param excludeUserId 排除的用户ID
     * @return 是否存在
     */
    @Select("<script>" +
            "SELECT COUNT(1) FROM sys_user " +
            "WHERE phone = #{phone} AND is_deleted = 0 " +
            "<if test='excludeUserId != null'>" +
            "AND user_id != #{excludeUserId} " +
            "</if>" +
            "</script>")
    int checkPhoneExists(@Param("phone") String phone, @Param("excludeUserId") Long excludeUserId);

    /**
     * 根据部门ID查询用户列表
     * 
     * @param deptId 部门ID
     * @return 用户列表
     */
    @Select("SELECT * FROM sys_user WHERE dept_id = #{deptId} AND is_deleted = 0 ORDER BY create_time DESC")
    List<UserEntity> selectByDeptId(@Param("deptId") Long deptId);

    /**
     * 根据角色ID查询用户列表
     * 
     * @param roleId 角色ID
     * @return 用户列表
     */
    @Select("SELECT u.* FROM sys_user u " +
            "INNER JOIN sys_user_role ur ON u.user_id = ur.user_id " +
            "WHERE ur.role_id = #{roleId} AND u.is_deleted = 0 " +
            "ORDER BY u.create_time DESC")
    List<UserEntity> selectByRoleId(@Param("roleId") Long roleId);

    /**
     * 查询用户的角色列表
     * 
     * @param userId 用户ID
     * @return 角色列表
     */
    @Select("SELECT r.* FROM sys_role r " +
            "INNER JOIN sys_user_role ur ON r.role_id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND r.status = 1 AND r.is_deleted = 0")
    List<String> selectUserRoles(@Param("userId") Long userId);

    /**
     * 查询用户的权限列表
     * 
     * @param userId 用户ID
     * @return 权限列表
     */
    @Select("SELECT DISTINCT p.permission_code FROM sys_permission p " +
            "INNER JOIN sys_role_permission rp ON p.permission_id = rp.permission_id " +
            "INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND p.status = 1 AND p.is_deleted = 0")
    List<String> selectUserPermissions(@Param("userId") Long userId);

    /**
     * 更新用户最后登录时间
     * 
     * @param userId 用户ID
     * @param lastLoginTime 最后登录时间
     * @param lastLoginIp 最后登录IP
     * @return 更新行数
     */
    @Update("UPDATE sys_user SET last_login_time = #{lastLoginTime}, last_login_ip = #{lastLoginIp} " +
            "WHERE user_id = #{userId}")
    int updateLastLoginInfo(@Param("userId") Long userId, 
                           @Param("lastLoginTime") LocalDateTime lastLoginTime,
                           @Param("lastLoginIp") String lastLoginIp);

    /**
     * 更新用户密码
     * 
     * @param userId 用户ID
     * @param newPassword 新密码
     * @return 更新行数
     */
    @Update("UPDATE sys_user SET password = #{newPassword}, password_update_time = NOW() " +
            "WHERE user_id = #{userId}")
    int updatePassword(@Param("userId") Long userId, @Param("newPassword") String newPassword);

    /**
     * 更新用户状态
     * 
     * @param userId 用户ID
     * @param status 状态
     * @return 更新行数
     */
    @Update("UPDATE sys_user SET status = #{status} WHERE user_id = #{userId}")
    int updateStatus(@Param("userId") Long userId, @Param("status") Integer status);

    /**
     * 批量更新用户状态
     * 
     * @param userIds 用户ID列表
     * @param status 状态
     * @return 更新行数
     */
    @Update("<script>" +
            "UPDATE sys_user SET status = #{status} " +
            "WHERE user_id IN " +
            "<foreach collection='userIds' item='userId' open='(' separator=',' close=')'>" +
            "#{userId}" +
            "</foreach>" +
            "</script>")
    int batchUpdateStatus(@Param("userIds") List<Long> userIds, @Param("status") Integer status);

    /**
     * 重置用户密码
     * 
     * @param userId 用户ID
     * @param defaultPassword 默认密码
     * @return 更新行数
     */
    @Update("UPDATE sys_user SET password = #{defaultPassword}, password_update_time = NOW(), " +
            "is_password_reset = 1 WHERE user_id = #{userId}")
    int resetPassword(@Param("userId") Long userId, @Param("defaultPassword") String defaultPassword);

    /**
     * 查询用户统计信息
     * 
     * @return 统计信息
     */
    @Select("SELECT " +
            "COUNT(1) as totalUsers, " +
            "SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as activeUsers, " +
            "SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as inactiveUsers, " +
            "SUM(CASE WHEN user_type = 1 THEN 1 ELSE 0 END) as systemUsers, " +
            "SUM(CASE WHEN user_type = 2 THEN 1 ELSE 0 END) as businessUsers, " +
            "SUM(CASE WHEN DATE(create_time) = CURDATE() THEN 1 ELSE 0 END) as todayNewUsers, " +
            "SUM(CASE WHEN YEAR(create_time) = YEAR(NOW()) AND MONTH(create_time) = MONTH(NOW()) THEN 1 ELSE 0 END) as monthNewUsers " +
            "FROM sys_user WHERE is_deleted = 0")
    UserDTO.UserStatisticsDTO selectUserStatistics();

    /**
     * 查询在线用户数量
     * 
     * @param timeThreshold 时间阈值（分钟）
     * @return 在线用户数量
     */
    @Select("SELECT COUNT(1) FROM sys_user " +
            "WHERE last_login_time >= DATE_SUB(NOW(), INTERVAL #{timeThreshold} MINUTE) " +
            "AND status = 1 AND is_deleted = 0")
    Long selectOnlineUserCount(@Param("timeThreshold") Integer timeThreshold);

    /**
     * 查询密码即将过期的用户
     * 
     * @param days 天数
     * @return 用户列表
     */
    @Select("SELECT * FROM sys_user " +
            "WHERE password_update_time <= DATE_SUB(NOW(), INTERVAL (90 - #{days}) DAY) " +
            "AND status = 1 AND is_deleted = 0 " +
            "ORDER BY password_update_time ASC")
    List<UserEntity> selectPasswordExpiringSoon(@Param("days") Integer days);

    /**
     * 查询长期未登录的用户
     * 
     * @param days 天数
     * @return 用户列表
     */
    @Select("SELECT * FROM sys_user " +
            "WHERE (last_login_time IS NULL OR last_login_time <= DATE_SUB(NOW(), INTERVAL #{days} DAY)) " +
            "AND status = 1 AND is_deleted = 0 " +
            "ORDER BY last_login_time ASC")
    List<UserEntity> selectLongTimeNoLogin(@Param("days") Integer days);

    /**
     * 查询部门用户统计
     * 
     * @return 部门用户统计列表
     */
    @Select("SELECT d.dept_name, COUNT(u.user_id) as userCount " +
            "FROM sys_dept d " +
            "LEFT JOIN sys_user u ON d.dept_id = u.dept_id AND u.is_deleted = 0 " +
            "WHERE d.is_deleted = 0 " +
            "GROUP BY d.dept_id, d.dept_name " +
            "ORDER BY userCount DESC")
    List<UserDTO.DeptUserStatisticsDTO> selectDeptUserStatistics();

    /**
     * 查询角色用户统计
     * 
     * @return 角色用户统计列表
     */
    @Select("SELECT r.role_name, COUNT(ur.user_id) as userCount " +
            "FROM sys_role r " +
            "LEFT JOIN sys_user_role ur ON r.role_id = ur.role_id " +
            "LEFT JOIN sys_user u ON ur.user_id = u.user_id AND u.is_deleted = 0 " +
            "WHERE r.is_deleted = 0 " +
            "GROUP BY r.role_id, r.role_name " +
            "ORDER BY userCount DESC")
    List<UserDTO.RoleUserStatisticsDTO> selectRoleUserStatistics();
}