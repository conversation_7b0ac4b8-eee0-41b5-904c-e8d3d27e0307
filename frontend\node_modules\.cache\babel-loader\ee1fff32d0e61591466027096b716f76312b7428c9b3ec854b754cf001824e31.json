{"ast": null, "code": "import { makeChartComp } from '../../../util/makeChartComp';\nvar TinyAreaChart = makeChartComp('TinyArea');\nexport default TinyAreaChart;", "map": {"version": 3, "names": ["makeChartComp", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/@ant-design/plots/es/components/tiny/area/index.js"], "sourcesContent": ["import { makeChartComp } from '../../../util/makeChartComp';\nvar TinyAreaChart = makeChartComp('TinyArea');\nexport default TinyAreaChart;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,6BAA6B;AAC3D,IAAIC,aAAa,GAAGD,aAAa,CAAC,UAAU,CAAC;AAC7C,eAAeC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}