{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { ConfigContext } from '../config-provider';\nconst Meta = props => {\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      avatar,\n      title,\n      description\n    } = props,\n    others = __rest(props, [\"prefixCls\", \"className\", \"avatar\", \"title\", \"description\"]);\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('card', customizePrefixCls);\n  const classString = classNames(`${prefixCls}-meta`, className);\n  const avatarDom = avatar ? (/*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-meta-avatar`\n  }, avatar)) : null;\n  const titleDom = title ? (/*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-meta-title`\n  }, title)) : null;\n  const descriptionDom = description ? (/*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-meta-description`\n  }, description)) : null;\n  const MetaDetail = titleDom || descriptionDom ? (/*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-meta-detail`\n  }, titleDom, descriptionDom)) : null;\n  return /*#__PURE__*/React.createElement(\"div\", Object.assign({}, others, {\n    className: classString\n  }), avatarDom, MetaDetail);\n};\nexport default Meta;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "ConfigContext", "Meta", "props", "prefixCls", "customizePrefixCls", "className", "avatar", "title", "description", "others", "getPrefixCls", "useContext", "classString", "avatarDom", "createElement", "titleDom", "descriptionDom", "MetaDetail", "assign"], "sources": ["D:/customerDemo/Link-YinMa/frontend/node_modules/antd/es/card/Meta.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { ConfigContext } from '../config-provider';\nconst Meta = props => {\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      avatar,\n      title,\n      description\n    } = props,\n    others = __rest(props, [\"prefixCls\", \"className\", \"avatar\", \"title\", \"description\"]);\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('card', customizePrefixCls);\n  const classString = classNames(`${prefixCls}-meta`, className);\n  const avatarDom = avatar ? (/*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-meta-avatar`\n  }, avatar)) : null;\n  const titleDom = title ? (/*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-meta-title`\n  }, title)) : null;\n  const descriptionDom = description ? (/*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-meta-description`\n  }, description)) : null;\n  const MetaDetail = titleDom || descriptionDom ? (/*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-meta-detail`\n  }, titleDom, descriptionDom)) : null;\n  return /*#__PURE__*/React.createElement(\"div\", Object.assign({}, others, {\n    className: classString\n  }), avatarDom, MetaDetail);\n};\nexport default Meta;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,MAAMC,IAAI,GAAGC,KAAK,IAAI;EACpB,MAAM;MACFC,SAAS,EAAEC,kBAAkB;MAC7BC,SAAS;MACTC,MAAM;MACNC,KAAK;MACLC;IACF,CAAC,GAAGN,KAAK;IACTO,MAAM,GAAGzB,MAAM,CAACkB,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;EACtF,MAAM;IACJQ;EACF,CAAC,GAAGZ,KAAK,CAACa,UAAU,CAACX,aAAa,CAAC;EACnC,MAAMG,SAAS,GAAGO,YAAY,CAAC,MAAM,EAAEN,kBAAkB,CAAC;EAC1D,MAAMQ,WAAW,GAAGb,UAAU,CAAC,GAAGI,SAAS,OAAO,EAAEE,SAAS,CAAC;EAC9D,MAAMQ,SAAS,GAAGP,MAAM,IAAI,aAAaR,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;IAClET,SAAS,EAAE,GAAGF,SAAS;EACzB,CAAC,EAAEG,MAAM,CAAC,IAAI,IAAI;EAClB,MAAMS,QAAQ,GAAGR,KAAK,IAAI,aAAaT,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;IAChET,SAAS,EAAE,GAAGF,SAAS;EACzB,CAAC,EAAEI,KAAK,CAAC,IAAI,IAAI;EACjB,MAAMS,cAAc,GAAGR,WAAW,IAAI,aAAaV,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;IAC5ET,SAAS,EAAE,GAAGF,SAAS;EACzB,CAAC,EAAEK,WAAW,CAAC,IAAI,IAAI;EACvB,MAAMS,UAAU,GAAGF,QAAQ,IAAIC,cAAc,IAAI,aAAalB,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;IACvFT,SAAS,EAAE,GAAGF,SAAS;EACzB,CAAC,EAAEY,QAAQ,EAAEC,cAAc,CAAC,IAAI,IAAI;EACpC,OAAO,aAAalB,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAEzB,MAAM,CAAC6B,MAAM,CAAC,CAAC,CAAC,EAAET,MAAM,EAAE;IACvEJ,SAAS,EAAEO;EACb,CAAC,CAAC,EAAEC,SAAS,EAAEI,UAAU,CAAC;AAC5B,CAAC;AACD,eAAehB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}