package com.yinma.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yinma.dto.MaterialDTO;
import com.yinma.entity.MaterialEntity;
import com.yinma.mapper.MaterialMapper;
import com.yinma.service.MaterialService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 物料主数据管理Service实现类
 * 
 * <AUTHOR>
 * @since 2024-01-20
 */
@Service
public class MaterialServiceImpl extends ServiceImpl<MaterialMapper, MaterialEntity> implements MaterialService {

    @Autowired
    private MaterialMapper materialMapper;

    @Override
    public IPage<MaterialDTO> queryPage(Page<MaterialEntity> page, MaterialDTO.MaterialQueryDTO queryDTO) {
        IPage<MaterialEntity> entityPage = materialMapper.selectPageWithStock(page, queryDTO);
        
        IPage<MaterialDTO> dtoPage = new Page<>();
        BeanUtils.copyProperties(entityPage, dtoPage);
        
        List<MaterialDTO> dtoList = convertToMaterialDTOList(entityPage.getRecords());
        dtoPage.setRecords(dtoList);
        
        return dtoPage;
    }

    @Override
    public MaterialDTO getMaterialDetail(Long materialId) {
        MaterialEntity material = materialMapper.selectMaterialWithStock(materialId);
        if (material == null) {
            return null;
        }
        return convertToMaterialDTO(material);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MaterialDTO createMaterial(MaterialDTO materialDTO) {
        // 参数校验
        validateMaterial(materialDTO);
        
        // 检查物料编码是否已存在
        if (materialMapper.checkCodeExists(materialDTO.getMaterialCode(), null)) {
            throw new RuntimeException("物料编码已存在: " + materialDTO.getMaterialCode());
        }
        
        MaterialEntity material = new MaterialEntity();
        BeanUtils.copyProperties(materialDTO, material);
        material.setCreateTime(LocalDateTime.now());
        material.setUpdateTime(LocalDateTime.now());
        material.setStatus("ACTIVE"); // 默认启用
        
        materialMapper.insert(material);
        
        return getMaterialDetail(material.getMaterialId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MaterialDTO updateMaterial(MaterialDTO materialDTO) {
        // 参数校验
        validateMaterial(materialDTO);
        
        MaterialEntity existingMaterial = materialMapper.selectById(materialDTO.getMaterialId());
        if (existingMaterial == null) {
            throw new RuntimeException("物料不存在");
        }
        
        // 检查物料编码是否已被其他物料使用
        if (materialMapper.checkCodeExists(materialDTO.getMaterialCode(), materialDTO.getMaterialId())) {
            throw new RuntimeException("物料编码已被其他物料使用: " + materialDTO.getMaterialCode());
        }
        
        BeanUtils.copyProperties(materialDTO, existingMaterial);
        existingMaterial.setUpdateTime(LocalDateTime.now());
        
        materialMapper.updateById(existingMaterial);
        
        return getMaterialDetail(existingMaterial.getMaterialId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMaterial(Long materialId) {
        MaterialEntity material = materialMapper.selectById(materialId);
        if (material == null) {
            throw new RuntimeException("物料不存在");
        }
        
        // 检查物料是否被BOM使用
        List<MaterialDTO.MaterialUsageDTO> usageList = materialMapper.selectMaterialUsage(materialId);
        if (!CollectionUtils.isEmpty(usageList)) {
            throw new RuntimeException("物料正在被BOM使用，无法删除");
        }
        
        return materialMapper.deleteById(materialId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enableMaterial(Long materialId) {
        MaterialEntity material = materialMapper.selectById(materialId);
        if (material == null) {
            throw new RuntimeException("物料不存在");
        }
        
        material.setStatus("ACTIVE");
        material.setUpdateTime(LocalDateTime.now());
        
        return materialMapper.updateById(material) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableMaterial(Long materialId) {
        MaterialEntity material = materialMapper.selectById(materialId);
        if (material == null) {
            throw new RuntimeException("物料不存在");
        }
        
        material.setStatus("INACTIVE");
        material.setUpdateTime(LocalDateTime.now());
        
        return materialMapper.updateById(material) > 0;
    }

    @Override
    public List<MaterialDTO.MaterialStockDTO> getMaterialStock(Long materialId) {
        return materialMapper.selectMaterialStock(materialId);
    }

    @Override
    public List<MaterialDTO.MaterialSupplierDTO> getMaterialSuppliers(Long materialId) {
        return materialMapper.selectMaterialSuppliers(materialId);
    }

    @Override
    public List<MaterialDTO.MaterialPriceHistoryDTO> getMaterialPriceHistory(Long materialId) {
        return materialMapper.selectMaterialPriceHistory(materialId);
    }

    @Override
    public List<MaterialDTO.MaterialStockDetailDTO> getStockDetails(MaterialDTO.StockQueryDTO queryDTO) {
        return materialMapper.selectStockDetails(queryDTO);
    }

    @Override
    public List<MaterialDTO.MaterialSubstituteDTO> getMaterialSubstitutes(Long materialId) {
        return materialMapper.selectMaterialSubstitutes(materialId);
    }

    @Override
    public List<MaterialDTO> getLowStockMaterials(BigDecimal threshold) {
        List<MaterialEntity> lowStockMaterials = materialMapper.selectLowStockMaterials(threshold);
        return convertToMaterialDTOList(lowStockMaterials);
    }

    @Override
    public List<MaterialDTO> getExpiredMaterials(Integer days) {
        List<MaterialEntity> expiredMaterials = materialMapper.selectExpiredMaterials(days);
        return convertToMaterialDTOList(expiredMaterials);
    }

    @Override
    public List<MaterialDTO.MaterialCategoryStatDTO> getCategoryStatistics() {
        return materialMapper.selectCategoryStatistics();
    }

    @Override
    public List<MaterialDTO.MaterialAbcAnalysisDTO> getAbcAnalysisData() {
        return materialMapper.selectAbcAnalysisData();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateAbcCategory(List<MaterialDTO.MaterialAbcUpdateDTO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return false;
        }
        
        return materialMapper.batchUpdateAbcCategory(updateList) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdatePrice(List<MaterialDTO.MaterialPriceUpdateDTO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return false;
        }
        
        return materialMapper.batchUpdatePrice(updateList) > 0;
    }

    @Override
    public List<MaterialDTO.MaterialUsageDTO> getMaterialUsage(Long materialId) {
        return materialMapper.selectMaterialUsage(materialId);
    }

    @Override
    public boolean checkCodeExists(String materialCode, Long excludeId) {
        return materialMapper.checkCodeExists(materialCode, excludeId);
    }

    @Override
    public List<String> generateCodeSuggestions(String prefix, String category) {
        return materialMapper.generateCodeSuggestions(prefix, category);
    }

    @Override
    public MaterialDTO getMaterialFullInfo(Long materialId) {
        MaterialEntity material = materialMapper.selectMaterialFullInfo(materialId);
        if (material == null) {
            return null;
        }
        
        MaterialDTO dto = convertToMaterialDTO(material);
        
        // 获取库存信息
        dto.setStockList(getMaterialStock(materialId));
        
        // 获取供应商信息
        dto.setSupplierList(getMaterialSuppliers(materialId));
        
        // 获取价格历史
        dto.setPriceHistoryList(getMaterialPriceHistory(materialId));
        
        // 获取替代关系
        dto.setSubstituteList(getMaterialSubstitutes(materialId));
        
        // 获取使用情况
        dto.setUsageList(getMaterialUsage(materialId));
        
        return dto;
    }

    @Override
    public MaterialDTO.MaterialValidationResultDTO validateMaterialData(MaterialDTO materialDTO) {
        MaterialDTO.MaterialValidationResultDTO result = new MaterialDTO.MaterialValidationResultDTO();
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();
        
        // 基础校验
        if (!StringUtils.hasText(materialDTO.getMaterialCode())) {
            errors.add("物料编码不能为空");
        } else if (checkCodeExists(materialDTO.getMaterialCode(), materialDTO.getMaterialId())) {
            errors.add("物料编码已存在");
        }
        
        if (!StringUtils.hasText(materialDTO.getMaterialName())) {
            errors.add("物料名称不能为空");
        }
        
        if (!StringUtils.hasText(materialDTO.getCategory())) {
            errors.add("物料分类不能为空");
        }
        
        if (!StringUtils.hasText(materialDTO.getUnit())) {
            errors.add("计量单位不能为空");
        }
        
        // 价格校验
        if (materialDTO.getStandardPrice() != null && materialDTO.getStandardPrice().compareTo(BigDecimal.ZERO) < 0) {
            errors.add("标准价格不能为负数");
        }
        
        // 库存校验
        if (materialDTO.getSafetyStock() != null && materialDTO.getSafetyStock().compareTo(BigDecimal.ZERO) < 0) {
            errors.add("安全库存不能为负数");
        }
        
        if (materialDTO.getMinStock() != null && materialDTO.getMinStock().compareTo(BigDecimal.ZERO) < 0) {
            errors.add("最小库存不能为负数");
        }
        
        if (materialDTO.getMaxStock() != null && materialDTO.getMaxStock().compareTo(BigDecimal.ZERO) < 0) {
            errors.add("最大库存不能为负数");
        }
        
        // 库存逻辑校验
        if (materialDTO.getMinStock() != null && materialDTO.getMaxStock() != null) {
            if (materialDTO.getMinStock().compareTo(materialDTO.getMaxStock()) > 0) {
                errors.add("最小库存不能大于最大库存");
            }
        }
        
        if (materialDTO.getSafetyStock() != null && materialDTO.getMinStock() != null) {
            if (materialDTO.getSafetyStock().compareTo(materialDTO.getMinStock()) > 0) {
                warnings.add("安全库存大于最小库存，建议调整");
            }
        }
        
        // 设置结果
        result.setValid(errors.isEmpty());
        result.setErrors(errors);
        result.setWarnings(warnings);
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MaterialDTO.MaterialImportResultDTO importMaterials(List<MaterialDTO> materialList) {
        MaterialDTO.MaterialImportResultDTO result = new MaterialDTO.MaterialImportResultDTO();
        List<String> successList = new ArrayList<>();
        List<String> failureList = new ArrayList<>();
        
        for (MaterialDTO materialDTO : materialList) {
            try {
                // 数据校验
                MaterialDTO.MaterialValidationResultDTO validation = validateMaterialData(materialDTO);
                if (!validation.getValid()) {
                    failureList.add(materialDTO.getMaterialCode() + ": " + String.join(", ", validation.getErrors()));
                    continue;
                }
                
                // 检查是否已存在
                if (checkCodeExists(materialDTO.getMaterialCode(), null)) {
                    // 更新
                    MaterialEntity existing = materialMapper.selectOne(
                        new LambdaQueryWrapper<MaterialEntity>()
                            .eq(MaterialEntity::getMaterialCode, materialDTO.getMaterialCode())
                    );
                    materialDTO.setMaterialId(existing.getMaterialId());
                    updateMaterial(materialDTO);
                    successList.add(materialDTO.getMaterialCode() + ": 更新成功");
                } else {
                    // 新增
                    createMaterial(materialDTO);
                    successList.add(materialDTO.getMaterialCode() + ": 创建成功");
                }
                
            } catch (Exception e) {
                failureList.add(materialDTO.getMaterialCode() + ": " + e.getMessage());
            }
        }
        
        result.setTotalCount(materialList.size());
        result.setSuccessCount(successList.size());
        result.setFailureCount(failureList.size());
        result.setSuccessDetails(successList);
        result.setFailureDetails(failureList);
        
        return result;
    }

    @Override
    public byte[] exportMaterials(MaterialDTO.MaterialQueryDTO queryDTO) {
        // TODO: 实现导出功能
        throw new RuntimeException("导出功能待实现");
    }

    /**
     * 校验物料数据
     */
    private void validateMaterial(MaterialDTO materialDTO) {
        if (!StringUtils.hasText(materialDTO.getMaterialCode())) {
            throw new RuntimeException("物料编码不能为空");
        }
        
        if (!StringUtils.hasText(materialDTO.getMaterialName())) {
            throw new RuntimeException("物料名称不能为空");
        }
        
        if (!StringUtils.hasText(materialDTO.getCategory())) {
            throw new RuntimeException("物料分类不能为空");
        }
        
        if (!StringUtils.hasText(materialDTO.getUnit())) {
            throw new RuntimeException("计量单位不能为空");
        }
        
        // 价格校验
        if (materialDTO.getStandardPrice() != null && materialDTO.getStandardPrice().compareTo(BigDecimal.ZERO) < 0) {
            throw new RuntimeException("标准价格不能为负数");
        }
        
        // 库存校验
        if (materialDTO.getSafetyStock() != null && materialDTO.getSafetyStock().compareTo(BigDecimal.ZERO) < 0) {
            throw new RuntimeException("安全库存不能为负数");
        }
        
        if (materialDTO.getMinStock() != null && materialDTO.getMinStock().compareTo(BigDecimal.ZERO) < 0) {
            throw new RuntimeException("最小库存不能为负数");
        }
        
        if (materialDTO.getMaxStock() != null && materialDTO.getMaxStock().compareTo(BigDecimal.ZERO) < 0) {
            throw new RuntimeException("最大库存不能为负数");
        }
        
        // 库存逻辑校验
        if (materialDTO.getMinStock() != null && materialDTO.getMaxStock() != null) {
            if (materialDTO.getMinStock().compareTo(materialDTO.getMaxStock()) > 0) {
                throw new RuntimeException("最小库存不能大于最大库存");
            }
        }
    }

    /**
     * 转换为DTO列表
     */
    private List<MaterialDTO> convertToMaterialDTOList(List<MaterialEntity> materialList) {
        if (CollectionUtils.isEmpty(materialList)) {
            return new ArrayList<>();
        }
        
        return materialList.stream()
            .map(this::convertToMaterialDTO)
            .collect(Collectors.toList());
    }

    /**
     * 转换为DTO
     */
    private MaterialDTO convertToMaterialDTO(MaterialEntity material) {
        MaterialDTO dto = new MaterialDTO();
        BeanUtils.copyProperties(material, dto);
        return dto;
    }
}